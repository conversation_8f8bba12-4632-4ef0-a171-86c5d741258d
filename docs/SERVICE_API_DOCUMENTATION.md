# Service API Documentation

This document provides comprehensive documentation for all service-related APIs in the Django backend, including their data structures, endpoints, and transformation logic.

## Overview

The backend provides a comprehensive set of APIs for managing services, employees, appointments, and business operations. All APIs follow RESTful conventions and use Django REST Framework with standard JSON responses.

## API Base URL

All endpoints are prefixed with `/api/v1/`

## Authentication

Most endpoints require authentication via JWT tokens. Use the `Authorization: Bearer <token>` header.

---

## 1. Service Categories API

### Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/api/v1/service-categories/` | List all service categories |
| `POST` | `/api/v1/service-categories/` | Create a new category |
| `GET` | `/api/v1/service-categories/{id}/` | Get specific category |
| `PUT` | `/api/v1/service-categories/{id}/` | Update category |
| `PATCH` | `/api/v1/service-categories/{id}/` | Partial update category |
| `DELETE` | `/api/v1/service-categories/{id}/` | Delete category |

### Data Structure

```typescript
interface ServiceCategory {
  id: number;
  name: string;
  description: string;
  color: string;  // Hex color code
  order: number;  // Display order
  is_active: boolean;
}
```

### Features
- **Pagination**: Supports paginated responses (10 items per page)
- **Filtering**: Only shows active categories by default
- **Search**: Search by name and description
- **Ordering**: Sort by name, order
- **Color Choices**: Predefined color palette for categories
- **Soft Delete**: Uses `is_active` field for soft deletion

### Example Response
```json
{
  "count": 6,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": 1,
      "name": "Lash Fullset",
      "description": "Full set of eyelash extensions",
      "color": "#B47E62",
      "order": 1,
      "is_active": true
    }
  ]
}
```

---

## 2. Services API

### Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/api/v1/services/` | List all services |
| `POST` | `/api/v1/services/` | Create a new service |
| `GET` | `/api/v1/services/{id}/` | Get specific service |
| `PUT` | `/api/v1/services/{id}/` | Update service |
| `PATCH` | `/api/v1/services/{id}/` | Partial update service |
| `DELETE` | `/api/v1/services/{id}/` | Delete service |
| `GET` | `/api/v1/services/{id}/employees/` | Get employees who offer this service |

### Data Structure

```typescript
interface Service {
  id: number;
  business: number;
  category: number;
  category_name: string;
  name: string;
  description: string;
  price: string;         // Decimal as string
  duration: number;      // Minutes
  total_duration: number; // Duration + buffer time
  buffer_time: number;   // Minutes
  color: string;         // Inherited from category
  is_active: boolean;
  image: string | null;
  display_order: number;
  addons: ServiceAddOn[];
  employee_services: EmployeeService[];
}

interface ServiceAddOn {
  id: number;
  service: number;
  addon: {
    id: number;
    name: string;
    description: string;
    base_price: string;
    duration: number;
    color: string;
    is_active: boolean;
    image: string | null;
    display_order: number;
  };
  price: number;
  duration: number;
  is_active: boolean;
  is_required: boolean;
  display_order: number;
}
```

### Query Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `category` | string | Filter by category ID(s) - comma-separated |
| `show_online` | boolean | Filter by online visibility |
| `search` | string | Search in name, description, category name |
| `ordering` | string | Sort by name, base_price, base_duration, display_order |

### Features
- **Category Filtering**: Single or multiple categories
- **Online Booking Filter**: Show only services available online
- **Search**: Full-text search across multiple fields
- **Ordering**: Multiple sort options
- **Add-ons**: Includes associated add-ons with pricing
- **Employee Services**: Shows employee-specific pricing

### Data Transformations

#### Duration Fields
- Backend stores as `DurationField` (Python timedelta)
- API converts to integer minutes for frontend consumption
- `base_duration` → `duration` (minutes)
- `buffer_time` → `buffer_time` (minutes)
- `total_duration` = `base_duration` + `buffer_time`

#### Price Fields
- Backend stores as `DecimalField`
- API serializes as string to avoid floating-point precision issues
- Frontend should parse as decimal/float for calculations

---

## 3. Employees API

### Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/api/v1/employees/` | List all employees |
| `GET` | `/api/v1/employees/{id}/` | Get specific employee |
| `GET` | `/api/v1/employees/{id}/services/` | Get employee's services |
| `GET` | `/api/v1/employees/{id}/service-addons/` | Get employee's add-ons |
| `GET` | `/api/v1/employees/{id}/services/{service_id}/addons/` | Get add-ons for specific service |

### Data Structure

```typescript
interface Employee {
  id: number;
  full_name: string;
  user_details: {
    id: number;
    email: string;
    first_name: string;
    last_name: string;
  };
  stylist_level: number | null;
  stylist_level_display: string;
  is_active: boolean;
}

interface EmployeeService {
  service_id: number;
  service_name: string;
  service_short_name: string;
  category_id: number;
  category_name: string;
  service_display_order: number;
  category_display_order: number;
  price: string;
  duration: number;
  price_source: string;
  service_details: {
    description: string;
    image: string | null;
    buffer_time: number;
    show_online: boolean;
  };
}
```

### Query Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `business_id` | number | Filter by business ID |
| `search` | string | Search in employee names and email |
| `ordering` | string | Sort by name |

### Features
- **Service Pricing Logic**: Combines employee-specific, stylist-level, and base pricing
- **Add-on Management**: Handles employee-specific add-on availability
- **Category Filtering**: Filter services/add-ons by category
- **Sorting**: Results sorted by category and display order

### Pricing Hierarchy
1. **Employee-specific pricing** (EmployeeService.custom_price)
2. **Stylist-level pricing** (StylistLevelService.price)
3. **Base service pricing** (Service.base_price)

---

## 4. Employee Services API

### Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/api/v1/employee-services/` | List employee-service relationships |
| `POST` | `/api/v1/employee-services/` | Create new relationship |
| `GET` | `/api/v1/employee-services/{id}/` | Get specific relationship |
| `PUT` | `/api/v1/employee-services/{id}/` | Update relationship |
| `DELETE` | `/api/v1/employee-services/{id}/` | Delete relationship |

### Data Structure

```typescript
interface EmployeeService {
  id: number;
  business: number;
  employee: EmployeeBasic;
  service: number;
  price: string;
  duration: number;
  is_active: boolean;
}
```

### Query Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `employee_id` | number | Filter by employee |
| `service_id` | number | Filter by service |
| `business_id` | number | Filter by business |

---

## 5. Stylist Level Services API

### Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/api/v1/stylist-level-services/` | List stylist-level service pricing |
| `POST` | `/api/v1/stylist-level-services/` | Create new pricing |
| `GET` | `/api/v1/stylist-level-services/{id}/` | Get specific pricing |
| `PUT` | `/api/v1/stylist-level-services/{id}/` | Update pricing |
| `DELETE` | `/api/v1/stylist-level-services/{id}/` | Delete pricing |

### Data Structure

```typescript
interface StylistLevelService {
  id: number;
  business: number;
  service: number;
  stylist_level: number;
  price: string;
  duration: number;
  is_offered: boolean;
  is_active: boolean;
}
```

### Purpose
- Define default pricing for services based on stylist level
- Control which services are available to each stylist level
- Acts as fallback pricing when no employee-specific pricing exists

---

## 6. Appointments API

### Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/api/v1/appointments/` | List appointments |
| `POST` | `/api/v1/appointments/` | Create appointment |
| `GET` | `/api/v1/appointments/{id}/` | Get specific appointment |
| `PUT` | `/api/v1/appointments/{id}/` | Update appointment |
| `PATCH` | `/api/v1/appointments/{id}/` | Partial update appointment |
| `DELETE` | `/api/v1/appointments/{id}/` | Delete appointment |
| `GET` | `/api/v1/appointments/available-times/` | Get available time slots |

### Data Structure

```typescript
interface Appointment {
  id: number;
  customer: number;
  employee: number;
  start_time: string;  // ISO datetime
  end_time: string;    // Computed from services
  status: 'requested' | 'confirmed' | 'accepted' | 'checked_in' | 'service_started' | 'completed' | 'cancelled' | 'no_show';
  payment_status: 'unpaid' | 'paid' | 'refunded';
  source: 'admin' | 'online';
  notes_from_customer: string;
  cancellation_reason: string;
  services: AppointmentService[];
  addons: AppointmentAddOn[];
  total_price: number;
  total_duration: number;
  created_at: string;
  updated_at: string;
}

interface AppointmentService {
  id: number;
  service: number;
  quantity: number;
  base_price: string;
  price_override: string | null;
  duration: number;
  buffer_time: number;
  notes: string;
  final_price: number;
  total_duration: number;
}
```

### Available Times Query Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `date` | string | Yes | Date in YYYY-MM-DD format |
| `service_id` | number | Yes | Service ID |
| `employee_id` | number | No | Specific employee ID |
| `business_id` | number | No | Business ID |

### Features
- **Computed End Times**: End time calculated from service durations
- **Conflict Detection**: Prevents double-booking employees
- **Smart Booking Rules**: Configurable rules for time slot optimization
- **Status Tracking**: Complete appointment lifecycle management

---

## 7. Business Customer API

### Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/api/v1/business-customers/` | List customers |
| `POST` | `/api/v1/business-customers/` | Create customer |
| `GET` | `/api/v1/business-customers/{id}/` | Get specific customer |
| `PUT` | `/api/v1/business-customers/{id}/` | Update customer |
| `DELETE` | `/api/v1/business-customers/{id}/` | Delete customer |

### Current User Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/api/v1/business-customers/me/` | List customers for current user's business |
| `GET` | `/api/v1/business-customers/me/{id}/` | Get specific customer |
| `GET` | `/api/v1/business-customers/me/{id}/service-suggestions/` | Get service suggestions |

### Data Structure

```typescript
interface BusinessCustomer {
  id: number;
  business: number;
  customer: CustomerProfile;
  notes: string;
  loyalty_points: number;
  opt_in_marketing: boolean;
  email_reminders: boolean;
  sms_reminders: boolean;
  tags: CustomerTag[];
  // Import fields
  customer_since: string;
  last_visited: string;
  membership_type: string;
  referred_by: string;
  appointments_booked: number;
  amount_paid: string;
  created_at: string;
  updated_at: string;
}
```

---

## 8. Businesses API

### Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/api/v1/businesses/` | List businesses |
| `POST` | `/api/v1/businesses/` | Create business |
| `GET` | `/api/v1/businesses/{id}/` | Get specific business |
| `PUT` | `/api/v1/businesses/{id}/` | Update business |
| `DELETE` | `/api/v1/businesses/{id}/` | Delete business |
| `GET` | `/api/v1/businesses/{id}/appointments/available-times/` | Get available appointment times |

### Data Structure

```typescript
interface Business {
  id: number;
  name: string;
  description: string;
  phone: string;
  email: string;
  website: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}
```

---

## 9. Locations API

### Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/api/v1/locations/` | List locations |
| `POST` | `/api/v1/locations/` | Create location |
| `GET` | `/api/v1/locations/{id}/` | Get specific location |
| `PUT` | `/api/v1/locations/{id}/` | Update location |
| `DELETE` | `/api/v1/locations/{id}/` | Delete location |

### Query Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `business_id` | number | Filter by business |

### Data Structure

```typescript
interface Location {
  id: number;
  business: number;
  name: string;
  address_line1: string;
  address_line2: string;
  city: string;
  state: string;
  country: string;
  postal_code: string;
  phone: string;
  email: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}
```

---

## 10. Online Booking Rules API

### Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/api/v1/booking-rules/` | List booking rules |
| `GET` | `/api/v1/booking-rules/{id}/` | Get specific rules |
| `GET` | `/api/v1/booking-rules/by-business/{business_id}/` | Get rules for business |

### Query Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `business_id` | number | Filter by business |

### Data Structure

```typescript
interface OnlineBookingRules {
  id: number;
  business: number;
  timezone: string;
  currency: string;
  max_days_in_advance: number;
  min_hours_before: number;
  appointment_interval: number;
  allow_cancellation: boolean;
  cancellation_hours_before: number;
  cancellation_policy: string;
  allow_rescheduling: boolean;
  rescheduling_hours_before: number;
  require_payment: boolean;
  deposit_percentage: string;
  // Smart booking rules
  enable_bookend_slots: boolean;
  enable_gapless_booking: boolean;
  enable_tentative_hold: boolean;
  tentative_hold_tolerance: number;
  created_at: string;
  updated_at: string;
}
```

---

## 11. Stylist Levels API

### Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/api/v1/stylist-levels/` | List stylist levels |
| `POST` | `/api/v1/stylist-levels/` | Create stylist level |
| `GET` | `/api/v1/stylist-levels/{id}/` | Get specific level |
| `PUT` | `/api/v1/stylist-levels/{id}/` | Update level |
| `DELETE` | `/api/v1/stylist-levels/{id}/` | Delete level |

### Query Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `business_id` | number | Filter by business |

### Data Structure

```typescript
interface StylistLevel {
  id: number;
  business: number;
  name: string;
  description: string;
  level_order: number;
  is_default: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}
```

---

## 12. Form Templates API

### Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/api/v1/forms/templates/` | List form templates |
| `POST` | `/api/v1/forms/templates/` | Create template |
| `GET` | `/api/v1/forms/templates/{id}/` | Get specific template |
| `PUT` | `/api/v1/forms/templates/{id}/` | Update template |
| `DELETE` | `/api/v1/forms/templates/{id}/` | Delete template |
| `GET` | `/api/v1/forms/templates/{id}/history/` | Get template history |

### Data Structure

```typescript
interface FormTemplate {
  id: number;
  name: string;
  document_type: string;
  status: 'Published' | 'Draft';
  content: Record<string, any>;  // JSON form structure
  business: number;
  created_at: string;
  updated_at: string;
}
```

---

## 13. Form Submissions API

### Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/api/v1/forms/submissions/` | List form submissions |
| `POST` | `/api/v1/forms/submissions/` | Create submission |
| `GET` | `/api/v1/forms/submissions/{id}/` | Get specific submission |
| `PUT` | `/api/v1/forms/submissions/{id}/` | Update submission |
| `DELETE` | `/api/v1/forms/submissions/{id}/` | Delete submission |
| `GET` | `/api/v1/forms/submissions/by_template/` | Get submissions by template |

### Query Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `business_id` | number | Filter by business |
| `template_id` | number | Filter by template |

### Data Structure

```typescript
interface FormSubmission {
  id: number;
  form_template: number;
  business: number;
  customer: number | null;
  submitted_by: number | null;
  content: Record<string, any>;  // JSON submission data
  status: 'Submitted' | 'Reviewed' | 'Approved' | 'Rejected';
  created_at: string;
  updated_at: string;
}
```

---

## Common Data Transformation Patterns

### 1. Duration Fields
- **Backend**: Python `timedelta` objects
- **API**: Integer minutes
- **Frontend**: Should handle as minutes for calculations

### 2. Price Fields
- **Backend**: `DecimalField` for precision
- **API**: String representation to avoid float precision issues
- **Frontend**: Parse as decimal/float, display with proper formatting

### 3. DateTime Fields
- **Backend**: Django `DateTimeField` with timezone support
- **API**: ISO 8601 strings
- **Frontend**: Parse with timezone awareness

### 4. Soft Deletion
- Most models use `is_active` field instead of hard deletion
- API filters show only active records by default
- Admin interfaces can show inactive records

### 5. Business Scoping
- Most endpoints are scoped to the current user's business
- Some endpoints accept `business_id` query parameter
- Authentication ensures users can only access their business data

---

## Error Handling

### Standard Error Response Format

```json
{
  "error": "Error message",
  "detail": "Detailed error description",
  "status_code": 400
}
```

### Validation Error Response

```json
{
  "field_name": ["Field-specific error message"],
  "non_field_errors": ["General validation errors"]
}
```

### Common HTTP Status Codes

| Status | Description |
|--------|-------------|
| 200 | OK - Request successful |
| 201 | Created - Resource created successfully |
| 400 | Bad Request - Validation error |
| 401 | Unauthorized - Authentication required |
| 403 | Forbidden - Permission denied |
| 404 | Not Found - Resource not found |
| 500 | Internal Server Error - Server error |

---

## Performance Considerations

### 1. Pagination
- Most list endpoints support pagination
- Default page size is typically 10-20 items
- Use `page` and `page_size` parameters

### 2. Filtering
- Use query parameters to filter results
- Avoid fetching unnecessary data
- Consider using sparse fieldsets where available

### 3. Caching
- Some endpoints may implement caching
- Use appropriate cache headers
- Consider client-side caching for static data

### 4. Bulk Operations
- For bulk operations, consider using batch endpoints
- Avoid making multiple sequential API calls
- Use transactions for data consistency

---

## Integration Tips

### 1. Service Selection Flow
1. Fetch service categories: `GET /api/v1/service-categories/`
2. Fetch services by category: `GET /api/v1/services/?category=1,2,3`
3. Get employee services: `GET /api/v1/employees/{id}/services/`
4. Get service add-ons: `GET /api/v1/employees/{id}/services/{service_id}/addons/`

### 2. Appointment Booking Flow
1. Check available times: `GET /api/v1/appointments/available-times/`
2. Create appointment: `POST /api/v1/appointments/`
3. Add services: Include in appointment creation
4. Add add-ons: Include in appointment creation

### 3. Business Data Flow
1. Get business info: `GET /api/v1/businesses/{id}/`
2. Get booking rules: `GET /api/v1/booking-rules/?business_id={id}`
3. Get locations: `GET /api/v1/locations/?business_id={id}`
4. Get employees: `GET /api/v1/employees/?business_id={id}`

This documentation provides a comprehensive overview of all service-related APIs in the Django backend. Use it as a reference for frontend integration and ensure proper data handling and transformation. 