{"name": "chatbook-business-frontend", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "dev:v18": "bash -c 'source $HOME/.nvm/nvm.sh && nvm use 18 && vite --host'", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@types/mjml": "^4.7.4", "mjml": "^4.15.3", "@heroicons/react": "^2.2.0", "@tanstack/react-query": "^5.81.5", "axios": "^1.6.5", "chart.js": "^4.5.0", "date-fns": "^4.1.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lodash": "^4.17.21", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-chartjs-2": "^5.3.0", "react-colorful": "^5.6.1", "react-dom": "^18.2.0", "react-quill": "^2.0.0", "react-hot-toast": "^2.5.2", "react-router-dom": "^6.22.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "vite": "^6.3.5"}, "engines": {"node": ">=18.0.0"}}