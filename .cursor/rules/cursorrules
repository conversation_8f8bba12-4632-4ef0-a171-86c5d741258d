# Cursor Rules

## Project Structure
This is a React + Vite + TailwindCSS project with the following structure:
- Components: `src/components/`
- Pages: `src/pages/`
- Features: `src/features/`
- Hooks: `src/hooks/`
- Utils: `src/utils/`

## Whenever you need a React component

1. Carefully consider the component's purpose, functionality, and design

2. Think slowly, step by step, and outline your reasoning

3. Check if a similar component already exists in `src/components/` or within relevant feature folders in `src/features/`

4. If it doesn't exist, generate a detailed prompt for the component, including:
   - Component name and purpose
   - Desired props and their types
   - Any specific styling or behavior requirements
   - Mention of using Tailwind CSS for styling
   - Use JavaScript/JSX (not TypeScript)

5. URL encode the prompt.

6. Create a clickable link in this format:
   [ComponentName](https://v0.dev/chat?q={encoded_prompt})

7. After generating, adapt the component to fit our project structure:
   - Import components from `../components/` or `../../components/` as needed
   - Ensure it follows our existing component patterns (function declarations, forwardRef when needed)
   - Add any necessary custom logic or state management
   - Use our established TailwindCSS classes and color scheme (primary-*)

## Code Style Guidelines

- Use function declarations for named components
- Use arrow functions for inline/callback functions
- Always add displayName for forwardRef components
- Document complex functions with JSDoc comments
- Use descriptive variable and function names
- Prefer feature-based organization for complex functionality
- Use custom hooks in `src/hooks/` for reusable logic

## API Integration

- Use the custom `useApi` hook from `src/hooks/useApi.js` for API calls
- Store API configuration in environment variables (VITE_API_URL)
- Handle loading states and errors consistently

Example component prompt template:
"Create a React component named {ComponentName} using JavaScript/JSX and Tailwind CSS. It should {description of functionality}. Props should include {list of props with types}. The component should {any specific styling or behavior notes}. Use function declaration syntax and include proper prop validation."