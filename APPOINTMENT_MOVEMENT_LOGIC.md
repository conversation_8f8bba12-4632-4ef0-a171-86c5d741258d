# Appointment Movement Logic - iOS Calendar Implementation

## Overview

This document provides a comprehensive analysis of the appointment click and drag movement logic extracted from the iOS Chatbook Pro Calendar implementation. The system supports sophisticated multi-dimensional movement with intelligent snapping, cross-day dragging, and adaptive behavior across different time resolutions (5, 15, 30 minutes) and view modes (Day, Week).

## Executive Summary

The iOS implementation features a **fully-supported cross-day dragging system** in Week view, allowing appointments to be moved both horizontally (between days) and vertically (time changes) with intelligent snapping to time grid lines and appointment boundaries. Day view supports vertical-only movement for single employees, and column-based movement for multi-employee scenarios.

## Architecture Components

### Core Classes
1. **CalendarAppointmentSlotView** - Main appointment UI component with gesture handling
2. **CalendarAppointmentSlotViewModel** - Business logic for appointment interactions
3. **TimeGridViewModel** - Time grid calculations and snapping logic
4. **CalendarViewModel** - Overall calendar state management

## Movement Modes

### 1. Regular Drag Mode (Time Change Only)
- **Trigger**: Standard drag gesture on appointment
- **Behavior**: Vertical-only movement to change time
- **Confirmation**: Shows dialog before applying changes

### 2. Move Mode (Full Repositioning)
- **Trigger**: Long press (0.5 seconds) on appointment
- **Behavior**: Full appointment repositioning with snapping
- **Visual**: Dimmed original + ghost copy during drag

## Gesture System

### Gesture Hierarchy
```swift
// Combined gesture handling with priority
.simultaneousGesture(LongPressGesture(minimumDuration: 0.5))
.simultaneousGesture(DragGesture())
.highPriorityGesture(moveModeDragGesture) // Blocks scrolling during move mode
.simultaneousGesture(TapGesture()) // Selection when not in move mode
```

### Gesture State Management
```swift
@State private var dragOffset: CGSize = .zero
@State private var isDragging: Bool = false
@State private var isInLongPress: Bool = false
@State private var currentDropColumn: Int? = nil
@State private var lastSnappedY: CGFloat? = nil
@State private var hasNotifiedActiveDrag: Bool = false
```

## Time Resolution Handling

### Supported Resolutions
- **5 minutes**: Intervals [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55]
- **15 minutes**: Intervals [0, 15, 30, 45]
- **30 minutes**: Intervals [0, 30]

### Grid Calculation
```swift
var hourMinuteIntervals: [Int] {
    switch timeResolution {
    case 5:
        return [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55]
    case 15:
        return [0, 15, 30, 45]
    case 30:
        return [0, 30]
    default:
        return [0, 30]
    }
}
```

## Snapping Logic

### Position Calculation
```swift
func calculatePositionRelativeToGridLines(hour: Int, minute: Int) -> CGFloat {
    let totalMinutesFromStart = (hour - displayHourStart) * 60 + minute
    let intervalIndex = totalMinutesFromStart / timeResolution
    let position = CGFloat(intervalIndex) * gridCellHeight + (gridCellHeight / 2.0)
    return position
}
```

### Smart Snapping Algorithm
```swift
func calculateSnappedPosition(
    currentY: CGFloat,
    verticalTranslation: CGFloat,
    lastSnappedY: CGFloat?,
    movingAppointment: Appointment?,
    allAppointments: [Appointment]
) -> (snappedY: CGFloat, newLastSnappedY: CGFloat, shouldTriggerHaptic: Bool) {
    let targetY = currentY + verticalTranslation
    var closestSnapY = targetY
    var closestDistance = CGFloat.greatestFiniteMagnitude

    // 1. Check appointment border snapping
    if let movingAppointment = movingAppointment {
        let movingHeight = calculateAppointmentHeight(for: movingAppointment)
        let movingBottomY = targetY + movingHeight

        for appointment in allAppointments {
            if appointment.id == movingAppointment.id { continue }
            
            let appointmentStartY = yPosition(for: appointment.startTime)
            let appointmentEndY = yPosition(for: appointment.endTime)

            // Snap moving appointment's TOP to other appointment borders
            let topToStartDistance = abs(targetY - appointmentStartY)
            if topToStartDistance < closestDistance && topToStartDistance < 20.0 {
                closestDistance = topToStartDistance
                closestSnapY = appointmentStartY
            }

            // Snap moving appointment's BOTTOM to other appointment borders
            let bottomToStartDistance = abs(movingBottomY - appointmentStartY)
            if bottomToStartDistance < closestDistance && bottomToStartDistance < 20.0 {
                closestDistance = bottomToStartDistance
                closestSnapY = appointmentStartY - movingHeight
            }
        }
    }

    // 2. Check time grid line snapping
    let (above, below) = findClosestTimePoints(to: targetY)
    let timeGridSnapPoints = [
        calculatePositionRelativeToGridLines(hour: above.hour, minute: above.minute),
        calculatePositionRelativeToGridLines(hour: below.hour, minute: below.minute)
    ]

    for snapPoint in timeGridSnapPoints {
        let distance = abs(targetY - snapPoint)
        if distance < closestDistance {
            closestDistance = distance
            closestSnapY = snapPoint
        }
    }

    // 3. Haptic feedback on position change
    let shouldTriggerHaptic = lastSnappedY == nil || abs(closestSnapY - (lastSnappedY ?? 0)) > 0.1

    return (snappedY: closestSnapY, newLastSnappedY: closestSnapY, shouldTriggerHaptic: shouldTriggerHaptic)
}
```

## Cross-Day Dragging Support

### ✅ FULLY SUPPORTED - Week View Cross-Day Movement

The iOS implementation **explicitly supports cross-day dragging** in Week view with sophisticated column-based positioning:

```swift
// Week view - FULL cross-day support
if let calendarVM = viewModel.calendarViewModel, calendarVM.viewMode == .week {
    let dayColumnWidth = viewModel.timeGridViewModel.dayColumnWidth
    let horizontalDrag = gesture.translation.width
    let currentAppointmentColumn = getCurrentAppointmentColumn()

    // Calculate target day column based on horizontal drag distance
    let columnOffset = Int(round(horizontalDrag / dayColumnWidth))
    let targetColumn = currentAppointmentColumn + columnOffset

    // Clamp to valid day range (prevents dragging outside visible week)
    let displayDays = calendarVM.displayDays  // Array of 7 dates for the week
    let clampedTargetColumn = max(0, min(targetColumn, displayDays.count - 1))
    
    // Apply discrete horizontal snapping to target day column
    let snappedHorizontalOffset = CGFloat(targetColumnOffset) * dayColumnWidth
    
    // Combined 2D movement: horizontal (days) + vertical (time)
    dragOffset = CGSize(width: snappedHorizontalOffset, height: snappedVerticalOffset)
}
```

### Cross-Day Movement Features:

1. **Discrete Day Snapping**: Appointments snap to exact day column boundaries
2. **Visual Week Range**: Supports movement across all 7 visible days in week view
3. **Date Calculation**: Automatically calculates new appointment date based on target column
4. **Boundary Clamping**: Prevents dragging outside visible week range
5. **Real-time Feedback**: Ghost copy shows appointment in target day during drag

### Day Column Detection Logic:

```swift
private func getCurrentAppointmentColumn() -> Int {
    if calendarVM.viewMode == .week {
        let displayDays = calendarVM.displayDays
        let appointmentDate = viewModel.appointment.startTime

        // Find which day column this appointment belongs to
        for (index, day) in displayDays.enumerated() {
            if calendar.isDate(appointmentDate, inSameDayAs: day) {
                return index  // Returns 0-6 for Mon-Sun (or configured week start)
            }
        }
        return 0 // Default to first column if not found
    }
}
```

### New Date Calculation for Cross-Day Drops:

```swift
private func calculateNewDateForDrop(column: Int, verticalTranslation: CGFloat) -> Date? {
    if calendarVM.viewMode == .week {
        let displayDays = calendarVM.displayDays
        
        // Get target date from day column (CROSS-DAY SUPPORT)
        let clampedColumn = max(0, min(column, displayDays.count - 1))
        let targetDate = displayDays[clampedColumn]  // New day for appointment
        
        // Calculate new time based on vertical position
        let originalYPosition = timeGridViewModel.yPosition(for: appointment.startTime)
        let newYPosition = originalYPosition + verticalTranslation
        
        // Combine new date + new time = complete cross-day movement
        return timeGridViewModel.dateAtPosition(
            y: newYPosition, 
            baseDate: targetDate,  // Uses target day, not original day
            useProportionalCalculation: true
        )
    }
}
```

## View Mode Specific Behavior

### Week View Movement (Cross-Day Enabled)
- **Horizontal Movement**: ✅ Full cross-day dragging with discrete column snapping
- **Vertical Movement**: ✅ Time-based snapping with appointment borders
- **2D Movement**: ✅ Simultaneous day and time changes in single gesture
- **Day Range**: 7 days (configurable week start: Monday/Sunday)
- **Column Calculation**: `columnOffset = Int(round(horizontalDrag / dayColumnWidth))`
- **Date Range**: Supports movement across entire visible week (displayDays array)

```swift
// Week view - FULL 2D movement support
let displayDays = calendarVM.displayDays  // [Mon, Tue, Wed, Thu, Fri, Sat, Sun]
let targetColumn = clampedTargetColumn    // 0-6 index
let targetDate = displayDays[targetColumn] // Actual date for new day
let snappedHorizontalOffset = CGFloat(targetColumnOffset) * dayColumnWidth

// Apply both horizontal (day) and vertical (time) movement
dragOffset = CGSize(width: snappedHorizontalOffset, height: snappedVerticalOffset)
```

### Day View Movement (Employee-Based)
- **Single Employee**: Vertical-only movement (time changes only)
- **Multi-Employee**: Column-based employee switching + vertical movement
- **Cross-Day**: ❌ Not supported (single day focus)
- **Constraints**: No horizontal movement in single-employee mode

```swift
// Day view movement constraints
if calendarVM.viewMode == .week {
    // Week view: Allow both horizontal (cross-day) and vertical (time) movement
    dragOffset = CGSize(width: snappedHorizontalOffset, height: snappedVerticalOffset)
} else {
    // Day view: Vertical-only (time changes), no cross-day movement
    dragOffset = CGSize(width: 0, height: snappedVerticalOffset)
    currentDropColumn = 0  // Always stays in same day
}
```

### Multi-Employee Day View Columns:
```swift
// Day view with multiple employees - column-based employee switching
if calendarVM.selectedEmployeeIDs.count > 1 {
    let sortedEmployeeIds = Array(calendarVM.selectedEmployeeIDs).sorted()
    let totalWidth = UIScreen.main.bounds.width - 50 // Subtract time column
    let employeeColumnWidth = totalWidth / CGFloat(sortedEmployeeIds.count)
    
    // Calculate which employee column based on horizontal position
    let columnIndex = Int(globalX / employeeColumnWidth)
    // Appointment can be moved between employee columns within same day
}
```

## Movement Scenarios by Time Resolution

### 5-Minute Resolution (Precision Mode)
- **Grid Lines**: Every 5 minutes (12 lines per hour)
- **Snapping Distance**: 20 points maximum
- **Intervals**: [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55]
- **Use Cases**: Medical appointments, precise scheduling, therapy sessions
- **Visual Density**: High grid density, smaller time slots
- **Cross-Day Behavior**: Full cross-day support with 5-minute precision
- **Performance**: Higher computation due to dense grid calculations

### 15-Minute Resolution (Default/Balanced)
- **Grid Lines**: Every 15 minutes (4 lines per hour)
- **Snapping Distance**: 20 points maximum
- **Intervals**: [0, 15, 30, 45]
- **Use Cases**: Standard business appointments, consultations, meetings
- **Visual Balance**: Optimal between precision and clarity
- **Cross-Day Behavior**: Full cross-day support with 15-minute increments
- **Performance**: Balanced computation and visual clarity

### 30-Minute Resolution (Block Mode)
- **Grid Lines**: Every 30 minutes (2 lines per hour)
- **Snapping Distance**: 20 points maximum
- **Intervals**: [0, 30]
- **Use Cases**: Long appointments, block scheduling, workshops
- **Visual Simplicity**: Clean, minimal grid with clear time blocks
- **Cross-Day Behavior**: Full cross-day support with 30-minute blocks
- **Performance**: Fastest computation, minimal grid complexity

### Resolution-Specific Cross-Day Movement:

```swift
// Position calculation adapts to resolution
func calculatePositionRelativeToGridLines(hour: Int, minute: Int) -> CGFloat {
    let totalMinutesFromStart = (hour - displayHourStart) * 60 + minute
    let intervalIndex = totalMinutesFromStart / timeResolution  // Adapts to 5/15/30
    let position = CGFloat(intervalIndex) * gridCellHeight + (gridCellHeight / 2.0)
    return position
}

// Cross-day movement works identically across all resolutions
// Only the vertical snapping precision changes based on timeResolution
```

### Time Resolution Impact on Cross-Day Dragging:

| Resolution | Vertical Precision | Cross-Day Support | Grid Density | Performance |
|------------|-------------------|-------------------|--------------|-------------|
| 5-minute   | ±2.5 minutes      | ✅ Full Support   | Very High    | Slower      |
| 15-minute  | ±7.5 minutes      | ✅ Full Support   | Medium       | Balanced    |
| 30-minute  | ±15 minutes       | ✅ Full Support   | Low          | Fastest     |

## Drag State Management

### Movement Detection
```swift
private func hasAppointmentActuallyMoved(from originalTime: Date, to newTime: Date) -> Bool {
    return originalTime != newTime
}
```

### Position Validation
```swift
private func calculateNewDateForDrop(column: Int, verticalTranslation: CGFloat) -> Date? {
    // Get target date based on column
    let targetDate = calendarVM.viewMode == .week ? 
        displayDays[clampedColumn] : calendarVM.selectedDate
    
    // Calculate new time from vertical position
    let originalYPosition = timeGridViewModel.yPosition(for: appointment.startTime)
    let newYPosition = originalYPosition + verticalTranslation
    
    return timeGridViewModel.dateAtPosition(
        y: newYPosition, 
        baseDate: targetDate, 
        useProportionalCalculation: true
    )
}
```

## Visual Feedback System

### Ghost Copy Rendering
- **Original**: Dimmed to 30% opacity during move mode
- **Ghost**: Full opacity copy follows drag gesture
- **Communication**: NotificationCenter for ghost copy updates

```swift
// Ghost copy data structure
NotificationCenter.default.post(
    name: Notification.Name("GhostCopyDragUpdate"),
    object: nil,
    userInfo: [
        "appointmentId": appointment.id,
        "dragOffset": dragOffset,
        "originalPosition": CGPoint(x: originalX, y: originalY),
        "size": CGSize(width: slotWidth, height: appointmentHeight)
    ]
)
```

### Haptic Feedback
- **Light Impact**: On snap position changes
- **Threshold**: Position change > 0.1 points
- **Timing**: Immediate on snap detection

## Boundary Scrolling (Disabled)
```swift
// Currently disabled due to complexity
private func checkAndHandleBoundaryScrolling(currentY: CGFloat, gesture: DragGesture.Value) {
    // DISABLE BOUNDARY SCROLLING FOR NOW - just return early
    return
}
```

## Working Hours Integration

### Boundary Detection
```swift
func isWorkingHoursBoundary(date: Date, hour: Int, minute: Int) -> Bool {
    // Check if time is exactly at working hours start/end
    let currentMinutes = hour * 60 + minute
    let startMinutes = startHour * 60 + startMinute
    let endMinutes = endHour * 60 + endMinute
    
    return currentMinutes == startMinutes || currentMinutes == endMinutes
}
```

### Outside Hours Detection
```swift
func isOutsideWorkingHours(date: Date, hour: Int, minute: Int) -> Bool {
    // Time is outside if before start OR at/after end
    let currentMinutes = hour * 60 + minute
    let startMinutes = startHour * 60 + startMinute
    let endMinutes = endHour * 60 + endMinute
    
    return currentMinutes < startMinutes || currentMinutes >= endMinutes
}
```

## Confirmation Flow System

The iOS calendar implements a sophisticated confirmation system that provides users with multiple options after dragging appointments to new positions. This system ensures data integrity while giving users control over customer notifications and providing recovery mechanisms.

### System Architecture

The confirmation system consists of four main components:

1. **CalendarViewModel** - State management and business logic
2. **AppointmentMoveConfirmationView** - User interface dialog  
3. **CalendarAppointmentSlotView** - Drag gesture handling and trigger logic
4. **CalendarView** - Integration and overlay management

### Complete Flow: From Drag to Confirmation

#### Phase 1: Drag Operation Detection

```swift
// In CalendarAppointmentSlotView - drag gesture detection
private func handleMoveModeDropEnd(_ gesture: DragGesture.Value) {
    // 1. Capture final drag offset (contains snapped position)
    let finalDragOffset = dragOffset
    let finalDropColumn = currentDropColumn ?? calculateFinalDropColumn(gesture: gesture)
    
    // 2. Reset visual state
    withAnimation(.easeOut(duration: 0.3)) {
        dragOffset = .zero
    }
    
    // 3. Calculate new date/time from drop position
    if let newDate = calculateNewDateForDrop(column: finalDropColumn, 
                                            verticalTranslation: finalDragOffset.height) {
        
        // 4. Check if appointment actually moved
        if hasAppointmentActuallyMoved(from: viewModel.appointment.startTime, to: newDate) {
            // 5. Trigger confirmation system
            calendarVM.moveAppointment(viewModel.appointment, to: newDate)
        } else {
            // 6. No change - just exit move mode
            calendarVM.exitMoveMode()
        }
    }
}
```

#### Phase 2: Confirmation State Setup

```swift
// In CalendarViewModel - preparing confirmation dialog
func moveAppointment(_ appointment: Appointment, to newDate: Date, employeeId: Int? = nil) {
    if Log.isDebugEnabled {
        log.debug("📦 Preparing to move appointment ID: \(appointment.id) to new date: \(newDate)")
    }

    // 1. Store pending move data for confirmation dialog
    pendingMoveData = (appointment: appointment, newDate: newDate)
    
    // 2. Show confirmation dialog
    showMoveConfirmation = true

    // 3. Exit move mode to show dialog cleanly
    exitMoveMode()
}
```

#### Phase 3: Confirmation Dialog Presentation

```swift
// In CalendarView - dialog overlay integration
if calendarVM.showMoveConfirmation,
   let moveData = calendarVM.pendingMoveData {
    AppointmentMoveConfirmationView(
        appointment: moveData.appointment,
        newDate: moveData.newDate,
        onConfirm: { notifyCustomer in
            calendarVM.confirmAppointmentMove(notifyCustomer: notifyCustomer)
        },
        onCancel: {
            calendarVM.cancelAppointmentMove()
        }
    )
    .zIndex(2000) // Highest z-index to appear above everything
}
```

### Confirmation Dialog User Interface

The `AppointmentMoveConfirmationView` provides a comprehensive interface with multiple sections:

#### Section 1: Movement Summary
```swift
VStack(alignment: .leading, spacing: 12) {
    Text("Moving \(getCustomerName())'s appointment from \(formatDateAndTime(appointment.startTime)) to \(formatDateAndTime(newDate))")
        .font(.system(size: 16, weight: .medium))
        .foregroundColor(.primary)
        .multilineTextAlignment(.leading)
}
```

#### Section 2: Notification Question
```swift
VStack(alignment: .leading, spacing: 12) {
    Text("Do you want to notify the Customer By Email, Text or Push Notification?")
        .font(.system(size: 16, weight: .medium))
        .foregroundColor(.primary)

    Text("Moving or changing the duration of this appointment will affect this appointment only.")
        .font(.system(size: 14))
        .foregroundColor(.secondary)
}
```

#### Section 3: Action Options

The dialog provides four distinct user actions:

1. **DON'T NOTIFY CUSTOMER**
   ```swift
   Button(action: {
       log.debug("🔔 User chose not to notify customer for appointment move")
       onConfirm(false)
   }) {
       Text("DON'T NOTIFY CUSTOMER")
           .font(.system(size: 16, weight: .medium))
           .foregroundColor(.primary)
   }
   ```

2. **NOTIFY CUSTOMER**
   ```swift
   Button(action: {
       log.debug("🔔 User chose to notify customer for appointment move")
       onConfirm(true)
   }) {
       Text("NOTIFY CUSTOMER")
           .font(.system(size: 16, weight: .medium))
           .foregroundColor(.primary)
   }
   ```

3. **ASK CLIENT TO MOVE**
   ```swift
   Button(action: {
       log.debug("💬 User chose to ask client to move appointment")
       showMessageTemplate = true
   }) {
       Text("ASK CLIENT TO MOVE")
           .font(.system(size: 16, weight: .medium))
           .foregroundColor(.primary)
   }
   ```

4. **UNDO MOVE**
   ```swift
   Button(action: {
       log.debug("↩️ User chose to undo appointment move")
       onCancel()
   }) {
       Text("UNDO MOVE")
           .font(.system(size: 16, weight: .medium))
           .foregroundColor(.red)
   }
   ```

### Message Template System

When users choose "ASK CLIENT TO MOVE", a secondary interface appears:

#### Template Generation
```swift
private func generateMessageTemplate() {
    let customerName = appointment.customerName ?? "Customer"
    let originalTime = formatDateAndTime(appointment.startTime)
    let newTime = formatDateAndTime(newDate)

    messageText = "Hi \(customerName), may we move your appointment from \(originalTime) to \(newTime)? Please let us know if this works for you. Thank you!"
}
```

#### Template Interface Features
- **Editable TextEditor**: Users can modify the generated message
- **Copy to Clipboard**: One-tap copying with confirmation alert
- **Auto-Dismiss**: Dialog closes automatically after copying
- **Timezone-Aware Formatting**: Displays times in business timezone

### Post-Confirmation Processing

#### Confirmation Accept Flow
```swift
func confirmAppointmentMove(notifyCustomer: Bool) {
    guard let moveData = pendingMoveData else {
        log.error("❌ No pending move data available")
        return
    }

    if Log.isDebugEnabled {
        log.debug("✅ Confirming appointment move with notification: \(notifyCustomer)")
    }

    // 1. Execute the actual appointment update
    updateAppointmentTime(moveData.appointment, to: moveData.newDate)

    // 2. TODO: Implement customer notification logic based on notifyCustomer flag
    // This would typically involve calling an API endpoint to send notifications

    // 3. Refresh calendar data asynchronously
    Task {
        try? await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
        await MainActor.run {
            safeRefreshAppointments()
        }
    }

    // 4. Clear confirmation state
    pendingMoveData = nil
    showMoveConfirmation = false
}
```

#### Cancellation Flow
```swift
func cancelAppointmentMove() {
    if Log.isDebugEnabled {
        log.debug("❌ Cancelling appointment move")
    }

    // Simply clear state - appointment returns to original position
    pendingMoveData = nil
    showMoveConfirmation = false
}
```

### Appointment Update Execution

The final step involves actually updating the appointment data:

```swift
func updateAppointmentTime(_ appointment: Appointment, to newStartTime: Date) {
    Task {
        // 1. Use business timezone for all date operations
        let timeZoneManager = TimeZoneManager.shared
        var calendar = Calendar.current
        calendar.timeZone = timeZoneManager.businessTimezone

        // 2. Preserve appointment duration
        let durationSeconds = calendar.dateComponents([.second], 
            from: appointment.startTime, to: appointment.endTime).second ?? 0

        // 3. Create updated appointment (immutable struct)
        let updatedAppointment = Appointment(
            id: appointment.id,
            customerId: appointment.customerId,
            employeeId: appointment.employeeId,
            startTime: newStartTime,
            status: appointment.status,
            paymentStatus: appointment.paymentStatus,
            source: appointment.source,
            notesFromCustomer: appointment.notesFromCustomer,
            cancellationReason: appointment.cancellationReason,
            recurrence: appointment.recurrence,
            services: appointment.services,
            addOns: appointment.addOns,
            customerName: appointment.customerName
        )

        // 4. Execute API update
        do {
            _ = try await appointmentManager.updateAppointment(updatedAppointment)
            log.debug("✅ Successfully updated appointment \(updatedAppointment.id)")
        } catch {
            log.error("❌ Error updating appointment: \(error.localizedDescription)")
        }
    }
}
```

### State Management Architecture

#### Published Properties
```swift
// In CalendarViewModel
@Published var showMoveConfirmation: Bool = false
@Published var pendingMoveData: (appointment: Appointment, newDate: Date)? = nil
```

#### State Transitions
1. **Initial State**: `showMoveConfirmation = false, pendingMoveData = nil`
2. **Drag Detected**: Appointment position calculations performed
3. **Move Triggered**: `pendingMoveData` populated, `showMoveConfirmation = true`
4. **Dialog Shown**: User sees confirmation options
5. **User Choice**: Either confirm (with/without notification) or cancel
6. **Final State**: Both properties reset to initial values

#### Thread Safety
- All UI updates occur on `@MainActor`
- Background API calls use `Task` for async execution
- Calendar refresh happens asynchronously with proper error handling

### Visual Design and UX

#### Dialog Appearance
- **Background**: Semi-transparent black overlay (80% opacity) blocks all content
- **Dialog**: White rounded rectangle with shadow
- **Sections**: Visually separated with gray dividers
- **Buttons**: Full-width touch targets with clear visual hierarchy
- **Typography**: Consistent font weights and sizes for readability

#### Interaction Design
- **Outside Tap**: Dismisses dialog (same as cancel)
- **Button Feedback**: Immediate response with proper styling
- **Error States**: Graceful handling with user feedback
- **Animation**: Smooth transitions for state changes

#### Accessibility Features
- **Voice Over**: Proper semantic labeling
- **Large Text**: Respects user accessibility preferences
- **High Contrast**: Maintains visibility in accessibility modes
- **Touch Targets**: Minimum 44pt touch areas

### Error Handling and Recovery

#### Validation Errors
```swift
// Invalid drop position handling
guard let newDate = calculateNewDateForDrop(column: finalDropColumn, 
                                           verticalTranslation: finalDragOffset.height) else {
    calendarVM.exitMoveMode() // Graceful fallback
    return
}
```

#### API Failures
```swift
// Network error recovery
do {
    _ = try await appointmentManager.updateAppointment(updatedAppointment)
} catch {
    log.error("❌ Error updating appointment: \(error.localizedDescription)")
    // TODO: Show user error message and allow retry
    // TODO: Revert local state changes if needed
}
```

#### State Corruption Recovery
```swift
// Ensure valid state during confirmation
guard let moveData = pendingMoveData else {
    log.error("❌ No pending move data available")
    // Reset confirmation state
    showMoveConfirmation = false
    return
}
```

### Integration with Move Mode System

#### Move Mode Coordination
```swift
// Exit move mode when showing confirmation
func moveAppointment(_ appointment: Appointment, to newDate: Date, employeeId: Int? = nil) {
    // Store data first
    pendingMoveData = (appointment: appointment, newDate: newDate)
    showMoveConfirmation = true
    
    // Then exit move mode to prevent conflicts
    exitMoveMode()
}
```

#### Ghost Copy Cleanup
```swift
// Clear ghost copy data when entering confirmation
NotificationCenter.default.post(
    name: Notification.Name("GhostCopyDragEnd"),
    object: nil,
    userInfo: ["appointmentId": viewModel.appointment.id]
)
```

### Performance Considerations

#### Debounced Updates
- **UI State**: Immediate feedback for user interactions
- **API Calls**: Batched with 0.5 second delay to prevent conflicts
- **Calendar Refresh**: Asynchronous to avoid blocking UI

#### Memory Management
- **Weak References**: Prevent retain cycles in async operations
- **State Cleanup**: Proper cleanup of confirmation state
- **Notification Cleanup**: Remove observers on view disappear

### Future Enhancements

#### Customer Notification System
```swift
// TODO: Implement customer notification logic
// This would typically involve:
// 1. Email notification service integration
// 2. SMS notification for text alerts  
// 3. Push notification for mobile app users
// 4. Notification preference management
// 5. Delivery confirmation tracking
```

#### Advanced Conflict Detection
```swift
// TODO: Enhanced conflict checking during confirmation
// 1. Real-time availability checking
// 2. Employee schedule conflicts
// 3. Resource booking conflicts
// 4. Working hours validation
// 5. Holiday/blackout date checking
```

## Performance Optimizations

### Debounced Updates
- **Boundary Checking**: Every 50ms during drag
- **Scroll Updates**: Throttled to prevent excessive calculations
- **Ghost Copy Updates**: Real-time but optimized

### Memory Management
- **Gesture State**: Reset on drag end
- **Notification Cleanup**: Proper cleanup on view disappear
- **Weak References**: Prevent retain cycles in ViewModels

## Error Handling

### Invalid Drop Positions
- **Out of Bounds**: Clamp to valid column/time ranges
- **Invalid Times**: Fallback to original position
- **Conflict Detection**: Check for appointment overlaps

### Recovery Mechanisms
- **Failed Moves**: Revert to original position with animation
- **Network Failures**: Show error and maintain local state
- **State Corruption**: Reset to known good state

## Advanced Features

### Week Configuration Support
The system supports configurable week start days (Monday/Sunday) through CalendarState:

```swift
// Week start day configuration (1=Sunday, 2=Monday, etc.)
private var weekStartDay: Int = 2 // Default to Monday

// Dynamic week generation based on configuration
let daysToFirstDayOfWeek = (weekday - weekStartDay + 7) % 7
let firstDayOfWeek = calendar.date(byAdding: .day, value: -daysToFirstDayOfWeek, to: selectedDate)!

// Generate 7-day array: [Mon, Tue, Wed, Thu, Fri, Sat, Sun] or [Sun, Mon, Tue, Wed, Thu, Fri, Sat]
displayDays = (0..<7).map { 
    calendar.date(byAdding: .day, value: $0, to: firstDayOfWeek)! 
}
```

### Business Timezone Handling
All cross-day calculations respect business timezone settings:

```swift
// Use business timezone for date comparisons
let timeZoneManager = TimeZoneManager.shared
var calendar = Calendar.current
calendar.timeZone = timeZoneManager.businessTimezone

// Find which day column appointment belongs to
for (index, day) in displayDays.enumerated() {
    if calendar.isDate(appointmentDate, inSameDayAs: day) {
        return index  // Accurate cross-day detection
    }
}
```

### Conflict Detection During Cross-Day Movement
```swift
// Check for appointment overlaps in target day/time
func hasConflict(for appointment: Appointment, excludingId: Int? = nil) -> Bool {
    return appointmentManager.hasConflict(for: appointment, excludingId: excludingId)
}

// Validate cross-day move doesn't create conflicts
if hasConflict(for: movedAppointment, excludingId: originalAppointment.id) {
    // Show conflict warning or prevent move
    return false
}
```

### Multi-Employee Cross-Day Coordination
```swift
// Week view with multiple employees - cross-day movement affects employee assignment
func calculateNewDateForDrop(column: Int, verticalTranslation: CGFloat) -> Date? {
    // In week view, column represents day (not employee)
    let targetDate = displayDays[clampedColumn]
    
    // Employee assignment remains unchanged during cross-day movement
    // Only date and time change, employee context preserved
    return combineDateAndTime(targetDate, newTime)
}
```

## Performance Optimizations

### Debounced Updates
- **Boundary Checking**: Every 50ms during drag to prevent excessive calculations
- **Scroll Updates**: Throttled to prevent UI lag during cross-day movement
- **Ghost Copy Updates**: Real-time but optimized with NotificationCenter batching
- **Column Calculations**: Cached day column widths to avoid repeated calculations

### Memory Management
- **Gesture State**: Properly reset on drag end to prevent memory leaks
- **Notification Cleanup**: Automatic cleanup on view disappear
- **Weak References**: Prevent retain cycles in ViewModels
- **Display Days Cache**: Cache week calculations to avoid repeated date computations

### Cross-Day Specific Optimizations
```swift
// Cache expensive day column calculations
private var _weekModeDayColumnWidth: CGFloat = 0

// Batch cross-day position updates
func sendGhostCopyDragUpdate(gesture: DragGesture.Value) {
    // Only send updates when position significantly changes
    // Reduces notification overhead during cross-day dragging
}
```

## Error Handling

### Invalid Drop Positions
- **Out of Bounds**: Clamp to valid column/time ranges during cross-day movement
- **Invalid Times**: Fallback to original position with smooth animation
- **Cross-Day Conflicts**: Detect and prevent overlapping appointments in target day
- **Weekend/Holiday Restrictions**: Respect business rules for cross-day movement

### Recovery Mechanisms
- **Failed Cross-Day Moves**: Revert to original day/time with visual feedback
- **Network Failures**: Maintain local state during cross-day operations
- **State Corruption**: Reset to known good state with proper day/time alignment
- **Timezone Issues**: Handle cross-day movement across timezone boundaries

### Cross-Day Specific Error Handling
```swift
private func calculateNewDateForDrop(column: Int, verticalTranslation: CGFloat) -> Date? {
    // Ensure column is within valid bounds - clamp instead of failing
    let clampedColumn = max(0, min(column, displayDays.count - 1))
    
    if clampedColumn != column {
        log.debug("📍 Column \(column) clamped to \(clampedColumn) - prevented out-of-bounds cross-day move")
    }
    
    guard let newDateTime = timeGridViewModel.dateAtPosition(
        y: newYPosition, 
        baseDate: targetDate, 
        useProportionalCalculation: true
    ) else {
        log.error("❌ Failed to calculate valid date for cross-day drop")
        return nil  // Graceful failure - appointment stays in original position
    }
    
    return newDateTime
}
```

## Integration Points

### Calendar State Updates
- **Immediate UI**: Update local state immediately for responsive cross-day feedback
- **API Sync**: Background sync with server for cross-day appointment changes
- **Conflict Resolution**: Handle concurrent modifications across different days
- **Date Range Updates**: Refresh appointment data when cross-day moves affect visible range

### Multi-View Coordination
- **Day ↔ Week**: Maintain appointment selection across views during cross-day operations
- **Employee Switching**: Handle multi-employee scenarios with cross-day movement
- **Date Navigation**: Preserve movement state during cross-day navigation
- **Working Hours**: Respect working hours when moving appointments across days

### Cross-Day State Synchronization
```swift
// Ensure all views reflect cross-day appointment changes
func confirmAppointmentMove(notifyCustomer: Bool) {
    guard let moveData = pendingMoveData else { return }
    
    // Update appointment with new date/time (potentially different day)
    updateAppointmentTime(moveData.appointment, to: moveData.newDate)
    
    // Refresh calendar data to show appointment in new day
    Task {
        await MainActor.run {
            safeRefreshAppointments()  // Updates all visible days
        }
    }
    
    // Clear move state
    pendingMoveData = nil
    showMoveConfirmation = false
}
``` 