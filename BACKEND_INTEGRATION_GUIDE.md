# Backend Integration Guide for Customer Import System

## 🎯 Overview

This guide explains how to connect your frontend import system with your backend API. The frontend is now configured to send customer data to your backend and poll for progress updates.

## 📁 File Structure Created

```
src/features/import/
├── services/
│   └── importApi.js          # API service for backend communication
├── components/
│   ├── FileDropZone.jsx      # File upload with validation
│   ├── HeaderRowSelector.jsx # Header row selection
│   ├── ImportPreview.jsx     # Field mapping
│   └── ImportProgress.jsx    # Real-time progress tracking
└── utils/
    └── fileUtils.js          # File parsing utilities
```

## ⚙️ Frontend Configuration

### 1. Environment Variables
Create a `.env` file in your project root:

```env
# Backend API Configuration
REACT_APP_API_URL=http://localhost:3001/api

# For different environments:
# REACT_APP_API_URL=https://api-staging.yourapp.com/api  # Staging
# REACT_APP_API_URL=https://api.yourapp.com/api         # Production
```

### 2. API Service Configuration
The `importApi.js` service handles all backend communication:

- **Base URL**: Configured via `REACT_APP_API_URL` environment variable
- **Authentication**: Add your auth token in the headers (currently commented)
- **Error Handling**: Comprehensive error handling with user-friendly messages

## 🔌 Required Backend API Endpoints

### 1. Start Import Job
```http
POST /api/customers/import
Content-Type: application/json
Authorization: Bearer {token}

{
  "fileName": "customers.xlsx",
  "fileSize": 54321,
  "totalRows": 150,
  "fieldMappings": {
    "firstName": "First Name",
    "lastName": "Last Name", 
    "email": "Email Address",
    // ... other mappings
  },
  "customers": [
    {
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>",
      // ... other customer data
    }
    // ... more customers
  ],
  "options": {
    "skipDuplicates": true,
    "updateExisting": false,
    "validateEmails": true
  }
}

Response:
{
  "success": true,
  "jobId": "import_job_12345",
  "message": "Import job started successfully"
}
```

### 2. Get Import Progress
```http
GET /api/customers/import/{jobId}/status

Response:
{
  "jobId": "import_job_12345",
  "status": "processing", // "pending", "processing", "completed", "failed", "cancelled"
  "progress": 65,         // Percentage (0-100)
  "processedRows": 97,
  "totalRows": 150,
  "startedAt": "2024-01-15T10:30:00Z",
  "estimatedCompletion": "2024-01-15T10:35:00Z"
}
```

### 3. Get Final Results
```http
GET /api/customers/import/{jobId}/results

Response:
{
  "jobId": "import_job_12345", 
  "status": "completed",
  "summary": {
    "totalRows": 150,
    "successful": 145,
    "failed": 5,
    "duplicatesSkipped": 12
  },
  "successful": [
    // Array of successfully imported customer records
  ],
  "errors": [
    {
      "row": 23,
      "data": { "firstName": "", "email": "invalid-email" },
      "errors": ["First name is required", "Invalid email format"]
    }
    // ... more error records
  ],
  "completedAt": "2024-01-15T10:34:22Z",
  "duration": 252000 // milliseconds
}
```

### 4. Cancel Import (Optional)
```http
DELETE /api/customers/import/{jobId}/cancel

Response:
{
  "success": true,
  "message": "Import job cancelled successfully"
}
```

### 5. Validation Endpoint (Optional)
```http
POST /api/customers/validate

{
  "customers": [...],
  "fieldMappings": {...},
  "validateOnly": true
}

Response:
{
  "valid": 145,
  "invalid": 5,
  "errors": [...]
}
```

## 🗄️ Backend Implementation Guidelines

### 1. Database Schema
Ensure your customer table supports all 27 fields:

```sql
CREATE TABLE customers (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  customer_since DATE,
  last_visited DATE,
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100) NOT NULL,
  membership VARCHAR(50),
  email VARCHAR(255) UNIQUE,
  birthdate DATE,
  gender VARCHAR(20),
  apt_suite VARCHAR(50),
  address VARCHAR(255),
  mobile VARCHAR(20),
  day_phone VARCHAR(20),
  night_phone VARCHAR(20),
  city VARCHAR(100),
  state VARCHAR(50),
  zip VARCHAR(20),
  referred_by VARCHAR(255),
  online_booking BOOLEAN DEFAULT false,
  credit_card VARCHAR(255),
  bank VARCHAR(255),
  tags TEXT,
  appointments_booked INT DEFAULT 0,
  classes_booked INT DEFAULT 0,
  check_ins INT DEFAULT 0,
  points_earned INT DEFAULT 0,
  amount_paid DECIMAL(10,2) DEFAULT 0.00,
  no_shows_cancellations INT DEFAULT 0,
  employee_seen VARCHAR(255),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 2. Job Queue System
Implement a job queue for handling large imports:

```javascript
// Example with Redis/Bull Queue
const importQueue = new Queue('customer import');

// Add job
const job = await importQueue.add('importCustomers', {
  jobId,
  customers,
  fieldMappings,
  options
});

// Process job
importQueue.process('importCustomers', async (job) => {
  const { customers, fieldMappings } = job.data;
  
  for (let i = 0; i < customers.length; i++) {
    // Process customer
    await processCustomer(customers[i], fieldMappings);
    
    // Update progress
    const progress = Math.round(((i + 1) / customers.length) * 100);
    await updateJobProgress(job.data.jobId, progress);
  }
});
```

### 3. Progress Tracking
Store job progress in database or cache:

```javascript
// Redis example
await redis.setex(`import_job:${jobId}`, 3600, JSON.stringify({
  status: 'processing',
  progress: 65,
  processedRows: 97,
  totalRows: 150
}));
```

### 4. Validation & Error Handling
Validate customer data before insertion:

```javascript
const validateCustomer = (customer, fieldMappings) => {
  const errors = [];
  
  // Required fields
  if (!customer.firstName) errors.push('First name is required');
  if (!customer.lastName) errors.push('Last name is required');
  if (!customer.email) errors.push('Email is required');
  
  // Email validation
  if (customer.email && !isValidEmail(customer.email)) {
    errors.push('Invalid email format');
  }
  
  // Date validation
  if (customer.birthdate && !isValidDate(customer.birthdate)) {
    errors.push('Invalid birthdate format');
  }
  
  return errors;
};
```

## 🔄 Data Flow

1. **Frontend**: User uploads file and maps fields
2. **Frontend**: Sends mapped data to `POST /customers/import`
3. **Backend**: Creates import job and returns `jobId`
4. **Backend**: Processes customers in background job
5. **Frontend**: Polls `GET /customers/import/{jobId}/status` every 2 seconds
6. **Backend**: Updates job progress in real-time
7. **Frontend**: Gets final results from `GET /customers/import/{jobId}/results`
8. **Frontend**: Shows completion status with success/error counts

## 🔐 Security Considerations

1. **Authentication**: Add proper JWT token validation
2. **Rate Limiting**: Limit import frequency per user
3. **File Size**: Limit maximum import size
4. **Validation**: Sanitize all input data
5. **Permissions**: Ensure user has import permissions

## 🧪 Testing

### Frontend Testing
```bash
npm run dev
# Upload a test Excel/CSV file
# Verify API calls in Network tab
```

### Backend Testing
```bash
# Test import endpoint
curl -X POST http://localhost:3001/api/customers/import \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d @test_import_data.json

# Test status endpoint  
curl http://localhost:3001/api/customers/import/job123/status
```

## 📝 Next Steps

1. **Set up your backend API** with the endpoints above
2. **Configure environment variables** in `.env`
3. **Add authentication** to the API service
4. **Test the complete flow** with sample data
5. **Add error monitoring** and logging
6. **Configure production URLs** for deployment

The frontend is now ready to communicate with your backend! Just implement the API endpoints and configure the environment variables. 