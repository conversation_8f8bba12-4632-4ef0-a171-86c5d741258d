# Online Booking System Documentation

## Overview
The online booking system in Chatbook Pro allows customers to book appointments directly through a web interface without staff intervention. The system is designed with granular control over which customers can book online and which time slots are available for online booking.

## 🏗️ System Architecture

### Core Components
- **Customer-Level Controls**: Individual customers can have online booking enabled/disabled
- **Time Slot Controls**: Personal tasks and time-off periods can block online booking
- **Appointment Source Tracking**: All appointments are tagged with their creation source
- **Business Rules Engine**: Configurable rules for online appointment validation

### Data Flow
```
Customer Request → Availability Check → Business Rules → Appointment Creation
                                   ↓
                             Staff Notification
```

## 📊 Data Models

### Customer Online Booking Configuration
```swift
// Customer.swift - Online booking flag per customer
struct Customer: Identifiable, Codable {
    let id: Int
    let firstName: String
    let lastName: String
    let email: String
    let phone: String
    let onlineBooking: Bool  // 🔑 Controls if customer can book online
    
    // Other customer properties...
}
```

### Personal Task Blocking
```swift
// PersonalTask.swift - Time slot blocking
struct PersonalTask: CalendarSlotItem, Codable {
    let id: Int
    let employeeId: Int
    var name: String?
    var startTime: Date
    var endTime: Date
    var blockOnlineBooking: Bool  // 🔑 Blocks online booking during this time
    
    // Other task properties...
}
```

### Appointment Source Tracking
```swift
// Appointment.swift - Source tracking
enum AppointmentSource: String, Codable {
    case admin  = "admin"   // Created by staff
    case online = "online"  // Created by customer online
    
    var displayName: String {
        switch self {
        case .admin:  return "Admin Interface"
        case .online: return "Online Booking"
        }
    }
}

struct Appointment: Identifiable, Codable {
    let id: Int
    let customerId: Int
    let employeeId: Int
    var source: AppointmentSource  // 🔑 Tracks how appointment was created
    
    // Other appointment properties...
}
```

## 🎛️ Settings & Configuration

### Business-Level Settings
```swift
// BusinessSettings.swift - Global online booking rules
struct GeneralBusinessSettings: Codable {
    let requiresConsultationForNewCustomers: Bool
    let gracePeriodDays: Int
    let suggestAddOnsFromHistory: Bool
    let maxHistoryLookbackDays: Int
}

struct EyelashBusinessSettings: Codable {
    let requiresConsultationForNewCustomers: Bool
    // Industry-specific online booking rules
}
```

### Settings UI Structure
```swift
// SettingsView.swift - Online booking configuration
// BOOKING SECTION
SectionHeader(title: "BOOKING", icon: "calendar")
    
if settingsViewModel.isBookingExpanded {
    VStack(spacing: 0) {
        SettingsRow(title: "Email & Text Notifications")
        SettingsRow(title: "Online Appointment Rules")  // 🔑 Online booking settings
    }
}
```

## 🎨 UI Components

### Customer Profile - Online Booking Toggle
```swift
// CustomerEditView.swift - Individual customer control
Section {
    Toggle("Marketing Opt-in", isOn: $viewModel.optInMarketing)
    Toggle("Email Reminders", isOn: $viewModel.emailReminders)
    Toggle("SMS Reminders", isOn: $viewModel.smsReminders)
    // Note: Online booking toggle would be implemented here
} header: {
    Text("Preferences")
}
```

### Personal Task - Block Online Booking
```swift
// PersonalTaskCreateView.swift - Time slot blocking
private var blockOnlineBookingSection: some View {
    VStack(spacing: 0) {
        HStack {
            Text("Block Online Booking")
                .font(.body)
                .foregroundColor(.black)
                .padding(.leading)
            
            Spacer()
            
            Toggle("", isOn: $viewModel.blockOnlineBooking)
                .padding(.trailing)
        }
        .padding(.vertical, 12)
        
        Divider()
            .padding(.leading)
    }
    .background(Color.white)
}
```

## 📱 Implementation Details

### Customer Management
```swift
// CustomerEntity+CoreDataClass.swift - Data persistence
func update(from customer: Customer) {
    // Standard customer fields...
    self.onlineBooking = customer.onlineBooking ? 1 : 0
}

func toCustomer() -> Customer {
    return Customer(
        id: Int(id),
        firstName: firstName ?? "",
        lastName: lastName ?? "",
        // ... other fields
        onlineBooking: onlineBooking != 0,  // Convert Int16 to Bool
        // ... remaining fields
    )
}
```

### Personal Task Management
```swift
// PersonalTaskCreateViewModel.swift - Task creation with blocking
@Published var blockOnlineBooking: Bool = true  // Default to blocking

// Create task with online booking control
let task = PersonalTask(
    employeeId: employeeId,
    name: taskName,
    startTime: startTime,
    endTime: endTime,
    blockOnlineBooking: blockOnlineBooking  // 🔑 Applied to task
)
```

## 🔧 Business Logic

### Availability Checking
```swift
// Pseudo-code for availability validation
func isTimeSlotAvailableForOnlineBooking(
    employeeId: Int,
    startTime: Date,
    endTime: Date
) -> Bool {
    // 1. Check if any personal tasks block online booking
    let blockingTasks = personalTasks.filter { task in
        task.employeeId == employeeId &&
        task.blockOnlineBooking &&
        task.overlaps(with: startTime...endTime)
    }
    
    if !blockingTasks.isEmpty {
        return false
    }
    
    // 2. Check business working hours
    if !isWithinWorkingHours(startTime, endTime) {
        return false
    }
    
    // 3. Check existing appointments
    if hasConflictingAppointments(employeeId, startTime, endTime) {
        return false
    }
    
    return true
}
```

### Customer Permission Check
```swift
// Pseudo-code for customer validation
func canCustomerBookOnline(customerId: Int) -> Bool {
    guard let customer = getCustomer(customerId) else { return false }
    
    // Check if customer has online booking enabled
    if !customer.onlineBooking {
        return false
    }
    
    // Check business rules (e.g., new customer consultation requirement)
    if isNewCustomer(customer) && businessSettings.requiresConsultationForNewCustomers {
        return false
    }
    
    return true
}
```

## 🚀 Features

### ✅ Currently Implemented
- **Per-Customer Control**: Individual customers can have online booking enabled/disabled
- **Time Slot Blocking**: Personal tasks can block online booking during specific periods
- **Source Tracking**: All appointments track whether they were created online or by staff
- **Settings Interface**: Dedicated settings section for online booking configuration
- **Business Rules**: Industry-specific rules for online booking validation

### 🔄 Integration Points
- **Calendar System**: Online booking integrates with the main calendar view
- **Customer Management**: Online booking status is part of customer profile
- **Working Hours**: Online booking respects business working hours
- **Buffer Time**: Online booking includes service buffer time calculations
- **Appointment Validation**: Online bookings go through the same validation as staff bookings

## 📋 Configuration Examples

### Enable Online Booking for Customer
```swift
// Update customer to allow online booking
var customer = existingCustomer
customer.onlineBooking = true
customerManager.updateCustomer(customer)
```

### Block Online Booking for Time Period
```swift
// Create personal task that blocks online booking
let timeOffTask = PersonalTask(
    employeeId: employeeId,
    name: "Lunch Break",
    startTime: lunchStart,
    endTime: lunchEnd,
    blockOnlineBooking: true  // 🔑 Blocks online booking
)
```

### Track Appointment Source
```swift
// When creating appointment online
let appointment = Appointment(
    customerId: customerId,
    employeeId: employeeId,
    startTime: selectedTime,
    source: .online,  // 🔑 Mark as online booking
    // ... other fields
)
```

## 🔍 Monitoring & Analytics

### Appointment Source Tracking
- All appointments have a `source` field indicating creation method
- Can filter and analyze online vs. staff-created appointments
- Useful for business metrics and customer behavior analysis

### Customer Behavior
- Track which customers use online booking vs. phone/in-person
- Analyze online booking patterns by time of day, service type
- Monitor cancellation rates between online and staff bookings

## 🛡️ Security & Validation

### Customer Authentication
- Online booking requires customer authentication (email/phone)
- Customer must have valid account with online booking enabled
- Rate limiting prevents booking abuse

### Business Rules Enforcement
- All online bookings must pass the same validation as staff bookings
- Respect working hours, buffer times, and employee availability
- Automatic conflict detection and prevention

### Staff Notifications
- Staff receive notifications for new online bookings
- Ability to approve/modify online bookings before confirmation
- Real-time updates to calendar when online bookings are made

## 🎯 Best Practices

### Customer Management
1. **Default Settings**: New customers should have online booking enabled by default
2. **Clear Communication**: Inform customers about online booking availability
3. **Preference Tracking**: Allow customers to opt-in/out of online booking

### Time Management
1. **Strategic Blocking**: Use personal tasks to block online booking during breaks
2. **Buffer Protection**: Ensure buffer times are respected in online bookings
3. **Availability Windows**: Consider limiting online booking to specific hours

### Business Operations
1. **Staff Training**: Ensure staff understand online booking workflow
2. **Monitoring**: Regularly review online booking patterns and customer feedback
3. **Optimization**: Adjust online booking rules based on business needs

## 📚 Related Documentation
- [Buffer Time System](BUFFER_TIME_SYSTEM.md)
- [Waitlist System Design](WAITLIST_SYSTEM_DESIGN.md)
- [Calendar Appointment Logic](CALENDAR_APPOINTMENT_LOGIC.md)

---

*This documentation reflects the current implementation in the iOS project and provides a foundation for implementing similar functionality in the React frontend.* 