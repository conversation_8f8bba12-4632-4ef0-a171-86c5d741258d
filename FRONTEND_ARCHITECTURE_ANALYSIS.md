# 🏗️ Chatbook Business Frontend - Easy-to-Understand Architecture Guide

## 📋 What You'll Learn

1. [What This App Does](#-what-this-app-does)
2. [The Tools We Use](#-the-tools-we-use)
3. [How Files Are Organized](#-how-files-are-organized)
4. [How The Calendar Works](#-how-the-calendar-works)
5. [How Data Flows Around](#-how-data-flows-around)
6. [How We Talk to the Backend](#-how-we-talk-to-the-backend)
7. [How Components Work Together](#-how-components-work-together)
8. [How We Handle Real-time Updates](#-how-we-handle-real-time-updates)
9. [How We Make It Fast](#-how-we-make-it-fast)
10. [Step-by-Step Implementation Examples](#-step-by-step-implementation-examples)

---

## 🎯 What This App Does

### Simple Explanation
Think of this app like **Google Calendar but for salons/spas**. Staff can:
- See their appointments on a calendar
- Drag appointments to different times
- Create new appointments by clicking empty slots
- See multiple employees' schedules at once
- Manage customers, services, and bookings

### Why We Built It This Way
- **Easy to Use**: Works like iPhone apps - smooth, intuitive
- **Fast**: Everything loads quickly and responds instantly
- **Reliable**: Won't crash or lose data
- **Flexible**: Easy to add new features later

---

## 🛠️ The Tools We Use (And Why)

### React - The Main Framework
**What it is**: A tool for building user interfaces (like buttons, forms, calendars)
**Why we chose it**: 
- Makes it easy to break the app into small, reusable pieces
- Automatically updates the screen when data changes
- Huge community support

### Vite - The Development Tool
**What it is**: Makes the development process super fast
**Why we chose it**:
- Page refreshes instantly when you change code
- Builds the final app very quickly
- Modern and reliable

### Tailwind CSS - The Styling System
**What it is**: A way to make things look good without writing custom CSS
**Why we chose it**:
- Consistent design across the entire app
- Faster development (no need to write CSS from scratch)
- Easy to maintain and update

### React Query - Data Management
**What it is**: Handles getting data from the backend and keeping it fresh
**Why we chose it**:
- Automatically caches data so pages load faster
- Handles loading states and errors automatically
- Updates data in the background

### Axios - HTTP Client
**What it is**: How we send requests to the Django backend
**Why we chose it**:
- Handles authentication automatically
- Better error handling than built-in fetch
- Works well with our Django setup

### **Important: What We DON'T Use**
- ❌ **No WebSockets**: We don't have real-time updates (appointments don't appear instantly on other users' screens)
- ❌ **No Redux**: We don't use complex state management
- ❌ **No Redis**: We don't have advanced caching on the frontend

---

## 📁 How Files Are Organized

### The Big Picture
```
Think of it like organizing a business:
├── Each department has its own folder (calendar, employees, customers)
├── Each folder contains everything that department needs
├── Shared tools go in a common area
└── Documentation explains how everything works
```

### Actual Structure
```
chatbook-business-frontend/
├── src/
│   ├── components/          # 🧩 Shared pieces (buttons, forms, etc.)
│   ├── features/           # 🎯 Main business features
│   │   ├── calendar/       # 📅 Calendar system (THE MAIN FEATURE)
│   │   ├── employees/      # 👥 Staff management
│   │   ├── customers/      # 👤 Customer management
│   │   └── services/       # 💼 Service catalog
│   ├── pages/              # 📄 Full pages (Calendar.jsx, Dashboard.jsx)
│   ├── hooks/              # 🔧 Reusable logic
│   └── utils/              # 🛠️ Helper functions
```

### Why This Organization?
**Easy to Find Things**: Need calendar stuff? Look in `features/calendar/`
**Easy to Work on Teams**: Different developers can work on different features
**Easy to Test**: Each feature is self-contained
**Easy to Add Features**: Just create a new folder

---

## 📅 How The Calendar Works (Step by Step)

### Overview - Like Building Blocks
```
🏠 Calendar Page (the house)
├── 📅 Calendar Grid (the rooms)
│   ├── ⏰ Time Slots (empty spaces)
│   └── 📝 Appointment Blocks (scheduled appointments)
├── 👥 Employee Headers (staff names at top)
├── 🎛️ Controls (date picker, view switcher)
└── 📋 Modals (pop-up forms)
```

### Step 1: The Main Calendar Component
**File**: `src/pages/Calendar.jsx`
**What it does**: This is the "master control" - it coordinates everything

```javascript
// Simplified version of what Calendar.jsx does:
function Calendar() {
  // 1. Get all the data we need
  const { appointments, employees, loading } = useAdvancedCalendar()
  
  // 2. Show loading screen while data loads
  if (loading) return <LoadingSpinner />
  
  // 3. Display the calendar
  return (
    <div>
      <CalendarHeader />          {/* Date picker, view controls */}
      <CalendarGrid               {/* The actual calendar */}
        appointments={appointments}
        employees={employees}
      />
      <CalendarModals />          {/* Pop-up forms */}
    </div>
  )
}
```

### Step 2: The Calendar Grid
**File**: `src/features/calendar/components/views/WeekView.jsx`
**What it does**: Creates the visual calendar grid

```javascript
// How the grid is built:
function WeekView({ appointments, employees }) {
  return (
    <div className="calendar-grid">
      {/* Top row: Employee names */}
      <div className="employee-headers">
        {employees.map(employee => (
          <div key={employee.id}>{employee.name}</div>
        ))}
      </div>
      
      {/* Time slots and appointments */}
      <div className="time-grid">
        {timeSlots.map(timeSlot => (
          <TimeSlot 
            key={timeSlot.id}
            time={timeSlot}
            appointments={appointmentsForThisSlot}
          />
        ))}
      </div>
    </div>
  )
}
```

### Step 3: Individual Time Slots
**File**: `src/features/calendar/components/slots/TimeSlot.jsx`
**What it does**: Each clickable time slot (like 9:00 AM, 9:15 AM, etc.)

```javascript
function TimeSlot({ time, employee, appointments }) {
  const handleClick = () => {
    // When user clicks an empty slot, show appointment creation form
    openNewAppointmentModal({
      startTime: time,
      employeeId: employee.id
    })
  }
  
  return (
    <div 
      className="time-slot" 
      onClick={handleClick}
    >
      {/* Show any appointments in this slot */}
      {appointments.map(appointment => (
        <AppointmentBlock key={appointment.id} appointment={appointment} />
      ))}
    </div>
  )
}
```

### Step 4: Appointment Blocks
**File**: `src/features/calendar/components/appointment-slot/AppointmentSlotCore.jsx`
**What it does**: The colored blocks that represent appointments

```javascript
function AppointmentBlock({ appointment }) {
  // Handle dragging to move appointments
  const handleDragStart = (e) => {
    setDragData({ appointmentId: appointment.id, originalTime: appointment.start })
  }
  
  const handleDragEnd = (e) => {
    // Calculate new time based on where user dropped it
    const newTime = calculateDropPosition(e.clientX, e.clientY)
    
    // Update the appointment
    updateAppointment(appointment.id, { start: newTime })
  }
  
  return (
    <div 
      className="appointment-block"
      draggable
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      style={{
        backgroundColor: getServiceColor(appointment.service),
        height: calculateHeight(appointment.duration)
      }}
    >
      <div className="appointment-title">{appointment.service_name}</div>
      <div className="appointment-customer">{appointment.customer_name}</div>
      <div className="appointment-time">{formatTime(appointment.start)}</div>
    </div>
  )
}
```

---

## 🔄 How Data Flows Around

### Simple Data Flow
```
1. User loads calendar page
2. Frontend asks Django for appointment data
3. Django sends back JSON data
4. Frontend transforms data into calendar format
5. Calendar displays appointments
6. User makes changes (drag, click, etc.)
7. Frontend immediately updates display (optimistic update)
8. Frontend sends change to Django
9. Django saves change and confirms
10. Frontend confirms everything is synced
```

### Detailed Implementation

#### Step 1: Getting Data from Django
**File**: `src/features/calendar/services/appointmentService.js`
```javascript
// This is how we get appointments from Django
export const fetchAppointments = async (startDate, endDate) => {
  try {
    // Make HTTP request to Django
    const response = await axios.get('/api/v1/appointments/', {
      params: {
        start_date: startDate.toISOString(),
        end_date: endDate.toISOString()
      }
    })
    
    // Django gives us data in this format:
    // {
    //   "results": [
    //     {
    //       "id": 1,
    //       "customer": { "id": 1, "full_name": "John Doe" },
    //       "employee": 2,
    //       "start_time": "2024-01-15T09:00:00Z",
    //       "appointment_services": [...]
    //     }
    //   ]
    // }
    
    return response.data.results
  } catch (error) {
    console.error('Failed to fetch appointments:', error)
    throw error
  }
}
```

#### Step 2: Transforming Data for Calendar Use
**File**: `src/features/calendar/utils/dataTransformers.js`
```javascript
// Django format -> Calendar format
export const transformAppointmentForCalendar = (djangoAppointment) => {
  return {
    // Calendar needs string IDs, Django gives numbers
    id: djangoAppointment.id.toString(),
    
    // Calendar needs employeeId, Django gives nested employee object
    employeeId: djangoAppointment.employee.toString(),
    
    // Calendar needs JavaScript Date objects
    start: new Date(djangoAppointment.start_time),
    end: new Date(djangoAppointment.end_time),
    
    // Calendar needs display-friendly names
    title: `${djangoAppointment.appointment_services[0]?.service_name} - ${djangoAppointment.customer.full_name}`,
    
    // Calendar needs colors for styling
    color: getServiceColor(djangoAppointment.appointment_services[0]?.service_name),
    
    // Keep original data for editing
    originalData: djangoAppointment
  }
}
```

#### Step 3: Managing State in React
**File**: `src/features/calendar/hooks/useAdvancedCalendar.js`
```javascript
// This is the "brain" of the calendar - it manages all the data
export const useAdvancedCalendar = () => {
  // State variables (like storage boxes)
  const [appointments, setAppointments] = useState([])
  const [selectedDate, setSelectedDate] = useState(new Date())
  const [selectedEmployees, setSelectedEmployees] = useState(new Set())
  const [loading, setLoading] = useState(true)
  
  // Get appointments from Django when date changes
  useEffect(() => {
    loadAppointments()
  }, [selectedDate])
  
  const loadAppointments = async () => {
    setLoading(true)
    try {
      // Get raw data from Django
      const rawAppointments = await fetchAppointments(selectedDate, selectedDate)
      
      // Transform for calendar display
      const calendarAppointments = rawAppointments.map(transformAppointmentForCalendar)
      
      // Update state (this will re-render the calendar)
      setAppointments(calendarAppointments)
    } finally {
      setLoading(false)
    }
  }
  
  // Functions that components can call
  const createAppointment = async (appointmentData) => {
    // 1. Immediately add to display (optimistic update)
    const tempAppointment = { ...appointmentData, id: 'temp-' + Date.now() }
    setAppointments(prev => [...prev, tempAppointment])
    
    // 2. Send to Django
    try {
      const savedAppointment = await appointmentService.createAppointment(appointmentData)
      
      // 3. Replace temp appointment with real one
      setAppointments(prev => 
        prev.map(apt => 
          apt.id === tempAppointment.id 
            ? transformAppointmentForCalendar(savedAppointment)
            : apt
        )
      )
    } catch (error) {
      // 4. Remove temp appointment if save failed
      setAppointments(prev => prev.filter(apt => apt.id !== tempAppointment.id))
      throw error
    }
  }
  
  // Return everything components need
  return {
    appointments,
    selectedDate,
    selectedEmployees,
    loading,
    setSelectedDate,
    setSelectedEmployees,
    createAppointment,
    loadAppointments
  }
}
```

---

## 🔌 How We Talk to the Backend

### The API Client Setup
**File**: `src/features/employees/services/apiClient.js`
```javascript
// This is our "messenger" to Django
const apiClient = axios.create({
  baseURL: 'http://localhost:8000/api/v1',  // Django server
  timeout: 10000,  // Wait max 10 seconds for response
})

// Automatically add authentication to every request
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('authToken')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

// Handle errors automatically
apiClient.interceptors.response.use(
  (response) => response,  // If successful, just return data
  (error) => {
    if (error.response?.status === 401) {
      // If unauthorized, redirect to login
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)
```

### Service-Specific API Calls
**File**: `src/features/calendar/services/appointmentService.js`
```javascript
// All appointment-related API calls in one place
export const appointmentService = {
  // Get appointments for a date range
  fetchAppointments: async (startDate, endDate) => {
    const response = await apiClient.get('/appointments/', {
      params: {
        start_date: startDate.toISOString(),
        end_date: endDate.toISOString()
      }
    })
    return response.data.results
  },
  
  // Create new appointment
  createAppointment: async (appointmentData) => {
    // Transform frontend data to Django format
    const djangoFormat = {
      customer: parseInt(appointmentData.customerId),
      employee: parseInt(appointmentData.employeeId),
      start_time: appointmentData.start.toISOString(),
      end_time: appointmentData.end.toISOString(),
      appointment_services: [{
        service: appointmentData.serviceId,
        duration: appointmentData.duration,
        base_price: appointmentData.price
      }]
    }
    
    const response = await apiClient.post('/appointments/', djangoFormat)
    return response.data
  },
  
  // Update existing appointment
  updateAppointment: async (appointmentId, updates) => {
    const response = await apiClient.put(`/appointments/${appointmentId}/`, updates)
    return response.data
  },
  
  // Delete appointment
  deleteAppointment: async (appointmentId) => {
    await apiClient.delete(`/appointments/${appointmentId}/`)
  }
}
```

### How React Query Manages Data
**File**: `src/features/calendar/hooks/useAdvancedCalendar.js`
```javascript
// React Query automatically handles caching and loading states
export const useAdvancedCalendar = () => {
  // This automatically:
  // - Fetches data when component mounts
  // - Caches data so it doesn't fetch again unnecessarily
  // - Provides loading and error states
  // - Refetches in background to keep data fresh
  const { 
    data: appointments, 
    isLoading, 
    error, 
    refetch 
  } = useQuery({
    queryKey: ['appointments', selectedDate],  // Unique key for this data
    queryFn: () => appointmentService.fetchAppointments(selectedDate, selectedDate),
    staleTime: 5 * 60 * 1000,  // Data is fresh for 5 minutes
    gcTime: 10 * 60 * 1000,    // Keep in cache for 10 minutes
    refetchInterval: 30 * 1000  // Refetch every 30 seconds
  })
  
  return { appointments, isLoading, error, refetch }
}
```

---

## 🧩 How Components Work Together

### The Component Hierarchy
```
📄 Calendar.jsx (Main Page)
├── 📅 CalendarContainer.jsx (Wrapper with loading states)
│   ├── 🎛️ CalendarHeader.jsx (Date picker, view controls)
│   ├── 📊 CalendarGrid.jsx (The calendar layout)
│   │   ├── 👥 EmployeeHeaders.jsx (Staff names)
│   │   ├── ⏰ TimeGrid.jsx (Time column)
│   │   └── 📝 AppointmentArea.jsx (Where appointments show)
│   │       ├── 🔲 TimeSlot.jsx (Empty clickable slots)
│   │       └── 📋 AppointmentSlot.jsx (Appointment blocks)
│   └── 🎯 CalendarModals.jsx (Pop-up forms)
│       ├── 📝 AppointmentDetailsModal.jsx
│       ├── ➕ AppointmentCreationModal.jsx
│       └── 📋 WaitlistModal.jsx
```

### How Props Flow Down
**Example**: Creating a new appointment
```javascript
// 1. Calendar.jsx (top level)
function Calendar() {
  const { createAppointment } = useAdvancedCalendar()
  
  return (
    <CalendarGrid onCreateAppointment={createAppointment} />
  )
}

// 2. CalendarGrid.jsx (passes down further)
function CalendarGrid({ onCreateAppointment }) {
  return (
    <div>
      {timeSlots.map(slot => (
        <TimeSlot 
          key={slot.id}
          slot={slot}
          onCreateAppointment={onCreateAppointment}
        />
      ))}
    </div>
  )
}

// 3. TimeSlot.jsx (handles the click)
function TimeSlot({ slot, onCreateAppointment }) {
  const handleClick = () => {
    onCreateAppointment({
      startTime: slot.time,
      employeeId: slot.employeeId
    })
  }
  
  return <div onClick={handleClick} className="time-slot" />
}
```

### How Events Bubble Up
**Example**: When user drags an appointment
```javascript
// 1. AppointmentSlot.jsx (detects drag)
function AppointmentSlot({ appointment, onMove }) {
  const handleDragEnd = (e) => {
    const newPosition = calculateDropPosition(e)
    onMove(appointment.id, newPosition)  // Tell parent about move
  }
  
  return <div onDragEnd={handleDragEnd}>...</div>
}

// 2. CalendarGrid.jsx (coordinates the move)
function CalendarGrid({ appointments, onUpdateAppointment }) {
  const handleMove = (appointmentId, newPosition) => {
    // Update immediately for smooth UI
    updateAppointmentPosition(appointmentId, newPosition)
    
    // Then save to database
    onUpdateAppointment(appointmentId, newPosition)
  }
  
  return (
    <div>
      {appointments.map(apt => (
        <AppointmentSlot 
          key={apt.id}
          appointment={apt}
          onMove={handleMove}
        />
      ))}
    </div>
  )
}

// 3. Calendar.jsx (saves to database)
function Calendar() {
  const { updateAppointment } = useAdvancedCalendar()
  
  const handleUpdateAppointment = async (id, updates) => {
    await updateAppointment(id, updates)
  }
  
  return (
    <CalendarGrid onUpdateAppointment={handleUpdateAppointment} />
  )
}
```

---

## ⚡ How We Handle Real-time Updates

### **Important**: We DON'T Have True Real-time
Unlike apps like Google Docs where you see changes instantly, our app uses a simpler approach:

### How Our "Fake Real-time" Works
1. **Auto-refresh**: Calendar refreshes every 30 seconds
2. **Manual refresh**: Users can click refresh button
3. **Optimistic updates**: Changes appear immediately, then sync to server

### Implementation Details
**File**: `src/features/calendar/hooks/useAdvancedCalendar.js`
```javascript
export const useAdvancedCalendar = () => {
  // Auto-refresh appointments every 30 seconds
  const { data: appointments } = useQuery({
    queryKey: ['appointments', selectedDate],
    queryFn: () => fetchAppointments(selectedDate),
    refetchInterval: 30000,  // 30 seconds
    refetchIntervalInBackground: true  // Even when tab is not active
  })
  
  // Manual refresh function
  const refreshAppointments = () => {
    queryClient.invalidateQueries(['appointments'])
  }
  
  // Optimistic updates (show changes immediately)
  const moveAppointment = async (appointmentId, newTime) => {
    // 1. Update UI immediately
    setAppointments(prev => 
      prev.map(apt => 
        apt.id === appointmentId 
          ? { ...apt, start: newTime }
          : apt
      )
    )
    
    // 2. Send to server
    try {
      await appointmentService.updateAppointment(appointmentId, { start: newTime })
    } catch (error) {
      // 3. Revert if server update fails
      refreshAppointments()
      showErrorMessage('Failed to move appointment')
    }
  }
  
  return { appointments, refreshAppointments, moveAppointment }
}
```

### Why We Don't Use WebSockets
**Advantages of our approach**:
- ✅ Simpler to implement and maintain
- ✅ Works with existing Django setup
- ✅ Fewer server resources needed
- ✅ More reliable (no connection drops)

**Disadvantages**:
- ❌ Changes don't appear instantly for other users
- ❌ Possible conflicts if multiple users edit same appointment
- ❌ Uses more bandwidth with periodic refreshes

---

## 🚀 How We Make It Fast

### Performance Strategies We Use

#### 1. Prevent Unnecessary Re-renders
**Problem**: Every time data changes, all components re-render
**Solution**: Only re-render when specific data changes

```javascript
// Bad: Component re-renders every time ANY prop changes
function AppointmentSlot({ appointment, employee, services }) {
  return <div>{appointment.title}</div>
}

// Good: Component only re-renders when appointment changes
const AppointmentSlot = React.memo(({ appointment, employee, services }) => {
  return <div>{appointment.title}</div>
}, (prevProps, nextProps) => {
  // Only re-render if appointment actually changed
  return prevProps.appointment.id === nextProps.appointment.id &&
         prevProps.appointment.title === nextProps.appointment.title
})
```

#### 2. Cache Expensive Calculations
**Problem**: Complex calculations run every time component renders
**Solution**: Cache results until inputs change

```javascript
function CalendarGrid({ appointments, employees }) {
  // Bad: This filter runs every time component renders
  const filteredAppointments = appointments.filter(apt => 
    selectedEmployees.has(apt.employeeId)
  )
  
  // Good: Only recalculate when appointments or selectedEmployees change
  const filteredAppointments = useMemo(() => {
    return appointments.filter(apt => 
      selectedEmployees.has(apt.employeeId)
    )
  }, [appointments, selectedEmployees])
  
  return <div>...</div>
}
```

#### 3. Cache Function References
**Problem**: New functions created every render cause child re-renders
**Solution**: Cache functions until their dependencies change

```javascript
function Calendar() {
  const { updateAppointment } = useAdvancedCalendar()
  
  // Bad: New function created every render
  const handleAppointmentClick = (appointment) => {
    updateAppointment(appointment.id, { clicked: true })
  }
  
  // Good: Function cached until updateAppointment changes
  const handleAppointmentClick = useCallback((appointment) => {
    updateAppointment(appointment.id, { clicked: true })
  }, [updateAppointment])
  
  return (
    <AppointmentList 
      appointments={appointments}
      onAppointmentClick={handleAppointmentClick}
    />
  )
}
```

#### 4. Smart Data Loading
**File**: `src/features/calendar/hooks/useAdvancedCalendar.js`
```javascript
// Only load data when actually needed
export const useAdvancedCalendar = () => {
  const [selectedDate, setSelectedDate] = useState(new Date())
  
  // Only fetch appointments for the selected date
  const { data: appointments } = useQuery({
    queryKey: ['appointments', selectedDate.toISOString()],
    queryFn: () => fetchAppointments(selectedDate),
    // Keep data fresh but don't fetch unnecessarily
    staleTime: 5 * 60 * 1000,  // 5 minutes
    gcTime: 10 * 60 * 1000,    // 10 minutes
    // Only fetch when component is actually visible
    refetchOnWindowFocus: true
  })
  
  return { appointments, selectedDate, setSelectedDate }
}
```

#### 5. Efficient Styling
**File**: `src/features/calendar/components/appointment-slot/StylingFunctions.jsx`
```javascript
// Pre-calculate styles to avoid recalculating every render
const getAppointmentStyle = (appointment) => {
  // Cache color calculations
  const colorCache = new Map()
  
  if (!colorCache.has(appointment.serviceType)) {
    colorCache.set(appointment.serviceType, calculateServiceColor(appointment.serviceType))
  }
  
  return {
    backgroundColor: colorCache.get(appointment.serviceType),
    // Use transform for better performance (GPU accelerated)
    transform: `translateY(${appointment.offsetY}px)`,
    // Pre-calculate height
    height: `${appointment.duration * 2}px`
  }
}
```

---

## 🛠️ Step-by-Step Implementation Examples

### Example 1: Creating a New Appointment

#### Step 1: User Clicks Empty Time Slot
**File**: `src/features/calendar/components/slots/TimeSlot.jsx`
```javascript
function TimeSlot({ time, employee, onCreateAppointment }) {
  const handleClick = () => {
    // 1. Collect context from the clicked slot
    const appointmentContext = {
      startTime: time,           // e.g., "2024-01-15T09:00:00"
      employeeId: employee.id,   // e.g., 5
      duration: 60,              // Default 60 minutes
      endTime: addMinutes(time, 60)
    }
    
    // 2. Open creation modal with this context
    onCreateAppointment(appointmentContext)
  }
  
  return (
    <div 
      className="time-slot cursor-pointer hover:bg-gray-100"
      onClick={handleClick}
    >
      {/* Visual indicator that slot is clickable */}
      <div className="slot-indicator">+</div>
    </div>
  )
}
```

#### Step 2: Show Creation Modal
**File**: `src/features/calendar/components/modals/AppointmentCreationModal.jsx`
```javascript
function AppointmentCreationModal({ isOpen, initialData, onSave, onClose }) {
  const [currentStep, setCurrentStep] = useState(1)
  const [formData, setFormData] = useState({
    // Pre-fill with clicked slot data
    startTime: initialData.startTime,
    employeeId: initialData.employeeId,
    duration: initialData.duration,
    // User needs to fill these
    customerId: null,
    serviceId: null,
    notes: ''
  })
  
  // Multi-step form process
  const steps = [
    { id: 1, name: 'Select Service', component: ServiceSelection },
    { id: 2, name: 'Choose Customer', component: CustomerSelection },
    { id: 3, name: 'Confirm Details', component: ConfirmationStep }
  ]
  
  const handleNext = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1)
    } else {
      handleSave()
    }
  }
  
  const handleSave = async () => {
    try {
      // 3. Save the appointment
      await onSave(formData)
      onClose()
    } catch (error) {
      alert('Failed to create appointment: ' + error.message)
    }
  }
  
  const CurrentStepComponent = steps[currentStep - 1].component
  
  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <div className="appointment-creation-modal">
        {/* Progress indicator */}
        <div className="progress-bar">
          {steps.map((step, index) => (
            <div 
              key={step.id}
              className={`step ${index + 1 <= currentStep ? 'active' : ''}`}
            >
              {step.name}
            </div>
          ))}
        </div>
        
        {/* Current step content */}
        <CurrentStepComponent 
          formData={formData}
          setFormData={setFormData}
        />
        
        {/* Navigation buttons */}
        <div className="modal-actions">
          <button onClick={onClose}>Cancel</button>
          <button onClick={handleNext}>
            {currentStep === steps.length ? 'Create Appointment' : 'Next'}
          </button>
        </div>
      </div>
    </Modal>
  )
}
```

#### Step 3: Save to Database
**File**: `src/features/calendar/hooks/useAdvancedCalendar.js`
```javascript
const createAppointment = async (formData) => {
  // 1. Validate the data
  if (!formData.customerId || !formData.serviceId) {
    throw new Error('Customer and service are required')
  }
  
  // 2. Check for conflicts
  const conflicts = await checkForConflicts({
    employeeId: formData.employeeId,
    startTime: formData.startTime,
    endTime: formData.endTime
  })
  
  if (conflicts.length > 0) {
    throw new Error('Time slot conflicts with existing appointment')
  }
  
  // 3. Transform data for Django API
  const appointmentData = {
    customer: parseInt(formData.customerId),
    employee: parseInt(formData.employeeId),
    start_time: formData.startTime.toISOString(),
    end_time: formData.endTime.toISOString(),
    notes: formData.notes,
    appointment_services: [{
      service: parseInt(formData.serviceId),
      duration: formData.duration,
      base_price: formData.price
    }]
  }
  
  // 4. Optimistic update (show immediately)
  const tempAppointment = {
    id: 'temp-' + Date.now(),
    ...formData,
    status: 'pending'
  }
  
  setAppointments(prev => [...prev, tempAppointment])
  
  // 5. Send to Django
  try {
    const savedAppointment = await appointmentService.createAppointment(appointmentData)
    
    // 6. Replace temp with real appointment
    setAppointments(prev => 
      prev.map(apt => 
        apt.id === tempAppointment.id 
          ? transformAppointmentForCalendar(savedAppointment)
          : apt
      )
    )
    
    // 7. Show success message
    toast.success('Appointment created successfully!')
    
  } catch (error) {
    // 8. Remove temp appointment if save failed
    setAppointments(prev => prev.filter(apt => apt.id !== tempAppointment.id))
    throw error
  }
}
```

### Example 2: Dragging an Appointment

#### Step 1: Start Dragging
**File**: `src/features/calendar/components/appointment-slot/DragBehaviors.jsx`
```javascript
function AppointmentSlot({ appointment, onMove }) {
  const [isDragging, setIsDragging] = useState(false)
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 })
  
  const handleDragStart = (e) => {
    setIsDragging(true)
    
    // Calculate offset from mouse to appointment top-left
    const rect = e.currentTarget.getBoundingClientRect()
    setDragOffset({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    })
    
    // Store appointment data for drop handling
    e.dataTransfer.setData('appointmentId', appointment.id)
    e.dataTransfer.setData('originalTime', appointment.start.toISOString())
  }
  
  const handleDragEnd = () => {
    setIsDragging(false)
    setDragOffset({ x: 0, y: 0 })
  }
  
  return (
    <div
      className={`appointment-slot ${isDragging ? 'dragging' : ''}`}
      draggable
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="appointment-content">
        {appointment.service_name}
      </div>
    </div>
  )
}
```

#### Step 2: Handle Drop
**File**: `src/features/calendar/components/slots/TimeSlot.jsx`
```javascript
function TimeSlot({ time, employee, onMoveAppointment }) {
  const handleDragOver = (e) => {
    e.preventDefault() // Allow drop
  }
  
  const handleDrop = (e) => {
    e.preventDefault()
    
    // 1. Get appointment data from drag
    const appointmentId = e.dataTransfer.getData('appointmentId')
    const originalTime = e.dataTransfer.getData('originalTime')
    
    // 2. Calculate new time based on drop position
    const newStartTime = time
    const originalStart = new Date(originalTime)
    const timeDifference = newStartTime.getTime() - originalStart.getTime()
    
    // 3. Validate the move
    if (timeDifference === 0) {
      return // No change
    }
    
    // 4. Call move handler
    onMoveAppointment(appointmentId, {
      startTime: newStartTime,
      employeeId: employee.id
    })
  }
  
  return (
    <div
      className="time-slot drop-zone"
      onDragOver={handleDragOver}
      onDrop={handleDrop}
    >
      {/* Show drop indicator when dragging over */}
      <div className="drop-indicator">Drop here</div>
    </div>
  )
}
```

#### Step 3: Update Appointment
**File**: `src/features/calendar/hooks/useAdvancedCalendar.js`
```javascript
const moveAppointment = async (appointmentId, newPosition) => {
  // 1. Find the appointment
  const appointment = appointments.find(apt => apt.id === appointmentId)
  if (!appointment) return
  
  // 2. Calculate new end time
  const duration = appointment.end.getTime() - appointment.start.getTime()
  const newEndTime = new Date(newPosition.startTime.getTime() + duration)
  
  // 3. Validate the move
  const conflicts = await checkForConflicts({
    appointmentId,
    employeeId: newPosition.employeeId,
    startTime: newPosition.startTime,
    endTime: newEndTime
  })
  
  if (conflicts.length > 0) {
    alert('Cannot move appointment - conflicts with existing booking')
    return
  }
  
  // 4. Optimistic update (move immediately in UI)
  setAppointments(prev => 
    prev.map(apt => 
      apt.id === appointmentId 
        ? { 
            ...apt, 
            start: newPosition.startTime,
            end: newEndTime,
            employeeId: newPosition.employeeId
          }
        : apt
    )
  )
  
  // 5. Send update to Django
  try {
    await appointmentService.updateAppointment(appointmentId, {
      start_time: newPosition.startTime.toISOString(),
      end_time: newEndTime.toISOString(),
      employee: parseInt(newPosition.employeeId)
    })
    
    toast.success('Appointment moved successfully!')
    
  } catch (error) {
    // 6. Revert if server update fails
    setAppointments(prev => 
      prev.map(apt => 
        apt.id === appointmentId 
          ? appointment  // Restore original
          : apt
      )
    )
    
    toast.error('Failed to move appointment: ' + error.message)
  }
}
```

### Example 3: Cancel Button Implementation

#### The Problem We Solved
**Original issue**: Cancel button tried to DELETE the appointment, but Django has foreign key constraints
**Solution**: Change Cancel to update appointment status to "cancelled"

#### Implementation
**File**: `src/features/calendar/components/modals/AppointmentDetailsModal.jsx`
```javascript
function AppointmentDetailsModal({ appointment, onClose, onUpdateStatus }) {
  const [isUpdating, setIsUpdating] = useState(false)
  
  const handleCancelAppointment = async () => {
    // 1. Confirm with user
    if (!confirm('Are you sure you want to cancel this appointment?')) {
      return
    }
    
    setIsUpdating(true)
    
    try {
      // 2. Update status instead of deleting
      await onUpdateStatus(appointment.id, {
        status: 'cancelled',
        cancellation_reason: 'Cancelled by staff',
        cancelled_at: new Date().toISOString()
      })
      
      // 3. Close modal
      onClose()
      
      // 4. Show success message
      toast.success('Appointment cancelled successfully')
      
    } catch (error) {
      // 5. Show error message
      toast.error('Failed to cancel appointment: ' + error.message)
    } finally {
      setIsUpdating(false)
    }
  }
  
  return (
    <Modal isOpen onClose={onClose}>
      <div className="appointment-details">
        <h2>Appointment Details</h2>
        
        {/* Appointment info */}
        <div className="appointment-info">
          <p><strong>Service:</strong> {appointment.service_name}</p>
          <p><strong>Customer:</strong> {appointment.customer_name}</p>
          <p><strong>Time:</strong> {formatTime(appointment.start)}</p>
          <p><strong>Status:</strong> {appointment.status}</p>
        </div>
        
        {/* Action buttons */}
        <div className="modal-actions">
          <button 
            onClick={handleCancelAppointment}
            disabled={isUpdating}
            className="btn-danger"
          >
            {isUpdating ? 'Cancelling...' : 'Cancel Appointment'}
          </button>
          <button onClick={onClose} className="btn-secondary">
            Close
          </button>
        </div>
      </div>
    </Modal>
  )
}
```

---

## 🎯 Summary: What Makes This Architecture Good

### ✅ **Easy to Understand**
- **Clear file organization**: Each feature has its own folder
- **Simple data flow**: Data flows down, events flow up
- **Self-contained features**: Calendar code is separate from employee code

### ✅ **Easy to Maintain**
- **Single responsibility**: Each component does one thing well
- **Reusable components**: Same components used in multiple places
- **Clear naming**: Function and variable names explain what they do

### ✅ **Easy to Test**
- **Pure functions**: Given same input, always returns same output
- **Isolated components**: Each component can be tested separately
- **Mocked services**: Can test without real database

### ✅ **Good Performance**
- **Smart caching**: Don't fetch same data multiple times
- **Optimistic updates**: UI responds immediately
- **Efficient rendering**: Only re-render when needed

### ✅ **Scalable**
- **Modular structure**: Easy to add new features
- **Separation of concerns**: UI, business logic, and data are separate
- **Consistent patterns**: Same patterns used throughout app

### 🎯 **Key Takeaways**
1. **Feature-based organization** makes code easier to find and maintain
2. **Custom hooks** handle complex logic, keeping components simple
3. **Service layer** handles all API communication
4. **Optimistic updates** make the app feel fast and responsive
5. **React Query** handles caching and loading states automatically

This architecture is designed to be **maintainable, scalable, and developer-friendly** while providing a **smooth user experience** for salon/spa management. 