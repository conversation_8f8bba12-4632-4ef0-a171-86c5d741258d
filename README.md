# chatbook-business-frontend (React + Vite + Tailwind)

This repository contains the **B2B** web frontend for our application, built with <PERSON><PERSON>, Vite, and Tailwind CSS. It communicates with the Django backend via RESTful APIs.

---

## 📂 Project Structure

```bash
chatbook-business-frontend/
├── public/
│   ├── favicon.ico       # App icon
│   └── robots.txt        # SEO / crawler rules
├── index.html            # HTML entrypoint
├── src/
│   ├── assets/           # Static assets (images, fonts, SVGs)
│   ├── components/       # Shared UI components (Buttons, Modals, etc.)
│   ├── features/         # Vertical slices (feature-centric modules)
│   ├── hooks/            # Reusable custom React hooks
│   ├── pages/            # Route-level components (screens)
│   ├── styles/           # Global CSS and Tailwind overrides
│   ├── utils/            # Utility functions and constants
│   ├── App.jsx           # Root application component
│   └── main.jsx          # Application bootstrap (ReactDOM.render)
├── .eslintrc.js          # ESLint configuration
├── package.json          # NPM dependencies & scripts
├── postcss.config.js     # PostCSS + Tailwind setup
├── tailwind.config.js    # Tailwind CSS configuration
└── vite.config.js        # Vite build & dev server settings
