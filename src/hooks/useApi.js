import { useState, useRef, useCallback } from 'react'
import axios from 'axios'

// 请求缓存
const requestCache = new Map();
// 防抖定时器
const debounceTimers = new Map();
// Base API configuration - updated fallback to match backend structure
const getApiBaseUrl = () => {
  // Check if we're in ECS/production environment
  if (window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1') {
    // Production: use relative URLs (same ALB)
    return '/api/v1'
  }
  
  // Development: use explicit localhost
  return import.meta.env.VITE_API_URL || 'http://localhost:8000/api/v1'
}

const api = axios.create({
  baseURL: getApiBaseUrl(),
  headers: {
    'Content-Type': 'application/json',
  },
})

// Add request interceptor for authentication
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('auth_token')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

// Add response interceptor for handling auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    // 处理401未授权错误
    if (error.response && error.response.status === 401) {
      // 清除token
      localStorage.removeItem('auth_token')
      
      // 重定向到登录页面
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

/**
 * 生成缓存键
 */
const getCacheKey = (endpoint, params) => {
  return `${endpoint}:${JSON.stringify(params || {})}`
}

/**
 * 确保 endpoint 末尾有斜杠
 */
const ensureTrailingSlash = (endpoint) => {
  if (!endpoint) return '/';
  return endpoint.endsWith('/') ? endpoint : `${endpoint}/`;
}

/**
 * Custom hook for API interactions
 * @param {string} endpoint - API endpoint
 * @param {Object} options - Hook options
 * @returns {Object} - API interaction methods and state
 */
export function useApi(endpoint, options = {}) {
  const { 
    enableCache = true, 
    cacheDuration = 60000, // 默认缓存1分钟
    debounceTime = 300, // 默认防抖300ms
    addTrailingSlash = true // 默认添加末尾斜杠
  } = options;
  
  // 确保 endpoint 末尾有斜杠
  const formattedEndpoint = addTrailingSlash ? ensureTrailingSlash(endpoint) : endpoint;
  
  const [data, setData] = useState(null)
  const [error, setError] = useState(null)
  const [loading, setLoading] = useState(false)
  const abortControllerRef = useRef(null)
  
  // 取消之前的请求
  const cancelPreviousRequest = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }
    abortControllerRef.current = new AbortController()
  }

  // 防抖函数
  const debounce = (fn, key, time) => {
    if (debounceTimers.has(key)) {
      clearTimeout(debounceTimers.get(key))
    }
    
    return new Promise((resolve) => {
      const timer = setTimeout(async () => {
        debounceTimers.delete(key)
        resolve(await fn())
      }, time)
      
      debounceTimers.set(key, timer)
    })
  }

  // Fetch data from the API
  const fetchData = useCallback(async (params = {}) => {
    // 处理ID参数情况
    let url = formattedEndpoint;
    let queryParams = params;
    
    // 如果params是字符串或数字，将其视为ID
    if (typeof params === 'string' || typeof params === 'number') {
      url = `${formattedEndpoint}${params}/`;
      queryParams = {};
      console.log(`Treating param as ID, new URL: ${url}`);
    }
    
    const cacheKey = getCacheKey(url, queryParams);
    
    if (enableCache && requestCache.has(cacheKey)) {
      const { data: cachedData, timestamp } = requestCache.get(cacheKey)
      if (Date.now() - timestamp < cacheDuration) {
        setData(cachedData)
        return cachedData
      }
      requestCache.delete(cacheKey)
    }
  
    cancelPreviousRequest()
    
    setLoading(true)
    setError(null)
    
    return debounce(async () => {
      try {
        console.log(`Fetching API: ${url} with params:`, queryParams);
        const response = await api.get(url, { 
          params: queryParams,
          signal: abortControllerRef.current.signal
        })
        
        console.log(`API Success: ${url}`, response.data);
        setData(response.data)
        if (enableCache) {
          requestCache.set(cacheKey, {
            data: response.data,
            timestamp: Date.now()
          })
        }
        
        return response.data
      } catch (err) {
        if (axios.isCancel(err)) {
          console.log(`API Request cancelled: ${url}`);
          return null
        }
        
        console.error(`API Error: ${url}`, err);
        setError(err.response?.data || { message: err.message })
        return null
      } finally {
        setLoading(false)
      }
    }, cacheKey, debounceTime)
  }, [formattedEndpoint, enableCache, cacheDuration, debounceTime])

  // Create a new resource
  // 支持可选 path 参数
  const createData = async (pathOrPayload, payloadMaybe) => {
    setLoading(true)
    setError(null)
    let url = formattedEndpoint;
    let payload = pathOrPayload;
    if (typeof pathOrPayload === 'string') {
      url = `${formattedEndpoint}${pathOrPayload}`;
      payload = payloadMaybe;
    }
    try {
      const response = await api.post(url, payload)
      return response.data
    } catch (err) {
      setError(err.response?.data || { message: err.message })
      return null
    } finally {
      setLoading(false)
    }
  }

  // Update an existing resource
  const updateData = async (id, payload) => {
    setLoading(true)
    setError(null)
    
    try {
      // 确保URL末尾有斜杠
      const url = `${formattedEndpoint}${id}/`;
      console.log(`Updating resource at: ${url}`, payload);
      const response = await api.put(url, payload)
      return response.data
    } catch (err) {
      setError(err.response?.data || { message: err.message })
      return null
    } finally {
      setLoading(false)
    }
  }

  // Delete a resource
  const deleteData = async (id) => {
    setLoading(true)
    setError(null)
    
    try {
      // 确保URL末尾有斜杠
      const url = `${formattedEndpoint}${id}/`;
      console.log(`Deleting resource at: ${url}`);
      const response = await api.delete(url)
      return response.data
    } catch (err) {
      setError(err.response?.data || { message: err.message })
      return null
    } finally {
      setLoading(false)
    }
  }

  const clearCache = (specificEndpoint = null) => {
    if (specificEndpoint) {
      const formattedSpecificEndpoint = addTrailingSlash ? ensureTrailingSlash(specificEndpoint) : specificEndpoint;
      for (const key of requestCache.keys()) {
        if (key.startsWith(`${formattedSpecificEndpoint}:`)) {
          requestCache.delete(key)
        }
      }
    } else {
      for (const key of requestCache.keys()) {
        if (key.startsWith(`${formattedEndpoint}:`)) {
          requestCache.delete(key)
        }
      }
    }
  }

  return {
    data,
    error,
    loading,
    fetchData,
    createData,
    updateData,
    deleteData,
    clearCache
  }
}

export default useApi 

// Named export so other modules can reuse the authenticated axios instance directly
export { api } 