import React, { useState, useRef, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import './CampaignDesign.css'; // Import custom styles
import { useMarketingApi } from '../../features/marketing/services/marketingApi';
import {
  getCampaignTypeId,
  getCampaignTypeString,
  getCampaignTypeDisplayName
} from '../../features/marketing/constants/campaignTypes';
// import mjml2html from 'mjml'; // Commented out due to browser compatibility issues

function CampaignDesign() {
  const navigate = useNavigate();
  const marketingApi = useMarketingApi();
  
  const [activeTab, setActiveTab] = useState('design'); // 'design', 'preview-desktop', 'preview-mobile'
  // State to track design area components
  const [designAreaComponents, setDesignAreaComponents] = useState([]);
  const [draggedComponent, setDraggedComponent] = useState(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [activeEditTab, setActiveEditTab] = useState('text'); // 'text', 'style', 'image'
  const [currentEditingContent, setCurrentEditingContent] = useState('');
  const [currentEditingIndex, setCurrentEditingIndex] = useState(null);
  const [lineHeight, setLineHeight] = useState('1.5'); // Default line height
  
  // Add campaign data state from CreateCampaign page
  const [campaignData, setCampaignData] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submissionError, setSubmissionError] = useState(null);
  
  // Email preview dialog state
  const [previewDialogOpen, setPreviewDialogOpen] = useState(false);
  const [previewEmail, setPreviewEmail] = useState('');
  const [isSendingPreview, setIsSendingPreview] = useState(false);
  
  // Component style state
  const [componentStyle, setComponentStyle] = useState({
    backgroundColor: '#ffffff',
    borderColor: '#000000',
    borderWidth: '0px',
    borderStyle: 'solid',
    paddingVertical: '10px',
    paddingHorizontal: '10px',
  });
  
  // Divider style state
  const [dividerStyle, setDividerStyle] = useState({
    borderColor: '#000000',
    borderWidth: '1px',
    borderStyle: 'solid',
    marginTop: '10px',
    marginBottom: '10px',
  });
  
  // component width setting
  const [componentWidth, setComponentWidth] = useState('full'); // 'full' or 'half'
  // component position setting (only applicable to half-width components)
  const [componentPosition, setComponentPosition] = useState('left'); // 'left' or 'right'
  
  // Color picker state
  const [showBackgroundColorPicker, setShowBackgroundColorPicker] = useState(false);
  const [showBorderColorPicker, setShowBorderColorPicker] = useState(false);
  
  // Text padding settings
  const [textPadding, setTextPadding] = useState({
    vertical: '10px',
    horizontal: '10px'
  });
  
  // Image settings
  const [imageSettings, setImageSettings] = useState({
    src: '',
    rotation: 0,
    scale: 1,
    flipHorizontal: false,
    flipVertical: false,
    alt: 'Image'
  });
  
  // Image settings history for undo/redo
  const [imageSettingsHistory, setImageSettingsHistory] = useState([]);
  const [imageSettingsRedoStack, setImageSettingsRedoStack] = useState([]);
  const [originalImageSettings, setOriginalImageSettings] = useState(null);
  
  // Button settings
  const [buttonSettings, setButtonSettings] = useState({
    buttons: [
      { 
        text: 'Button 1', 
        link: '', 
        color: '#000000', 
        textColor: '#ffffff',
        borderColor: '#000000',
        borderWidth: '0px',
        borderRadius: '20px',
        width: '150px',
        height: '40px',
        font: 'Comic Sans MS',
        fontSize: '14px',
        active: true // default to show the first button
      }
    ]
  });
  const [currentEditingButton, setCurrentEditingButton] = useState(0);
  
  // Social Media Button settings
  const [socialButtonsSettings, setSocialButtonsSettings] = useState({
    theme: 'default',
    buttons: [
      { type: 'facebook', link: '', active: true },
      { type: 'instagram', link: '', active: true },
      { type: 'youtube', link: '', active: true },
      { type: 'x', link: '', active: true },
      { type: 'linkedin', link: '', active: true },
      { type: 'tiktok', link: '', active: true },
      { type: 'pinterest', link: '', active: true },
      { type: 'yelp', link: '', active: true },
      { type: 'threads', link: '', active: true }
    ]
  });
  
  // File input ref for image upload
  const fileInputRef = useRef(null);
  
  // Predefined color options
  const colorOptions = [
    '#ffffff', '#f3f4f6', '#e5e7eb', '#d1d5db', '#9ca3af', 
    '#6b7280', '#4b5563', '#374151', '#1f2937', '#111827',
    '#fee2e2', '#fecaca', '#fca5a5', '#f87171', '#ef4444',
    '#dcfce7', '#bbf7d0', '#86efac', '#4ade80', '#22c55e',
    '#dbeafe', '#bfdbfe', '#93c5fd', '#60a5fa', '#3b82f6',
    '#f3e8ff', '#e9d5ff', '#d8b4fe', '#c084fc', '#a855f7',
    '#fef3c7', '#fde68a', '#fcd34d', '#fbbf24', '#f59e0b',
  ];
  
  // Border style options
  const borderStyleOptions = [
    'none', 'solid', 'dashed', 'dotted', 'double'
  ];
  
  const quillRef = useRef(null);
  const draggedItemRef = useRef(null);
  
  // Quill editor modules and formats
  const modules = {
    toolbar: {
      container: [
        [{ 'font': [] }],
        [{ 'size': ['small', false, 'large', 'huge'] }],
        ['bold', 'italic', 'underline', 'strike'],
        ['clean'], // Remove formatting button
        [{ 'color': [] }, { 'background': [] }],
        [{ 'align': ['', 'center', 'right', 'justify'] }],
        [{ 'list': 'ordered' }, { 'list': 'bullet' }],
        [{ 'blockquote': true }]
      ]
    },
    clipboard: {
      matchVisual: true
    }
  };
  
  const formats = [
    'font', 'size',
    'bold', 'italic', 'underline', 'strike',
    'color', 'background',
    'align', 'list', 'blockquote'
  ];
  
  // Design components for the left sidebar
  const availableComponents = [
    { id: 'heading', name: 'Heading', icon: 'H1', content: 'Heading Text' },
    { id: 'header-image', name: 'Header Image', icon: '🖼️', content: 'Image Placeholder' },
    { id: 'divider', name: 'Divider', icon: '—', content: null },
    { id: 'text', name: 'Text', icon: 'Aa', content: 'Sample text content. Click to edit.' },
    { id: 'image', name: 'Image', icon: '🖼️', content: 'Image Placeholder' },
    { 
      id: 'two-images', 
      name: 'Two Images', 
      icon: (
        <div className="flex">
          <span className="mr-1">🖼️</span>
          <span>🖼️</span>
        </div>
      ),
      content: 'Two Images Placeholder'
    },
    { 
      id: 'image-text', 
      name: 'Image & Text', 
      icon: (
        <div className="flex">
          <span className="mr-1">🖼️</span>
          <span>Aa</span>
        </div>
      ),
      content: 'Image and Text Placeholder'
    },
    { 
      id: 'text-image', 
      name: 'Text & Image', 
      icon: (
        <div className="flex">
          <span className="mr-1">Aa</span>
          <span>🖼️</span>
        </div>
      ),
      content: 'Text and Image Placeholder'
    },
    // Image On Text component hidden
    // { id: 'image-on-text', name: 'Image On Text', icon: '🖼️/Aa', content: 'Image on Text Placeholder' },
    { id: 'buttons', name: 'Buttons', icon: '🔘', content: 'Button Placeholder' },
    { id: 'social-buttons', name: 'Social Buttons', icon: '📱', content: 'Social Buttons Placeholder' },
    { id: 'footer-image', name: 'Footer Image', icon: '🖼️', content: 'Footer Image Placeholder' },
  ];
  
    // Drag and drop handlers
  const handleDragStart = (e, component) => {
    setDraggedComponent(component);
    e.dataTransfer.effectAllowed = 'move';
    
    // add debug information
    console.log('Drag start:', component.id, component);
    
    // Store the component data in the dataTransfer object
    try {
      const data = JSON.stringify({
        id: component.id,
        isFromPalette: true,
        index: e.currentTarget.dataset.index // add index information
      });
      e.dataTransfer.setData('text/plain', data);
    } catch (err) {
      console.error('Error setting drag data:', err);
    }
    
    // Use a timeout to make the dragged item invisible after dragging starts
    setTimeout(() => {
      if (draggedItemRef.current) {
        draggedItemRef.current.style.display = 'none';
      }
    }, 0);
  };
  
  const [dragOverIndex, setDragOverIndex] = useState(null);
  
  // add auto-scrolling related states
  const [autoScrolling, setAutoScrolling] = useState(false);
  const [scrollDirection, setScrollDirection] = useState(null);
  const designAreaRef = useRef(null);
  const autoScrollIntervalRef = useRef(null);
  
  // auto-scrolling function
  const handleAutoScroll = (e) => {
    if (!designAreaRef.current || !draggedComponent) return;
    
    const container = designAreaRef.current;
    const containerRect = container.getBoundingClientRect();
    const mouseY = e.clientY;
    
    const scrollThreshold = 80; // how many pixels from the edge to start scrolling
    const maxScrollSpeed = 15; // maximum scroll speed
    
    // clear existing scroll interval
    if (autoScrollIntervalRef.current) {
      clearInterval(autoScrollIntervalRef.current);
      autoScrollIntervalRef.current = null;
    }
    
    // Check if near the top edge
    if (mouseY < containerRect.top + scrollThreshold) {
      // calculate dynamic scroll speed - the closer to the edge, the faster the scroll
      const distance = mouseY - containerRect.top;
      const speedFactor = 1 - Math.max(0, Math.min(1, distance / scrollThreshold));
      const scrollSpeed = Math.ceil(maxScrollSpeed * speedFactor);
      
      setScrollDirection('up');
      setAutoScrolling(true);
      autoScrollIntervalRef.current = setInterval(() => {
        container.scrollTop -= scrollSpeed;
      }, 16); // about 60fps update frequency
    } 
    // Check if near the bottom edge
    else if (mouseY > containerRect.bottom - scrollThreshold) {
      // calculate dynamic scroll speed - the closer to the edge, the faster the scroll
      const distance = containerRect.bottom - mouseY;
      const speedFactor = 1 - Math.max(0, Math.min(1, distance / scrollThreshold));
      const scrollSpeed = Math.ceil(maxScrollSpeed * speedFactor);
      
      setScrollDirection('down');
      setAutoScrolling(true);
      autoScrollIntervalRef.current = setInterval(() => {
        container.scrollTop += scrollSpeed;
      }, 16); // about 60fps update frequency
    } 
    // Not near edges, stop scrolling
    else {
      setAutoScrolling(false);
      setScrollDirection(null);
    }
  };
  
        // stop auto-scrolling
  const stopAutoScroll = () => {
    if (autoScrollIntervalRef.current) {
      clearInterval(autoScrollIntervalRef.current);
      autoScrollIntervalRef.current = null;
    }
    setAutoScrolling(false);
    setScrollDirection(null);
  };
  
  // Modify drag-related functions to support auto-scrolling
  const handleDragOver = (e, index) => {
    e.preventDefault();
    e.stopPropagation();
    e.dataTransfer.dropEffect = 'move';
    setDragOverIndex(index);
    
    // Handle auto-scrolling
    handleAutoScroll(e);
    
    return false;
  };
  
  const handleDragLeave = () => {
    setDragOverIndex(null);
    
          // Don't stop auto-scrolling here, as it would affect edge scrolling functionality
      // Only stop when mouse leaves the entire design area
  };
  
  const handleDrop = (e, targetIndex) => {
    e.preventDefault();
    e.stopPropagation();
    
    // stop auto-scrolling
    stopAutoScroll();
    
    // add debug information
    console.log('Drop at index:', targetIndex);
    
    try {
      // Ensure target index is a valid number
      const validTargetIndex = typeof targetIndex === 'number' ? targetIndex : designAreaComponents.length;
      
      // Try to get the data from dataTransfer
      const data = e.dataTransfer.getData('text/plain');
      if (data) {
        const parsedData = JSON.parse(data);
        console.log('Dropped data:', parsedData);
        
        // If dropping from component palette
        if (parsedData.isFromPalette && !parsedData.index) {
          // Find the component in availableComponents
          const sourceComponent = availableComponents.find(comp => comp.id === parsedData.id);
          console.log('Source component:', sourceComponent);
          
          if (sourceComponent) {
                    // handle special cases for combined components
        if (sourceComponent.id === 'two-images') {
          // create two half-width image components
          const timestamp = Date.now();
          const leftImageComponent = {
            ...availableComponents.find(comp => comp.id === 'image'),
            id: `image-left-${timestamp}`,
            width: 'half',
            position: 'left',
            content: 'Left Image'
          };
          
          const rightImageComponent = {
            ...availableComponents.find(comp => comp.id === 'image'),
            id: `image-right-${timestamp}`,
            width: 'half',
            position: 'right',
            content: 'Right Image'
          };
          
          // insert these two components
          const updatedComponents = [...designAreaComponents];
          updatedComponents.splice(validTargetIndex, 0, leftImageComponent, rightImageComponent);
          setDesignAreaComponents(updatedComponents);
          console.log('Added two image components at position:', validTargetIndex);
        } else if (sourceComponent.id === 'image-text') {
          // create half-width image component and half-width text component
          const timestamp = Date.now();
          const leftImageComponent = {
            ...availableComponents.find(comp => comp.id === 'image'),
            id: `image-left-${timestamp}`,
            width: 'half',
            position: 'left',
            content: 'Image'
          };
          
          const rightTextComponent = {
            ...availableComponents.find(comp => comp.id === 'text'),
            id: `text-right-${timestamp}`,
            width: 'half',
            position: 'right',
            content: '<p>Your text content here. Click to edit.</p>'
          };
          
          // insert these two components
          const updatedComponents = [...designAreaComponents];
          updatedComponents.splice(validTargetIndex, 0, leftImageComponent, rightTextComponent);
          setDesignAreaComponents(updatedComponents);
          console.log('Added image & text components at position:', validTargetIndex);
        } else if (sourceComponent.id === 'text-image') {
          // create half-width text component and half-width image component
          const timestamp = Date.now();
          const leftTextComponent = {
            ...availableComponents.find(comp => comp.id === 'text'),
            id: `text-left-${timestamp}`,
            width: 'half',
            position: 'left',
            content: '<p>Your text content here. Click to edit.</p>'
          };
          
          const rightImageComponent = {
            ...availableComponents.find(comp => comp.id === 'image'),
            id: `image-right-${timestamp}`,
            width: 'half',
            position: 'right',
            content: 'Image'
          };
          
          // insert these two components
          const updatedComponents = [...designAreaComponents];
          updatedComponents.splice(validTargetIndex, 0, leftTextComponent, rightImageComponent);
          setDesignAreaComponents(updatedComponents);
          console.log('Added text & image components at position:', validTargetIndex);
            } else {
              // Handle regular components
              // Create a new instance of the component with a unique ID
              const newComponent = {
                ...sourceComponent,
                id: `${sourceComponent.id}-${Date.now()}`,
                width: 'full', // Default to full width
              };
              
              // If it's a social media buttons component, add default settings
              if (sourceComponent.id === 'social-buttons') {
                newComponent.socialButtonsSettings = {
                  theme: 'default',
                  buttons: [
                    { type: 'facebook', link: '', active: true },
                    { type: 'instagram', link: '', active: true },
                    { type: 'youtube', link: '', active: true },
                    { type: 'x', link: '', active: true },
                    { type: 'linkedin', link: '', active: true },
                    { type: 'tiktok', link: '', active: true },
                    { type: 'pinterest', link: '', active: true },
                    { type: 'yelp', link: '', active: true },
                    { type: 'threads', link: '', active: true }
                  ]
                };
              }
              
              // check if it's a drop onto the left drop zone of the half-width right component
              if (e.currentTarget.dataset.isLeftDropZone === 'true') {
                newComponent.width = 'half';
                newComponent.position = 'left';
              }
              
              // Insert at the target position
              const updatedComponents = [...designAreaComponents];
              updatedComponents.splice(validTargetIndex, 0, newComponent);
              setDesignAreaComponents(updatedComponents);
              console.log('Added component from palette:', newComponent, 'at position:', validTargetIndex);
            }
          }
        }
        // If reordering existing components
        else if (parsedData.index !== undefined) {
          const sourceIndex = parseInt(parsedData.index);
          
          // Don't do anything if dropping onto the same item
          if (sourceIndex === validTargetIndex || sourceIndex === validTargetIndex - 1) {
            return;
          }
          
          const updatedComponents = [...designAreaComponents];
          const [movedComponent] = updatedComponents.splice(sourceIndex, 1);
          
          // check if it's a drop onto the left drop zone of the half-width right component
          if (e.currentTarget.dataset.isLeftDropZone === 'true') {
            movedComponent.width = 'half';
            movedComponent.position = 'left';
          }
          
          // If target index is greater than source index, adjust it (since we've removed an element)
          const adjustedTargetIndex = sourceIndex < validTargetIndex ? validTargetIndex - 1 : validTargetIndex;
          updatedComponents.splice(adjustedTargetIndex, 0, movedComponent);
          
          setDesignAreaComponents(updatedComponents);
          console.log('Reordered component:', sourceIndex, 'to', adjustedTargetIndex);
        }
      }
      // Fallback to using the draggedComponent state if dataTransfer doesn't work
      else if (draggedComponent) {
        // handle special cases for combined components
        if (draggedComponent.id === 'two-images') {
          // create two half-width image components
          const timestamp = Date.now();
          const leftImageComponent = {
            ...availableComponents.find(comp => comp.id === 'image'),
            id: `image-left-${timestamp}`,
            width: 'half',
            position: 'left',
            content: 'Left Image'
          };
          
          const rightImageComponent = {
            ...availableComponents.find(comp => comp.id === 'image'),
            id: `image-right-${timestamp}`,
            width: 'half',
            position: 'right',
            content: 'Right Image'
          };
          
          // insert these two components
          const updatedComponents = [...designAreaComponents];
          updatedComponents.splice(validTargetIndex, 0, leftImageComponent, rightImageComponent);
          setDesignAreaComponents(updatedComponents);
          console.log('Added two image components at position:', validTargetIndex);
        } else if (draggedComponent.id === 'image-text') {
          // create half-width image component and half-width text component
          const timestamp = Date.now();
          const leftImageComponent = {
            ...availableComponents.find(comp => comp.id === 'image'),
            id: `image-left-${timestamp}`,
            width: 'half',
            position: 'left',
            content: 'Image'
          };
          
          const rightTextComponent = {
            ...availableComponents.find(comp => comp.id === 'text'),
            id: `text-right-${timestamp}`,
            width: 'half',
            position: 'right',
            content: '<p>Your text content here. Click to edit.</p>'
          };
          
          // insert these two components
          const updatedComponents = [...designAreaComponents];
          updatedComponents.splice(validTargetIndex, 0, leftImageComponent, rightTextComponent);
          setDesignAreaComponents(updatedComponents);
          console.log('Added image & text components at position:', validTargetIndex);
        } else if (draggedComponent.id === 'text-image') {
          // create half-width text component and half-width image component
          const timestamp = Date.now();
          const leftTextComponent = {
            ...availableComponents.find(comp => comp.id === 'text'),
            id: `text-left-${timestamp}`,
            width: 'half',
            position: 'left',
            content: '<p>Your text content here. Click to edit.</p>'
          };
          
          const rightImageComponent = {
            ...availableComponents.find(comp => comp.id === 'image'),
            id: `image-right-${timestamp}`,
            width: 'half',
            position: 'right',
            content: 'Image'
          };
          
          // insert these two components
          const updatedComponents = [...designAreaComponents];
          updatedComponents.splice(validTargetIndex, 0, leftTextComponent, rightImageComponent);
          setDesignAreaComponents(updatedComponents);
          console.log('Added text & image components at position:', validTargetIndex);
        } else {
          // handle regular components
          // Create a new instance of the component with a unique ID
          const newComponent = {
            ...draggedComponent,
            id: `${draggedComponent.id}-${Date.now()}`,
          };
          
          // if it's a social media buttons component, add default settings
          if (draggedComponent.id === 'social-buttons') {
            newComponent.socialButtonsSettings = {
              theme: 'default',
              buttons: [
                { type: 'facebook', link: '', active: true },
                { type: 'instagram', link: '', active: true },
                { type: 'youtube', link: '', active: true },
                { type: 'x', link: '', active: true },
                { type: 'linkedin', link: '', active: true },
                { type: 'tiktok', link: '', active: true },
                { type: 'pinterest', link: '', active: true },
                { type: 'yelp', link: '', active: true },
                { type: 'threads', link: '', active: true }
              ]
            };
          }
          
          // check if it's a drop onto the left drop zone of the half-width right component
          if (e.currentTarget.dataset.isLeftDropZone === 'true') {
            newComponent.width = 'half';
            newComponent.position = 'left';
          }
          
          // Insert at the target position
          const updatedComponents = [...designAreaComponents];
          updatedComponents.splice(validTargetIndex, 0, newComponent);
          setDesignAreaComponents(updatedComponents);
          console.log('Added component using state fallback:', newComponent, 'at position:', validTargetIndex);
        }
      }
    } catch (error) {
      console.error('Error in handleDrop:', error);
    }
    
    // Reset all drag-related states
    setDraggedComponent(null);
    setDragOverIndex(null);
    if (draggedItemRef.current) {
      draggedItemRef.current.style.display = 'block';
      draggedItemRef.current = null;
    }
  };
  
  const handleDragEnd = () => {
    if (draggedItemRef.current) {
      draggedItemRef.current.style.display = 'block';
      draggedItemRef.current = null;
    }
    setDraggedComponent(null);
    setDragOverIndex(null);
    
    // stop auto-scrolling
    stopAutoScroll();
  };
  
  // Component editing and deletion
  const handleEditComponent = (index) => {
    const component = designAreaComponents[index];
    setCurrentEditingIndex(index);
    setCurrentEditingContent(component.content || '');
    
    // Set component width
    setComponentWidth(component.width || 'full');
    
    // Set component position if available
    setComponentPosition(component.position || 'left');
    
    // get component type
    const componentId = component.id;
    // check if it's a social-buttons component
    const isSocialButtons = componentId.includes('social-buttons');
    const componentType = isSocialButtons ? 'social-buttons' :
                          componentId.startsWith('header-image') ? 'header-image' : 
                          componentId.startsWith('footer-image') ? 'footer-image' : 
                          componentId.split('-')[0];
    
    console.log('Component type:', componentType, 'Component ID:', componentId);
    
    // special handling for divider component
    if (componentType === 'divider') {
      // if the component has dividerStyle, use it; otherwise use default value
      if (component.dividerStyle) {
        setDividerStyle(component.dividerStyle);
      } else {
        setDividerStyle({
          borderColor: '#000000',
          borderWidth: '1px',
          borderStyle: 'solid',
          marginTop: '10px',
          marginBottom: '10px',
        });
      }
      
      setEditDialogOpen(true);
      setActiveEditTab('divider'); // Use special divider edit tab
      
      // Reset color picker states
      setShowBackgroundColorPicker(false);
      setShowBorderColorPicker(false);
      
        return; // return early, don't execute the subsequent regular component processing logic
    }
    
    // special handling for buttons component
    if (componentType === 'buttons') {
      // if the component has buttonSettings, use it; otherwise use default value
      if (component.buttonSettings) {
        setButtonSettings(component.buttonSettings);
      } else {
        setButtonSettings({
          buttons: [
            { 
              text: 'Button 1', 
              link: '', 
              color: '#000000', 
              textColor: '#ffffff',
              borderColor: '#000000',
              borderWidth: '0px',
              borderRadius: '20px',
              width: '150px',
              height: '40px',
              font: 'Comic Sans MS',
              fontSize: '14px',
              active: true
            }
          ]
        });
      }
      
      setCurrentEditingButton(0); // Default to editing the first button
      setEditDialogOpen(true);
      setActiveEditTab('button'); // Use special button edit tab
      
      // Reset color picker states
      setShowBackgroundColorPicker(false);
      setShowBorderColorPicker(false);
      
      return; // return early, don't execute the subsequent regular component processing logic
    }
    
    // special handling for social-buttons component
    if (componentType === 'social-buttons') {
      // if the component has socialButtonsSettings, use it; otherwise use default value
      if (component.socialButtonsSettings) {
        setSocialButtonsSettings(component.socialButtonsSettings);
      } else {
        setSocialButtonsSettings({
          theme: 'default',
          buttons: [
            { type: 'facebook', link: '', active: true },
            { type: 'instagram', link: '', active: true },
            { type: 'youtube', link: '', active: true },
            { type: 'x', link: '', active: true },
            { type: 'linkedin', link: '', active: true },
            { type: 'tiktok', link: '', active: true },
            { type: 'pinterest', link: '', active: true },
            { type: 'yelp', link: '', active: true },
            { type: 'threads', link: '', active: true }
          ]
        });
      }
      
      setEditDialogOpen(true);
      setActiveEditTab('social-buttons'); // Use special social-buttons edit tab
      
      // Reset color picker states
      setShowBackgroundColorPicker(false);
      setShowBorderColorPicker(false);
      
      return; // return early, don't execute the subsequent regular component processing logic
    }
    
    // Set component style
    if (component.style) {
      setComponentStyle(component.style);
      
      // Set text padding if available
      if (component.textPadding) {
        setTextPadding(component.textPadding);
      } else {
        setTextPadding({
          vertical: '10px',
          horizontal: '10px'
        });
      }
      
      // Set line height if available
      if (component.lineHeight) {
        setLineHeight(component.lineHeight);
      } else {
        setLineHeight('1.5'); // Default line height
      }
    } else {
      // Default style
      setComponentStyle({
        backgroundColor: '#ffffff',
        borderColor: '#000000',
        borderWidth: '0px',
        borderStyle: 'solid'
      });
      
      // Default text padding
      setTextPadding({
        vertical: '10px',
        horizontal: '10px'
      });
      
      setLineHeight('1.5'); // Default line height
    }
    
    // Set image settings if available
    let initialImageSettings;
    if (component.imageSettings) {
      initialImageSettings = component.imageSettings;
    } else {
      initialImageSettings = {
        src: component.imageSrc || '',
        rotation: 0,
        scale: 1,
        flipHorizontal: false,
        flipVertical: false,
        alt: 'Image'
      };
    }
    
    setImageSettings(initialImageSettings);
    
    // Reset history and redo stack
    setImageSettingsHistory([]);
    setImageSettingsRedoStack([]);
    
    // Set original settings to current settings
    if (initialImageSettings.src) {
      setOriginalImageSettings({...initialImageSettings});
    } else {
      setOriginalImageSettings(null);
    }
    
    setEditDialogOpen(true);
    
    // Set appropriate edit tab based on component type
    if (['header-image', 'image', 'footer-image'].includes(componentType)) {
      // Pure image components only show image and style tabs, default to image
      setActiveEditTab('image');
    } else if (['two-images', 'image-text', 'text-image', 'image-on-text'].includes(componentType)) {
      // Mixed components show all tabs, default to text
      setActiveEditTab('text');
    } else if (componentType === 'social-buttons') {
      // Social buttons component shows social-buttons tab
      setActiveEditTab('social-buttons');
    } else if (componentType === 'buttons') {
      // Buttons component shows button tab
      setActiveEditTab('button');
    } else if (componentType === 'divider') {
      // Divider component shows divider tab
      setActiveEditTab('divider');
    } else {
      // Text components only show text and style tabs, default to text
      setActiveEditTab('text');
    }
    
    // Reset color picker states
    setShowBackgroundColorPicker(false);
    setShowBorderColorPicker(false);
  };
  
  // Save edit content
  const handleSaveEditDialog = () => {
    const updatedComponents = [...designAreaComponents];
    const component = updatedComponents[currentEditingIndex];
    const componentId = component.id;
    const componentType = componentId.startsWith('header-image') ? 'header-image' : 
                          componentId.startsWith('footer-image') ? 'footer-image' : 
                          componentId.split('-')[0];
                          
    // special handling for divider component
    if (componentType === 'divider') {
      updatedComponents[currentEditingIndex] = {
        ...component,
        dividerStyle: dividerStyle,
        width: componentWidth, // save component width settingponent width setting
        position: componentWidth === 'half' ? componentPosition : null, // save position only when it's a half-width componentn only when it's a half-width component
      };
    } 
    // special handling for buttons component
    else if (componentType === 'buttons') {
      updatedComponents[currentEditingIndex] = {
        ...component,
        buttonSettings: buttonSettings,
        width: componentWidth, // save component width setting
        position: componentWidth === 'half' ? componentPosition : null, // save position only when it's a half-width component
      };
    } 
    // special handling for social-buttons component
    else if (componentType === 'social-buttons') {
      updatedComponents[currentEditingIndex] = {
        ...component,
        socialButtonsSettings: socialButtonsSettings,
        width: componentWidth, // save component width setting
        position
      };
    } else {
      updatedComponents[currentEditingIndex] = {
        ...component,
        content: currentEditingContent,
        style: componentStyle,
        lineHeight: lineHeight,
        textPadding: textPadding,
        imageSettings: imageSettings,
        width: componentWidth, // save component width setting
        position: componentWidth === 'half' ? componentPosition : null, // save position only when it's a half-width component
      };
      
      // if it's a picture-related component and has a picture source, update the picture
      if (['header-image', 'image', 'footer-image', 'two-images', 'image-text', 'text-image', 'image-on-text'].includes(componentType)) {
        // save picture source and settings
        updatedComponents[currentEditingIndex].imageSrc = imageSettings.src;
        
        // for pure picture components, update the content using alt text
        if (['header-image', 'image', 'footer-image'].includes(componentType)) {
          updatedComponents[currentEditingIndex].content = imageSettings.alt;
        }
      }
    }
    
    // Check if components need to be reordered
    if (componentWidth === 'half') {
      // Find the row that contains the current component
      const rows = [];
      let currentRow = [];
      let currentRowWidth = 0;
      let currentComponentRowIndex = -1;
      
      // iterate through all components, organize them by rows
      updatedComponents.forEach((comp, idx) => {
        const compWidth = comp.width || 'full';
        const widthValue = compWidth === 'half' ? 0.5 : 1;
        
        // if the current row is full or the component is full-width, start a new row
        if (currentRowWidth + widthValue > 1 || widthValue === 1) {
          // save the current row (if there are components)
          if (currentRow.length > 0) {
            rows.push([...currentRow]);
            currentRow = [];
            currentRowWidth = 0;
          }
        }
        
        // add component to the current row
        currentRow.push({ comp, idx });
        currentRowWidth += widthValue;
        
        // if the current component is the one we are editing, record its row index
        if (idx === currentEditingIndex) {
          currentComponentRowIndex = rows.length;
        }
        
        // if the current row is full, save and start a new row
        if (currentRowWidth >= 1) {
          rows.push([...currentRow]);
          currentRow = [];
          currentRowWidth = 0;
        }
      });
      
      // add the last row (if there are components)
      if (currentRow.length > 0) {
        rows.push([...currentRow]);
        // if the current component is in the last row, update the row index
        if (currentComponentRowIndex === -1) {
          currentComponentRowIndex = rows.length - 1;
        }
      }
      
      // if the current component's row has two half-width components, it may need to be reordered
      if (currentComponentRowIndex !== -1 && rows[currentComponentRowIndex].length === 2) {
        const currentRow = rows[currentComponentRowIndex];
        const firstCompIndex = currentRow[0].idx;
        const secondCompIndex = currentRow[1].idx;
        
        // determine the position of the current component in the row (first or second)
        const isFirstInRow = firstCompIndex === currentEditingIndex;
        
        // decide whether to swap based on position properties
        const firstComp = updatedComponents[firstCompIndex];
        const secondComp = updatedComponents[secondCompIndex];
        
        // if both are half-width components
        if (firstComp.width === 'half' && secondComp.width === 'half') {
          // if the first component should be on the right and the second component should be on the left, swap them
          if ((firstComp.position === 'right' && secondComp.position === 'left') ||
              (isFirstInRow && componentPosition === 'right' && secondComp.position !== 'right') ||
              (!isFirstInRow && componentPosition === 'left' && firstComp.position !== 'left')) {
            // swap components
            [updatedComponents[firstCompIndex], updatedComponents[secondCompIndex]] = 
            [updatedComponents[secondCompIndex], updatedComponents[firstCompIndex]];
            
            // if the current editing component is swapped, update currentEditingIndex
            if (currentEditingIndex === firstCompIndex) {
              setCurrentEditingIndex(secondCompIndex);
            } else if (currentEditingIndex === secondCompIndex) {
              setCurrentEditingIndex(firstCompIndex);
            }
          }
        }
      }
    }
    
    setDesignAreaComponents(updatedComponents);
    
    // Clear history when saving
    setImageSettingsHistory([]);
    setImageSettingsRedoStack([]);
    
    setEditDialogOpen(false);
    setCurrentEditingIndex(null);
    setCurrentEditingContent('');
  };
  
  // Close edit dialog
  const handleCloseEditDialog = () => {
    setEditDialogOpen(false);
    setCurrentEditingIndex(null);
    setCurrentEditingContent('');
  };
  
  const handleDeleteComponent = (index) => {
    const updatedComponents = [...designAreaComponents];
    updatedComponents.splice(index, 1);
    setDesignAreaComponents(updatedComponents);
  };

  const handleSaveForLater = async () => {
    setIsSubmitting(true);
    setSubmissionError(null);
    
    try {
      // Get campaign data from session storage
      if (!campaignData) {
        setSubmissionError('Campaign data not found. Please start from the campaign creation page.');
        return;
      }

      // Check for any remaining base64 images (should be rare now)
      console.log('🔍 [SAVE] Checking for any remaining base64 images...');
      console.log(`🔍 [SAVE] Total components to check: ${designAreaComponents.length}`);

      let processedComponents = [...designAreaComponents];
      let imageUploadCount = 0;

      // Find and upload any remaining base64 images in components
      for (let i = 0; i < processedComponents.length; i++) {
        const component = processedComponents[i];
        if (component.imageSrc && component.imageSrc.startsWith('data:image/')) {
          imageUploadCount++;
          console.warn(`⚠️ [SAVE] Found base64 image in component ${component.id} - this should not happen with new upload flow`);

          try {
            console.log(`📤 [SAVE] Uploading remaining base64 image ${imageUploadCount} for component ${component.id}...`);

            const uploadResult = await marketingApi.uploadEmailImage(component.imageSrc, `component_${component.id}_image`);
            if (uploadResult.success) {
              processedComponents[i] = {
                ...component,
                imageSrc: uploadResult.url
              };
              console.log(`✅ [SAVE] Remaining image ${imageUploadCount} uploaded successfully for component ${component.id}`);
              console.log(`✅ [SAVE] New URL: ${uploadResult.url}`);
            }
          } catch (error) {
            console.error(`❌ [SAVE] Failed to upload remaining image ${imageUploadCount} for component ${component.id}:`, error);
            // Continue with original base64 data if upload fails
          }
        } else if (component.imageSrc && component.imageSrc.startsWith('http')) {
          console.log(`✅ [SAVE] Component ${component.id} already uses URL: ${component.imageSrc.substring(0, 50)}...`);
        }
      }

      if (imageUploadCount === 0) {
        console.log(`✅ [SAVE] All images are already using URLs - no uploads needed!`);
      } else {
        console.log(`🔍 [SAVE] Processed ${imageUploadCount} remaining base64 images.`);
      }

      // Prepare design content for channels with both HTML and raw components
      console.log('📧 [SAVE] Generating email content with processed components...');
      const preparedChannels = campaignData.channels?.map(channel => {
        if (channel.channel_type === 'email') {
          const emailContent = generateEmailContent(processedComponents);
          console.log(`📧 [SAVE] Generated email content length: ${emailContent.length} characters`);

          // Check if content still contains base64 images (should not happen)
          const base64Pattern = /data:image\/[^;]+;base64,[^"'>\s]+/g;
          const base64Matches = emailContent.match(base64Pattern);
          if (base64Matches) {
            console.warn(`⚠️ [SAVE] Warning: Generated content still contains ${base64Matches.length} base64 images!`);
          } else {
            console.log('✅ [SAVE] Generated content contains no base64 images');
          }

          return {
            ...channel,
            content: emailContent,
            raw_components: processedComponents,
            subject: campaignData.campaignSubject || channel.subject || 'Email Campaign'
          };
        } else if (channel.channel_type === 'text') {
          return {
            ...channel,
            content: generateTextContent(processedComponents),
            subject: campaignData.campaignSubject || channel.subject || ''
          };
        }
        return channel;
      }) || [];
      
      // 添加调试日志
      console.log('=== DEBUG: Prepared Channels ===');
      console.log('Original campaignData.channels:', campaignData.channels);
      console.log('Prepared channels:', preparedChannels);
      preparedChannels.forEach((channel, index) => {
        console.log(`Channel ${index + 1} (${channel.channel_type}):`, {
          channel_type: channel.channel_type,
          subject: channel.subject,
          content_length: channel.content?.length || 0,
          content_preview: channel.content?.substring(0, 100) + '...'
        });
      });
      console.log('Design components count:', designAreaComponents.length);
      console.log('================================');
      
             // Calculate scheduled time based on campaign data
       let scheduledDateTime = new Date();
       
       if (campaignData.scheduleOption === 'scheduled' && campaignData.scheduleDate) {
         scheduledDateTime = new Date(campaignData.scheduleDate);
         if (campaignData.scheduleTime) {
           const [hours, minutes] = campaignData.scheduleTime.split(':');
           scheduledDateTime.setHours(parseInt(hours, 10), parseInt(minutes, 10));
         }
       }
       
       // Format the date according to the required format: YYYY-MM-DDThh:mm:ss+HHMM
       const tzOffset = scheduledDateTime.getTimezoneOffset();
       const tzOffsetHours = Math.abs(Math.floor(tzOffset / 60)).toString().padStart(2, '0');
       const tzOffsetMinutes = Math.abs(tzOffset % 60).toString().padStart(2, '0');
       const tzOffsetSign = tzOffset <= 0 ? '+' : '-';
       const tzOffsetFormatted = `${tzOffsetSign}${tzOffsetHours}${tzOffsetMinutes}`;
       
       const year = scheduledDateTime.getFullYear();
       const month = (scheduledDateTime.getMonth() + 1).toString().padStart(2, '0');
       const day = scheduledDateTime.getDate().toString().padStart(2, '0');
       const hours = scheduledDateTime.getHours().toString().padStart(2, '0');
       const minutes = scheduledDateTime.getMinutes().toString().padStart(2, '0');
       const seconds = scheduledDateTime.getSeconds().toString().padStart(2, '0');
       
       const formattedDateTime = `${year}-${month}-${day}T${hours}:${minutes}:${seconds}${tzOffsetFormatted}`;
       
       // Prepare complete campaign data with design
       const completeCampaignData = {
         name: campaignData.campaignSubject || 'Untitled Campaign',
         description: '',
         campaign_type: campaignData.campaignTypeId || getCampaignTypeId(campaignData.campaignType),
         scheduled_time: formattedDateTime,
         active: true,
         channels: preparedChannels,
         recipients: [
           ...campaignData.selectedCustomers?.map(customer => customer.id) || [],
           ...campaignData.selectedEmployees?.map(employee => employee.id) || []
         ],
         post_to_facebook: campaignData.postToFacebook || false,
         status: 'draft'
       };
       
       // 添加完整数据的调试日志
       console.log('=== DEBUG: Complete Campaign Data ===');
       console.log('Complete campaign data being sent:', completeCampaignData);
       console.log('Channels in complete data:', completeCampaignData.channels);
       console.log('=====================================');
      
      // Add campaign type specific settings
      if (campaignData.campaignType === 'birthday' && campaignData.daysBefore !== undefined) {
        completeCampaignData.offset_value = campaignData.daysBefore;
      } else if (campaignData.campaignType === 'lost-customer' && campaignData.weeksLost !== undefined) {
        completeCampaignData.offset_value = campaignData.weeksLost;
      } else if (campaignData.campaignType === 'before-visit' && campaignData.daysBeforeVisit !== undefined) {
        completeCampaignData.offset_value = campaignData.daysBeforeVisit;
      } else if (campaignData.campaignType === 'after-visit' && campaignData.daysAfterVisit !== undefined) {
        completeCampaignData.offset_value = campaignData.daysAfterVisit;
      }
      
      console.log('Saving campaign with design:', completeCampaignData);
      
      // Check if we're updating an existing campaign
      // Priority: 1. campaignId from sessionStorage, 2. campaignId from URL params
      let campaignId = campaignData.campaignId;
      if (!campaignId) {
        const params = new URLSearchParams(window.location.search);
        campaignId = params.get('id');
        if (campaignId) {
          campaignId = parseInt(campaignId, 10);
        }
      }
      
      console.log('Campaign ID for save operation:', campaignId);
      
      let result;
      if (campaignId) {
        // Update existing campaign
        result = await marketingApi.updateCampaign(campaignId, completeCampaignData);
        console.log('Campaign updated as draft:', result);
      } else {
        // Create new campaign
        result = await marketingApi.saveCampaignForLater(completeCampaignData);
        console.log('Campaign saved as draft:', result);
      }
      
      // Navigate to campaigns list with success message
      navigate('/marketing/campaigns', { 
        state: { 
          message: campaignId ? 'Campaign updated successfully as draft.' : 'Campaign saved successfully as draft.' 
        } 
      });
      
    } catch (error) {
      console.error('Error saving campaign:', error);
      
      // Extract detailed error information
      let errorMessage = 'Failed to save campaign. Please try again.';
      if (error.response && error.response.data) {
        const errorData = error.response.data;
        if (typeof errorData === 'object') {
          // Try to extract detailed information from the error object
          const errorDetails = Object.entries(errorData)
            .map(([field, errors]) => `${field}: ${Array.isArray(errors) ? errors.join(', ') : errors}`)
            .join('; ');
          
          if (errorDetails) {
            errorMessage = `Validation error: ${errorDetails}`;
          }
        } else if (typeof errorData === 'string') {
          errorMessage = errorData;
        }
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      setSubmissionError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  const handleBack = () => {
    // Go back to campaign creation, preserving campaign ID if in edit mode
    let campaignId = null;
    
    // Check sessionStorage first, then URL params
    if (campaignData?.campaignId) {
      campaignId = campaignData.campaignId;
    } else {
      const params = new URLSearchParams(window.location.search);
      const urlCampaignId = params.get('id');
      if (urlCampaignId) {
        campaignId = parseInt(urlCampaignId, 10);
      }
    }
    
    const backUrl = campaignId ? `/marketing/create-campaign?id=${campaignId}` : '/marketing/create-campaign';
    navigate(backUrl);
  };
  
  const handleSendPreview = () => {
    // only open the dialog if the campaign is saved
    setPreviewDialogOpen(true);
    setPreviewEmail('');
  };
  
  const handleSendPreviewConfirm = async () => {
    if (!previewEmail.trim()) {
      return; // Don't send if email is empty
    }
    
    setIsSendingPreview(true);
    
    try {
      // first save the current campaign
      console.log('Saving campaign before sending preview');
      await handleSaveForLater();
      
      // Get campaign ID
      let campaignId = campaignData?.campaignId;
      if (!campaignId) {
        const params = new URLSearchParams(window.location.search);
        campaignId = params.get('id');
      }
      
      if (!campaignId) {
        throw new Error('Campaign ID not found');
      }
      
      // Send preview email
      await marketingApi.sendCampaignPreview(
        campaignId,
        previewEmail.split(',').map(e => e.trim()).filter(Boolean) // 传递邮箱数组，去除空格和空字符串
      );
      
      // Close dialog after sending
      setPreviewDialogOpen(false);
      setIsSendingPreview(false);
      setPreviewEmail('');
      
      // Show success message
      setSubmissionError('Preview email sent successfully!');
      
      // Clear success message after 3 seconds
      setTimeout(() => {
        setSubmissionError(null);
      }, 3000);
    } catch (error) {
      console.error('Error sending preview:', error);
      setIsSendingPreview(false);
      
      // Extract error message
      let errorMessage = 'Failed to send preview email. Please try again.';
      if (error.response && error.response.data && error.response.data.error) {
        errorMessage = error.response.data.error;
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      setSubmissionError(errorMessage);
    }
  };
  
  const handlePreviewDialogClose = () => {
    setPreviewDialogOpen(false);
    setPreviewEmail('');
  };

  // Handle Gmail-compatible email preview
  const handleEmailPreview = (device = 'desktop') => {
    console.log(`Opening ${device} email preview...`);

    if (!designAreaComponents || designAreaComponents.length === 0) {
      setSubmissionError('No components in design area. Please add some components first.');
      setTimeout(() => setSubmissionError(null), 3000);
      return;
    }

    try {
      const htmlContent = generateEmailContent(designAreaComponents);
      console.log(`Generated ${device} HTML:`, htmlContent.length, 'characters');

      // Open in new window to preview
      const newWindow = window.open('', '_blank');
      if (newWindow) {
        newWindow.document.write(htmlContent);
        newWindow.document.close();

        // Set window title based on device
        newWindow.document.title = `Email Preview - ${device === 'mobile' ? 'Mobile' : 'Desktop'} View`;

        console.log(`✅ ${device} email preview opened successfully`);
      } else {
        setSubmissionError('Please allow popups to see the email preview.');
        setTimeout(() => setSubmissionError(null), 3000);
      }
    } catch (error) {
      console.error(`${device} preview generation failed:`, error);
      setSubmissionError(`Preview generation failed: ${error.message}`);
      setTimeout(() => setSubmissionError(null), 5000);
    }
  };


  
  const handleAnnounce = async () => {
    setIsSubmitting(true);
    setSubmissionError(null);
    
    try {
      // Get campaign data from session storage
      if (!campaignData) {
        setSubmissionError('Campaign data not found. Please start from the campaign creation page.');
        return;
      }
      
      // Prepare design content for channels with both HTML and raw components
      const preparedChannels = campaignData.channels?.map(channel => {
        if (channel.channel_type === 'email') {
          return {
            ...channel,
            content: generateEmailContent(designAreaComponents),
            raw_components: designAreaComponents,
            subject: campaignData.campaignSubject || channel.subject || 'Email Campaign'
          };
        } else if (channel.channel_type === 'text') {
          return {
            ...channel,
            content: generateTextContent(designAreaComponents),
            subject: campaignData.campaignSubject || channel.subject || ''
          };
        }
        return channel;
      }) || [];
      
      // 添加调试日志
      console.log('=== DEBUG: Announce - Prepared Channels ===');
      console.log('Original campaignData.channels:', campaignData.channels);
      console.log('Prepared channels:', preparedChannels);
      preparedChannels.forEach((channel, index) => {
        console.log(`Channel ${index + 1} (${channel.channel_type}):`, {
          channel_type: channel.channel_type,
          subject: channel.subject,
          content_length: channel.content?.length || 0,
          content_preview: channel.content?.substring(0, 100) + '...'
        });
      });
      console.log('Design components count:', designAreaComponents.length);
      console.log('===========================================');
      
      // Calculate scheduled time based on campaign data
      let scheduledDateTime = new Date();
      
      if (campaignData.scheduleOption === 'scheduled' && campaignData.scheduleDate) {
        scheduledDateTime = new Date(campaignData.scheduleDate);
        if (campaignData.scheduleTime) {
          const [hours, minutes] = campaignData.scheduleTime.split(':');
          scheduledDateTime.setHours(parseInt(hours, 10), parseInt(minutes, 10));
        }
      }
      
      // Format the date according to the required format: YYYY-MM-DDThh:mm:ss+HHMM
      const tzOffset = scheduledDateTime.getTimezoneOffset();
      const tzOffsetHours = Math.abs(Math.floor(tzOffset / 60)).toString().padStart(2, '0');
      const tzOffsetMinutes = Math.abs(tzOffset % 60).toString().padStart(2, '0');
      const tzOffsetSign = tzOffset <= 0 ? '+' : '-';
      const tzOffsetFormatted = `${tzOffsetSign}${tzOffsetHours}${tzOffsetMinutes}`;
      
      const year = scheduledDateTime.getFullYear();
      const month = (scheduledDateTime.getMonth() + 1).toString().padStart(2, '0');
      const day = scheduledDateTime.getDate().toString().padStart(2, '0');
      const hours = scheduledDateTime.getHours().toString().padStart(2, '0');
      const minutes = scheduledDateTime.getMinutes().toString().padStart(2, '0');
      const seconds = scheduledDateTime.getSeconds().toString().padStart(2, '0');
      
      const formattedDateTime = `${year}-${month}-${day}T${hours}:${minutes}:${seconds}${tzOffsetFormatted}`;
      
      // Prepare complete campaign data with design (set as scheduled status)
      const completeCampaignData = {
        name: campaignData.campaignSubject || 'Untitled Campaign',
        description: '',
        campaign_type: campaignData.campaignTypeId || getCampaignTypeId(campaignData.campaignType),
        scheduled_time: formattedDateTime,
        active: true,
        channels: preparedChannels,
        recipients: [
          ...campaignData.selectedCustomers?.map(customer => customer.id) || [],
          ...campaignData.selectedEmployees?.map(employee => employee.id) || []
        ],
        post_to_facebook: campaignData.postToFacebook || false,
        status: 'scheduled'
      };
      
      // Add campaign type specific settings
      if (campaignData.campaignType === 'birthday' && campaignData.daysBefore !== undefined) {
        completeCampaignData.offset_value = campaignData.daysBefore;
      } else if (campaignData.campaignType === 'lost-customer' && campaignData.weeksLost !== undefined) {
        completeCampaignData.offset_value = campaignData.weeksLost;
      } else if (campaignData.campaignType === 'before-visit' && campaignData.daysBeforeVisit !== undefined) {
        completeCampaignData.offset_value = campaignData.daysBeforeVisit;
      } else if (campaignData.campaignType === 'after-visit' && campaignData.daysAfterVisit !== undefined) {
        completeCampaignData.offset_value = campaignData.daysAfterVisit;
      }
      
      console.log('Announcing campaign with design:', completeCampaignData);
      
      // Check if we're updating an existing campaign
      // Priority: 1. campaignId from sessionStorage, 2. campaignId from URL params
      let campaignId = campaignData.campaignId;
      if (!campaignId) {
        const params = new URLSearchParams(window.location.search);
        campaignId = params.get('id');
        if (campaignId) {
          campaignId = parseInt(campaignId, 10);
        }
      }
      
      console.log('Campaign ID for announce operation:', campaignId);
      
      let result;
      if (campaignId) {
        // Update existing campaign and set as scheduled
        result = await marketingApi.updateCampaign(campaignId, completeCampaignData);
        console.log('Campaign updated and announced:', result);
      } else {
        // Create new campaign and announce it
        result = await marketingApi.announceCampaign(completeCampaignData);
        console.log('Campaign announced:', result);
      }
      
      // Navigate to campaigns list with success message
      navigate('/marketing/campaigns', { 
        state: { 
          message: campaignId ? 'Campaign updated and scheduled successfully!' : 'Campaign scheduled successfully!' 
        } 
      });
      
    } catch (error) {
      console.error('Error announcing campaign:', error);
      
      // Extract detailed error information
      let errorMessage = 'Failed to announce campaign. Please try again.';
      if (error.response && error.response.data) {
        const errorData = error.response.data;
        if (typeof errorData === 'object') {
          // Try to extract detailed information from the error object
          const errorDetails = Object.entries(errorData)
            .map(([field, errors]) => `${field}: ${Array.isArray(errors) ? errors.join(', ') : errors}`)
            .join('; ');
          
          if (errorDetails) {
            errorMessage = `Validation error: ${errorDetails}`;
          }
        } else if (typeof errorData === 'string') {
          errorMessage = errorData;
        }
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      setSubmissionError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Handle style changes
  const handleStyleChange = (property, value) => {
    setComponentStyle(prev => ({
      ...prev,
      [property]: value
    }));
  };
  
  // Save current state to history before making changes
  const saveToHistory = (currentSettings) => {
    setImageSettingsHistory(prev => [...prev, currentSettings]);
    // Clear redo stack when a new change is made
    setImageSettingsRedoStack([]);
  };

  // Handle image upload
  const handleImageUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    console.log('🖼️ [IMAGE UPLOAD] Starting image upload for component...');

    // Show loading state
    const loadingSettings = {
      src: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkxvYWRpbmcuLi48L3RleHQ+PC9zdmc+',
      alt: 'Loading...',
      rotation: 0,
      scale: 1,
      flipHorizontal: false,
      flipVertical: false,
      isLoading: true
    };

    setImageSettings(loadingSettings);
    setOriginalImageSettings(loadingSettings);

    try {
      // Convert file to base64 for upload
      const reader = new FileReader();
      reader.onload = async (event) => {
        try {
          console.log('🖼️ [IMAGE UPLOAD] Converting to base64 and uploading...');

          // Upload image to server
          const uploadResult = await marketingApi.uploadEmailImage(
            event.target.result,
            `component_image_${Date.now()}_${file.name}`
          );

          if (uploadResult.success) {
            console.log('✅ [IMAGE UPLOAD] Upload successful:', uploadResult.url);

            const newSettings = {
              src: uploadResult.url, // Use server URL instead of base64
              alt: file.name,
              rotation: 0,
              scale: 1,
              flipHorizontal: false,
              flipVertical: false,
              isLoading: false,
              uploadedUrl: uploadResult.url,
              originalFileName: file.name
            };

            // Set new image and reset history
            setImageSettings(newSettings);
            setOriginalImageSettings(newSettings);
            setImageSettingsHistory([]);
            setImageSettingsRedoStack([]);

          } else {
            console.error('❌ [IMAGE UPLOAD] Upload failed:', uploadResult.error);
            // Fallback to base64 if upload fails
            const fallbackSettings = {
              src: event.target.result,
              alt: file.name,
              rotation: 0,
              scale: 1,
              flipHorizontal: false,
              flipVertical: false,
              isLoading: false,
              uploadFailed: true
            };

            setImageSettings(fallbackSettings);
            setOriginalImageSettings(fallbackSettings);
            setImageSettingsHistory([]);
            setImageSettingsRedoStack([]);
          }

        } catch (error) {
          console.error('❌ [IMAGE UPLOAD] Upload error:', error);
          // Fallback to base64 if upload fails
          const fallbackSettings = {
            src: event.target.result,
            alt: file.name,
            rotation: 0,
            scale: 1,
            flipHorizontal: false,
            flipVertical: false,
            isLoading: false,
            uploadFailed: true
          };

          setImageSettings(fallbackSettings);
          setOriginalImageSettings(fallbackSettings);
          setImageSettingsHistory([]);
          setImageSettingsRedoStack([]);
        }
      };

      reader.readAsDataURL(file);

    } catch (error) {
      console.error('❌ [IMAGE UPLOAD] File reading error:', error);

      // Reset to empty state on error
      setImageSettings(null);
      setOriginalImageSettings(null);
      setImageSettingsHistory([]);
      setImageSettingsRedoStack([]);
    }
  };

  // Handle image rotation
  const handleImageRotate = (direction) => {
    // Only save history when we have original settings
    if (originalImageSettings) {
      // Save current state to history
      saveToHistory({...imageSettings});
    }
    
    setImageSettings(prev => ({
      ...prev,
      rotation: prev.rotation + (direction === 'left' ? -90 : 90)
    }));
  };

  // Handle image flip
  const handleImageFlip = (axis) => {
    // Only save history when we have original settings
    if (originalImageSettings) {
      // Save current state to history
      saveToHistory({...imageSettings});
    }
    
    if (axis === 'horizontal') {
      setImageSettings(prev => ({
        ...prev,
        flipHorizontal: !prev.flipHorizontal
      }));
    } else {
      setImageSettings(prev => ({
        ...prev,
        flipVertical: !prev.flipVertical
      }));
    }
  };

  // Handle image scale
  const handleImageScale = (value) => {
    // For scale, we don't want to save history on every small change
    // We'll save the current state when the user starts dragging
    setImageSettings(prev => ({
      ...prev,
      scale: parseFloat(value)
    }));
  };
  
  // Save scale history before changing
  const handleScaleStart = () => {
    // Only save history when we have original settings
    if (originalImageSettings) {
      saveToHistory({...imageSettings});
    }
  };

  // Handle image delete
  const handleImageDelete = () => {
    // Reset all states
    setImageSettings({
      src: '',
      rotation: 0,
      scale: 1,
      flipHorizontal: false,
      flipVertical: false,
      alt: 'Image'
    });
    setOriginalImageSettings(null);
    setImageSettingsHistory([]);
    setImageSettingsRedoStack([]);
  };
  
  // Handle undo
  const handleUndo = () => {
    if (imageSettingsHistory.length === 0) return;
    
    // Get the last state from history
    const lastState = imageSettingsHistory[imageSettingsHistory.length - 1];
    
    // Add current state to redo stack
    setImageSettingsRedoStack(prev => [...prev, {...imageSettings}]);
    
    // Set image settings to the previous state
    setImageSettings(lastState);
    
    // Remove the last state from history
    setImageSettingsHistory(prev => prev.slice(0, -1));
  };
  
  // Handle redo
  const handleRedo = () => {
    if (imageSettingsRedoStack.length === 0) return;
    
    // Get the last state from redo stack
    const nextState = imageSettingsRedoStack[imageSettingsRedoStack.length - 1];
    
    // Add current state to history
    setImageSettingsHistory(prev => [...prev, {...imageSettings}]);
    
    // Set image settings to the next state
    setImageSettings(nextState);
    
    // Remove the last state from redo stack
    setImageSettingsRedoStack(prev => prev.slice(0, -1));
  };
  
  // Handle reset to original
  const handleReset = () => {
    // Only reset when we have original settings and history
    if (!originalImageSettings || imageSettingsHistory.length === 0) return;
    
    // Clear all history after reset
    setImageSettingsHistory([]);
    setImageSettingsRedoStack([]);
    
    // Reset to original settings
    setImageSettings({...originalImageSettings});
  };

  // Trigger file input click
  const triggerFileInput = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };
  

  // MJML Email Content Generator - Gmail Compatible
  // Helper function to generate email content from design components using MJML
  const generateEmailContent = (components) => {
    if (!components || components.length === 0) {
      return '<div>No content designed yet.</div>';
    }

    // We'll use our browser-compatible MJML-style generator instead of actual MJML

    // Process components and group them into rows for MJML
    let currentRow = [];
    let rows = [];

    // Group components into rows based on width
    components.forEach(component => {
      const componentWidth = component.width || 'full';

      if (componentWidth === 'full') {
        // If there's a current row, finish it first
        if (currentRow.length > 0) {
          rows.push([...currentRow]);
          currentRow = [];
        }
        // Add full-width component as its own row
        rows.push([component]);
      } else {
        // Add half-width component to current row
        currentRow.push(component);

        // If current row has two half-width components, finish the row
        if (currentRow.length === 2) {
          rows.push([...currentRow]);
          currentRow = [];
        }
      }
    });

    // Add any remaining components in the current row
    if (currentRow.length > 0) {
      rows.push([...currentRow]);
    }

    // This section is no longer used since we're using the browser-compatible generator
    // The actual processing happens in generateMJMLStyleEmailContent function

    try {
      // Since MJML has browser compatibility issues, we'll use our enhanced HTML generator
      // that follows MJML principles but works in the browser
      return generateMJMLStyleEmailContent(components);
    } catch (error) {
      console.error('Email generation error:', error);
      // Fallback to simple HTML if generation fails
      return generateFallbackEmailContent(components);
    }
  };

  // MJML-Style HTML Generator (Browser Compatible)
  // This function generates Gmail-compatible HTML following MJML principles
  const generateMJMLStyleEmailContent = (components) => {
    if (!components || components.length === 0) {
      return '<div>No content designed yet.</div>';
    }

    // Gmail-compatible HTML structure with inline styles
    let htmlContent = `
      <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
      <html xmlns="http://www.w3.org/1999/xhtml">
      <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Email Campaign</title>
        <!--[if !mso]><!-->
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <!--<![endif]-->
        <style type="text/css">
          /* Gmail-compatible CSS reset */
          body, table, td, p, a, li, blockquote { -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; }
          table, td { border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; }
          img { border: 0; height: auto; line-height: 100%; outline: none; text-decoration: none; -ms-interpolation-mode: bicubic; }
          p { display: block; margin: 13px 0; }

          /* Mobile styles */
          @media only screen and (max-width: 480px) {
            .mobile-full-width { width: 100% !important; }
            .mobile-center { text-align: center !important; }
          }
        </style>
      </head>
      <body style="margin: 0; padding: 0; background-color: #ffffff; font-family: Arial, sans-serif;">
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="background-color: #ffffff;">
          <tr>
            <td align="center" style="padding: 0;">
              <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="600" style="max-width: 600px; background-color: #ffffff;" class="mobile-full-width">
                <tr>
                  <td style="padding: 0;">
    `;

    // Process components and group them into rows
    let currentRow = [];
    let rows = [];

    components.forEach(component => {
      const componentWidth = component.width || 'full';

      if (componentWidth === 'full') {
        if (currentRow.length > 0) {
          rows.push([...currentRow]);
          currentRow = [];
        }
        rows.push([component]);
      } else {
        currentRow.push(component);
        if (currentRow.length === 2) {
          rows.push([...currentRow]);
          currentRow = [];
        }
      }
    });

    if (currentRow.length > 0) {
      rows.push([...currentRow]);
    }

    // Generate Gmail-compatible HTML for each row
    rows.forEach(row => {
      if (row.length === 1 && row[0].width !== 'half') {
        // Full-width component
        htmlContent += `<table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">`;
        htmlContent += `<tr>`;
        htmlContent += generateSimpleComponent(row[0], '100%');
        htmlContent += `</tr></table>`;
      } else {
        // Half-width components
        htmlContent += `<table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">`;
        htmlContent += `<tr>`;
        row.forEach(component => {
          htmlContent += generateSimpleComponent(component, '50%');
        });
        htmlContent += `</tr></table>`;
      }
    });

    // Close Gmail-compatible HTML structure
    htmlContent += `
                  </td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
      </body>
      </html>
    `;

    return htmlContent;
  };

  // Helper function to convert Quill alignment classes to inline styles
  const convertQuillAlignmentToInlineStyles = (htmlContent) => {
    if (!htmlContent) return htmlContent;

    let processedContent = htmlContent;

    // 1. 处理字体大小 - 将Quill的size-large/small类转换为内联样式
    processedContent = processedContent
      .replace(/class="ql-size-small"/g, 'style="font-size: 0.75em;"')
      .replace(/class="ql-size-large"/g, 'style="font-size: 1.5em;"')
      .replace(/class="ql-size-huge"/g, 'style="font-size: 2.5em;"');

    // 处理已有style的元素的字体大小
    processedContent = processedContent
      .replace(/style="([^"]*)" class="ql-size-small"/g, 'style="$1 font-size: 0.75em;"')
      .replace(/style="([^"]*)" class="ql-size-large"/g, 'style="$1 font-size: 1.5em;"')
      .replace(/style="([^"]*)" class="ql-size-huge"/g, 'style="$1 font-size: 2.5em;"');

    // 2. 处理对齐方式 - 将Quill的align类转换为内联样式
    processedContent = processedContent
      .replace(/class="ql-align-center"/g, 'style="text-align: center;"')
      .replace(/class="ql-align-right"/g, 'style="text-align: right;"')
      .replace(/class="ql-align-justify"/g, 'style="text-align: justify;"')
      .replace(/class="ql-align-left"/g, 'style="text-align: left;"');

    // 处理已有style的元素的对齐方式
    processedContent = processedContent
      .replace(/style="([^"]*)" class="ql-align-center"/g, 'style="$1 text-align: center;"')
      .replace(/style="([^"]*)" class="ql-align-right"/g, 'style="$1 text-align: right;"')
      .replace(/style="([^"]*)" class="ql-align-justify"/g, 'style="$1 text-align: justify;"')
      .replace(/style="([^"]*)" class="ql-align-left"/g, 'style="$1 text-align: left;"');

    // 3. 处理字体颜色 - 将Quill的color类转换为内联样式
    const colorRegex = /class="ql-color-([a-zA-Z0-9#]+)"/g;
    processedContent = processedContent.replace(colorRegex, (match, color) => {
      return `style="color: #${color};"`;
    });

    // 处理已有style的元素的颜色
    const colorWithStyleRegex = /style="([^"]*)" class="ql-color-([a-zA-Z0-9#]+)"/g;
    processedContent = processedContent.replace(colorWithStyleRegex, (match, style, color) => {
      return `style="${style} color: #${color};"`;
    });

    // 4. 处理背景颜色 - 将Quill的background类转换为内联样式
    const bgRegex = /class="ql-background-([a-zA-Z0-9#]+)"/g;
    processedContent = processedContent.replace(bgRegex, (match, color) => {
      return `style="background-color: #${color};"`;
    });

    // 处理已有style的元素的背景颜色
    const bgWithStyleRegex = /style="([^"]*)" class="ql-background-([a-zA-Z0-9#]+)"/g;
    processedContent = processedContent.replace(bgWithStyleRegex, (match, style, color) => {
      return `style="${style} background-color: #${color};"`;
    });

    // 5. 移除所有剩余的Quill类
    processedContent = processedContent
      .replace(/class="[^"]*ql-[^"]*"/g, '')
      .replace(/class=""/g, '');

    return processedContent;
  };

  // Helper function to generate Gmail-compatible component HTML
  const generateSimpleComponent = (component, width) => {
    const componentType = component.id.split('-')[0];
    let componentHtml = '';

    console.log('Processing component:', component.id, 'Type:', componentType, 'Content:', component.content);

    // Gmail-compatible table cell with inline styles
    componentHtml += `<td width="${width}" style="vertical-align: top; padding: 0; margin: 0;" class="mobile-full-width">`;

    switch (componentType) {
      case 'heading':
      case 'text':
        if (component.content) {
          const backgroundColor = component.style?.backgroundColor || 'transparent';
          const borderColor = component.style?.borderColor || 'transparent';
          const borderWidth = component.style?.borderWidth || '0px';
          const borderStyle = component.style?.borderStyle || 'solid';
          const padding = component.textPadding ?
            `${component.textPadding.vertical} ${component.textPadding.horizontal}` : '10px';
          const lineHeight = component.lineHeight || '1.5';

          const borderCss = borderWidth !== '0px' ? `${borderWidth} ${borderStyle} ${borderColor}` : 'none';

          // Convert Quill alignment classes to inline styles for Gmail compatibility
          const processedContent = convertQuillAlignmentToInlineStyles(component.content);

          componentHtml += `
            <div style="background-color:${backgroundColor}; border:${borderCss}; padding:${padding}; line-height:${lineHeight}; font-family:Arial, sans-serif; margin:0; box-sizing:border-box; width:100%; height:100%;">
              ${processedContent}
            </div>
          `;
        }
        break;

      case 'header':
      case 'image':
      case 'footer':
        if (component.imageSrc) {
          const alt = component.imageSettings?.alt || 'Image';
          const width = component.imageSettings?.width || '';
          const height = component.imageSettings?.height || '';

          componentHtml += `
            <div style="text-align:center; margin:10px 0;">
              <img src="${component.imageSrc}"
                   alt="${alt}"
                   style="max-width:100%; height:auto; display:block; margin:0 auto; border:0; outline:none; text-decoration:none;"
                   ${width ? `width="${width}"` : ''}
                   ${height ? `height="${height}"` : ''}
                   border="0" />
            </div>
          `;
        }
        break;

      case 'divider':
        const divStyle = component.dividerStyle || {
          borderColor: '#000000',
          borderWidth: '1px',
          borderStyle: 'solid',
          marginTop: '10px',
          marginBottom: '10px'
        };
        componentHtml += `
          <hr style="border:none; border-top:${divStyle.borderWidth} ${divStyle.borderStyle} ${divStyle.borderColor}; margin:${divStyle.marginTop} 0 ${divStyle.marginBottom} 0;" />
        `;
        break;

      case 'buttons':
        if (component.buttonSettings?.buttons) {
          const activeButtons = component.buttonSettings.buttons.filter(btn => btn.active !== false);
          if (activeButtons.length > 0) {
            componentHtml += `<div style="text-align:center; margin:10px 0;">`;
            activeButtons.forEach(button => {
              componentHtml += `
                <a href="${button.link || '#'}" style="display:inline-block; background-color:${button.color}; color:${button.textColor}; text-decoration:none; padding:10px 20px; margin:5px; border-radius:${button.borderRadius}; border:${button.borderWidth} solid ${button.borderColor}; font-family:${button.font}; font-size:${button.fontSize};" target="_blank">
                  ${button.text}
                </a>
              `;
            });
            componentHtml += `</div>`;
          }
        }
        break;

      case 'social':
        if (component.socialButtonsSettings?.buttons) {
          const activeSocial = component.socialButtonsSettings.buttons.filter(btn => btn.active && btn.link);
          if (activeSocial.length > 0) {
            componentHtml += `<div style="text-align:center; margin:10px 0;">`;
            activeSocial.forEach(social => {
              componentHtml += `
                <a href="${social.link}" style="display:inline-block; margin:0 10px; text-decoration:none; color:#333333; font-size:14px;" target="_blank">
                  ${social.type}
                </a>
              `;
            });
            componentHtml += `</div>`;
          }
        }
        break;
    }

    // Close table cell
    componentHtml += `</td>`;

    return componentHtml;
  };

  // Fallback email content generator (simple HTML for when MJML fails)
  const generateFallbackEmailContent = (components) => {
    let htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Email Template</title>
      </head>
      <body style="margin: 0; padding: 0; font-family: Arial, sans-serif;">
        <div style="max-width: 600px; margin: 0 auto;">
    `;

    components.forEach(component => {
      const componentType = component.id.split('-')[0];

      switch (componentType) {
        case 'heading':
        case 'text':
          if (component.content) {
            htmlContent += `<div style="padding: 10px;">${component.content}</div>`;
          }
          break;
        case 'header':
        case 'image':
        case 'footer':
          if (component.imageSrc) {
            const alt = component.imageSettings?.alt || 'Image';
            const width = component.imageSettings?.width || '';
            const height = component.imageSettings?.height || '';

            htmlContent += `<div style="text-align: center; padding: 10px;">
              <img src="${component.imageSrc}"
                   alt="${alt}"
                   style="max-width: 100%; height: auto; border: 0; outline: none; text-decoration: none;"
                   ${width ? `width="${width}"` : ''}
                   ${height ? `height="${height}"` : ''}
                   border="0" />
            </div>`;
          }
          break;
        case 'divider':
          htmlContent += '<hr style="border: none; border-top: 1px solid #000; margin: 10px 0;" />';
          break;
        case 'buttons':
          if (component.buttonSettings?.buttons) {
            const activeButtons = component.buttonSettings.buttons.filter(btn => btn.active !== false);
            if (activeButtons.length > 0) {
              htmlContent += '<div style="text-align: center; padding: 10px;">';
              activeButtons.forEach(button => {
                htmlContent += `<a href="${button.link || '#'}" style="display: inline-block; background-color: ${button.color}; color: ${button.textColor}; text-decoration: none; padding: 12px 24px; margin: 5px; border-radius: ${button.borderRadius};">${button.text}</a>`;
              });
              htmlContent += '</div>';
            }
          }
          break;
      }
    });

    htmlContent += `
        </div>
      </body>
      </html>
    `;

    return htmlContent;
  };

  // // New Email Content Generator. GPT
  // function sanitizeQuillHTML(content) {
  //   if (!content) return '';
  
  //   // 1. 用 DOMParser 解析 HTML 字符串（浏览器环境下可用）
  //   const parser = new DOMParser();
  //   const doc = parser.parseFromString(content, 'text/html');
  
  //   // 2. 替换 ql-align-* 类为内联样式
  //   const alignments = {
  //     'ql-align-center': 'center',
  //     'ql-align-right': 'right',
  //     'ql-align-left': 'left',
  //   };
  
  //   Object.keys(alignments).forEach(cls => {
  //     const elems = doc.querySelectorAll(`.${cls}`);
  //     elems.forEach(el => {
  //       // 添加内联样式 text-align
  //       const currentStyle = el.getAttribute('style') || '';
  //       el.setAttribute('style', `text-align: ${alignments[cls]}; ${currentStyle}`);
  //       // 移除类名
  //       el.classList.remove(cls);
  //       // 如果没有其他类，可以移除class属性
  //       if (el.classList.length === 0) el.removeAttribute('class');
  //     });
  //   });
  
  //   // 3. 过滤标签和属性：仅允许白名单标签和属性
  //   const allowedTags = ['p', 'br', 'b', 'i', 'u', 'ul', 'ol', 'li', 'strong', 'em', 'span', 'div'];
  //   const allowedAttrs = ['style'];
  
  //   // 遍历所有元素，移除不允许的标签或属性
  //   const allElements = doc.body.querySelectorAll('*');
  //   allElements.forEach(el => {
  //     // 标签过滤，不在白名单的替换为文本节点（保留文本内容）
  //     if (!allowedTags.includes(el.tagName.toLowerCase())) {
  //       const textNode = document.createTextNode(el.textContent);
  //       el.parentNode.replaceChild(textNode, el);
  //       return;
  //     }
  
  //     // 属性过滤，移除不允许的属性
  //     [...el.attributes].forEach(attr => {
  //       if (!allowedAttrs.includes(attr.name)) {
  //         el.removeAttribute(attr.name);
  //       }
  //     });
  //   });
  
  //   // 4. 返回处理后的HTML字符串（body内容）
  //   return doc.body.innerHTML;
  // }
  

  // const generateEmailContent = (components) => {
  //   if (!components || components.length === 0) {
  //     return '<div>No content designed yet.</div>';
  //   }
  
  //   let htmlContent = `
  //     <!DOCTYPE html>
  //     <html>
  //     <head>
  //       <meta charset="utf-8">
  //       <title>Email</title>
  //       <meta name="viewport" content="width=device-width, initial-scale=1.0">
  //     </head>
  //     <body style="margin:0;padding:0;font-family:Arial,sans-serif;background-color:#ffffff;">
  //       <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%" style="max-width:600px;margin:auto;">
  //   `;
  
  //   let row = [];
  
  //   const flushRow = () => {
  //     if (row.length === 0) return;
  //     htmlContent += `<tr>`;
  //     row.forEach(component => {
  //       htmlContent += `<td width="50%" style="padding:10px;vertical-align:top;">${renderComponent(component)}</td>`;
  //     });
  //     if (row.length === 1) {
  //       htmlContent += `<td width="50%" style="padding:10px;"></td>`; // 补全右侧
  //     }
  //     htmlContent += `</tr>`;
  //     row = [];
  //   };
  
  //   const renderComponent = (component) => {
  //     const {
  //       content = '',
  //       imageSrc,
  //       style = {},
  //       textPadding = { vertical: '10px', horizontal: '10px' },
  //       lineHeight = '1.5',
  //       dividerStyle = {},
  //       imageSettings = {},
  //       buttonSettings,
  //       socialButtonsSettings,
  //     } = component;
  
  //     const blockStyle = `
  //       background-color: ${style.backgroundColor || 'transparent'};
  //       border: ${style.borderWidth || '0px'} ${style.borderStyle || 'solid'} ${style.borderColor || 'transparent'};
  //       padding: ${textPadding.vertical} ${textPadding.horizontal};
  //       font-size: 14px;
  //       line-height: ${lineHeight};
  //       color: #000000;
  //       font-family: Arial, sans-serif;
  //     `.trim();
  
  //     const sanitizeContent = sanitizeQuillHTML(content); // ✨ 关键：修复 <p class="...">
  
  //     const renderButtons = () => {
  //       return (buttonSettings?.buttons || []).filter(btn => btn.active).map(btn => {
  //         return `
  //           <a href="${btn.link || '#'}" style="
  //             display: inline-block;
  //             background-color: ${btn.color || '#007BFF'};
  //             color: ${btn.textColor || '#ffffff'};
  //             padding: 10px 20px;
  //             text-decoration: none;
  //             font-family: ${btn.font || 'Arial'};
  //             font-size: ${btn.fontSize || '14px'};
  //             border: ${btn.borderWidth || '0px'} solid ${btn.borderColor || 'transparent'};
  //             border-radius: ${btn.borderRadius || '4px'};
  //             width: ${btn.width || 'auto'};
  //             height: ${btn.height || 'auto'};
  //             text-align: center;
  //             margin: 5px 0;
  //           ">${btn.text}</a>
  //         `;
  //       }).join('');
  //     };
  
  //     const renderSocialIcons = () => {
  //       return (socialButtonsSettings?.buttons || []).filter(btn => btn.active).map(btn => {
  //         const iconAlt = btn.type[0].toUpperCase() + btn.type.slice(1);
  //         const iconUrl = `https://dummyimage.com/24x24/ccc/000&text=${btn.type[0].toUpperCase()}`;
  //         return `
  //           <a href="${btn.link || '#'}" style="margin-right:10px;display:inline-block;">
  //             <img src="${iconUrl}" alt="${iconAlt}" width="24" height="24" style="display:block;" />
  //           </a>
  //         `;
  //       }).join('');
  //     };
  
  //     switch (component.id.split('-')[0]) {
  //       case 'text':
  //       case 'heading':
  //         return `<div style="${blockStyle}">${sanitizeContent}</div>`;
  
  //       case 'image':
  //       case 'header':
  //       case 'footer':
  //         return imageSrc
  //           ? `<img src="${imageSrc}" alt="${imageSettings.alt || 'Image'}" style="display:block;width:100%;max-width:100%;height:auto;" />`
  //           : '';
  
  //       case 'divider':
  //         return `<hr style="
  //           border:none;
  //           border-top:${dividerStyle.borderWidth || '1px'} ${dividerStyle.borderStyle || 'solid'} ${dividerStyle.borderColor || '#000'};
  //           margin-top:${dividerStyle.marginTop || '10px'};
  //           margin-bottom:${dividerStyle.marginBottom || '10px'};
  //         " />`;
  
  //       case 'buttons':
  //         return `<div style="${blockStyle}">${renderButtons()}</div>`;
  
  //       case 'social':
  //         return `<div style="${blockStyle}">${renderSocialIcons()}</div>`;
  
  //       default:
  //         return `<div style="${blockStyle}">${sanitizeContent}</div>`;
  //     }
  //   };
  
  //   for (const component of components) {
  //     if (component.width === 'half') {
  //       row.push(component);
  //       if (row.length === 2) flushRow();
  //     } else {
  //       flushRow();
  //       htmlContent += `<tr><td colspan="2" style="padding:10px;">${renderComponent(component)}</td></tr>`;
  //     }
  //   }
  //   flushRow();
  
  //   htmlContent += `
  //       </table>
  //     </body>
  //     </html>
  //   `;
  
  //   return htmlContent;
  // };
  
  
  // Helper function to generate text content from design components
  const generateTextContent = (components) => {
    if (!components || components.length === 0) {
      return 'No content designed yet.';
    }
    
    let textContent = '';
    
    components.forEach(component => {
      const componentType = component.id.split('-')[0];
      
      switch (componentType) {
        case 'heading':
        case 'text':
          if (component.content) {
            // Strip HTML tags and convert to plain text
            const textOnly = component.content.replace(/<[^>]*>/g, '').trim();
            if (textOnly) {
              textContent += textOnly + '\n\n';
            }
          }
          break;
          
        case 'buttons':
          if (component.buttonSettings?.buttons) {
            const activeButtons = component.buttonSettings.buttons.filter(btn => btn.active !== false);
            activeButtons.forEach(button => {
              if (button.link) {
                textContent += `${button.text}: ${button.link}\n`;
              } else {
                textContent += `${button.text}\n`;
              }
            });
            textContent += '\n';
          }
          break;
          
        case 'social':
          if (component.socialButtonsSettings?.buttons) {
            const activeSocial = component.socialButtonsSettings.buttons.filter(btn => btn.active && btn.link);
            if (activeSocial.length > 0) {
              textContent += 'Follow us:\n';
              activeSocial.forEach(social => {
                textContent += `${social.type}: ${social.link}\n`;
              });
              textContent += '\n';
            }
          }
          break;
      }
    });
    
    return textContent.trim();
  };
  
  // Component rendering functions
  const renderComponentContent = (component) => {
    // get component type
    const componentId = component.id;
    // check if it's a social-buttons component
    const isSocialButtons = componentId.includes('social-buttons');
    const componentType = isSocialButtons ? 'social-buttons' :
                          componentId.startsWith('header-image') ? 'header-image' : 
                          componentId.startsWith('footer-image') ? 'footer-image' : 
                          componentId.split('-')[0];
    
    // Special handling for divider component, it doesn't need content
    if (componentType === 'divider') {
      // Use dividerStyle if it exists; otherwise use default style
      const divStyle = component.dividerStyle || {
        borderColor: '#000000',
        borderWidth: '1px',
        borderStyle: 'solid',
        marginTop: '10px',
        marginBottom: '10px',
      };
      
             return (
         <div className="relative w-full py-2">
           <hr className="w-full" style={{
             borderWidth: '0',
             borderTopWidth: divStyle.borderWidth,
             borderStyle: divStyle.borderStyle,
             borderColor: divStyle.borderColor,
             marginTop: divStyle.marginTop,
             marginBottom: divStyle.marginBottom,
           }} />
         </div>
       );
    }
    
    // For other components, check if content exists
    if (!component.content) return 'No content';
    
    // For rich text content, use dangerouslySetInnerHTML
    const renderHtml = (html) => ({ __html: html });
    
    // Apply style
    const style = {};
    
    // Apply component style
    if (component.style) {
      Object.assign(style, {
        backgroundColor: component.style.backgroundColor,
        borderColor: component.style.borderColor,
        borderWidth: component.style.borderWidth,
        borderStyle: component.style.borderStyle
      });
    }
    
    // Apply text padding if available
    if (component.textPadding) {
      style.padding = `${component.textPadding.vertical} ${component.textPadding.horizontal}`;
    }
    
    // Apply line height if available
    if (component.lineHeight) {
      style.lineHeight = component.lineHeight;
    }
    
    // add debug information
    console.log('Rendering component:', component.id, componentType);
    
    // add drag handle
    const dragHandle = (
      <div className="absolute top-2 left-2 z-30 cursor-move bg-blue-500 text-white p-1 rounded-md opacity-70 hover:opacity-100">
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8h16M4 16h16" />
        </svg>
      </div>
    );
    
    // Handle image components
    if (['header-image', 'image', 'footer-image'].includes(componentType)) {
      if (component.imageSrc) {
        // Check for loading or error states
        const isLoading = component.imageSettings?.isLoading;
        const uploadFailed = component.imageSettings?.uploadFailed;
        const isBase64 = component.imageSrc.startsWith('data:image/');

        // Apply image transformations
        const imageTransforms = [];
        if (component.imageSettings?.rotation) {
          imageTransforms.push(`rotate(${component.imageSettings.rotation}deg)`);
        }
        if (component.imageSettings?.flipHorizontal) {
          imageTransforms.push('scaleX(-1)');
        }
        if (component.imageSettings?.flipVertical) {
          imageTransforms.push('scaleY(-1)');
        }
        if (component.imageSettings?.scale && component.imageSettings.scale !== 1) {
          imageTransforms.push(`scale(${component.imageSettings.scale})`);
        }
        
        const imageStyle = {
          ...style,
          height: '200px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          overflow: 'hidden'
        };
        
        // special handling for header-image and footer-image
        if (componentType === 'header-image' || componentType === 'footer-image') {
          // no longer return the full div, but return the content part
          return (
            <div className="w-full rounded-md relative" style={{
              ...style,
              padding: '0', // remove padding
              overflow: 'hidden'
            }}>
              <img
                src={component.imageSrc}
                alt={component.content || 'Image'}
                style={{
                  width: '100%', // fill the container horizontally
                  display: 'block', // ensure no extra space
                  margin: '0 auto', // center display
                  transform: imageTransforms.join(' '),
                  opacity: isLoading ? 0.5 : 1
                }}
              />

              {/* Loading overlay */}
              {isLoading && (
                <div className="absolute inset-0 bg-gray-100 bg-opacity-75 flex items-center justify-center">
                  <div className="text-sm text-gray-600">Uploading...</div>
                </div>
              )}

              {/* Upload failed indicator */}
              {uploadFailed && (
                <div className="absolute top-2 right-2 bg-red-500 text-white text-xs px-2 py-1 rounded">
                  Upload Failed
                </div>
              )}

              {/* Base64 warning */}
              {isBase64 && !isLoading && (
                <div className="absolute top-2 right-2 bg-yellow-500 text-white text-xs px-2 py-1 rounded">
                  Base64
                </div>
              )}
            </div>
          );
        }
        
        // regular picture component with status indicators
        return (
          <div className="w-full rounded-md relative" style={imageStyle}>
            <img
              src={component.imageSrc}
              alt={component.content || 'Image'}
              style={{
                maxWidth: '100%',
                maxHeight: '100%',
                transform: imageTransforms.join(' '),
                opacity: isLoading ? 0.5 : 1
              }}
            />

            {/* Loading overlay */}
            {isLoading && (
              <div className="absolute inset-0 bg-gray-100 bg-opacity-75 flex items-center justify-center">
                <div className="text-sm text-gray-600">Uploading...</div>
              </div>
            )}

            {/* Upload failed indicator */}
            {uploadFailed && (
              <div className="absolute top-2 right-2 bg-red-500 text-white text-xs px-2 py-1 rounded">
                Upload Failed
              </div>
            )}

            {/* Base64 warning */}
            {isBase64 && !isLoading && (
              <div className="absolute top-2 right-2 bg-yellow-500 text-white text-xs px-2 py-1 rounded">
                Base64
              </div>
            )}
          </div>
        );
      } else {
        // special handling for header-image and footer-image placeholders
        if (componentType === 'header-image' || componentType === 'footer-image') {
          return (
            <div className="w-full bg-gray-200 rounded-md flex items-center justify-center relative" style={{...style, height: '250px', padding: '0'}}>
              <span className="text-gray-500 text-lg font-medium">{componentType === 'header-image' ? 'Header Image Placeholder' : 'Footer Image Placeholder'}</span>
            </div>
          );
        }
        
        // regular picture placeholder without drag handle
        return (
          <div className="w-full bg-gray-200 rounded-md flex items-center justify-center relative" style={{...style, height: '200px'}}>
            <span className="text-gray-500">{component.content || 'Image Placeholder'}</span>
          </div>
        );
      }
    }
    
    // Handle divider is now at the top of the function
        
    // Handle remaining component types with switch
    switch (componentType) {
      case 'heading':
      case 'text':
          // explicitly set padding value
        const textPaddingValue = component.textPadding 
          ? `${component.textPadding.vertical} ${component.textPadding.horizontal}`
          : '10px';
        return <div className="rich-text-content ql-editor" style={{...style, padding: textPaddingValue}} dangerouslySetInnerHTML={renderHtml(component.content)} />;
        
      case 'two-images':
        // this case won't be executed because the two-images component will be converted to two separate image components
        return (
          <div className="w-full flex space-x-4" style={style}>
            <div className="w-1/2 bg-gray-200 rounded-md flex items-center justify-center" style={{height: '150px'}}>
              <span className="text-gray-500">Image 1</span>
            </div>
            <div className="w-1/2 bg-gray-200 rounded-md flex items-center justify-center" style={{height: '150px'}}>
              <span className="text-gray-500">Image 2</span>
            </div>
          </div>
        );
        
      case 'image-text':
        // this case won't be executed because the image-text component will be converted to a half-width image component and a half-width text component
        return (
          <div className="w-full flex space-x-4" style={style}>
            <div className="w-1/2 bg-gray-200 rounded-md flex items-center justify-center overflow-hidden" style={{height: '150px'}}>
              {component.imageSrc ? (
                <img 
                  src={component.imageSrc} 
                  alt={component.imageSettings?.alt || 'Image'} 
                  className="max-w-full max-h-full"
                  style={{
                    transform: component.imageSettings ? `
                      rotate(${component.imageSettings.rotation || 0}deg)
                      ${component.imageSettings.flipHorizontal ? 'scaleX(-1)' : ''}
                      ${component.imageSettings.flipVertical ? 'scaleY(-1)' : ''}
                      scale(${component.imageSettings.scale || 1})
                    ` : 'none'
                  }}
                />
              ) : (
                <span className="text-gray-500">Image</span>
              )}
            </div>
            <div className="w-1/2 flex items-center">
              <div className="rich-text-content ql-editor" style={{
                lineHeight: component.lineHeight,
                padding: component.textPadding ? `${component.textPadding.vertical} ${component.textPadding.horizontal}` : '10px'
              }} dangerouslySetInnerHTML={renderHtml(component.content)} />
            </div>
          </div>
        );
        
      case 'text-image':
        // this case won't be executed because the text-image component will be converted to a half-width text component and a half-width image component
        return (
          <div className="w-full flex space-x-4" style={style}>
            <div className="w-1/2 flex items-center">
              <div className="rich-text-content ql-editor" style={{
                lineHeight: component.lineHeight,
                padding: component.textPadding ? `${component.textPadding.vertical} ${component.textPadding.horizontal}` : '10px'
              }} dangerouslySetInnerHTML={renderHtml(component.content)} />
            </div>
            <div className="w-1/2 bg-gray-200 rounded-md flex items-center justify-center overflow-hidden" style={{height: '150px'}}>
              {component.imageSrc ? (
                <img 
                  src={component.imageSrc} 
                  alt={component.imageSettings?.alt || 'Image'} 
                  className="max-w-full max-h-full"
                  style={{
                    transform: component.imageSettings ? `
                      rotate(${component.imageSettings.rotation || 0}deg)
                      ${component.imageSettings.flipHorizontal ? 'scaleX(-1)' : ''}
                      ${component.imageSettings.flipVertical ? 'scaleY(-1)' : ''}
                      scale(${component.imageSettings.scale || 1})
                    ` : 'none'
                  }}
                />
              ) : (
                <span className="text-gray-500">Image</span>
              )}
            </div>
          </div>
        );
        
      case 'image-on-text':
        return (
          <div className="w-full relative" style={style}>
            <div className="w-full bg-gray-200 rounded-md flex items-center justify-center overflow-hidden" style={{height: '200px'}}>
              {component.imageSrc ? (
                <img 
                  src={component.imageSrc} 
                  alt={component.imageSettings?.alt || 'Image'} 
                  className="w-full h-full object-cover"
                  style={{
                    transform: component.imageSettings ? `
                      rotate(${component.imageSettings.rotation || 0}deg)
                      ${component.imageSettings.flipHorizontal ? 'scaleX(-1)' : ''}
                      ${component.imageSettings.flipVertical ? 'scaleY(-1)' : ''}
                      scale(${component.imageSettings.scale || 1})
                    ` : 'none'
                  }}
                />
              ) : (
                <span className="text-gray-500">Image</span>
              )}
            </div>
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="bg-white bg-opacity-75 p-4 rounded-md">
                <div className="rich-text-content ql-editor" style={{
                  lineHeight: component.lineHeight,
                  padding: component.textPadding ? `${component.textPadding.vertical} ${component.textPadding.horizontal}` : '10px'
                }} dangerouslySetInnerHTML={renderHtml(component.content)} />
              </div>
            </div>
          </div>
        );
        
      case 'buttons':
        // use buttonSettings if it exists; otherwise use default value
        const buttonsData = component.buttonSettings?.buttons || [
          { 
            text: 'Button 1', 
            link: '', 
            color: '#000000', 
            textColor: '#ffffff',
            borderColor: '#000000',
            borderWidth: '0px',
            borderRadius: '20px',
            width: '150px',
            height: '40px',
            font: 'Comic Sans MS',
            fontSize: '14px',
            active: true
          }
        ];
        
        // filter out active buttons
        const activeButtonsList = buttonsData.filter(button => button.active !== false);
        
        // determine layout style based on button count
        const layoutClass = activeButtonsList.length === 1 
          ? "justify-center" 
          : activeButtonsList.length === 2 
            ? "justify-center gap-8" 
            : "justify-center gap-4";
        
        return (
          <div className={`w-full flex flex-wrap ${layoutClass} py-4`} style={style}>
            {activeButtonsList.map((button, index) => {
              const buttonStyle = {
                backgroundColor: button.color,
                color: button.textColor,
                borderColor: button.borderColor,
                borderWidth: button.borderWidth,
                borderStyle: button.borderWidth !== '0px' ? 'solid' : 'none',
                borderRadius: button.borderRadius,
                width: button.width,
                height: button.height,
                fontFamily: button.font,
                fontSize: button.fontSize,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                textDecoration: 'none',
                cursor: 'pointer'
              };
              
              return button.link ? (
                <a 
                  key={index}
                  href={button.link}
                  target="_blank"
                  rel="noopener noreferrer"
                  style={buttonStyle}
                  className="no-underline"
                >
                  {button.text}
                </a>
              ) : (
                <button 
                  key={index}
                  style={buttonStyle}
                >
                  {button.text}
                </button>
              );
            })}
          </div>
        );
        
      case 'social-buttons':
        // use socialButtonsSettings if it exists; otherwise use default value
        const socialSettings = component.socialButtonsSettings || {
          theme: 'default',
          buttons: [
            { type: 'facebook', link: '', active: true },
            { type: 'instagram', link: '', active: true },
            { type: 'youtube', link: '', active: true },
            { type: 'x', link: '', active: true },
            { type: 'linkedin', link: '', active: true },
            { type: 'tiktok', link: '', active: true },
            { type: 'pinterest', link: '', active: true },
            { type: 'yelp', link: '', active: true },
            { type: 'threads', link: '', active: true }
          ]
        };
        
        // filter out active buttons
        const activeSocialButtons = socialSettings.buttons.filter(button => button.active);
        
        // social media icon and color mapping
        const socialIcons = {
          facebook: {
            icon: <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"/></svg>,
            color: '#1877F2',
            textColor: '#ffffff'
          },
          instagram: {
            icon: <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/></svg>,
            color: '#E4405F',
            textColor: '#ffffff'
          },
          youtube: {
            icon: <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z"/></svg>,
            color: '#FF0000',
            textColor: '#ffffff'
          },
          x: {
            icon: <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/></svg>,
            color: '#000000',
            textColor: '#ffffff'
          },
          linkedin: {
            icon: <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M4.98 3.5c0 1.381-1.11 2.5-2.48 2.5s-2.48-1.119-2.48-2.5c0-1.38 1.11-2.5 2.48-2.5s2.48 1.12 2.48 2.5zm.02 4.5h-5v16h5v-16zm7.982 0h-4.968v16h4.969v-8.399c0-4.67 6.029-5.052 6.029 0v8.399h4.988v-10.131c0-7.88-8.922-7.593-11.018-3.714v-2.155z"/></svg>,
            color: '#0A66C2',
            textColor: '#ffffff'
          },
          tiktok: {
            icon: <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/></svg>,
            color: '#000000',
            textColor: '#ffffff'
          },
          pinterest: {
            icon: <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M12 0c-6.627 0-12 5.372-12 12 0 5.084 3.163 9.426 7.627 11.174-.105-.949-.2-2.405.042-3.441.218-.937 1.407-5.965 1.407-5.965s-.359-.719-.359-1.782c0-1.668.967-2.914 2.171-2.914 1.023 0 1.518.769 1.518 1.69 0 1.029-.655 2.568-.994 3.995-.283 1.194.599 2.169 1.777 2.169 2.133 0 3.772-2.249 3.772-5.495 0-2.873-2.064-4.882-5.012-4.882-3.414 0-5.418 2.561-5.418 5.207 0 1.031.397 2.138.893 2.738.098.119.112.224.083.345l-.333 1.36c-.053.22-.174.267-.402.161-1.499-.698-2.436-2.889-2.436-4.649 0-3.785 2.75-7.262 7.929-7.262 4.163 0 7.398 2.967 7.398 6.931 0 4.136-2.607 7.464-6.227 7.464-1.216 0-2.359-.631-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146 1.124.347 2.317.535 3.554.535 6.627 0 12-5.373 12-12 0-6.628-5.373-12-12-12z"/></svg>,
            color: '#E60023',
            textColor: '#ffffff'
          },
          yelp: {
            icon: <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M20.16 12.594l-4.995 1.433c-.96.276-1.74-.8-1.176-1.63l3.206-4.712c.703-1.035 2.28-.506 2.29.768l.003 3.24c-.004.61-.45 1.046-1.327.9zm-7.421 3.979l-4.67-2.054c-.898-.394-.844-1.666.1-1.962l5.378-1.67c.96-.3 1.83.616 1.32 1.385l-1.12 1.817c-.293.475-.813.738-1.008.484zm.518-5.325l-5.347-1.77c-.967-.32-.717-1.735.358-1.886l5.81-.82c.966-.137 1.687.96 1.09 1.665l-1.275 1.887c-.274.407-.676.944-1.636.925zm-1.127 8.473l-4.155 2.87c-.83.574-1.85-.177-1.483-1.09l2.048-5.12c.352-.878 1.58-.732 1.85.22l.692 2.44c.13.454-.055 1.06-.952.68zm11.54-11.47l-4.308-2.895c-.929-.625-.376-2.055.755-1.957l5.61.5c.952.084 1.566 1.233.797 1.892l-1.695 1.75c-.387.402-.963.456-1.16.71z"/></svg>,
            color: '#FF1A1A',
            textColor: '#ffffff'
          },
          threads: {
            icon: <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M12.186 24h-.007c-3.581-.024-6.334-1.205-8.184-3.509C2.35 18.44 1.5 15.587 1.5 12.037c0-3.55.851-6.403 2.495-8.454C5.852 1.284 8.606.102 12.18.076h.014c2.058 0 4.343.478 6.4 2.536 2.058 2.058 2.52 4.28 2.52 6.4 0 2.136-.477 4.358-2.535 6.415-2.057 2.058-4.343 2.536-6.4 2.536h-.014c-.786 0-1.557-.087-2.292-.26-1.67-.392-2.815-1.252-3.403-2.55-.58-1.279-.501-2.786.229-4.654a.506.506 0 01.626-.319c.24.07.38.32.31.56-.635 1.63-.7 2.881-.25 3.873.452.999 1.368 1.654 2.725 1.953.683.16 1.4.24 2.133.24h.007c1.85-.018 3.885-.467 5.724-2.307 1.84-1.839 2.29-3.874 2.307-5.724.018-1.85-.431-3.886-2.27-5.726-1.84-1.84-3.874-2.289-5.724-2.307-3.33.024-5.848 1.09-7.514 3.181-1.467 1.84-2.21 4.45-2.21 7.767 0 3.318.743 5.928 2.21 7.767 1.666 2.09 4.184 3.157 7.514 3.181 1.87-.018 3.8-.479 5.562-2.31a.507.507 0 01.718.715c-1.975 2.046-4.144 2.57-6.28 2.595z"/></svg>,
            color: '#000000',
            textColor: '#ffffff'
          }
        };
        
        // set style based on theme
        const getButtonStyle = (type) => {
          const socialInfo = socialIcons[type];
          let buttonStyle = {
            width: '40px',
            height: '40px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            borderRadius: '50%',
            color: socialInfo.textColor,
            backgroundColor: socialInfo.color,
            cursor: 'pointer',
            transition: 'all 0.3s ease'
          };
          
          // add different styles based on theme
          if (socialSettings.theme === 'outline') {
            buttonStyle = {
              ...buttonStyle,
              backgroundColor: 'transparent',
              color: socialInfo.color,
              border: `2px solid ${socialInfo.color}`
            };
          } else if (socialSettings.theme === 'simple') {
            buttonStyle = {
              ...buttonStyle,
              backgroundColor: 'transparent',
              color: socialInfo.color,
              border: 'none'
            };
          }
          
          return buttonStyle;
        };
        
        return (
          <div className="w-full flex justify-center flex-wrap gap-4 py-4" style={style}>
            {activeSocialButtons.map((button, index) => {
              const socialInfo = socialIcons[button.type];
              if (!socialInfo) return null;
              
              return (
                <a
                  key={index}
                  href={button.link || '#'}
                  target="_blank"
                  rel="noopener noreferrer"
                  style={getButtonStyle(button.type)}
                  className="hover:opacity-80"
                  title={button.type.charAt(0).toUpperCase() + button.type.slice(1)}
                >
                  {socialInfo.icon}
                </a>
              );
            })}
          </div>
        );
        
      default:
        // explicitly set padding value
        const defaultTextPadding = component.textPadding 
          ? `${component.textPadding.vertical} ${component.textPadding.horizontal}`
          : '10px';
        return <div className="rich-text-content ql-editor" style={{...style, padding: defaultTextPadding}} dangerouslySetInnerHTML={renderHtml(component.content)} />;
    }
  };
  
  const renderDesignTab = () => {
    // add debug information
    console.log('Rendering design tab with components:', designAreaComponents);
    
    return (
    <div 
      className="flex-1 bg-gray-100 overflow-y-auto relative" 
      ref={designAreaRef}
      onMouseLeave={() => {
        if (draggedComponent) {
          // stop auto-scrolling only during drag
          stopAutoScroll();
        }
      }}
    >
      {/* auto-scrolling indicator */}
      {autoScrolling && scrollDirection === 'up' && (
        <div className="absolute top-0 left-0 right-0 h-16 bg-gradient-to-b from-blue-300/50 to-transparent z-10 pointer-events-none flex items-center justify-center">
          <div className="bg-blue-500 text-white px-3 py-1 rounded-full text-sm">Scroll Up</div>
        </div>
      )}
      {autoScrolling && scrollDirection === 'down' && (
        <div className="absolute bottom-0 left-0 right-0 h-16 bg-gradient-to-t from-blue-300/50 to-transparent z-10 pointer-events-none flex items-end justify-center pb-2">
          <div className="bg-blue-500 text-white px-3 py-1 rounded-full text-sm">Scroll Down</div>
        </div>
      )}
      <div className="max-w-3xl mx-auto bg-white shadow-md h-full">
        {designAreaComponents.length === 0 ? (
          <div 
            className={`border-2 border-dashed ${dragOverIndex === 0 ? 'border-blue-500 bg-blue-50' : 'border-gray-300'} min-h-[600px] flex items-center justify-center w-full transition-colors duration-200`}
            onDragOver={(e) => {
              e.preventDefault();
              e.stopPropagation();
              handleDragOver(e, 0);
            }}
            onDragLeave={(e) => {
              e.preventDefault();
              e.stopPropagation();
              handleDragLeave();
            }}
            onDrop={(e) => {
              e.preventDefault();
              e.stopPropagation();
              handleDrop(e, 0);
              setDragOverIndex(null);
            }}
            data-testid="empty-drop-area"
          >
            <div className="text-center text-gray-500 w-full">
              <p className="mb-2">Drag components from the left sidebar to build your design</p>
            </div>
          </div>
        ) : (
          <div className="min-h-[600px] w-full">
            {/* handle component rendering, support half-width components side by side */}
            {(() => {
              // create an array to store components
              const rows = [];
              let currentRow = [];
              let currentRowWidth = 0;
              
              // iterate through all components, organize them by rows
              designAreaComponents.forEach((component, index) => {
                const componentWidth = component.width || 'full';
                const widthValue = componentWidth === 'half' ? 0.5 : 1;
                
                // if the current row is full or the component is full-width, start a new row
                if (currentRowWidth + widthValue > 1 || widthValue === 1) {
                  // save the current row (if there are components)
                  if (currentRow.length > 0) {
                    rows.push([...currentRow]);
                    currentRow = [];
                    currentRowWidth = 0;
                  }
                }
                
                // add component to the current row
                currentRow.push({ component, index });
                currentRowWidth += widthValue;
                
                // if the current row is full, save and start a new row
                if (currentRowWidth >= 1) {
                  rows.push([...currentRow]);
                  currentRow = [];
                  currentRowWidth = 0;
                }
              });
              
              // add the last row (if there are components)
              if (currentRow.length > 0) {
                rows.push([...currentRow]);
              }
              
              // render all rows
              return rows.map((row, rowIndex) => (
                <div key={`row-${rowIndex}`} className="flex w-full">
                  {/* sort components in the row based on position properties */}
                  {(() => {
                    // if the row has two half-width components, sort them based on position properties
                    if (row.length === 2 && 
                        row[0].component.width === 'half' && 
                        row[1].component.width === 'half') {
                      
                        // copy the row to avoid modifying the original data
                      const sortedRow = [...row];
                      
                      // if the first component should be on the right and the second component should be on the left, swap them
                      if (sortedRow[0].component.position === 'right' && 
                          sortedRow[1].component.position === 'left') {
                        [sortedRow[0], sortedRow[1]] = [sortedRow[1], sortedRow[0]];
                      }
                      
                      return sortedRow.map(({ component, index }, colIndex) => (
                        <div 
                          key={component.id}
                          className={`group border-b border-gray-200 relative ${dragOverIndex === index ? 'border-t-2 border-t-blue-500' : ''} ${
                            component.width === 'half' ? 'w-1/2' : 'w-full'
                          } ${
                            component.width === 'half' && colIndex === 0 ? 'pr-1' : ''
                          } ${
                            component.width === 'half' && colIndex === 1 ? 'pl-1' : ''
                          }`}
                          draggable="true"
                          data-index={index}
                          ref={index === parseInt(draggedItemRef.current?.dataset.index) ? draggedItemRef : null}
                          onDragStart={(e) => {
                            e.stopPropagation();
                            draggedItemRef.current = e.currentTarget;
                            try {
                              const data = JSON.stringify({
                                id: component.id,
                                index: index,
                                isFromPalette: false
                              });
                              e.dataTransfer.setData('text/plain', data);
                            } catch (err) {
                              console.error('Error setting drag data:', err);
                            }
                            handleDragStart(e, component);
                          }}
                          onDragEnd={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleDragEnd();
                          }}
                          onDragOver={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleDragOver(e, index);
                          }}
                          onDrop={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleDrop(e, index);
                          }}
                        >
                          {/* Action buttons - only visible on hover */}
                          <div className="absolute top-2 right-2 hidden group-hover:flex space-x-2 z-20">
                            <button 
                              onClick={() => handleEditComponent(index)}
                              className="p-1 text-white bg-blue-500 rounded-md hover:bg-blue-600 flex items-center opacity-90"
                              title="Edit"
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                              </svg>
                            </button>
                            <button 
                              onClick={() => handleDeleteComponent(index)}
                              className="p-1 text-white bg-red-500 rounded-md hover:bg-red-600 flex items-center opacity-90"
                              title="Delete"
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                              </svg>
                            </button>
                          </div>
                          
                          <div className="w-full">
                            {renderComponentContent(component)}
                          </div>
                          
                          {/* Drop zone after each component */}
                          <div 
                            className={`h-8 w-full absolute -bottom-4 left-0 z-10 ${dragOverIndex === index + 1 ? 'bg-blue-200' : ''} transition-colors duration-200`}
                            onDragOver={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              handleDragOver(e, index + 1);
                            }}
                            onDragLeave={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              handleDragLeave();
                            }}
                            onDrop={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              handleDrop(e, index + 1);
                              setDragOverIndex(null);
                            }}
                          />
                        </div>
                      ));
                    } else if (row.length === 1 && row[0].component.width === 'half' && row[0].component.position === 'right') {
                      // handle the case of a single half-width right component, need to add a drop zone on the left
                      const { component, index } = row[0];
                      
                      return (
                        <>
                          {/* left drop zone */}
                          <div 
                            className={`w-1/2 pr-1 border-b border-gray-200 relative ${
                              dragOverIndex === 'left-' + index ? 'border-2 border-dashed border-blue-500 bg-blue-50' : 'border-2 border-dashed border-gray-300'
                            } min-h-[100px] flex items-center justify-center transition-colors duration-200`}
                            onDragOver={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              setDragOverIndex('left-' + index);
                            }}
                            onDragLeave={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              setDragOverIndex(null);
                            }}
                            onDrop={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              // set a custom attribute to mark this as a left drop zone
                              e.currentTarget.dataset.isLeftDropZone = 'true';
                              handleDrop(e, index);
                              setDragOverIndex(null);
                            }}
                            data-is-left-drop-zone="true"
                          >
                            <div className="text-center text-gray-500">
                              <p className="text-sm">Drop component here</p>
                              <p className="text-xs">(Will be half width on left)</p>
                            </div>
                          </div>
                          
                          {/* right component */}
                          <div 
                            key={component.id}
                            className={`group border-b border-gray-200 relative ${dragOverIndex === index ? 'border-t-2 border-t-blue-500' : ''} w-1/2 pl-1`}
                            draggable="true"
                            data-index={index}
                            ref={index === parseInt(draggedItemRef.current?.dataset.index) ? draggedItemRef : null}
                            onDragStart={(e) => {
                              e.stopPropagation();
                              draggedItemRef.current = e.currentTarget;
                              try {
                                const data = JSON.stringify({
                                  id: component.id,
                                  index: index,
                                  isFromPalette: false
                                });
                                e.dataTransfer.setData('text/plain', data);
                              } catch (err) {
                                console.error('Error setting drag data:', err);
                              }
                              handleDragStart(e, component);
                            }}
                            onDragEnd={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              handleDragEnd();
                            }}
                            onDragOver={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              handleDragOver(e, index);
                            }}
                            onDrop={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              handleDrop(e, index);
                            }}
                          >
                            {/* Action buttons - only visible on hover */}
                            <div className="absolute top-2 right-2 hidden group-hover:flex space-x-2 z-20">
                              <button 
                                onClick={() => handleEditComponent(index)}
                                className="p-1 text-white bg-blue-500 rounded-md hover:bg-blue-600 flex items-center opacity-90"
                                title="Edit"
                              >
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                </svg>
                              </button>
                              <button 
                                onClick={() => handleDeleteComponent(index)}
                                className="p-1 text-white bg-red-500 rounded-md hover:bg-red-600 flex items-center opacity-90"
                                title="Delete"
                              >
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                              </button>
                            </div>
                            
                            <div className="w-full">
                              {renderComponentContent(component)}
                            </div>
                            
                            {/* Drop zone after each component */}
                            <div 
                              className={`h-8 w-full absolute -bottom-4 left-0 z-10 ${dragOverIndex === index + 1 ? 'bg-blue-200' : ''} transition-colors duration-200`}
                              onDragOver={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                handleDragOver(e, index + 1);
                              }}
                              onDragLeave={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                handleDragLeave();
                              }}
                              onDrop={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                handleDrop(e, index + 1);
                                setDragOverIndex(null);
                              }}
                            />
                          </div>
                        </>
                      );
                    } else {
                      // if it's not the case of two half-width components, render as is
                      return row.map(({ component, index }, colIndex) => (
                        <div 
                          key={component.id}
                          className={`group border-b border-gray-200 relative ${dragOverIndex === index ? 'border-t-2 border-t-blue-500' : ''} ${
                            component.width === 'half' ? 'w-1/2' : 'w-full'
                          } ${
                            component.width === 'half' && colIndex === 0 && row.length > 1 ? 'pr-1' : ''
                          } ${
                            component.width === 'half' && colIndex === 1 ? 'pl-1' : ''
                          }`}
                          draggable="true"
                          data-index={index}
                          ref={index === parseInt(draggedItemRef.current?.dataset.index) ? draggedItemRef : null}
                          onDragStart={(e) => {
                            e.stopPropagation();
                            draggedItemRef.current = e.currentTarget;
                            try {
                              const data = JSON.stringify({
                                id: component.id,
                                index: index,
                                isFromPalette: false
                              });
                              e.dataTransfer.setData('text/plain', data);
                            } catch (err) {
                              console.error('Error setting drag data:', err);
                            }
                            handleDragStart(e, component);
                          }}
                          onDragEnd={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleDragEnd();
                          }}
                          onDragOver={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleDragOver(e, index);
                          }}
                          onDrop={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleDrop(e, index);
                          }}
                        >
                          {/* Action buttons - only visible on hover */}
                          <div className="absolute top-2 right-2 hidden group-hover:flex space-x-2 z-20">
                            <button 
                              onClick={() => handleEditComponent(index)}
                              className="p-1 text-white bg-blue-500 rounded-md hover:bg-blue-600 flex items-center opacity-90"
                              title="Edit"
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                              </svg>
                            </button>
                            <button 
                              onClick={() => handleDeleteComponent(index)}
                              className="p-1 text-white bg-red-500 rounded-md hover:bg-red-600 flex items-center opacity-90"
                              title="Delete"
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                              </svg>
                            </button>
                          </div>
                          
                          <div className="w-full">
                            {renderComponentContent(component)}
                          </div>
                          
                          {/* Drop zone after each component */}
                          <div 
                            className={`h-8 w-full absolute -bottom-4 left-0 z-10 ${dragOverIndex === index + 1 ? 'bg-blue-200' : ''} transition-colors duration-200`}
                            onDragOver={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              handleDragOver(e, index + 1);
                            }}
                            onDragLeave={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              handleDragLeave();
                            }}
                            onDrop={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              handleDrop(e, index + 1);
                              setDragOverIndex(null);
                            }}
                          />
                        </div>
                      ));
                    }
                  })()}
                </div>
              ));
            })()}
            
            {/* Add an explicit drop zone at the end of the list */}
            <div 
              className={`border-2 border-dashed ${dragOverIndex === designAreaComponents.length ? 'border-blue-500 bg-blue-50' : 'border-gray-300'} p-4 w-full text-center text-gray-500 hover:border-blue-300 transition-colors duration-200`}
              onDragOver={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handleDragOver(e, designAreaComponents.length);
              }}
              onDragLeave={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handleDragLeave();
              }}
              onDrop={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handleDrop(e, designAreaComponents.length);
                setDragOverIndex(null);
              }}
            >
              Drop components here to add to the end
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
  
  const renderPreviewTab = (device) => (
    <div className="flex-1 bg-gray-100 overflow-y-auto">
      <div className={device === 'mobile' ? 'max-w-xs mx-auto h-full' : 'max-w-3xl mx-auto h-full'}>
        <div className="bg-white shadow-md h-full">
          <div className="border min-h-[600px] w-full h-full">
            {designAreaComponents.length > 0 ? (
              <div className="w-full">
                {/* handle component rendering, support half-width components side by side */}
                {(() => {
                  // create an array to store components
                  const rows = [];
                  let currentRow = [];
                  let currentRowWidth = 0;
                  
                  // iterate through all components, organize them by rows
                  designAreaComponents.forEach((component, index) => {
                    const componentWidth = component.width || 'full';
                    // In mobile preview mode, all components are full width
                    const widthValue = device === 'mobile' ? 1 : (componentWidth === 'half' ? 0.5 : 1);
                    
                    // if the current row is full or the component is full-width, start a new row
                    if (currentRowWidth + widthValue > 1 || widthValue === 1) {
                      // save the current row (if there are components)
                      if (currentRow.length > 0) {
                        rows.push([...currentRow]);
                        currentRow = [];
                        currentRowWidth = 0;
                      }
                    }
                    
                    // add component to the current row
                    currentRow.push({ component, index });
                    currentRowWidth += widthValue;
                    
                    // if the current row is full, save and start a new row
                    if (currentRowWidth >= 1) {
                      rows.push([...currentRow]);
                      currentRow = [];
                      currentRowWidth = 0;
                    }
                  });
                  
                  // add the last row (if there are components)t row (if there are components)
                  if (currentRow.length > 0) {
                    rows.push([...currentRow]);
                  }
                  
                  // render all rows
                  return rows.map((row, rowIndex) => (
                    <div key={`row-${rowIndex}`} className="flex w-full">
                      {/* Sort components in the row by position property (only in desktop preview mode) */}
                      {(() => {
                        // If it's mobile device preview, render as is
                        if (device === 'mobile') {
                          return row.map(({ component }, colIndex) => (
                            <div 
                              key={component.id} 
                              className="w-full"
                            >
                              {renderComponentContent(component)}
                            </div>
                          ));
                        }
                        
                        // In desktop preview mode, if there are two half-width components in a row, sort them by position property
                        if (row.length === 2 && 
                            row[0].component.width === 'half' && 
                            row[1].component.width === 'half') {
                          
                          // copy the row to avoid modifying the original data
                          const sortedRow = [...row];
                          
                          // if the first component should be on the right and the second component should be on the left, swap them
                          if (sortedRow[0].component.position === 'right' && 
                              sortedRow[1].component.position === 'left') {
                            [sortedRow[0], sortedRow[1]] = [sortedRow[1], sortedRow[0]];
                          }
                          
                          return sortedRow.map(({ component }, colIndex) => (
                            <div
                              key={component.id}
                              className={`${
                                component.width === 'half' ? 'w-1/2' : 'w-full'
                              }`}
                            >
                              {renderComponentContent(component)}
                            </div>
                          ));
                        } else {
                          // if it's not the case of two half-width components, render as is
                          return row.map(({ component }, colIndex) => (
                            <div
                              key={component.id}
                              className={`${
                                device === 'mobile' ? 'w-full' : (component.width === 'half' ? 'w-1/2' : 'w-full')
                              }`}
                            >
                              {renderComponentContent(component)}
                            </div>
                          ));
                        }
                      })()}
                    </div>
                  ));
                })()}
              </div>
            ) : (
              <div className="flex items-center justify-center h-full w-full text-gray-500">
                No content added yet. Drag components from the sidebar to build your design.
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
  
  const renderMainContent = () => {
    switch (activeTab) {
      case 'design':
        return renderDesignTab();
      case 'preview-desktop':
        return renderPreviewTab('desktop');
      case 'preview-mobile':
        return renderPreviewTab('mobile');
      default:
        return renderDesignTab();
    }
  };
  
  // Render the Edit Dialog
  const renderEditDialog = () => {
    if (!editDialogOpen) return null;
    
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] flex flex-col">
          <div className="flex justify-between items-center p-4 border-b">
            <h2 className="text-lg font-medium">Edit content</h2>
            <div className="flex space-x-6">
              {/* if it's a divider component, only show the divider label */}
              {designAreaComponents[currentEditingIndex] && 
               designAreaComponents[currentEditingIndex].id.split('-')[0] === 'divider' && (
                <button 
                  className={`px-4 py-2 border-b-2 border-blue-500 text-blue-600`}
                >
                  Divider
                </button>
              )}
              
              {/* if it's a buttons component, only show the button label */}
              {designAreaComponents[currentEditingIndex] && 
               designAreaComponents[currentEditingIndex].id.split('-')[0] === 'buttons' && (
                <button 
                  className={`px-4 py-2 border-b-2 border-blue-500 text-blue-600`}
                >
                  Button
                </button>
              )}
              
              {/* if it's a social-buttons component, only show the Social Media Buttons label */}
              {designAreaComponents[currentEditingIndex] && 
               designAreaComponents[currentEditingIndex].id.split('-')[0] === 'social-buttons' && (
                <button 
                  className={`px-4 py-2 border-b-2 border-blue-500 text-blue-600`}
                >
                  Social Media Buttons
                </button>
              )}
            
              {/* Show text tab only for text-related components */}
              {designAreaComponents[currentEditingIndex] && 
               !['header-image', 'image', 'footer-image', 'divider', 'buttons', 'social-buttons'].includes(
                 designAreaComponents[currentEditingIndex].id.split('-')[0]
               ) && (
                <button 
                  className={`px-4 py-2 ${activeEditTab === 'text' ? 'border-b-2 border-blue-500 text-blue-600' : 'text-gray-600'}`}
                  onClick={() => setActiveEditTab('text')}
                >
                  Text
                </button>
              )}
              
              {/* Show image tab for image-related components */}
              {designAreaComponents[currentEditingIndex] && 
               ['header-image', 'image', 'footer-image', 'two-images', 'image-text', 'text-image', 'image-on-text'].includes(
                 designAreaComponents[currentEditingIndex].id.split('-')[0]
               ) && (
                <button 
                  className={`px-4 py-2 ${activeEditTab === 'image' ? 'border-b-2 border-blue-500 text-blue-600' : 'text-gray-600'}`}
                  onClick={() => setActiveEditTab('image')}
                >
                  Image
                </button>
              )}
              
              {/* Style tab for all components except divider, buttons and social-buttons */}
              {designAreaComponents[currentEditingIndex] && 
               !['divider', 'buttons', 'social-buttons'].includes(designAreaComponents[currentEditingIndex].id.split('-')[0]) && (
                <button 
                  className={`px-4 py-2 ${activeEditTab === 'style' ? 'border-b-2 border-blue-500 text-blue-600' : 'text-gray-600'}`}
                  onClick={() => setActiveEditTab('style')}
                >
                  Style
                </button>
              )}
            </div>
            <button onClick={handleCloseEditDialog} className="text-gray-500 hover:text-gray-700">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          {/* add component width and position selector to the top, but hide it for divider components */}
          {designAreaComponents[currentEditingIndex] && 
           designAreaComponents[currentEditingIndex].id.split('-')[0] !== 'divider' && (
            <div className="px-4 py-2 border-b bg-gray-50 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2">
              <div className="flex items-center">
                <span className="mr-2 font-medium">Component Width:</span>
                <div className="flex border border-gray-300 rounded-md overflow-hidden">
                  <button
                    className={`px-4 py-1 ${componentWidth === 'full' ? 'bg-blue-500 text-white' : 'bg-gray-100'}`}
                    onClick={() => setComponentWidth('full')}
                  >
                    Full width
                  </button>
                  <button
                    className={`px-4 py-1 ${componentWidth === 'half' ? 'bg-blue-500 text-white' : 'bg-gray-100'}`}
                    onClick={() => setComponentWidth('half')}
                  >
                    Half width
                  </button>
                </div>
              </div>
              
              {/* add position selector, only show it in half-width mode */}
              {componentWidth === 'half' && (
                <div className="flex items-center">
                  <span className="mr-2 font-medium">Position:</span>
                  <div className="flex border border-gray-300 rounded-md overflow-hidden">
                    <button
                      className={`px-4 py-1 ${componentPosition === 'left' ? 'bg-blue-500 text-white' : 'bg-gray-100'}`}
                      onClick={() => setComponentPosition('left')}
                    >
                      Left
                    </button>
                    <button
                      className={`px-4 py-1 ${componentPosition === 'right' ? 'bg-blue-500 text-white' : 'bg-gray-100'}`}
                      onClick={() => setComponentPosition('right')}
                    >
                      Right
                    </button>
                  </div>
                </div>
              )}
              
              <div className="text-sm text-gray-500">
                {componentWidth === 'half' 
                  ? `Component will take up half of the row (${componentPosition} side)` 
                  : 'Component will take up the full row'}
              </div>
            </div>
          )}
          
          <div className="p-4 flex-1 overflow-y-auto">
            {activeEditTab === 'divider' && (
              <div>
                <h3 className="text-lg font-medium mb-4">Divider Settings</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                  {/* border color */}
                  <div className="flex items-center relative">
                    <div 
                      className="w-8 h-8 rounded-md cursor-pointer border"
                      style={{ backgroundColor: dividerStyle.borderColor }}
                      onClick={() => setShowBorderColorPicker(!showBorderColorPicker)}
                    />
                    <span className="ml-2">Line Color</span>
                    
                    {showBorderColorPicker && (
                      <div className="absolute top-10 left-0 z-10 bg-white border border-gray-200 rounded-md shadow-lg p-2">
                        <div className="flex flex-wrap gap-2 w-64">
                          {colorOptions.map((color, index) => (
                            <div 
                              key={index}
                              className={`w-8 h-8 rounded-md cursor-pointer border ${dividerStyle.borderColor === color ? 'ring-2 ring-blue-500' : ''}`}
                              style={{ backgroundColor: color }}
                              onClick={() => {
                                setDividerStyle({...dividerStyle, borderColor: color});
                                setShowBorderColorPicker(false);
                              }}
                            />
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                  
                  {/* border style */}
                  <div className="flex items-center">
                    <select
                      className="w-32 h-8 border border-gray-300 rounded-md px-2"
                      value={dividerStyle.borderStyle}
                      onChange={(e) => setDividerStyle({...dividerStyle, borderStyle: e.target.value})}
                    >
                      {borderStyleOptions.map((style) => (
                        <option key={style} value={style}>{style}</option>
                      ))}
                    </select>
                    <span className="ml-2">Line Style</span>
                  </div>
                  
                  {/* border width */}
                  <div className="flex items-center">
                    <input 
                      type="number" 
                      className="w-16 h-8 border border-gray-300 rounded-md px-2 text-center"
                      value={parseInt(dividerStyle.borderWidth)}
                      onChange={(e) => setDividerStyle({...dividerStyle, borderWidth: `${e.target.value}px`})}
                      min="1"
                      max="20"
                    />
                    <span className="ml-1">px</span>
                    <span className="ml-2">Line Width</span>
                  </div>
                  
                  {/* top margin */}
                  <div className="flex items-center">
                    <input 
                      type="number" 
                      className="w-16 h-8 border border-gray-300 rounded-md px-2 text-center"
                      value={parseInt(dividerStyle.marginTop)}
                      onChange={(e) => setDividerStyle({...dividerStyle, marginTop: `${e.target.value}px`})}
                      min="0"
                      max="100"
                    />
                    <span className="ml-1">px</span>
                    <span className="ml-2">Top Margin</span>
                  </div>
                  
                  {/* bottom margin */}
                  <div className="flex items-center">
                    <input 
                      type="number" 
                      className="w-16 h-8 border border-gray-300 rounded-md px-2 text-center"
                      value={parseInt(dividerStyle.marginBottom)}
                      onChange={(e) => setDividerStyle({...dividerStyle, marginBottom: `${e.target.value}px`})}
                      min="0"
                      max="100"
                    />
                    <span className="ml-1">px</span>
                    <span className="ml-2">Bottom Margin</span>
                  </div>
                </div>
                
                {/* preview */}
                <div className="border rounded-md p-4 mb-4">
                  <h3 className="text-sm font-medium text-gray-700 mb-4">Preview</h3>
                  <div className="p-4 bg-white">
                    <hr style={{
                      borderWidth: dividerStyle.borderWidth,
                      borderStyle: dividerStyle.borderStyle,
                      borderColor: dividerStyle.borderColor,
                      marginTop: dividerStyle.marginTop,
                      marginBottom: dividerStyle.marginBottom,
                    }} />
                  </div>
                </div>
              </div>
            )}
            
            {activeEditTab === 'button' && (
              <div>
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium">Button Settings</h3>
                  <div className="flex items-center space-x-2">
                    <div className="flex space-x-2">
                      {buttonSettings.buttons.map((button, index) => (
                        <button
                          key={index}
                          className={`px-3 py-1 ${currentEditingButton === index ? 'bg-blue-500 text-white' : 'bg-gray-200'} rounded-md`}
                          onClick={() => setCurrentEditingButton(index)}
                        >
                          Button {index + 1}
                        </button>
                      ))}
                    </div>
                    
                    {/* Add/Remove Button Controls */}
                    <div className="flex space-x-1 ml-4">
                      {buttonSettings.buttons.length < 3 && (
                        <button
                          className="p-1 bg-green-500 text-white rounded-md hover:bg-green-600"
                          onClick={() => {
                            const newButtons = [...buttonSettings.buttons];
                            newButtons.push({
                              text: `Button ${newButtons.length + 1}`,
                              link: '',
                              color: '#000000',
                              textColor: '#ffffff',
                              borderColor: '#000000',
                              borderWidth: '0px',
                              borderRadius: '20px',
                              width: '150px',
                              height: '40px',
                              font: 'Comic Sans MS',
                              fontSize: '14px',
                              active: true
                            });
                            setButtonSettings({...buttonSettings, buttons: newButtons});
                            setCurrentEditingButton(newButtons.length - 1);
                          }}
                          title="Add Button"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                          </svg>
                        </button>
                      )}
                      
                      {buttonSettings.buttons.length > 1 && (
                        <button
                          className="p-1 bg-red-500 text-white rounded-md hover:bg-red-600"
                          onClick={() => {
                            if (buttonSettings.buttons.length > 1) {
                              const newButtons = [...buttonSettings.buttons];
                              newButtons.pop();
                              setButtonSettings({...buttonSettings, buttons: newButtons});
                              if (currentEditingButton >= newButtons.length) {
                                setCurrentEditingButton(newButtons.length - 1);
                              }
                            }
                          }}
                          title="Delete Last Button"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                          </svg>
                        </button>
                      )}
                    </div>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                  {/* Button Color */}
                  <div className="flex items-center relative">
                    <div 
                      className="w-8 h-8 rounded-md cursor-pointer border"
                      style={{ backgroundColor: buttonSettings.buttons[currentEditingButton].color }}
                      onClick={() => setShowBackgroundColorPicker(!showBackgroundColorPicker)}
                    />
                    <span className="ml-2">Button color</span>
                    
                    {showBackgroundColorPicker && (
                      <div className="absolute top-10 left-0 z-10 bg-white border border-gray-200 rounded-md shadow-lg p-2">
                        <div className="flex flex-wrap gap-2 w-64">
                          {colorOptions.map((color, index) => (
                            <div 
                              key={index}
                              className={`w-8 h-8 rounded-md cursor-pointer border ${buttonSettings.buttons[currentEditingButton].color === color ? 'ring-2 ring-blue-500' : ''}`}
                              style={{ backgroundColor: color }}
                              onClick={() => {
                                const newButtons = [...buttonSettings.buttons];
                                newButtons[currentEditingButton] = {
                                  ...newButtons[currentEditingButton],
                                  color: color
                                };
                                setButtonSettings({...buttonSettings, buttons: newButtons});
                                setShowBackgroundColorPicker(false);
                              }}
                            />
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                  
                  {/* Button Border Color */}
                  <div className="flex items-center relative">
                    <div 
                      className="w-8 h-8 rounded-md cursor-pointer border"
                      style={{ backgroundColor: buttonSettings.buttons[currentEditingButton].borderColor }}
                      onClick={() => setShowBorderColorPicker(!showBorderColorPicker)}
                    />
                    <span className="ml-2">Button Border</span>
                    
                    {showBorderColorPicker && (
                      <div className="absolute top-10 left-0 z-10 bg-white border border-gray-200 rounded-md shadow-lg p-2">
                        <div className="flex flex-wrap gap-2 w-64">
                          {colorOptions.map((color, index) => (
                            <div 
                              key={index}
                              className={`w-8 h-8 rounded-md cursor-pointer border ${buttonSettings.buttons[currentEditingButton].borderColor === color ? 'ring-2 ring-blue-500' : ''}`}
                              style={{ backgroundColor: color }}
                              onClick={() => {
                                const newButtons = [...buttonSettings.buttons];
                                newButtons[currentEditingButton] = {
                                  ...newButtons[currentEditingButton],
                                  borderColor: color
                                };
                                setButtonSettings({...buttonSettings, buttons: newButtons});
                                setShowBorderColorPicker(false);
                              }}
                            />
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                  
                  {/* Border Size */}
                  <div className="flex items-center">
                    <input 
                      type="number" 
                      className="w-16 h-8 border border-gray-300 rounded-md px-2 text-center"
                      value={parseInt(buttonSettings.buttons[currentEditingButton].borderWidth) || 0}
                      onChange={(e) => {
                        const newButtons = [...buttonSettings.buttons];
                        newButtons[currentEditingButton] = {
                          ...newButtons[currentEditingButton],
                          borderWidth: `${e.target.value}px`
                        };
                        setButtonSettings({...buttonSettings, buttons: newButtons});
                      }}
                      min="0"
                      max="20"
                    />
                    <span className="ml-2">Border size</span>
                  </div>
                  
                  {/* Button Height */}
                  <div className="flex items-center">
                    <input 
                      type="number" 
                      className="w-16 h-8 border border-gray-300 rounded-md px-2 text-center"
                      value={parseInt(buttonSettings.buttons[currentEditingButton].height)}
                      onChange={(e) => {
                        const newButtons = [...buttonSettings.buttons];
                        newButtons[currentEditingButton] = {
                          ...newButtons[currentEditingButton],
                          height: `${e.target.value}px`
                        };
                        setButtonSettings({...buttonSettings, buttons: newButtons});
                      }}
                      min="20"
                      max="100"
                    />
                    <span className="ml-2">Button Height</span>
                  </div>
                  
                  {/* Border Radius */}
                  <div className="flex items-center">
                    <input 
                      type="number" 
                      className="w-16 h-8 border border-gray-300 rounded-md px-2 text-center"
                      value={parseInt(buttonSettings.buttons[currentEditingButton].borderRadius)}
                      onChange={(e) => {
                        const newButtons = [...buttonSettings.buttons];
                        newButtons[currentEditingButton] = {
                          ...newButtons[currentEditingButton],
                          borderRadius: `${e.target.value}px`
                        };
                        setButtonSettings({...buttonSettings, buttons: newButtons});
                      }}
                      min="0"
                      max="50"
                    />
                    <span className="ml-2">Radius</span>
                  </div>
                  
                  {/* button width */}
                  <div className="flex items-center">
                    <input 
                      type="number" 
                      className="w-16 h-8 border border-gray-300 rounded-md px-2 text-center"
                      value={parseInt(buttonSettings.buttons[currentEditingButton].width)}
                      onChange={(e) => {
                        const newButtons = [...buttonSettings.buttons];
                        newButtons[currentEditingButton] = {
                          ...newButtons[currentEditingButton],
                          width: `${e.target.value}px`
                        };
                        setButtonSettings({...buttonSettings, buttons: newButtons});
                      }}
                      min="50"
                      max="500"
                    />
                    <span className="ml-2">Button Width</span>
                  </div>
                  
                  {/* button text color */}
                  <div className="flex items-center relative">
                    <div 
                      className="w-8 h-8 rounded-md cursor-pointer border"
                      style={{ backgroundColor: buttonSettings.buttons[currentEditingButton].textColor }}
                      onClick={() => {
                        setShowBackgroundColorPicker(false);
                        setShowBorderColorPicker(!showBorderColorPicker);
                      }}
                    />
                    <span className="ml-2">Button Text color</span>
                    
                    {showBorderColorPicker && (
                      <div className="absolute top-10 left-0 z-10 bg-white border border-gray-200 rounded-md shadow-lg p-2">
                        <div className="flex flex-wrap gap-2 w-64">
                          {colorOptions.map((color, index) => (
                            <div 
                              key={index}
                              className={`w-8 h-8 rounded-md cursor-pointer border ${buttonSettings.buttons[currentEditingButton].textColor === color ? 'ring-2 ring-blue-500' : ''}`}
                              style={{ backgroundColor: color }}
                              onClick={() => {
                                const newButtons = [...buttonSettings.buttons];
                                newButtons[currentEditingButton] = {
                                  ...newButtons[currentEditingButton],
                                  textColor: color
                                };
                                setButtonSettings({...buttonSettings, buttons: newButtons});
                                setShowBorderColorPicker(false);
                              }}
                            />
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                  
                  {/* font selection */}
                  <div className="flex items-center">
                    <span className="mr-2">Font</span>
                    <select
                      className="border border-gray-300 rounded-md px-2 py-1"
                      value={buttonSettings.buttons[currentEditingButton].font}
                      onChange={(e) => {
                        const newButtons = [...buttonSettings.buttons];
                        newButtons[currentEditingButton] = {
                          ...newButtons[currentEditingButton],
                          font: e.target.value
                        };
                        setButtonSettings({...buttonSettings, buttons: newButtons});
                      }}
                    >
                      <option value="Arial">Arial</option>
                      <option value="Helvetica">Helvetica</option>
                      <option value="Times New Roman">Times New Roman</option>
                      <option value="Comic Sans MS">Comic Sans MS</option>
                      <option value="Courier New">Courier New</option>
                      <option value="Georgia">Georgia</option>
                      <option value="Verdana">Verdana</option>
                    </select>
                  </div>
                  
                  {/* font size */}
                  <div className="flex items-center">
                    <span className="mr-2">Font-size</span>
                    <select
                      className="border border-gray-300 rounded-md px-2 py-1"
                      value={buttonSettings.buttons[currentEditingButton].fontSize.replace('px', '')}
                      onChange={(e) => {
                        const newButtons = [...buttonSettings.buttons];
                        newButtons[currentEditingButton] = {
                          ...newButtons[currentEditingButton],
                          fontSize: `${e.target.value}px`
                        };
                        setButtonSettings({...buttonSettings, buttons: newButtons});
                      }}
                    >
                      <option value="10">10</option>
                      <option value="12">12</option>
                      <option value="14">14</option>
                      <option value="16">16</option>
                      <option value="18">18</option>
                      <option value="20">20</option>
                      <option value="24">24</option>
                    </select>
                  </div>
                </div>
                
                {/* button text */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Button Text</label>
                  <input 
                    type="text" 
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                    value={buttonSettings.buttons[currentEditingButton].text}
                    onChange={(e) => {
                      const newButtons = [...buttonSettings.buttons];
                      newButtons[currentEditingButton] = {
                        ...newButtons[currentEditingButton],
                        text: e.target.value
                      };
                      setButtonSettings({...buttonSettings, buttons: newButtons});
                    }}
                    placeholder="Enter button text"
                  />
                </div>
                
                {/* button link */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Button Link</label>
                  <select
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                    value={buttonSettings.buttons[currentEditingButton].link ? 'custom' : 'none'}
                    onChange={(e) => {
                      const newButtons = [...buttonSettings.buttons];
                      newButtons[currentEditingButton] = {
                        ...newButtons[currentEditingButton],
                        link: e.target.value === 'none' ? '' : 'https://'
                      };
                      setButtonSettings({...buttonSettings, buttons: newButtons});
                    }}
                  >
                    <option value="none">None</option>
                    <option value="custom">Custom URL</option>
                  </select>
                  
                  {buttonSettings.buttons[currentEditingButton].link && (
                    <input 
                      type="text" 
                      className="w-full border border-gray-300 rounded-md px-3 py-2 mt-2"
                      value={buttonSettings.buttons[currentEditingButton].link}
                      onChange={(e) => {
                        const newButtons = [...buttonSettings.buttons];
                        newButtons[currentEditingButton] = {
                          ...newButtons[currentEditingButton],
                          link: e.target.value
                        };
                        setButtonSettings({...buttonSettings, buttons: newButtons});
                      }}
                      placeholder="https://example.com"
                    />
                  )}
                </div>
                
                {/* button enable/disable */}
                {currentEditingButton > 0 && (
                  <div className="mb-4 flex items-center">
                    <input 
                      type="checkbox" 
                      id="button-active"
                      className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                      checked={buttonSettings.buttons[currentEditingButton].active !== false}
                      onChange={(e) => {
                        const newButtons = [...buttonSettings.buttons];
                        newButtons[currentEditingButton] = {
                          ...newButtons[currentEditingButton],
                          active: e.target.checked
                        };
                        setButtonSettings({...buttonSettings, buttons: newButtons});
                      }}
                    />
                    <label htmlFor="button-active" className="ml-2 text-sm text-gray-700">
                      Enable this button
                    </label>
                  </div>
                )}
                
                {/* preview */}
                <div className="border rounded-md p-4 mb-4">
                  <h3 className="text-sm font-medium text-gray-700 mb-4">Preview</h3>
                  <div className="p-4 bg-white flex justify-center">
                    <div 
                      style={{
                        backgroundColor: buttonSettings.buttons[currentEditingButton].color,
                        color: buttonSettings.buttons[currentEditingButton].textColor,
                        borderColor: buttonSettings.buttons[currentEditingButton].borderColor,
                        borderWidth: buttonSettings.buttons[currentEditingButton].borderWidth,
                        borderStyle: buttonSettings.buttons[currentEditingButton].borderWidth !== '0px' ? 'solid' : 'none',
                        borderRadius: buttonSettings.buttons[currentEditingButton].borderRadius,
                        width: buttonSettings.buttons[currentEditingButton].width,
                        height: buttonSettings.buttons[currentEditingButton].height,
                        fontFamily: buttonSettings.buttons[currentEditingButton].font,
                        fontSize: buttonSettings.buttons[currentEditingButton].fontSize,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                      }}
                      className="cursor-pointer"
                    >
                      {buttonSettings.buttons[currentEditingButton].text || 'Button Text'}
                    </div>
                  </div>
                </div>
              </div>
            )}
            
            {activeEditTab === 'social-buttons' && (
              <div>
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium">Social Media Button Settings</h3>
                  <div className="flex items-center space-x-2">
                    <select 
                      className="border border-gray-300 rounded-md px-3 py-2"
                      value={socialButtonsSettings.theme}
                      onChange={(e) => {
                        setSocialButtonsSettings({
                          ...socialButtonsSettings,
                          theme: e.target.value
                        });
                      }}
                    >
                      <option value="default">Default Theme</option>
                      <option value="outline">Outline Theme</option>
                      <option value="simple">Simple Theme</option>
                    </select>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  {socialButtonsSettings.buttons.map((button, index) => {
                    const socialType = button.type;
                    const capitalizedType = socialType.charAt(0).toUpperCase() + socialType.slice(1);
                    
                    return (
                      <div key={index} className="border rounded-md p-4">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center">
                            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                              socialButtonsSettings.theme === 'default' ? 
                              `bg-${socialType === 'facebook' ? 'blue-600' : 
                                socialType === 'instagram' ? 'pink-500' : 
                                socialType === 'youtube' ? 'red-600' : 
                                socialType === 'x' ? 'black' : 
                                socialType === 'linkedin' ? 'blue-700' : 
                                socialType === 'tiktok' ? 'black' : 
                                socialType === 'pinterest' ? 'red-500' : 
                                socialType === 'yelp' ? 'red-600' : 
                                'black'} text-white` : 
                              `border-2 border-${socialType === 'facebook' ? 'blue-600' : 
                                socialType === 'instagram' ? 'pink-500' : 
                                socialType === 'youtube' ? 'red-600' : 
                                socialType === 'x' ? 'black' : 
                                socialType === 'linkedin' ? 'blue-700' : 
                                socialType === 'tiktok' ? 'black' : 
                                socialType === 'pinterest' ? 'red-500' : 
                                socialType === 'yelp' ? 'red-600' : 
                                'black'} text-${socialType === 'facebook' ? 'blue-600' : 
                                socialType === 'instagram' ? 'pink-500' : 
                                socialType === 'youtube' ? 'red-600' : 
                                socialType === 'x' ? 'black' : 
                                socialType === 'linkedin' ? 'blue-700' : 
                                socialType === 'tiktok' ? 'black' : 
                                socialType === 'pinterest' ? 'red-500' : 
                                socialType === 'yelp' ? 'red-600' : 
                                'black'}`
                            }`}>
                              {socialType === 'facebook' && <span className="text-xs">f</span>}
                              {socialType === 'instagram' && <span className="text-xs">Ig</span>}
                              {socialType === 'youtube' && <span className="text-xs">Yt</span>}
                              {socialType === 'x' && <span className="text-xs">X</span>}
                              {socialType === 'linkedin' && <span className="text-xs">in</span>}
                              {socialType === 'tiktok' && <span className="text-xs">Tk</span>}
                              {socialType === 'pinterest' && <span className="text-xs">P</span>}
                              {socialType === 'yelp' && <span className="text-xs">Y</span>}
                              {socialType === 'threads' && <span className="text-xs">Th</span>}
                            </div>
                            <span className="ml-2 font-medium">{capitalizedType}</span>
                          </div>
                          <div className="flex items-center">
                            <input 
                              type="checkbox" 
                              id={`social-active-${index}`}
                              className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                              checked={button.active !== false}
                              onChange={(e) => {
                                const newButtons = [...socialButtonsSettings.buttons];
                                newButtons[index] = {
                                  ...newButtons[index],
                                  active: e.target.checked
                                };
                                setSocialButtonsSettings({
                                  ...socialButtonsSettings,
                                  buttons: newButtons
                                });
                              }}
                            />
                            <label htmlFor={`social-active-${index}`} className="ml-2 text-sm text-gray-700">
                              Enable
                            </label>
                          </div>
                        </div>
                        
                        <div className="mb-2">
                          <label className="block text-sm font-medium text-gray-700 mb-1">Link</label>
                          <input 
                            type="text" 
                            className="w-full border border-gray-300 rounded-md px-3 py-2"
                            value={button.link || ''}
                            onChange={(e) => {
                              const newButtons = [...socialButtonsSettings.buttons];
                              newButtons[index] = {
                                ...newButtons[index],
                                link: e.target.value
                              };
                              setSocialButtonsSettings({
                                ...socialButtonsSettings,
                                buttons: newButtons
                              });
                            }}
                            placeholder={`https://${socialType}.com/youraccount`}
                          />
                        </div>
                      </div>
                    );
                  })}
                </div>
                
                {/* preview */}
                <div className="border rounded-md p-4 mb-4">
                  <h3 className="text-sm font-medium text-gray-700 mb-4">Preview</h3>
                  <div className="p-4 bg-white">
                    <div className="flex flex-wrap justify-center gap-3">
                      {socialButtonsSettings.buttons.filter(btn => btn.active !== false).map((socialBtn, index) => {
                        const socialType = socialBtn.type;
                        const bgColor = 
                          socialType === 'facebook' ? '#1877F2' : 
                          socialType === 'instagram' ? '#E4405F' : 
                          socialType === 'youtube' ? '#FF0000' : 
                          socialType === 'x' ? '#000000' : 
                          socialType === 'linkedin' ? '#0A66C2' : 
                          socialType === 'tiktok' ? '#000000' : 
                          socialType === 'pinterest' ? '#E60023' : 
                          socialType === 'yelp' ? '#FF1A1A' : 
                          '#000000';
                        
                        let buttonStyle = {
                          width: '40px',
                          height: '40px',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          borderRadius: '50%',
                          color: '#ffffff',
                          backgroundColor: bgColor,
                          cursor: 'pointer'
                        };
                        
                        if (socialButtonsSettings.theme === 'outline') {
                          buttonStyle = {
                            ...buttonStyle,
                            backgroundColor: 'transparent',
                            color: bgColor,
                            border: `2px solid ${bgColor}`
                          };
                        } else if (socialButtonsSettings.theme === 'simple') {
                          buttonStyle = {
                            ...buttonStyle,
                            backgroundColor: 'transparent',
                            color: bgColor,
                            border: 'none'
                          };
                        }
                        
                        return (
                          <div
                            key={index}
                            style={buttonStyle}
                            className="hover:opacity-80"
                          >
                            {socialType === 'facebook' && <span className="text-lg">f</span>}
                            {socialType === 'instagram' && <span className="text-lg">Ig</span>}
                            {socialType === 'youtube' && <span className="text-lg">Yt</span>}
                            {socialType === 'x' && <span className="text-lg">X</span>}
                            {socialType === 'linkedin' && <span className="text-lg">in</span>}
                            {socialType === 'tiktok' && <span className="text-lg">Tk</span>}
                            {socialType === 'pinterest' && <span className="text-lg">P</span>}
                            {socialType === 'yelp' && <span className="text-lg">Y</span>}
                            {socialType === 'threads' && <span className="text-lg">Th</span>}
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </div>
              </div>
            )}
            
            {activeEditTab === 'text' && (
              <div>
                <div className="flex items-center gap-4 mb-2">
                  <div className="flex items-center">
                    <label className="mr-2 text-sm font-medium">Line height:</label>
                    <select 
                      value={lineHeight}
                      onChange={(e) => setLineHeight(e.target.value)}
                      className="border border-gray-300 rounded-md px-2 py-1"
                    >
                      <option value="1.0">1.0</option>
                      <option value="1.2">1.2</option>
                      <option value="1.5">1.5</option>
                      <option value="1.8">1.8</option>
                      <option value="2.0">2.0</option>
                      <option value="2.5">2.5</option>
                    </select>
                  </div>
                  
                  <div className="flex items-center">
                    <label className="mr-2 text-sm font-medium">Vertical padding:</label>
                    <input 
                      type="number" 
                      className="w-16 h-8 border border-gray-300 rounded-md px-2 text-center"
                      value={parseInt(textPadding.vertical)}
                      onChange={(e) => setTextPadding({...textPadding, vertical: `${e.target.value}px`})}
                      min="0"
                      max="50"
                    />
                    <span className="ml-1">px</span>
                  </div>
                  
                  <div className="flex items-center">
                    <label className="mr-2 text-sm font-medium">Horizontal padding:</label>
                    <input 
                      type="number" 
                      className="w-16 h-8 border border-gray-300 rounded-md px-2 text-center"
                      value={parseInt(textPadding.horizontal)}
                      onChange={(e) => setTextPadding({...textPadding, horizontal: `${e.target.value}px`})}
                      min="0"
                      max="50"
                    />
                    <span className="ml-1">px</span>
                  </div>
                </div>
                <ReactQuill
                  ref={quillRef}
                  theme="snow"
                  value={currentEditingContent}
                  onChange={setCurrentEditingContent}
                  modules={modules}
                  formats={formats}
                  style={{ 
                    height: '320px',
                    width: '100%'
                  }}
                />
              </div>
            )}
            
            {activeEditTab === 'image' && (
              <div className="max-h-[600px] overflow-y-auto">
                {/* Hidden file input */}
                <input 
                  type="file" 
                  ref={fileInputRef} 
                  className="hidden" 
                  accept="image/*" 
                  onChange={handleImageUpload}
                />
                
                <div className="flex flex-col md:flex-row gap-4">
                  {/* Left side: Image preview and upload */}
                  <div className="w-full md:w-1/2">
                    <div 
                      className="flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-md p-4 mb-4 h-64"
                      onDragOver={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        e.currentTarget.classList.add('border-blue-500', 'bg-blue-50');
                      }}
                      onDragLeave={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        e.currentTarget.classList.remove('border-blue-500', 'bg-blue-50');
                      }}
                      onDrop={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        e.currentTarget.classList.remove('border-blue-500', 'bg-blue-50');
                        
                        if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
                          const file = e.dataTransfer.files[0];
                          if (file.type.startsWith('image/')) {
                            const reader = new FileReader();
                            reader.onload = (event) => {
                              const newSettings = {
                                src: event.target.result,
                                alt: file.name,
                                rotation: 0,
                                scale: 1,
                                flipHorizontal: false,
                                flipVertical: false
                              };
                              
                              setImageSettings(newSettings);
                              setOriginalImageSettings(newSettings);
                              setImageSettingsHistory([]);
                              setImageSettingsRedoStack([]);
                            };
                            reader.readAsDataURL(file);
                          }
                        }
                      }}
                    >
                      {imageSettings.src ? (
                        <div className="relative w-full h-full flex items-center justify-center">
                          <img 
                            src={imageSettings.src} 
                            alt={imageSettings.alt} 
                            className="max-w-full max-h-full rounded-md object-contain"
                            style={{
                              transform: `
                                rotate(${imageSettings.rotation}deg)
                                ${imageSettings.flipHorizontal ? 'scaleX(-1)' : ''}
                                ${imageSettings.flipVertical ? 'scaleY(-1)' : ''}
                                scale(${imageSettings.scale})
                              `
                            }}
                          />
                        </div>
                      ) : (
                        <div className="flex flex-col items-center justify-center h-full">
                          <svg className="w-12 h-12 text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V5a2 2 0 00-2-2H6a2 2 0 00-2 2v10a2 2 0 002 2z" />
                          </svg>
                          <span className="text-gray-500 mb-3">No image selected</span>
                          <button 
                            onClick={triggerFileInput}
                            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 flex items-center"
                          >
                            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                            </svg>
                            Upload Image
                          </button>
                          <p className="text-gray-500 text-sm mt-2">or drag and drop an image here</p>
                        </div>
                      )}
                    </div>
                    
                    {/* Image action buttons */}
                    {imageSettings.src && (
                      <div className="flex space-x-2 mb-4">
                        <button 
                          onClick={triggerFileInput}
                          className="flex-1 px-3 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 text-sm"
                        >
                          Replace Image
                        </button>
                        <button 
                          onClick={handleImageDelete}
                          className="flex-1 px-3 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 text-sm"
                        >
                          Remove Image
                        </button>
                      </div>
                    )}
                  </div>
                  
                  {/* Right side: Image settings */}
                  <div className="w-full md:w-1/2">
                    {imageSettings.src ? (
                      <div className="border rounded-md p-4">
                        <div className="flex justify-between items-center mb-3">
                          <h3 className="text-sm font-medium text-gray-700">Image Settings</h3>
                          
                          {/* History Controls */}
                          <div className="flex space-x-1">
                            <button 
                              onClick={handleUndo}
                              disabled={imageSettingsHistory.length === 0}
                              className={`p-1 rounded-md ${imageSettingsHistory.length === 0 ? 'text-gray-300 cursor-not-allowed' : 'text-gray-600 hover:bg-gray-100'}`}
                              title="Undo Setting Changes"
                            >
                              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h10a4 4 0 0 1 0 8H9" />
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10l5-5m-5 5l5 5" />
                              </svg>
                            </button>
                            <button 
                              onClick={handleRedo}
                              disabled={imageSettingsRedoStack.length === 0}
                              className={`p-1 rounded-md ${imageSettingsRedoStack.length === 0 ? 'text-gray-300 cursor-not-allowed' : 'text-gray-600 hover:bg-gray-100'}`}
                              title="Redo Setting Changes"
                            >
                              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 10h-10a4 4 0 0 0 0 8h4" />
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 10l-5-5m5 5l-5 5" />
                              </svg>
                            </button>
                            <button 
                              onClick={handleReset}
                              disabled={!originalImageSettings || imageSettingsHistory.length === 0}
                              className={`p-1 rounded-md ${!originalImageSettings || imageSettingsHistory.length === 0 ? 'text-gray-300 cursor-not-allowed' : 'text-gray-600 hover:bg-gray-100'}`}
                              title="Reset to Original"
                            >
                              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                              </svg>
                            </button>
                          </div>
                        </div>
                        
                        {/* Image Alt Text */}
                        <div className="mb-3">
                          <label className="block text-sm font-medium text-gray-700 mb-1">Alt Text</label>
                          <input 
                            type="text" 
                            value={imageSettings.alt} 
                            onChange={(e) => {
                              setImageSettings({...imageSettings, alt: e.target.value});
                            }}
                            onBlur={() => {
                              // Only save history when text actually changes
                              if (originalImageSettings && originalImageSettings.alt !== imageSettings.alt) {
                                saveToHistory({...imageSettings, alt: originalImageSettings.alt});
                              }
                            }}
                            className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                            placeholder="Describe the image"
                          />
                        </div>
                        
                        {/* Image Scale */}
                        <div className="mb-3">
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Scale: {imageSettings.scale.toFixed(1)}x
                          </label>
                          <input 
                            type="range" 
                            min="0.1" 
                            max="2" 
                            step="0.1" 
                            value={imageSettings.scale}
                            onChange={(e) => handleImageScale(e.target.value)}
                            onMouseDown={handleScaleStart}
                            onTouchStart={handleScaleStart}
                            className="w-full"
                          />
                        </div>
                        
                        {/* Image Rotation */}
                        <div className="mb-3">
                          <label className="block text-sm font-medium text-gray-700 mb-1">Rotation</label>
                          <div className="flex space-x-2">
                            <button 
                              onClick={() => handleImageRotate('left')}
                              className="flex-1 px-3 py-1 border border-gray-300 rounded-md hover:bg-gray-50 text-sm"
                            >
                              <span className="flex items-center justify-center">
                                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                                </svg>
                                Rotate Left
                              </span>
                            </button>
                            <button 
                              onClick={() => handleImageRotate('right')}
                              className="flex-1 px-3 py-1 border border-gray-300 rounded-md hover:bg-gray-50 text-sm"
                            >
                              <span className="flex items-center justify-center">
                                Rotate Right
                                <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                                </svg>
                              </span>
                            </button>
                          </div>
                        </div>
                        
                        {/* Image Flip */}
                        <div className="mb-3">
                          <label className="block text-sm font-medium text-gray-700 mb-1">Flip</label>
                          <div className="flex space-x-2">
                            <button 
                              onClick={() => handleImageFlip('horizontal')}
                              className={`flex-1 px-3 py-1 border rounded-md text-sm ${imageSettings.flipHorizontal ? 'bg-blue-100 border-blue-500' : 'border-gray-300 hover:bg-gray-50'}`}
                            >
                              <span className="flex items-center justify-center">
                                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
                                </svg>
                                Horizontal
                              </span>
                            </button>
                            <button 
                              onClick={() => handleImageFlip('vertical')}
                              className={`flex-1 px-3 py-1 border rounded-md text-sm ${imageSettings.flipVertical ? 'bg-blue-100 border-blue-500' : 'border-gray-300 hover:bg-gray-50'}`}
                            >
                              <span className="flex items-center justify-center">
                                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
                                </svg>
                                Vertical
                              </span>
                            </button>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="border rounded-md p-4 flex items-center justify-center h-full">
                        <p className="text-gray-500 text-center">Upload an image to see settings</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
            
            {activeEditTab === 'style' && (
              <div>
                <div className="flex flex-wrap gap-4 mb-4">
                  <div className="flex items-center relative">
                    <div 
                      className="w-8 h-8 rounded-md cursor-pointer border"
                      style={{ backgroundColor: componentStyle.backgroundColor }}
                      onClick={() => setShowBackgroundColorPicker(!showBackgroundColorPicker)}
                    />
                    <span className="ml-2">Background color</span>
                    
                    {showBackgroundColorPicker && (
                      <div className="absolute top-10 left-0 z-10 bg-white border border-gray-200 rounded-md shadow-lg p-2">
                        <div className="flex flex-wrap gap-2 w-64">
                          {colorOptions.map((color, index) => (
                            <div 
                              key={index}
                              className={`w-8 h-8 rounded-md cursor-pointer border ${componentStyle.backgroundColor === color ? 'ring-2 ring-blue-500' : ''}`}
                              style={{ backgroundColor: color }}
                              onClick={() => {
                                handleStyleChange('backgroundColor', color);
                                setShowBackgroundColorPicker(false);
                              }}
                            />
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex items-center relative">
                    <div 
                      className="w-8 h-8 rounded-md cursor-pointer border"
                      style={{ backgroundColor: componentStyle.borderColor }}
                      onClick={() => setShowBorderColorPicker(!showBorderColorPicker)}
                    />
                    <span className="ml-2">Border color</span>
                    
                    {showBorderColorPicker && (
                      <div className="absolute top-10 left-0 z-10 bg-white border border-gray-200 rounded-md shadow-lg p-2">
                        <div className="flex flex-wrap gap-2 w-64">
                          {colorOptions.map((color, index) => (
                            <div 
                              key={index}
                              className={`w-8 h-8 rounded-md cursor-pointer border ${componentStyle.borderColor === color ? 'ring-2 ring-blue-500' : ''}`}
                              style={{ backgroundColor: color }}
                              onClick={() => {
                                handleStyleChange('borderColor', color);
                                setShowBorderColorPicker(false);
                              }}
                            />
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex items-center">
                    <select
                      className="w-32 h-8 border border-gray-300 rounded-md px-2"
                      value={componentStyle.borderStyle}
                      onChange={(e) => handleStyleChange('borderStyle', e.target.value)}
                    >
                      {borderStyleOptions.map((style) => (
                        <option key={style} value={style}>{style}</option>
                      ))}
                    </select>
                    <span className="ml-2">Border style</span>
                  </div>
                  
                  <div className="flex items-center">
                    <input 
                      type="number" 
                      className="w-16 h-8 border border-gray-300 rounded-md px-2 text-center"
                      value={parseInt(componentStyle.borderWidth)}
                      onChange={(e) => handleStyleChange('borderWidth', `${e.target.value}px`)}
                      min="0"
                      max="20"
                    />
                    <span className="ml-2">Border size</span>
                  </div>
                  
                  {/* Padding controls moved to text tab */}
                </div>
                
                {/* Preview with simplified structure */}
                <div className="border rounded-md p-4 mb-4">
                  <h3 className="text-sm font-medium text-gray-700 mb-2">Preview</h3>
                  <div 
                    className="border rounded-md"
                    style={{
                      backgroundColor: componentStyle.backgroundColor,
                      borderColor: componentStyle.borderColor,
                      borderWidth: componentStyle.borderWidth,
                      borderStyle: componentStyle.borderStyle,
                    }}
                  >
                    <div 
                      className="rich-text-content ql-editor" 
                      style={{
                        minHeight: '150px',
                        padding: `${textPadding.vertical} ${textPadding.horizontal}`,
                        lineHeight: lineHeight
                      }}
                      dangerouslySetInnerHTML={{ __html: currentEditingContent }}
                    />
                  </div>
                </div>
              </div>
            )}
          </div>
          
          <div className="flex justify-end p-4 border-t">
            <button 
              onClick={handleCloseEditDialog}
              className="px-6 py-2 border border-gray-300 rounded-md mr-2 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button 
              onClick={handleSaveEditDialog}
              className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
            >
              Save
            </button>
          </div>
        </div>
      </div>
    );
  };
  
  // Effect to apply styles to the editor content area
  useEffect(() => {
    if (editDialogOpen && activeEditTab === 'text' && quillRef.current) {
      const editor = quillRef.current.getEditor();
      const editorElement = editor.container.querySelector('.ql-editor');
      
      if (editorElement) {
        editorElement.style.padding = `${textPadding.vertical} ${textPadding.horizontal}`;
        editorElement.style.backgroundColor = componentStyle.backgroundColor;
        editorElement.style.lineHeight = lineHeight;
        
        // Apply line height to all paragraphs and headings
        const paragraphs = editorElement.querySelectorAll('p, h1, h2, h3, h4, h5, h6');
        paragraphs.forEach(p => {
          p.style.lineHeight = lineHeight;
        });
      }
    }
  }, [editDialogOpen, activeEditTab, componentStyle, quillRef, lineHeight, textPadding]);
  
  // Load campaign data from session storage on mount
  useEffect(() => {
    let isMounted = true; // 防止组件卸载后仍然设置状态
    
    const loadCampaignData = async () => {
      if (!isMounted) return;
      
      try {
        // 首先检查URL中是否有campaign ID
        const params = new URLSearchParams(window.location.search);
        const urlCampaignId = params.get('id');
        
        // 从sessionStorage中获取数据
        const savedCampaignData = sessionStorage.getItem('campaignData');
        if (savedCampaignData && isMounted) {
          const parsedData = JSON.parse(savedCampaignData);
          console.log('Loaded campaign data from session storage:', parsedData);
          
          // 检查是否是编辑模式
          const campaignId = parsedData.campaignId || urlCampaignId;
          console.log('Edit mode detected:', !!campaignId, 'Campaign ID:', campaignId);
          
          // 设置基本数据
          if (isMounted) {
            setCampaignData(parsedData);
          }
          
          // 如果是编辑模式，从API获取完整的活动数据
          if (campaignId) {
            try {
              console.log('Fetching campaign data from API for ID:', campaignId);
              const campaignData = await marketingApi.getCampaign(campaignId);
              console.log('Campaign data from API:', campaignData);
              
              // 检查是否有channels数据
              if (campaignData.channels && campaignData.channels.length > 0 && isMounted) {
                console.log('Found channels in campaign data:', campaignData.channels);
                
                // 查找email渠道
                const emailChannel = campaignData.channels.find(c => c.channel_type === 'email');
                if (emailChannel) {
                  console.log('Found email channel:', emailChannel);
                  
                  // 检查raw_components字段
                  if (emailChannel.raw_components) {
                    console.log('Raw components type:', typeof emailChannel.raw_components);
                    console.log('Raw components is array:', Array.isArray(emailChannel.raw_components));
                    
                    let rawComponents = emailChannel.raw_components;
                    
                    // 如果raw_components是字符串，尝试解析为JSON
                    if (typeof rawComponents === 'string') {
                      try {
                        console.log('Attempting to parse raw_components string');
                        rawComponents = JSON.parse(rawComponents);
                        console.log('Successfully parsed raw_components');
                      } catch (parseError) {
                        console.error('Failed to parse raw_components string:', parseError);
                      }
                    }
                    
                    // 验证raw_components是一个有效的数组
                    if (Array.isArray(rawComponents) && rawComponents.length > 0) {
                      console.log('Valid raw_components array found with length:', rawComponents.length);
                      console.log('First component:', rawComponents[0]);
                      
                      if (isMounted) {
                        setDesignAreaComponents(rawComponents);
                      }
                    } else {
                      console.warn('raw_components is not a valid array or is empty');
                      // 如果raw_components不是有效数组，尝试从content创建组件
                      createComponentsFromContent(emailChannel);
                    }
                  } else {
                    console.warn('No raw_components field in email channel');
                    // 如果没有raw_components字段，尝试从content创建组件
                    createComponentsFromContent(emailChannel);
                  }
                } else {
                  console.warn('No email channel found in campaign data');
                }
              } else {
                console.warn('No channels found in campaign data or not mounted');
              }
            } catch (error) {
              console.error('Error fetching campaign from API:', error);
              if (isMounted) {
                setSubmissionError('Error fetching campaign data from API.');
              }
            }
          }
        } else {
          // 直接从URL ID加载数据
          if (urlCampaignId) {
            try {
              console.log('Fetching campaign data from API for URL ID:', urlCampaignId);
              const campaignData = await marketingApi.getCampaign(urlCampaignId);
              console.log('Campaign data from API:', campaignData);
              
              // 设置基本数据
              if (isMounted) {
                setCampaignData({
                  campaignId: urlCampaignId,
                  campaignSubject: campaignData.name,
                  campaignType: getCampaignTypeString(campaignData.campaign_type),
                  channels: campaignData.channels || []
                });
              }
              
              // 设置设计组件
              if (campaignData.channels && campaignData.channels.length > 0 && isMounted) {
                console.log('Found channels in campaign data:', campaignData.channels);
                
                // 查找email渠道
                const emailChannel = campaignData.channels.find(c => c.channel_type === 'email');
                if (emailChannel) {
                  console.log('Found email channel:', emailChannel);
                  
                  // 检查raw_components字段
                  if (emailChannel.raw_components) {
                    console.log('Raw components type:', typeof emailChannel.raw_components);
                    console.log('Raw components is array:', Array.isArray(emailChannel.raw_components));
                    
                    let rawComponents = emailChannel.raw_components;
                    
                    // 如果raw_components是字符串，尝试解析为JSON
                    if (typeof rawComponents === 'string') {
                      try {
                        console.log('Attempting to parse raw_components string');
                        rawComponents = JSON.parse(rawComponents);
                        console.log('Successfully parsed raw_components');
                      } catch (parseError) {
                        console.error('Failed to parse raw_components string:', parseError);
                      }
                    }
                    
                    // 验证raw_components是一个有效的数组
                    if (Array.isArray(rawComponents) && rawComponents.length > 0) {
                      console.log('Valid raw_components array found with length:', rawComponents.length);
                      console.log('First component:', rawComponents[0]);
                      
                      if (isMounted) {
                        setDesignAreaComponents(rawComponents);
                      }
                    } else {
                      console.warn('raw_components is not a valid array or is empty');
                      // 如果raw_components不是有效数组，尝试从content创建组件
                      createComponentsFromContent(emailChannel);
                    }
                  } else {
                    console.warn('No raw_components field in email channel');
                    // 如果没有raw_components字段，尝试从content创建组件
                    createComponentsFromContent(emailChannel);
                  }
                } else {
                  console.warn('No email channel found in campaign data');
                }
              } else {
                console.warn('No channels found in campaign data or not mounted');
              }
            } catch (error) {
              console.error('Error fetching campaign from API for URL ID:', error);
              if (isMounted) {
                setSubmissionError('Error loading campaign data from API.');
              }
            }
          } else {
            console.warn('No campaign data found in session storage and no ID in URL');
            if (isMounted) {
              setSubmissionError('No campaign data found. Please start from the campaign creation page.');
            }
          }
        }
      } catch (error) {
        console.error('Error loading campaign data:', error);
        if (isMounted) {
          setSubmissionError('Error loading campaign data. Please start from the campaign creation page.');
        }
      }
    };
    
    // 辅助函数：从HTML内容创建组件
    const createComponentsFromContent = (emailChannel) => {
      if (!emailChannel || !emailChannel.content || !isMounted) return;
      
      console.log('Creating components from HTML content');
      // 创建一个简单的文本组件来显示内容
      setDesignAreaComponents([
        {
          id: `text-${Date.now()}`,
          name: 'Text',
          icon: 'Aa',
          content: emailChannel.content,
          width: 'full'
        }
      ]);
    };
    
    loadCampaignData();
    
    // 清理函数
    return () => {
      isMounted = false;
    };
  }, []);
  
  // 组件卸载时清理
  useEffect(() => {
    return () => {
      if (autoScrollIntervalRef.current) {
        clearInterval(autoScrollIntervalRef.current);
      }
    };
  }, []);
  
  // Render the Email Preview Dialog
  const renderPreviewDialog = () => {
    if (!previewDialogOpen) return null;
    
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
          <div className="flex justify-between items-center p-4 border-b">
            <h2 className="text-lg font-medium">Send Email Preview</h2>
            <button onClick={handlePreviewDialogClose} className="text-gray-500 hover:text-gray-700">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          <div className="p-4">
            <p className="mb-4">
              Enter an email address to send a preview of your email campaign.
              If sending to multiple emails, separate by commas.
            </p>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Enter email address(es)
              </label>
              <input 
                type="text" 
                value={previewEmail}
                onChange={(e) => setPreviewEmail(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
                placeholder="<EMAIL>"
              />
              <p className="text-sm text-gray-500 mt-1">
                Enter multiple emails separated by commas (,)
              </p>
            </div>
            
            <div className="bg-gray-50 p-3 rounded-md text-sm">
              <p>Note: Clicking "Send" will automatically save your current campaign as a draft before sending the preview.</p>
            </div>
          </div>
          
          <div className="flex justify-end p-4 border-t">
            <button 
              onClick={handlePreviewDialogClose}
              className="px-6 py-2 border border-gray-300 rounded-md mr-2 hover:bg-gray-50"
              disabled={isSendingPreview}
            >
              Cancel
            </button>
            <button 
              onClick={handleSendPreviewConfirm}
              className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
              disabled={!previewEmail.trim() || isSendingPreview}
            >
              {isSendingPreview ? 'Sending...' : 'Send'}
            </button>
          </div>
        </div>
      </div>
    );
  };
  
  return (
    <div className="h-full flex overflow-hidden">
      {/* Left Sidebar - Components */}
      <div className="w-64 border-r border-gray-200 bg-white flex flex-col h-full">
        <div className="flex-1 overflow-y-auto p-4">
          <div className="grid grid-cols-2 gap-2">
            {availableComponents.map((component) => (
              <div 
                key={component.id}
                className="flex flex-col items-center p-2 border border-gray-200 rounded-md hover:bg-gray-50 cursor-pointer"
                draggable
                onDragStart={(e) => {
                  e.stopPropagation();
                  handleDragStart(e, component);
                }}
                onDragEnd={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleDragEnd();
                }}
              >
                <div className="w-10 h-10 flex items-center justify-center bg-gray-100 rounded-md mb-1">
                  {component.icon}
                </div>
                <span className="text-xs font-medium text-gray-700 text-center">{component.name}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col h-full overflow-hidden">
        {/* Top Navigation */}
        <div className="bg-white border-b border-gray-200 px-4 py-3 flex items-center">
          <button 
            onClick={handleBack}
            className="text-gray-600 mr-3 hover:text-gray-900"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <div className="flex space-x-4">
            <button 
              className={`px-4 py-1 ${activeTab === 'design' ? 'text-gray-700 border-b-2 border-blue-500 font-medium' : 'text-gray-500 hover:text-gray-700'}`}
              onClick={() => setActiveTab('design')}
            >
              Design
            </button>
            <button
              className={`px-4 py-1 flex items-center ${activeTab === 'preview-desktop' ? 'text-gray-700 border-b-2 border-blue-500 font-medium' : 'text-gray-500 hover:text-gray-700'}`}
              onClick={() => setActiveTab('preview-desktop')}
              onDoubleClick={() => handleEmailPreview('desktop')}
              title="Click to preview in page, double-click to open Gmail-compatible preview in new window"
            >
              <span>Preview</span>
              <svg className="w-5 h-5 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            </button>
            <button
              className={`px-4 py-1 flex items-center ${activeTab === 'preview-mobile' ? 'text-gray-700 border-b-2 border-blue-500 font-medium' : 'text-gray-500 hover:text-gray-700'}`}
              onClick={() => setActiveTab('preview-mobile')}
              onDoubleClick={() => handleEmailPreview('mobile')}
              title="Click to preview in page, double-click to open Gmail-compatible preview in new window"
            >
              <span>Preview</span>
              <svg className="w-5 h-5 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
            </button>
          </div>
        </div>

        {/* Main Design Area - Make sure this area can scroll */}
        <div className="flex-1 overflow-auto">
          {renderMainContent()}
        </div>

        {/* Bottom Action Bar */}
        <div className="bg-white border-t border-gray-200 p-4">
          {/* Error message */}
          {submissionError && (
            <div className="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
              {submissionError}
            </div>
          )}
          
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="confirm-checkbox"
                className="h-4 w-4 text-blue-600 border-gray-300 rounded"
              />
              <label htmlFor="confirm-checkbox" className="ml-2 text-sm text-gray-700">
                I acknowledge that I have reviewed all changes to be accurate and understand that once I press the "Announce" button below, emails will be sent out and I will not be able to make any further modifications.
              </label>
            </div>
            <div className="flex space-x-3">
              <button
                onClick={handleBack}
                disabled={isSubmitting}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Back
              </button>


              <button
                onClick={handleSaveForLater}
                disabled={isSubmitting}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? 'Saving...' : 'Save for Later'}
              </button>
              <button
                onClick={handleSendPreview}
                disabled={isSubmitting}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Send Email Preview
              </button>
              <button
                onClick={handleAnnounce}
                disabled={isSubmitting}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Announce
              </button>
            </div>
          </div>
        </div>
      </div>
      
      {/* Edit Dialog */}
      {renderEditDialog()}
      
      {/* Email Preview Dialog */}
      {renderPreviewDialog()}
    </div>
  );
}

export default CampaignDesign; 