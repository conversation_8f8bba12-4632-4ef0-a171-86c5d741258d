/* Rich text editor styles */
.quill-editor {
  font-family: 'Arial', sans-serif;
}

/* Styled editor with separated toolbar */
.styled-quill-editor {
  font-family: 'Arial', sans-serif;
  width: 100%;
}

/* 移除隐藏工具栏的规则，因为我们使用自定义工具栏 */
.styled-quill-editor .ql-container {
  font-size: 16px;
  border: none !important; /* Remove default borders */
}

/* Ensure the editor content area can receive custom styles */
.styled-quill-editor .ql-editor {
  transition: all 0.3s ease;
  padding: 0;
  min-height: 150px;
  width: 100%;
}

/* Apply consistent line height to Quill editor */
.ql-editor p {
  margin-top: 0;
  margin-bottom: 0.5em;
  line-height: 1.4;
}

.ql-editor h1,
.ql-editor h2,
.ql-editor h3,
.ql-editor h4,
.ql-editor h5,
.ql-editor h6 {
  margin-top: 0;
  margin-bottom: 0.5em;
  line-height: 1.2;
}

.ql-editor ul,
.ql-editor ol {
  margin-top: 0;
  margin-bottom: 0.5em;
}

.ql-editor li {
  margin-bottom: 0.2em;
}

.ql-editor blockquote {
  margin-top: 0;
  margin-bottom: 0.5em;
}

/* Custom toolbar styles */
#toolbar {
  border: none !important;
}

/* 确保自定义工具栏正常显示 */
#toolbar.ql-toolbar {
  display: flex;
  flex-wrap: wrap;
  padding: 8px;
  background-color: #f9fafb;
}

#toolbar .ql-formats {
  margin-right: 15px;
  margin-bottom: 5px;
}

/* Rich text content styles */
.rich-text-content {
  font-family: 'Arial', sans-serif;
  width: 100%;
  /* 默认不设置padding，让内联样式控制 */
}

/* Import Quill styles to ensure consistent rendering */
.rich-text-content.ql-editor {
  white-space: normal;
  font-family: 'Arial', sans-serif;
  /* 移除默认padding，让内联样式控制 */
  padding: 0;
}

/* 移除这些规则，让内联样式直接控制padding */

/* Ensure Quill formatting is preserved */
.rich-text-content.ql-editor h1,
.rich-text-content.ql-editor h2,
.rich-text-content.ql-editor h3,
.rich-text-content.ql-editor h4,
.rich-text-content.ql-editor h5,
.rich-text-content.ql-editor h6 {
  margin-top: 0;
  margin-bottom: 0.5em;
}

.rich-text-content.ql-editor p {
  margin-top: 0;
  margin-bottom: 0.5em;
}

/* Line height specific styles */
.rich-text-content.ql-editor[style*="line-height"] p,
.rich-text-content.ql-editor[style*="line-height"] h1,
.rich-text-content.ql-editor[style*="line-height"] h2,
.rich-text-content.ql-editor[style*="line-height"] h3,
.rich-text-content.ql-editor[style*="line-height"] h4,
.rich-text-content.ql-editor[style*="line-height"] h5,
.rich-text-content.ql-editor[style*="line-height"] h6,
.rich-text-content.ql-editor[style*="line-height"] li,
.rich-text-content.ql-editor[style*="line-height"] blockquote {
  line-height: inherit !important;
}

/* Force line height on all text elements in the editor */
.ql-editor p,
.ql-editor h1,
.ql-editor h2,
.ql-editor h3,
.ql-editor h4,
.ql-editor h5,
.ql-editor h6,
.ql-editor li,
.ql-editor blockquote {
  line-height: inherit !important;
}

.rich-text-content.ql-editor ul,
.rich-text-content.ql-editor ol {
  padding-left: 1.5em;
  margin-top: 0;
  margin-bottom: 0.5em;
}

.rich-text-content.ql-editor li {
  margin-bottom: 0.2em;
}

.rich-text-content.ql-editor blockquote {
  border-left: 4px solid #e2e8f0;
  padding-left: 1em;
  margin-top: 0;
  margin-bottom: 0.5em;
}

/* Ensure text formatting is preserved */
.rich-text-content.ql-editor strong,
.rich-text-content.ql-editor b {
  font-weight: bold !important;
}

.rich-text-content.ql-editor em,
.rich-text-content.ql-editor i {
  font-style: italic !important;
}

.rich-text-content.ql-editor u {
  text-decoration: underline !important;
}

.rich-text-content.ql-editor s {
  text-decoration: line-through !important;
}

/* Alignment classes */
.rich-text-content.ql-editor .ql-align-center {
  text-align: center !important;
}

.rich-text-content.ql-editor .ql-align-right {
  text-align: right !important;
}

.rich-text-content.ql-editor .ql-align-justify {
  text-align: justify !important;
}

/* List styles */
.rich-text-content.ql-editor ul {
  list-style-type: disc !important;
}

.rich-text-content.ql-editor ol {
  list-style-type: decimal !important;
}

/* Ensure line height is respected */
.rich-text-content.ql-editor * {
  line-height: inherit !important;
}

/* Quill editor styles */
.ql-editor {
  font-family: 'Arial', sans-serif;
}

.ql-editor p,
.ql-editor h1,
.ql-editor h2,
.ql-editor h3,
.ql-editor h4,
.ql-editor h5,
.ql-editor h6,
.ql-editor ul,
.ql-editor ol,
.ql-editor blockquote {
  margin-bottom: 0.5em;
}

/* Custom toolbar styles */
#toolbar {
  border: none !important;
}

/* 确保自定义工具栏正常显示 */
#toolbar.ql-toolbar {
  display: flex;
  flex-wrap: wrap;
  padding: 8px;
  background-color: #f9fafb;
}

#toolbar .ql-formats {
  margin-right: 15px;
  margin-bottom: 5px;
}

.rich-text-content h1 {
  font-size: 2em;
  font-weight: bold;
  margin-bottom: 0.5em;
}

.rich-text-content h2 {
  font-size: 1.5em;
  font-weight: bold;
  margin-bottom: 0.5em;
}

.rich-text-content h3 {
  font-size: 1.17em;
  font-weight: bold;
  margin-bottom: 0.5em;
}

.rich-text-content p {
  margin-bottom: 1em;
}

.rich-text-content ul, 
.rich-text-content ol {
  margin-left: 2em;
  margin-bottom: 1em;
}

.rich-text-content ul {
  list-style-type: disc;
}

.rich-text-content ol {
  list-style-type: decimal;
}

.rich-text-content li {
  margin-bottom: 0.5em;
}

.rich-text-content blockquote {
  border-left: 4px solid #e2e8f0;
  padding-left: 1em;
  margin-left: 0;
  margin-right: 0;
  font-style: italic;
  color: #4a5568;
}

/* Ensure rich text content displays correctly in preview */
.rich-text-content * {
  max-width: 100%;
}

/* Fix blockquote styles */
.rich-text-content .ql-blockquote {
  border-left: 4px solid #e2e8f0;
  padding-left: 1em;
  margin: 1em 0;
}

/* Fix list styles */
.rich-text-content .ql-list {
  padding-left: 1.5em;
}

/* Style editor related styles */
.color-option {
  width: 2rem;
  height: 2rem;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s;
}

.color-option.selected {
  box-shadow: 0 0 0 2px white, 0 0 0 4px #3b82f6;
}

.style-preview {
  transition: all 0.3s ease;
  box-sizing: border-box;
}

/* Style selector styles */
.style-selector {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(2rem, 1fr));
  gap: 0.5rem;
}

/* Border style preview */
.border-style-preview {
  height: 0.25rem;
  width: 100%;
  margin-top: 0.5rem;
}

.border-style-preview.none {
  border-top: none;
}

.border-style-preview.solid {
  border-top: 2px solid #374151;
}

.border-style-preview.dashed {
  border-top: 2px dashed #374151;
}

.border-style-preview.dotted {
  border-top: 2px dotted #374151;
}

.border-style-preview.double {
  border-top: 2px double #374151;
}

/* Size option styles */
.size-option {
  padding: 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
}

.size-option.selected {
  background-color: #ebf5ff;
  border-color: #3b82f6;
  color: #3b82f6;
}

/* Styled editor container */
.styled-editor-container {
  overflow: hidden;
  border-width: 1px;
  border-style: solid;
  border-color: #e2e8f0;
  border-radius: 0.375rem;
}

.styled-editor-container .ql-toolbar.ql-snow {
  border-top: none;
  border-left: none;
  border-right: none;
}

.styled-editor-container .ql-container.ql-snow {
  border: none;
} 