import React, { useState, useEffect } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import RecipientSelector from '../../features/marketing/components/RecipientSelector'
import { useMarketingApi } from '../../features/marketing/services/marketingApi'
import { 
  getCampaignTypeId, 
  getCampaignTypeString, 
  getCampaignTypeDisplayName 
} from '../../features/marketing/constants/campaignTypes'

console.log('CreateCampaign module loaded');

function CreateCampaign() {
  console.log('CreateCampaign rendering');
  
  const navigate = useNavigate();
  const marketingApi = useMarketingApi();
  
  const [deliveryMethod, setDeliveryMethod] = useState('email');
  const [campaignSubject, setCampaignSubject] = useState('');
  const [campaignType, setCampaignType] = useState('email-blast');
  const [scheduleOption, setScheduleOption] = useState('now');
  const [scheduleDate, setScheduleDate] = useState('');
  const [scheduleTime, setScheduleTime] = useState('');
  const [postToFacebook, setPostToFacebook] = useState(false);
  const [recipientType, setRecipientType] = useState('all');
  const [showFilteredModal, setShowFilteredModal] = useState(false);
  const [minAge, setMinAge] = useState(0);
  const [maxAge, setMaxAge] = useState(110);
  const [daysBefore, setDaysBefore] = useState(0);
  const [weeksLost, setWeeksLost] = useState(4);
  const [daysBeforeVisit, setDaysBeforeVisit] = useState(1);
  const [daysAfterVisit, setDaysAfterVisit] = useState(0);
  const [currentStep, setCurrentStep] = useState('setup'); // 'setup' or 'design'
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submissionError, setSubmissionError] = useState(null);
  const [confirmChecked, setConfirmChecked] = useState(false);
  
  // Add RecipientSelector related states
  const [showRecipientSelector, setShowRecipientSelector] = useState(false);
  const [recipientSelectorType, setRecipientSelectorType] = useState('customers');
  const [selectedCustomers, setSelectedCustomers] = useState([]);
  const [selectedEmployees, setSelectedEmployees] = useState([]);
  
  // Add state for opted out users
  const [showOptedOutModal, setShowOptedOutModal] = useState(false);
  const [optedOutUsers, setOptedOutUsers] = useState([]);
  const [loadingOptedOut, setLoadingOptedOut] = useState(false);
  
  // Fetch opted out users on mount
  useEffect(() => {
    const fetchOptedOutUsers = async () => {
      setLoadingOptedOut(true);
      try {
        const data = await marketingApi.getOptedOutUsers();
        setOptedOutUsers(data || []);
      } catch (error) {
        console.error('Error fetching opted out users:', error);
      } finally {
        setLoadingOptedOut(false);
      }
    };
    
    fetchOptedOutUsers();
  }, []);
  
  // Debug mount
  useEffect(() => {
    console.log('CreateCampaign mounted');
    
    // if there is a campaign id in the url, fetch the campaign data
    const fetchCampaignData = async (id) => {
      try {
        console.log('Fetching campaign data for ID:', id);
        setIsSubmitting(true);
        
        // make sure the id is a number
        const numericId = parseInt(id, 10);
        if (isNaN(numericId)) {
          console.error('Invalid campaign ID:', id);
          setSubmissionError('Invalid campaign ID');
          return;
        }
        
        console.log('Calling marketingApi.getCampaign with ID:', numericId);
        const response = await marketingApi.getCampaign(numericId);
        console.log('Campaign data fetched:', response);
        
        if (!response) {
          console.error('No response data received');
          setSubmissionError('Failed to load campaign data. No data received.');
          return;
        }
        
        // set campaign subject
        setCampaignSubject(response.name || '');
        console.log('Set campaign subject to:', response.name);
        
        // set campaign type
        if (response.campaign_type) {
          console.log('Campaign type from API:', response.campaign_type);
          // make sure campaign_type is a number
          const campaignTypeId = typeof response.campaign_type === 'object' ? 
            response.campaign_type.id : 
            parseInt(response.campaign_type, 10);
          
          console.log('Converted campaign type ID:', campaignTypeId);
          const typeString = getCampaignTypeString(campaignTypeId);
          console.log('Converted to type string:', typeString);
          
          if (typeString) {
            setCampaignType(typeString);
            
            // set delivery method based on campaign type
            if (typeString === 'email-blast') {
              setDeliveryMethod('email');
            } else if (typeString === 'text-blast') {
              setDeliveryMethod('text');
            } else if (typeString === 'email-text') {
              setDeliveryMethod('email-text');
            }
            console.log('Set delivery method based on type:', typeString);
          } else {
            console.error('Failed to convert campaign type ID to string:', campaignTypeId);
          }
          
          // set campaign type specific settings
          if (typeString === 'birthday' && response.offset_value !== undefined) {
            setDaysBefore(response.offset_value);
          } else if (typeString === 'lost-customer' && response.offset_value !== undefined) {
            setWeeksLost(response.offset_value);
          } else if (typeString === 'before-visit' && response.offset_value !== undefined) {
            setDaysBeforeVisit(response.offset_value);
          } else if (typeString === 'after-visit' && response.offset_value !== undefined) {
            setDaysAfterVisit(response.offset_value);
          }
          console.log('Set offset value if applicable');
        }
        
        // set schedule information
        if (response.scheduled_time) {
          console.log('Scheduled time from API:', response.scheduled_time);
          const scheduledDate = new Date(response.scheduled_time);
          console.log('Parsed scheduled date:', scheduledDate);
          
          // get current time, but set seconds and milliseconds to 0 for more accurate comparison
          const now = new Date();
          now.setSeconds(0, 0);
          console.log('Current time (for comparison):', now);
          
          // if the time is now or in the past, set to "now", otherwise set to "scheduled"
          const isNow = now >= scheduledDate || response.status === 'completed';
          console.log('Is scheduled time in the past or completed?', isNow);
          setScheduleOption(isNow ? 'now' : 'scheduled');
          console.log('Set schedule option to:', isNow ? 'now' : 'scheduled');
          
          // set date and time for both now and scheduled, so the user can see or modify
          // format date to YYYY-MM-DD format
          let dateToUse = isNow ? now : scheduledDate;
          const year = dateToUse.getFullYear();
          const month = (dateToUse.getMonth() + 1).toString().padStart(2, '0');
          const day = dateToUse.getDate().toString().padStart(2, '0');
          const formattedDate = `${year}-${month}-${day}`;
          console.log('Formatted date to set:', formattedDate);
          setScheduleDate(formattedDate);
          console.log('Set schedule date to:', formattedDate);
          
          // format time to HH:MM format
          const hours = dateToUse.getHours().toString().padStart(2, '0');
          const minutes = dateToUse.getMinutes().toString().padStart(2, '0');
          const formattedTime = `${hours}:${minutes}`;
          console.log('Formatted time to set:', formattedTime);
          setScheduleTime(formattedTime);
          console.log('Set schedule time to:', formattedTime);
        } else {
          console.warn('No scheduled_time in response');
        }
        
          // set channel information
        if (response.channels && response.channels.length > 0) {
          console.log('Channels from API:', response.channels);
          // check if there is an email channel
          const emailChannel = response.channels.find(c => c.channel_type === 'email');
          // check if there is a text channel
          const textChannel = response.channels.find(c => c.channel_type === 'text');
          
          if (emailChannel && textChannel) {
            setDeliveryMethod('email-text');
          } else if (emailChannel) {
            setDeliveryMethod('email');
          } else if (textChannel) {
            setDeliveryMethod('text');
          }
          console.log('Set delivery method based on channels');
        }
        
        // set recipients
        if (response.recipients) {
          console.log('Recipients from API:', response.recipients);
          // get recipient details
          try {
            console.log('Fetching customers and employees for recipients');
            const customersResponse = await marketingApi.getCustomers();
            const employeesResponse = await marketingApi.getEmployees();
            
            console.log('Customers response:', customersResponse);
            console.log('Employees response:', employeesResponse);
            
            if (customersResponse && employeesResponse) {
              // use the recipients field returned by the backend, which is an array of IDs
              const recipientIds = Array.isArray(response.recipients) ? response.recipients : [];
              
              console.log('Recipient IDs from API:', recipientIds);
              
              // filter out recipients that belong to the campaign
              const selectedCustomers = customersResponse.filter(
                customer => recipientIds.includes(customer.id)
              );
              const selectedEmployees = employeesResponse.filter(
                employee => recipientIds.includes(employee.id)
              );
              
              console.log('Selected customers:', selectedCustomers);
              console.log('Selected employees:', selectedEmployees);
              
              setSelectedCustomers(selectedCustomers);
              setSelectedEmployees(selectedEmployees);
              
              // set recipient type
              if (selectedCustomers.length > 0 || selectedEmployees.length > 0) {
                setRecipientType('specific');
                console.log('Set recipient type to specific');
              }
            }
          } catch (error) {
            console.error('Error fetching recipients:', error);
          }
        }
        
        // set other information
        console.log('Post to Facebook from API:', response.post_to_facebook);
        setPostToFacebook(response.post_to_facebook === true);
        console.log('Set post to Facebook to:', response.post_to_facebook === true);
        
        // if in edit mode, set current step to setup
        setCurrentStep('setup');
        console.log('Set current step to setup');
      } catch (error) {
        console.error('Error fetching campaign:', error);
        
        // extract more detailed error information
        let errorMessage = 'Failed to load campaign data. Please try again.';
        if (error.response) {
          console.error('Error response:', error.response);
          if (error.response.data) {
            console.error('Error response data:', error.response.data);
            if (typeof error.response.data === 'string') {
              errorMessage = error.response.data;
            } else if (typeof error.response.data === 'object') {
              errorMessage = JSON.stringify(error.response.data);
            }
          }
          errorMessage += ` (Status: ${error.response.status})`;
        } else if (error.message) {
          errorMessage = error.message;
        }
        
        setSubmissionError(errorMessage);
      } finally {
        setIsSubmitting(false);
      }
    };
    
    // if there is a campaign id in the url, fetch the campaign data
    const params = new URLSearchParams(window.location.search);
    const campaignId = params.get('id');
    if (campaignId) {
      console.log('Found campaign ID in URL:', campaignId);
      fetchCampaignData(campaignId);
    }
    
    return () => {
      console.log('CreateCampaign unmounting');
    };
  }, []);
  
  // Update campaignType when deliveryMethod changes
  useEffect(() => {
    console.log('deliveryMethod changed to:', deliveryMethod);
    
    // only update campaignType in edit mode
    const params = new URLSearchParams(window.location.search);
    const campaignId = params.get('id');
    
    // if in edit mode, do not auto-update campaignType
    if (campaignId) {
      console.log('In edit mode, not auto-updating campaignType');
      return;
    }
    
    // Set default campaign type based on delivery method
    let newCampaignType = campaignType;
    
    if (deliveryMethod === 'email') {
      newCampaignType = 'email-blast';
    } else if (deliveryMethod === 'text') {
      newCampaignType = 'text-blast';
    } else if (deliveryMethod === 'email-text') {
      newCampaignType = 'email-text';
    }
    
    console.log('Setting campaignType to:', newCampaignType);
    setCampaignType(newCampaignType);
  }, [deliveryMethod]);
  
  // Debug recipient selector state
  useEffect(() => {
    console.log('Recipient selector state changed:', { 
      showRecipientSelector, 
      recipientSelectorType,
      selectedCustomers: selectedCustomers.length,
      selectedEmployees: selectedEmployees.length
    });
  }, [showRecipientSelector, recipientSelectorType, selectedCustomers, selectedEmployees]);
  
  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return '';
    
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };
  
  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Check if in edit mode
    const params = new URLSearchParams(window.location.search);
    const campaignId = params.get('id');
    
    // Prepare channels based on delivery method
    const channels = [];
    if (deliveryMethod === 'email' || deliveryMethod === 'email-text') {
      channels.push({
        channel_type: 'email',
        subject: campaignSubject,
        content: '',  // Will be populated in design step
        raw_components: [],  // 初始化为空数组，将在设计步骤中填充
        is_automatic: scheduleOption === 'now'
      });
    }
    
    if (deliveryMethod === 'text' || deliveryMethod === 'email-text') {
      channels.push({
        channel_type: 'text',
        subject: '',
        content: '',  // Will be populated in design step
        is_automatic: scheduleOption === 'now'
      });
    }
    
    // Store campaign data in session storage or state management
    const campaignData = {
      deliveryMethod,
      campaignSubject,
      campaignType,
      campaignTypeId: getCampaignTypeId(campaignType), // add ID mapping
      scheduleOption,
      scheduleDate,
      scheduleTime,
      postToFacebook,
      recipientType,
      selectedCustomers,
      selectedEmployees,
      channels, // 添加channels信息
      // Include campaign type specific settings
      ...(campaignType === 'birthday' && daysBefore !== undefined && { daysBefore }),
      ...(campaignType === 'lost-customer' && weeksLost !== undefined && { weeksLost }),
      ...(campaignType === 'before-visit' && daysBeforeVisit !== undefined && { daysBeforeVisit }),
      ...(campaignType === 'after-visit' && daysAfterVisit !== undefined && { daysAfterVisit }),
      // Include campaign ID if in edit mode
      ...(campaignId && { campaignId: parseInt(campaignId, 10) })
    };
    
    // 添加调试日志
    console.log('Storing campaign data in session storage:', campaignData);
    console.log('Channels being passed:', channels);
    
    // Save to session storage for persistence between pages
    sessionStorage.setItem('campaignData', JSON.stringify(campaignData));
    
    // Navigate to the design page with campaign ID if in edit mode
    const designUrl = campaignId ? `/marketing/campaign-design?id=${campaignId}` : '/marketing/campaign-design';
    navigate(designUrl);
  };
  
  const handleRecipientTypeChange = (type) => {
    setRecipientType(type);
    if (type === 'filtered') {
      setShowFilteredModal(true);
    }
  };

  // Handle edit recipients
  const handleEditRecipients = (type) => {
    console.log('handleEditRecipients called with type:', type);
    
    try {
      setRecipientSelectorType(type);
      console.log('Set recipientSelectorType to:', type);
      
      setShowRecipientSelector(true);
      console.log('Set showRecipientSelector to true');
      
      console.log('Current selected items:', type === 'customers' 
        ? selectedCustomers 
        : selectedEmployees);
    } catch (error) {
      console.error('Error in handleEditRecipients:', error);
    }
  };

  // Save selected recipients
  const handleSaveRecipients = (selectedItems) => {
    console.log('handleSaveRecipients called with items:', selectedItems);
    
    try {
      if (recipientSelectorType === 'customers') {
        console.log('Updating selectedCustomers');
        setSelectedCustomers(selectedItems);
      } else {
        console.log('Updating selectedEmployees');
        setSelectedEmployees(selectedItems);
      }
      
      setShowRecipientSelector(false);
      console.log('Set showRecipientSelector to false');
    } catch (error) {
      console.error('Error in handleSaveRecipients:', error);
    }
  };

  // Prepare campaign data for API
  const prepareCampaignData = () => {
    // check if in edit mode
    const params = new URLSearchParams(window.location.search);
    const campaignId = params.get('id');
    
    console.log('prepareCampaignData called with:', {
      scheduleOption,
      scheduleDate,
      scheduleTime,
      postToFacebook
    });
    
    // Calculate scheduled time based on option
    let scheduledDateTime = new Date();
    
    if (scheduleOption === 'scheduled' && scheduleDate) {
      scheduledDateTime = new Date(scheduleDate);
      if (scheduleTime) {
        const [hours, minutes] = scheduleTime.split(':');
        scheduledDateTime.setHours(parseInt(hours, 10), parseInt(minutes, 10));
      }
      console.log('Calculated scheduled date/time:', scheduledDateTime);
    } else {
      console.log('Using current time for scheduled_time:', scheduledDateTime);
    }
    
    // Format the date according to the required format: YYYY-MM-DDThh:mm:ss+HHMM
    // Get timezone offset in minutes, convert to HHMM format
    const tzOffset = scheduledDateTime.getTimezoneOffset();
    const tzOffsetHours = Math.abs(Math.floor(tzOffset / 60)).toString().padStart(2, '0');
    const tzOffsetMinutes = Math.abs(tzOffset % 60).toString().padStart(2, '0');
    const tzOffsetSign = tzOffset <= 0 ? '+' : '-'; // Note: getTimezoneOffset returns negative for positive timezone
    const tzOffsetFormatted = `${tzOffsetSign}${tzOffsetHours}${tzOffsetMinutes}`;
    
    // Format the date
    const year = scheduledDateTime.getFullYear();
    const month = (scheduledDateTime.getMonth() + 1).toString().padStart(2, '0');
    const day = scheduledDateTime.getDate().toString().padStart(2, '0');
    const hours = scheduledDateTime.getHours().toString().padStart(2, '0');
    const minutes = scheduledDateTime.getMinutes().toString().padStart(2, '0');
    const seconds = scheduledDateTime.getSeconds().toString().padStart(2, '0');
    
    const formattedDateTime = `${year}-${month}-${day}T${hours}:${minutes}:${seconds}${tzOffsetFormatted}`;
    console.log('Formatted scheduled_time:', formattedDateTime);
    
    // Get recipient IDs
    const recipientIds = [
      ...selectedCustomers.map(customer => customer.id),
      ...selectedEmployees.map(employee => employee.id)
    ];
    
    // Prepare channels based on delivery method
    const channels = [];
    if (deliveryMethod === 'email' || deliveryMethod === 'email-text') {
      channels.push({
        channel_type: 'email',
        subject: campaignSubject,
        content: '',  // Will be populated in design step
        raw_components: [],  // 初始化为空数组，将在设计步骤中填充
        is_automatic: scheduleOption === 'now'
      });
    }
    
    if (deliveryMethod === 'text' || deliveryMethod === 'email-text') {
      channels.push({
        channel_type: 'text',
        subject: '',
        content: '',  // Will be populated in design step
        is_automatic: scheduleOption === 'now'
      });
    }
    
    // Prepare campaign data
    const campaignData = {
      name: campaignSubject || 'Untitled Campaign',
      description: '',
      campaign_type: getCampaignTypeId(campaignType), // use getCampaignTypeId to convert to backend ID
      scheduled_time: formattedDateTime, // use formatted date time
      active: true,
      channels: channels,
      recipients: recipientIds,
      post_to_facebook: postToFacebook
    };
    
    console.log('Final campaign data prepared:', campaignData);
    
    // if in edit mode, add ID
    if (campaignId) {
      campaignData.id = parseInt(campaignId, 10);
    }
    
    // Add campaign type specific settings
    if (campaignType === 'birthday' && daysBefore !== undefined) {
      campaignData.offset_value = daysBefore;
    } else if (campaignType === 'lost-customer' && weeksLost !== undefined) {
      campaignData.offset_value = weeksLost;
    } else if (campaignType === 'before-visit' && daysBeforeVisit !== undefined) {
      campaignData.offset_value = daysBeforeVisit;
    } else if (campaignType === 'after-visit' && daysAfterVisit !== undefined) {
      campaignData.offset_value = daysAfterVisit;
    }
    
    return campaignData;
  };
  
  // Validate form data
  const validateForm = () => {
    // validate campaign subject
    if (!campaignSubject || campaignSubject.trim() === '') {
      setSubmissionError('Please enter a campaign subject.');
      return false;
    }
    
    // validate recipients
    if (selectedCustomers.length === 0 && selectedEmployees.length === 0) {
      setSubmissionError('Please select at least one recipient.');
      return false;
    }
    
    // validate schedule time
    if (scheduleOption === 'scheduled') {
      if (!scheduleDate) {
        setSubmissionError('Please select a schedule date.');
        return false;
      }
      if (!scheduleTime) {
        setSubmissionError('Please select a schedule time.');
        return false;
      }
    }
    
    return true;
  };

  // Save campaign for later (draft)
  const handleSaveForLater = async () => {
    setIsSubmitting(true);
    setSubmissionError(null);
    
    // validate form
    if (!validateForm()) {
      setIsSubmitting(false);
      return;
    }
    
    try {
      const campaignData = prepareCampaignData();
      console.log('Saving campaign as draft:', campaignData);
      
      // check if in edit mode
      const params = new URLSearchParams(window.location.search);
      const campaignId = params.get('id');
      
      let result;
      if (campaignId) {
        // edit mode - update existing campaign
        result = await marketingApi.updateCampaign(campaignId, campaignData);
        console.log('Campaign updated as draft:', result);
      } else {
        // create mode - create new campaign
        result = await marketingApi.saveCampaignForLater(campaignData);
        console.log('Campaign saved as draft:', result);
      }
      
      // Navigate to campaigns list
      navigate('/marketing/campaigns', { 
        state: { 
          message: campaignId ? 'Campaign updated successfully as draft.' : 'Campaign saved successfully as draft.' 
        } 
      });
    } catch (error) {
      console.error('Error saving campaign:', error);
      
        // extract detailed error information
      let errorMessage = 'Failed to save campaign. Please try again.';
      if (error.response && error.response.data) {
        const errorData = error.response.data;
        if (typeof errorData === 'object') {
          // try to extract detailed information from the error object
          const errorDetails = Object.entries(errorData)
            .map(([field, errors]) => `${field}: ${Array.isArray(errors) ? errors.join(', ') : errors}`)
            .join('; ');
          
          if (errorDetails) {
            errorMessage = `Validation error: ${errorDetails}`;
          }
        } else if (typeof errorData === 'string') {
          errorMessage = errorData;
        }
      }
      
      setSubmissionError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Announce campaign (scheduled)
  const handleAnnounce = async () => {
    setIsSubmitting(true);
    setSubmissionError(null);
    
    // validate form
    if (!validateForm()) {
      setIsSubmitting(false);
      return;
    }
    
    try {
      const campaignData = prepareCampaignData();
      console.log('Announcing campaign:', campaignData);
      
      // check if in edit mode
      const params = new URLSearchParams(window.location.search);
      const campaignId = params.get('id');
      
      let result;
      if (campaignId) {
        // edit mode - update existing campaign
        // set status to scheduled
        campaignData.status = 'scheduled';
        result = await marketingApi.updateCampaign(campaignId, campaignData);
        console.log('Campaign updated and announced:', result);
      } else {
        // create mode - create new campaign
        result = await marketingApi.announceCampaign(campaignData);
        console.log('Campaign announced:', result);
      }
      
      // Navigate to campaigns list
      navigate('/marketing/campaigns', { 
        state: { 
          message: campaignId ? 'Campaign updated and scheduled successfully!' : 'Campaign scheduled successfully!' 
        } 
      });
    } catch (error) {
      console.error('Error announcing campaign:', error);
      
      // extract detailed error information
      let errorMessage = 'Failed to announce campaign. Please try again.';
      if (error.response && error.response.data) {
        const errorData = error.response.data;
        if (typeof errorData === 'object') {
          // try to extract detailed information from the error object
          const errorDetails = Object.entries(errorData)
            .map(([field, errors]) => `${field}: ${Array.isArray(errors) ? errors.join(', ') : errors}`)
            .join('; ');
          
          if (errorDetails) {
            errorMessage = `Validation error: ${errorDetails}`;
          }
        } else if (typeof errorData === 'string') {
          errorMessage = errorData;
        }
      }
      
      setSubmissionError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Design components for the left sidebar
  const designComponents = [
    { id: 'heading', name: 'Heading', icon: 'H1' },
    { id: 'header-image', name: 'Header Image', icon: '🖼️' },
    { id: 'divider', name: 'Divider', icon: '—' },
    { id: 'text', name: 'Text', icon: '¶' },
    { id: 'image', name: 'Image', icon: '🖼️' },
    { id: 'two-images', name: 'Two Images', icon: '🖼️🖼️' },
    { id: 'three-images', name: 'Three Images', icon: '🖼️🖼️🖼️' },
    { id: 'image-text', name: 'Image & Text', icon: '🖼️¶' },
    { id: 'text-image', name: 'Text & Image', icon: '¶🖼️' },
    { id: 'image-on-text', name: 'Image On Text', icon: '🖼️/¶' },
    { id: 'text-on-image', name: 'Text On Image', icon: '¶/🖼️' },
    { id: 'buttons', name: 'Buttons', icon: '🔘' },
    { id: 'social-buttons', name: 'Social Buttons', icon: '📱' },
    { id: 'footer-image', name: 'Footer Image', icon: '🖼️' },
  ];
  
  // Available email templates
  const emailTemplates = [
    { id: 'after-visit', name: 'After Visit', thumbnail: '/templates/after-visit.jpg' },
    { id: 'animated', name: 'Animated', thumbnail: '/templates/animated.jpg' },
    { id: 'announcements', name: 'Announcements', thumbnail: '/templates/announcements.jpg' },
    { id: 'barber', name: 'Barber', thumbnail: '/templates/barber.jpg' },
    { id: 'before-visit', name: 'Before Visit', thumbnail: '/templates/before-visit.jpg' },
    { id: 'black-friday', name: 'Black Friday', thumbnail: '/templates/black-friday.jpg' },
    { id: 'book-now', name: 'Book Now', thumbnail: '/templates/book-now.jpg' },
    { id: 'christmas', name: 'Christmas', thumbnail: '/templates/christmas.jpg' },
    { id: 'classic', name: 'Classic', thumbnail: '/templates/classic.jpg' },
    { id: 'cyber-monday', name: 'Cyber Monday', thumbnail: '/templates/cyber-monday.jpg' },
    { id: 'espanol', name: 'Español', thumbnail: '/templates/espanol.jpg' },
  ];
  
  // Render campaign setup form
  const renderSetupForm = () => (
    <div className="h-full bg-gray-50 overflow-y-auto">
      <div className="p-6">
        <div className="mb-6 flex items-center">
          <Link to="/marketing/campaigns" className="text-gray-600 mr-3">
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </Link>
          <h1 className="text-2xl font-semibold text-gray-900">New Campaign</h1>
        </div>
        
        <div className="space-y-6">
          {/* Select Delivery Method */}
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-medium text-gray-900">Select Delivery Method</h2>
            
              <div className="flex space-x-4">
              <button
                type="button"
                onClick={() => setDeliveryMethod('email')}
                className={`px-6 py-2 border rounded-md ${
                  deliveryMethod === 'email' 
                    ? 'border-blue-500 bg-blue-50 text-blue-700' 
                    : 'border-gray-300 text-gray-700'
                }`}
              >
                Email
              </button>
              <button
                type="button"
                onClick={() => setDeliveryMethod('text')}
                className={`px-6 py-2 border rounded-md ${
                  deliveryMethod === 'text' 
                    ? 'border-blue-500 bg-blue-50 text-blue-700' 
                    : 'border-gray-300 text-gray-700'
                }`}
              >
                Text
              </button>
              <button
                type="button"
                onClick={() => setDeliveryMethod('email-text')}
                className={`px-6 py-2 border rounded-md ${
                  deliveryMethod === 'email-text' 
                    ? 'border-blue-500 bg-blue-50 text-blue-700' 
                    : 'border-gray-300 text-gray-700'
                }`}
              >
                Email and Text
              </button>
            </div>
            </div>
            
            <p className="text-gray-600">Choose how you would like the customers to receive your campaign.</p>
          </div>
          
          {/* Campaign Subject */}
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <h2 className="text-lg font-medium text-gray-900">
                {deliveryMethod === 'email' ? 'Campaign Email Subject' : 
                deliveryMethod === 'text' ? 'Campaign Text Subject' : 
                'Campaign Email and Text Subject'}
              </h2>
              <button type="button" className="ml-2 text-blue-500">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </button>
            </div>
            
            <div className="mt-4 relative max-w-2xl">
              <input
                type="text"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter Your Email Subject"
                value={campaignSubject}
                onChange={(e) => setCampaignSubject(e.target.value)}
              />
            </div>
          </div>
          
          {/* Select Campaign Type */}
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-medium text-gray-900">Select Campaign Type</h2>
            </div>
            
            <p className="text-gray-600">Choose the type of {deliveryMethod === 'email' ? 'email' : deliveryMethod === 'text' ? 'text' : 'message'} you would like to send.</p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4 mt-4">
              {/* display the corresponding Blast type based on deliveryMethod */}
              {deliveryMethod === 'email' && (
                <div className={`border rounded-md p-4 text-center cursor-pointer ${campaignType === 'email-blast' ? 'bg-blue-50 border-blue-500' : 'border-gray-200'}`}
                    onClick={() => setCampaignType('email-blast')}>
                  <div className="flex justify-center mb-2">
                    <div className="relative">
                      <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                      <div className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs">1</div>
                    </div>
                  </div>
                  <div className="font-medium">
                    {getCampaignTypeDisplayName('email-blast')}
                  </div>
                </div>
              )}
              
              {deliveryMethod === 'text' && (
                <div className={`border rounded-md p-4 text-center cursor-pointer ${campaignType === 'text-blast' ? 'bg-blue-50 border-blue-500' : 'border-gray-200'}`}
                    onClick={() => setCampaignType('text-blast')}>
                  <div className="flex justify-center mb-2">
                    <div className="relative">
                      <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                      </svg>
                      <div className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs">2</div>
                    </div>
                  </div>
                  <div className="font-medium">
                    {getCampaignTypeDisplayName('text-blast')}
                  </div>
                </div>
              )}
              
              {deliveryMethod === 'email-text' && (
                <div className={`border rounded-md p-4 text-center cursor-pointer ${campaignType === 'email-text' ? 'bg-blue-50 border-blue-500' : 'border-gray-200'}`}
                    onClick={() => setCampaignType('email-text')}>
                  <div className="flex justify-center mb-2">
                    <div className="relative">
                      <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z M8 10h.01M12 10h.01M16 10h.01" />
                      </svg>
                      <div className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs">3</div>
                    </div>
                  </div>
                  <div className="font-medium">
                    {getCampaignTypeDisplayName('email-text')}
                  </div>
                </div>
              )}
              
              {/* the other four Campaign Type options are always displayed */}
              <div className={`border rounded-md p-4 text-center cursor-pointer ${campaignType === 'birthday' ? 'bg-blue-50 border-blue-500' : 'border-gray-200'}`}
                  onClick={() => setCampaignType('birthday')}>
                <div className="flex justify-center mb-2">
                  <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M21 15.546c-.523 0-1.046.151-1.5.454a2.704 2.704 0 01-3 0 2.704 2.704 0 00-3 0 2.704 2.704 0 01-3 0 2.704 2.704 0 00-3 0 2.701 2.701 0 00-1.5-.454M9 6v2m3-2v2m3-2v2M9 3h.01M12 3h.01M15 3h.01M21 21v-7a2 2 0 00-2-2H5a2 2 0 00-2 2v7h18zm-3-9v-2a2 2 0 00-2-2H8a2 2 0 00-2 2v2h12z" />
                  </svg>
                </div>
                <div className="font-medium">
                  {getCampaignTypeDisplayName('birthday')}
                </div>
              </div>
              
              <div className={`border rounded-md p-4 text-center cursor-pointer ${campaignType === 'lost-customer' ? 'bg-blue-50 border-blue-500' : 'border-gray-200'}`}
                  onClick={() => setCampaignType('lost-customer')}>
                <div className="flex justify-center mb-2">
                  <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                </div>
                <div className="font-medium">
                  {getCampaignTypeDisplayName('lost-customer')}
                </div>
              </div>
              
              <div className={`border rounded-md p-4 text-center cursor-pointer ${campaignType === 'before-visit' ? 'bg-blue-50 border-blue-500' : 'border-gray-200'}`}
                  onClick={() => setCampaignType('before-visit')}>
                <div className="flex justify-center mb-2">
                  <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
                <div className="font-medium">
                  {getCampaignTypeDisplayName('before-visit')}
                </div>
              </div>
              
              <div className={`border rounded-md p-4 text-center cursor-pointer ${campaignType === 'after-visit' ? 'bg-blue-50 border-blue-500' : 'border-gray-200'}`}
                  onClick={() => setCampaignType('after-visit')}>
                <div className="flex justify-center mb-2">
                  <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
                <div className="font-medium">
                  {getCampaignTypeDisplayName('after-visit')}
                </div>
              </div>
            </div>
            
            {/* Campaign Type Specific Settings */}
            {campaignType === 'birthday' && (
              <div className="mt-6 border-t pt-4">
                <h3 className="font-medium text-gray-800 mb-2">Birthday Campaign Settings</h3>
                <div className="max-w-md mx-auto">
                  <label className="block text-sm font-medium text-gray-700 mb-1 text-center">
                    {daysBefore === 0 ? 'On day of birthday' : `${daysBefore} days before birthday`}
                  </label>
                  <div className="relative">
                    <div
                      className="absolute h-2 bg-blue-500 rounded-l-lg"
                      style={{ width: `${(daysBefore / 30) * 100}%` }}
                    />
                    <input
                      type="range"
                      min="0"
                      max="30"
                      value={daysBefore}
                      onChange={(e) => setDaysBefore(parseInt(e.target.value))}
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer relative z-10"
                      style={{
                        background: 'transparent',
                        WebkitAppearance: 'none'
                      }}
                    />
                  </div>
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>On birthday</span>
                    <span>30 days before</span>
                  </div>
                </div>
              </div>
            )}
            
            {campaignType === 'lost-customer' && (
              <div className="mt-6 border-t pt-4">
                <h3 className="font-medium text-gray-800 mb-2">Lost Customer Campaign Settings</h3>
                <div className="max-w-md mx-auto">
                  <label className="block text-sm font-medium text-gray-700 mb-1 text-center">
                    {weeksLost} weeks since last visit
                  </label>
                  <div className="relative">
                    <div
                      className="absolute h-2 bg-blue-500 rounded-l-lg"
                      style={{ width: `${(weeksLost / 24) * 100}%` }}
                    />
                    <input
                      type="range"
                      min="1"
                      max="24"
                      value={weeksLost}
                      onChange={(e) => setWeeksLost(parseInt(e.target.value))}
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer relative z-10"
                      style={{
                        background: 'transparent',
                        WebkitAppearance: 'none'
                      }}
                    />
                  </div>
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>1 week</span>
                    <span>24 weeks</span>
                  </div>
                </div>
              </div>
            )}
            
            {campaignType === 'before-visit' && (
              <div className="mt-6 border-t pt-4">
                <h3 className="font-medium text-gray-800 mb-2">Before Visit Campaign Settings</h3>
                <div className="max-w-md mx-auto">
                  <label className="block text-sm font-medium text-gray-700 mb-1 text-center">
                    {daysBeforeVisit} {daysBeforeVisit === 1 ? 'day' : 'days'} before visit
                  </label>
                  <div className="relative">
                    <div
                      className="absolute h-2 bg-blue-500 rounded-l-lg"
                      style={{ width: `${(daysBeforeVisit / 30) * 100}%` }}
                    />
                    <input
                      type="range"
                      min="1"
                      max="30"
                      value={daysBeforeVisit}
                      onChange={(e) => setDaysBeforeVisit(parseInt(e.target.value))}
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer relative z-10"
                      style={{
                        background: 'transparent',
                        WebkitAppearance: 'none'
                      }}
                    />
                  </div>
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>1 day before</span>
                    <span>30 days before</span>
                  </div>
                </div>
              </div>
            )}
            
            {campaignType === 'after-visit' && (
              <div className="mt-6 border-t pt-4">
                <h3 className="font-medium text-gray-800 mb-2">After Visit Campaign Settings</h3>
                <div className="max-w-md mx-auto">
                  <label className="block text-sm font-medium text-gray-700 mb-1 text-center">
                    {daysAfterVisit === 0 ? 'Immediately after visit' : `${daysAfterVisit} ${daysAfterVisit === 1 ? 'day' : 'days'} after visit`}
                  </label>
                  <div className="relative">
                    <div
                      className="absolute h-2 bg-blue-500 rounded-l-lg"
                      style={{ width: `${(daysAfterVisit / 30) * 100}%` }}
                    />
                    <input
                      type="range"
                      min="0"
                      max="30"
                      value={daysAfterVisit}
                      onChange={(e) => setDaysAfterVisit(parseInt(e.target.value))}
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer relative z-10"
                      style={{
                        background: 'transparent',
                        WebkitAppearance: 'none'
                      }}
                    />
                  </div>
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>Immediately</span>
                    <span>30 days after</span>
                  </div>
                </div>
              </div>
            )}
          </div>
          
          {/* Select Recipients */}
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-medium text-gray-900">Select Recipients</h2>
            
              <div className="inline-flex rounded-md shadow-sm">
                <button
                  type="button"
                  onClick={() => handleRecipientTypeChange('all')}
                  className={`px-6 py-2 text-sm font-medium rounded-l-md ${
                    recipientType === 'all' 
                      ? 'bg-blue-500 text-white' 
                      : 'bg-white text-gray-700 border border-gray-300'
                  }`}
                >
                  All Customers
                </button>
                <button
                  type="button"
                  onClick={() => handleRecipientTypeChange('filtered')}
                  className={`px-6 py-2 text-sm font-medium ${
                    recipientType === 'filtered' 
                      ? 'bg-blue-500 text-white' 
                      : 'bg-white text-gray-700 border-t border-b border-gray-300'
                  }`}
                >
                  Filtered List
                </button>
                <button
                  type="button"
                  onClick={() => handleRecipientTypeChange('specific')}
                  className={`px-6 py-2 text-sm font-medium rounded-r-md ${
                    recipientType === 'specific' 
                      ? 'bg-blue-500 text-white' 
                      : 'bg-white text-gray-700 border border-gray-300'
                  }`}
                >
                  Specific Customers
                </button>
              </div>
            </div>
            
            <p className="text-gray-600 mb-4">Choose your target audience.</p>
            
            <h3 className="font-medium text-gray-800 mb-4">Who Will Receive Your Campaign:</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="border rounded-md p-4 flex justify-between items-center">
                <div>
                  <div className="text-2xl font-bold">{selectedCustomers.length}</div>
                  <div className="text-gray-600">Customers</div>
                </div>
                <button 
                  className="text-blue-500 hover:text-blue-800"
                  onClick={(e) => {
                    e.preventDefault();
                    console.log('Edit button clicked for Customers');
                    handleEditRecipients('customers');
                  }}
                >
                  Edit
                </button>
              </div>
              
              <div className="border rounded-md p-4 flex justify-between items-center">
                <div>
                  <div className="text-2xl font-bold">{selectedEmployees.length}</div>
                  <div className="text-gray-600">Employees</div>
                </div>
                <button 
                  className="text-blue-500 hover:text-blue-800"
                  onClick={(e) => {
                    e.preventDefault();
                    console.log('Edit button clicked for Employees');
                    handleEditRecipients('employees');
                  }}
                >
                  Edit
                </button>
              </div>
              
              <div className="border rounded-md p-4 flex justify-between items-center">
                <div>
                  <div className="text-2xl font-bold">{optedOutUsers.length}</div>
                  <div className="text-gray-600">Opted Out</div>
                </div>
                <button 
                  className="text-blue-500 hover:text-blue-800"
                  onClick={(e) => {
                    e.preventDefault();
                    console.log('See Who button clicked for Opted Out users');
                    setShowOptedOutModal(true);
                  }}
                >
                  See Who
                </button>
              </div>
            </div>
          </div>
          
          {/* Schedule */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-lg font-medium text-gray-900 mb-2">Schedule</h2>
            <p className="text-gray-600 mb-4">Choose when you would like your campaign to be sent out.</p>
            
            <div className="flex justify-end mb-4">
              <div className="border rounded-md overflow-hidden">
                <button 
                  onClick={() => setScheduleOption('now')}
                  className={`px-4 py-2 ${
                    scheduleOption === 'now' ? 'bg-blue-500 text-white' : 'bg-white text-gray-700'
                  }`}
                >
                  Send Now
                </button>
                <button 
                  onClick={() => setScheduleOption('scheduled')}
                  className={`px-4 py-2 ${
                    scheduleOption === 'scheduled' ? 'bg-blue-500 text-white' : 'bg-white text-gray-700'
                  }`}
                >
                  Schedule
                </button>
              </div>
            </div>
            
            {scheduleOption === 'scheduled' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Select Date & Time of Send:</label>
                <div className="grid grid-cols-2 gap-4 max-w-xl">
                <div>
                  <input
                    type="date"
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md"
                    value={scheduleDate}
                    onChange={(e) => setScheduleDate(e.target.value)}
                      lang="en-US"
                  />
                    {scheduleDate && (
                      <p className="mt-1 text-xs text-gray-500">
                        {formatDate(scheduleDate)}
                      </p>
                    )}
                </div>
                <div>
                  <input
                    type="time"
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md"
                    value={scheduleTime}
                    onChange={(e) => setScheduleTime(e.target.value)}
                  />
                  </div>
                </div>
              </div>
            )}
            
            <div className="mt-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <span className="text-gray-700 mr-3">Post On Facebook</span>
                  <button
                    type="button"
                    onClick={() => setPostToFacebook(!postToFacebook)}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                      postToFacebook ? 'bg-blue-500' : 'bg-gray-200'
                    }`}
                  >
                    <span
                      className={`inline-block h-5 w-5 transform rounded-full bg-white transition-transform ${
                        postToFacebook ? 'translate-x-5' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>
                <p className="text-gray-500 text-sm">Post this campaign to your Facebook page.</p>
              </div>
            </div>
          </div>
        </div>
        
        <div className="mt-6 flex justify-end space-x-4 sticky bottom-0 bg-gray-50 py-4">
          <Link 
            to="/marketing/campaigns"
            className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            Cancel
          </Link>
          <button
            className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50"
            onClick={handleSaveForLater}
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Saving...' : 'Save for Later'}
          </button>
          <button
            type="button"
            onClick={handleSubmit}
            className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
          >
            Next
          </button>
        </div>
      </div>

      {/* Filtered List Modal */}
      {showFilteredModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center p-6 border-b">
              <h2 className="text-xl font-semibold text-gray-900">Select Filtered Audience</h2>
              <button 
                onClick={() => setShowFilteredModal(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            {/* Modal content */}
            {/* ... existing code ... */}
            
            <div className="flex justify-end p-6 border-t bg-gray-50">
              <button 
                onClick={() => setShowFilteredModal(false)}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white mr-3"
              >
                Close
              </button>
              <button className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white mr-3">
                Clear Filter
              </button>
              <button 
                className="px-4 py-2 bg-green-600 text-white rounded-md"
                onClick={() => setShowFilteredModal(false)}
              >
                Apply
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  // Render design editor
  const renderDesignEditor = () => (
    <div className="h-full flex flex-col">
      {/* Top Navigation */}
      <div className="bg-white border-b border-gray-200 px-4 py-3 flex items-center justify-between">
        <div className="flex items-center">
          <button 
            onClick={() => setCurrentStep('setup')}
            className="text-gray-600 mr-3 hover:text-gray-900"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <h1 className="text-xl font-semibold text-gray-900">Design Campaign</h1>
        </div>
        <div className="flex space-x-4">
          <button className="px-4 py-1 text-gray-700 border-b-2 border-blue-500 font-medium">
            Templates
          </button>
          <button className="px-4 py-1 text-gray-500 hover:text-gray-700">
            Design
          </button>
          <button className="px-4 py-1 text-gray-500 hover:text-gray-700 flex items-center">
            <span>Preview</span>
            <svg className="w-5 h-5 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </button>
          <button className="px-4 py-1 text-gray-500 hover:text-gray-700 flex items-center">
            <span>Preview</span>
            <svg className="w-5 h-5 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
            </svg>
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex flex-1 overflow-hidden">
        {/* Left Sidebar - Components */}
        <div className="w-64 border-r border-gray-200 bg-white overflow-y-auto">
          <div className="p-4">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Select Email Template:</h2>
            <div className="h-[calc(100vh-200px)] overflow-y-auto">
              <div className="space-y-2">
                {designComponents.map((component) => (
                  <div 
                    key={component.id}
                    className="flex items-center p-2 border border-gray-200 rounded-md hover:bg-gray-50 cursor-pointer"
                  >
                    <div className="w-10 h-10 flex items-center justify-center bg-gray-100 rounded-md mr-3">
                      {component.icon}
                    </div>
                    <span className="text-sm font-medium text-gray-700">{component.name}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Main Design Area */}
        <div className="flex-1 bg-gray-100 overflow-y-auto p-6">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Select Email Template:</h2>
            
            <div className="grid grid-cols-3 gap-4">
              {emailTemplates.map((template) => (
                <div 
                  key={template.id}
                  className={`border rounded-md overflow-hidden cursor-pointer ${
                    selectedTemplate === template.id ? 'ring-2 ring-blue-500' : 'hover:shadow-md'
                  }`}
                  onClick={() => setSelectedTemplate(template.id)}
                >
                  <div className="h-40 bg-gray-200 flex items-center justify-center">
                    <div className="text-center p-4">
                      <div className="text-xl font-serif">Now on Vagaro</div>
                    </div>
                  </div>
                  <div className="p-2 text-center text-sm font-medium">{template.name}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Action Bar */}
      <div className="bg-white border-t border-gray-200 p-4 flex justify-between items-center">
        <div className="flex items-center">
          <input
            type="checkbox"
            id="confirm-checkbox"
            className="h-4 w-4 text-blue-600 border-gray-300 rounded"
            checked={confirmChecked}
            onChange={(e) => setConfirmChecked(e.target.checked)}
          />
          <label htmlFor="confirm-checkbox" className="ml-2 text-sm text-gray-700">
            I acknowledge that I have reviewed all changes to be accurate and understand that once I press the "Announce" button below, emails will be sent out and I will not be able to make any further modifications.
          </label>
        </div>
        <div className="flex space-x-3">
          <button 
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50"
            onClick={() => setCurrentStep('setup')}
            disabled={isSubmitting}
          >
            Back
          </button>
          <button 
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50"
            onClick={handleSaveForLater}
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Saving...' : 'Save for Later'}
          </button>
          <button 
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
            disabled={isSubmitting}
          >
            Send Email Preview
          </button>
          <button 
            className={`px-4 py-2 ${confirmChecked ? 'bg-green-600 hover:bg-green-700' : 'bg-gray-400 cursor-not-allowed'} text-white rounded-md`}
            onClick={handleAnnounce}
            disabled={!confirmChecked || isSubmitting}
          >
            {isSubmitting ? 'Announcing...' : 'Announce'}
          </button>
        </div>
      </div>
      
      {/* Error message */}
      {submissionError && (
        <div className="p-4 bg-red-100 border border-red-400 text-red-700 rounded mt-4">
          {submissionError}
        </div>
      )}
    </div>
  );
  
  return (
    <>
      {currentStep === 'setup' ? renderSetupForm() : renderDesignEditor()}
      
      {/* recipient selector modal */}
      {showRecipientSelector && (
        <RecipientSelector 
          type={recipientSelectorType}
          onClose={() => {
            console.log('RecipientSelector onClose called');
            setShowRecipientSelector(false);
          }}
          onSave={handleSaveRecipients}
          initialSelected={recipientSelectorType === 'customers' 
            ? selectedCustomers 
            : selectedEmployees}
        />
      )}
      
      {/* opted out users modal */}
      {showOptedOutModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] flex flex-col">
            {/* header */}
            <div className="p-6 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <h2 className="text-xl font-semibold text-gray-900">
                  Opted Out Recipients
                </h2>
                <button 
                  onClick={() => setShowOptedOutModal(false)} 
                  className="text-gray-400 hover:text-gray-500"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              <p className="text-sm text-gray-500 mt-1">
                These contacts have opted out of marketing communications and will not receive your campaign.
              </p>
            </div>
            
            {/* main */}
            <div className="flex-1 overflow-hidden flex flex-col">
              {/* list */}
              <div className="flex-1 overflow-y-auto p-4">
                {loadingOptedOut ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    <span className="ml-2">Loading opted out users...</span>
                  </div>
                ) : optedOutUsers.length === 0 ? (
                  <div className="flex items-center justify-center h-40 text-gray-500">
                    No opted out recipients found
                  </div>
                ) : (
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-100">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Name
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Email Address
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Opt-Out Date
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {optedOutUsers.map((user) => (
                        <tr key={user.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900">
                              {user.first_name} {user.last_name}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-500">{user.email}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-500">{formatDate(user.opt_out_date || user.opted_out_date)}</div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                )}
              </div>
            </div>
            
            {/* footer */}
            <div className="p-4 border-t border-gray-200 flex justify-end">
              <button 
                onClick={() => setShowOptedOutModal(false)}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

export default CreateCampaign; 