import React, { useEffect } from 'react'
import MarketingSidebar from '../../features/marketing/components/MarketingSidebar'
import CampaignList from '../../features/marketing/components/CampaignList'

// memoize components
const MarketingSidebarMemo = React.memo(MarketingSidebar)
const CampaignListMemo = React.memo(CampaignList)

function Campaigns() {
  // listen to component mount and unmount
  useEffect(() => {
    console.log('Campaigns component mounted')
    
    return () => {
      console.log('Campaigns component unmounted')
    }
  }, [])
  
  return (
    <div className="h-full">
      <div className="flex flex-col md:flex-row h-full gap-4">
        <div className="w-full md:w-64 flex-shrink-0">
          <MarketingSidebarMemo />
        </div>
        <div className="flex-grow">
          <CampaignListMemo />
        </div>
      </div>
    </div>
  )
}

// memoize the entire component to avoid unnecessary re-renders
export default React.memo(Campaigns) 