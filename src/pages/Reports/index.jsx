import { Link } from "react-router-dom";

function Reports() {
  const reportTypes = [
    {
      title: "Transaction List",
      description: "View detailed transaction records with advanced filtering",
      path: "/reports/transactions",
      icon: "📊",
    },
    {
      title: "Sales Summary",
      description: "Analyze sales performance and revenue trends",
      path: "/reports/sales-summary",
      icon: "💰",
    },
    {
      title: "Services",
      description: "View detailed service reports and performance metrics",
      path: "/reports/services",
      icon: "✂️",
    },
    {
      title: "Booking Percentage",
      description: "Analyze booking efficiency and time utilization",
      path: "/reports/booking-percentage",
      icon: "📅",
    },
    {
      title: "All Reports",
      description: "Access all available reports and analytics",
      path: "/reports/all",
      icon: "📈",
    },
  ];

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <h1 className="text-3xl font-bold text-gray-800 mb-6">Reports</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {reportTypes.map((report) => (
          <Link
            key={report.path}
            to={report.path}
            className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow border border-gray-200 hover:border-blue-300"
          >
            <div className="flex items-center mb-4">
              <span className="text-3xl mr-3">{report.icon}</span>
              <h2 className="text-xl font-semibold text-gray-800">
                {report.title}
              </h2>
            </div>
            <p className="text-gray-600 mb-4">{report.description}</p>
            <div className="text-blue-600 font-medium">View Report →</div>
          </Link>
        ))}
      </div>

      <div className="mt-8 bg-white p-6 rounded-lg shadow-md border border-gray-200">
        <h2 className="text-lg font-semibold text-gray-800 mb-3">
          Quick Stats
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">$0.00</div>
            <div className="text-sm text-gray-600">Today's Revenue</div>
          </div>
          <div className="text-center p-4 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">0</div>
            <div className="text-sm text-gray-600">Transactions Today</div>
          </div>
          <div className="text-center p-4 bg-purple-50 rounded-lg">
            <div className="text-2xl font-bold text-purple-600">0</div>
            <div className="text-sm text-gray-600">New Customers</div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Reports;
