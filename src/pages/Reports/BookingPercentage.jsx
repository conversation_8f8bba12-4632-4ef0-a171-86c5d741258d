import { useState, useEffect, useCallback, useMemo } from "react";
import ReportsFilterPanel from "../../features/reports/components/ReportsFilterPanel";
import useReportsFilter from "../../features/reports/hooks/useReportsFilter";
import { reportsApi } from "../../features/reports/services/reportsApi";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Chart } from 'react-chartjs-2';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

function BookingPercentage() {
  // ========== State Management with Shared Hook ==========
  const {
    filters,
    setFilters,
    filterOptions,
    loading: filterLoading,
    error: filterError,
    setLoading,
    setError,
    getApiParams,
    resetFilters
  } = useReportsFilter();

  // Advanced filter conditions specific to booking percentage report
  const [advancedFilters, setAdvancedFilters] = useState({
    timeRange: "week",
    includeBlockedTime: true,
    includeBreaks: false,
    showOnlyAvailable: false,
    includePastEmployees: false,
  });

  // Auto-determine time range based on selected date range
  const getAutoTimeRange = useCallback(() => {
    if (!filters.startDate || !filters.endDate) return "week";
    
    const startDate = filters.startDate;
    const endDate = filters.endDate;
    const daysDifference = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24)) + 1;
    
    // Case 4: Check if this is a "This Year" or "Last Year" selection (year-long periods)
    const isYearSelection = daysDifference >= 365 || 
      (startDate.getMonth() === 0 && startDate.getDate() === 1 && 
       ((endDate.getMonth() === 11 && endDate.getDate() === 31) || 
        (endDate.getFullYear() === new Date().getFullYear() && endDate <= new Date())));
    
    // Case 4: Year selections (This Year/Last Year) → month view (x-axis: months like "Jul-25")
    if (isYearSelection) {
      return "month";
    }
    // Case 3: <= 14 days (2 weeks) → day view (x-axis: individual days like "07/20/25")
    else if (daysDifference <= 14) {
      return "day";
    }
    // Case 1: 15-31 days (up to 1 month) → week view (x-axis: week ranges like "07/1/25 - 07/5/25")
    else if (daysDifference <= 31) {
      return "week";
    }
    // Case 2: > 31 days (more than 1 month) → month view (x-axis: months like "Jul-25")
    else {
      return "month";
    }
  }, [filters.startDate, filters.endDate]);

  // Get auto-detection explanation
  const getAutoDetectionExplanation = useCallback(() => {
    if (!filters.startDate || !filters.endDate) return "Default: week view";
    
    const daysDifference = Math.ceil((filters.endDate - filters.startDate) / (1000 * 60 * 60 * 24)) + 1;
    const isYearSelection = daysDifference >= 365 || 
      (filters.startDate.getMonth() === 0 && filters.startDate.getDate() === 1);
    
    if (isYearSelection) {
      return `Case 4: Year range (${daysDifference} days) → month view`;
    } else if (daysDifference <= 14) {
      return `Case 3: ≤2 weeks (${daysDifference} days) → day view`;
    } else if (daysDifference <= 31) {
      return `Case 1: ≤1 month (${daysDifference} days) → week view`;
    } else {
      return `Case 2: >1 month (${daysDifference} days) → month view`;
    }
  }, [filters.startDate, filters.endDate]);

  // Update time range automatically when date range changes
  useEffect(() => {
    const autoTimeRange = getAutoTimeRange();
    setAdvancedFilters(prev => ({
      ...prev,
      timeRange: autoTimeRange
    }));
  }, [getAutoTimeRange]);

  // Update booking data when time range changes
  const handleTimeRangeChange = (newTimeRange) => {
    
    setAdvancedFilters((prev) => ({ 
      ...prev, 
      timeRange: newTimeRange 
    }));
    
    // Re-run the report with new time range immediately
    if (hasRunReport) {
      // Use setTimeout to ensure state updates first
      setTimeout(() => {
        loadBookingPercentage();
      }, 100);
    }
  };



  const [bookingData, setBookingData] = useState({});
  const [hasRunReport, setHasRunReport] = useState(false);

  // View type for chart or grid display
  const [viewType, setViewType] = useState("chart"); // 'chart' or 'grid'

  // ========== Chart Data Preparation ==========
  
  // Calculate dynamic y-axis bounds for better bar visibility
  const calculateUtilizationBounds = useCallback((chartData) => {
    if (!chartData || chartData.length === 0) {
      return { min: 0, max: 100 };
    }

    const utilizationValues = chartData.map(item => item.percentage).filter(val => val != null);
    
    if (utilizationValues.length === 0) {
      return { min: 0, max: 100 };
    }

    const minValue = Math.min(...utilizationValues);
    const maxValue = Math.max(...utilizationValues);
    
    // Calculate range and add padding
    const range = maxValue - minValue;
    const padding = Math.max(range * 0.2, 5); // 20% padding or minimum 5%
    
    // Calculate bounds - NO ARBITRARY CAPS for production flexibility
    let min = Math.max(0, minValue - padding);
    let max = maxValue + padding; // Remove 100% cap to handle over-utilization
    
    // Ensure minimum range for visibility (adapt based on scale)
    const baseRange = maxValue > 100 ? 30 : 20; // Larger range for over-utilization scenarios
    const finalRange = max - min;
    
    if (finalRange < baseRange) {
      const midpoint = (min + max) / 2;
      min = Math.max(0, midpoint - (baseRange / 2));
      max = midpoint + (baseRange / 2); // No cap - allow over 100%
    }
    
    // Smart rounding based on scale
    let roundingFactor = 5;
    if (max > 200) {
      roundingFactor = 10; // Round to 10% for very high utilization
    } else if (max > 500) {
      roundingFactor = 25; // Round to 25% for extreme cases
    }
    
    // Round to nice numbers
    min = Math.floor(min / roundingFactor) * roundingFactor;
    max = Math.ceil(max / roundingFactor) * roundingFactor;
    
    // Production safety: Handle extreme edge cases
    if (max > 1000) {
      console.warn('⚠️ Extremely high utilization detected:', maxValue.toFixed(1) + '%');
      // Still allow it, but log for investigation
    }
    
    return { min, max };
  }, []);

  // Prepare data for Chart.js
  const prepareChartData = () => {
    const chartData = bookingData.chart_data || [];
    
    if (chartData.length === 0) {
      return {
        labels: [],
        datasets: []
      };
    }
    
    // Extract labels (date ranges)
    const labels = chartData.map(item => item.dateRange || 'Unknown');
    
    // Extract data values
    const timeAvailable = chartData.map(item => item.timeAvailable || 0);
    const timeBooked = chartData.map(item => item.timeBooked || 0);
    const utilizationPercentage = chartData.map(item => item.percentage || 0);
    
    return {
      labels,
      datasets: [
        {
          type: 'bar',
          label: 'Utilization %',
          data: utilizationPercentage,
          backgroundColor: 'rgba(59, 130, 246, 0.6)', // Blue bars for percentage
          borderColor: 'rgba(59, 130, 246, 1)',
          borderWidth: 1,
          yAxisID: 'y1', // Right axis for percentage
        },
        {
          type: 'line',
          label: 'Time Available (hrs)',
          data: timeAvailable,
          borderColor: 'rgba(34, 197, 94, 1)', // Green line
          backgroundColor: 'rgba(34, 197, 94, 0.1)',
          borderWidth: 3,
          pointBackgroundColor: 'rgba(34, 197, 94, 1)',
          pointBorderColor: '#fff',
          pointBorderWidth: 2,
          pointRadius: 6,
          yAxisID: 'y', // Left axis for hours
          tension: 0.4,
        },
        {
          type: 'line', 
          label: 'Time Booked (hrs)',
          data: timeBooked,
          borderColor: 'rgba(236, 72, 153, 1)', // Pink line
          backgroundColor: 'rgba(236, 72, 153, 0.1)',
          borderWidth: 3,
          pointBackgroundColor: 'rgba(236, 72, 153, 1)',
          pointBorderColor: '#fff',
          pointBorderWidth: 2,
          pointRadius: 6,
          yAxisID: 'y', // Left axis for hours
          tension: 0.4,
        }
      ]
    };
  };

  // Chart.js options
  const chartOptions = useMemo(() => {
    // Calculate dynamic bounds for utilization axis
    const utilizationBounds = calculateUtilizationBounds(bookingData?.chart_data || []);
    
    return {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top',
          labels: {
            usePointStyle: true,
            padding: 20,
          }
        },
        title: {
          display: false,
        },
        tooltip: {
          mode: 'index',
          intersect: false,
          callbacks: {
            label: function(context) {
              const label = context.dataset.label || '';
              const value = context.parsed.y;
              if (label.includes('%')) {
                return `${label}: ${value}%`;
              } else {
                return `${label}: ${value} hrs`;
              }
            }
          }
        }
      },
      scales: {
        x: {
          display: true,
          title: {
            display: true,
            text: 'Time Period'
          },
          grid: {
            display: false,
          }
        },
        y: {
          type: 'linear',
          display: true,
          position: 'left',
          title: {
            display: true,
            text: 'Hours'
          },
          grid: {
            color: 'rgba(0, 0, 0, 0.1)',
          },
          beginAtZero: true,
        },
        y1: {
          type: 'linear',
          display: true,
          position: 'right',
          title: {
            display: true,
            text: 'Utilization %'
          },
          grid: {
            drawOnChartArea: false,
          },
          min: utilizationBounds.min,
          max: utilizationBounds.max,
          ticks: {
            callback: function(value) {
              return value + '%';
            }
          }
        },
      },
      interaction: {
        mode: 'index',
        intersect: false,
      },
    };
  }, [bookingData?.chart_data, calculateUtilizationBounds]);

  // ========== Event Handlers ==========

  // Handle advanced filter condition changes
  const handleAdvancedFilterChange = (key, value) => {
    setAdvancedFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  // Load booking percentage data
  const loadBookingPercentage = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Use manual override if it exists and differs from auto-detection, otherwise use auto-detected
      const autoDetectedTimeRange = getAutoTimeRange();
      const isManualOverride = advancedFilters.timeRange && 
                               advancedFilters.timeRange !== autoDetectedTimeRange;
      const finalTimeRange = isManualOverride ? advancedFilters.timeRange : autoDetectedTimeRange;
      
      // Update the state to match what we're actually using (only if it's auto-detected)
      if (!isManualOverride) {
        setAdvancedFilters(prev => ({
          ...prev,
          timeRange: autoDetectedTimeRange
        }));
      }

      const apiParams = {
        ...getApiParams(),
        ...advancedFilters, // Include advanced filters specific to booking percentage
        timeRange: finalTimeRange, // Use the final determined value
      };

      const data = await reportsApi.getBookingPercentage(apiParams);
      
      setBookingData(data);
      setHasRunReport(true);
    } catch (error) {
      console.error("Failed to load booking percentage:", error);
      setError("Failed to load booking percentage. Please try again.");
      // Set default/empty data on error
      setBookingData({
        employees: [],
        chart_data: [],
        summary: {
          overall_utilization: 0,
          total_appointments: 0,
          total_no_shows: 0,
          peak_times: [],
        },
        period_start: apiParams.startDate,
        period_end: apiParams.endDate,
      });
    } finally {
      setLoading(false);
    }
  }, [getApiParams, advancedFilters, setLoading, setError, getAutoTimeRange]);

  // Run report function that calls the loadBookingPercentage
  const handleRunReport = () => {
    loadBookingPercentage();
  };

  // Export functionality
  const handleExport = async () => {
    try {
      setLoading(true);
      const apiParams = {
        ...getApiParams(),
        ...advancedFilters,
      };
      const response = await reportsApi.exportBookingPercentage(apiParams, 'csv');
      
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `booking-percentage_${apiParams.startDate.toISOString().split('T')[0]}_${apiParams.endDate.toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
      } else {
        throw new Error('Export failed');
      }
    } catch (error) {
      console.error("Export failed:", error);
      setError("Export failed. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Print report
  const handlePrint = () => {
    window.print();
  };

  // Advanced filter fields configuration for the shared component
  const advancedFields = [
    {
      key: 'includePastEmployees',
      label: 'Include Past Employees',
      type: 'checkbox',
      checkboxLabel: 'Include Past Employees',
      defaultValue: false
    }
  ];

  // Custom fields for the booking percentage report
  const customFields = [];

  // Merge advanced filters into the main filters for the shared hook
  const combinedFilters = { ...filters, ...advancedFilters };
  const setCombinedFilters = (newFilters) => {
    if (typeof newFilters === 'function') {
      const updated = newFilters(combinedFilters);
      setFilters(updated);
      setAdvancedFilters(updated);
    } else {
      setFilters(newFilters);
      setAdvancedFilters(newFilters);
    }
  };

  return (
    <div className="container mx-auto px-4 py-6">
      {/* Shared Filter Panel */}
      <ReportsFilterPanel
        filters={combinedFilters}
        onFiltersChange={setCombinedFilters}
        filterOptions={filterOptions}
        loading={filterLoading}
        onRunReport={handleRunReport}
        showAdvancedFilters={true}
        showPaymentMethods={false}
        showCustomers={false}
        showServiceProviders={true}
        showSearch={false}
        showAmountRange={false}
        advancedFields={advancedFields}
        customFields={customFields}
        title="Booking Percentage Filters"
      />

      {/* Booking Percentage title */}
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold text-gray-800">
          Booking Percentage
        </h2>
      </div>

      {/* Main content area */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {!hasRunReport ? (
          // Placeholder content
          <div className="flex flex-col items-center justify-center py-20">
            <div className="text-6xl text-gray-400 mb-4">↑</div>
            <p className="text-lg text-gray-600 font-medium">
              Select filters above, then press Run Report.
            </p>
          </div>
        ) : (
          // Report results area
          <div className="p-6">
            {filterLoading ? (
              <div className="flex items-center justify-center py-20">
                <div className="text-lg text-gray-600">Loading...</div>
              </div>
            ) : filterError ? (
              <div className="flex items-center justify-center py-20">
                <div className="text-lg text-red-600">Error: {filterError}</div>
              </div>
            ) : (
              // Booking percentage report content
              <div>
                {/* View type selector */}
                <div className="flex justify-between items-center mb-6">
                  <div className="flex gap-2">
                    <button
                      onClick={() => setViewType("chart")}
                      className={`px-4 py-2 rounded-md text-sm font-medium flex items-center gap-2 ${
                        viewType === "chart"
                          ? "bg-blue-600 text-white"
                          : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                      }`}
                    >
                      📊 Chart View
                    </button>
                    <button
                      onClick={() => setViewType("grid")}
                      className={`px-4 py-2 rounded-md text-sm font-medium flex items-center gap-2 ${
                        viewType === "grid"
                          ? "bg-blue-600 text-white"
                          : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                      }`}
                    >
                      ⚏ Grid View
                    </button>
                  </div>

                  {viewType === "chart" && (
                    <div className="flex items-center gap-4">
                      <select
                        value={advancedFilters.timeRange}
                        onChange={(e) => handleTimeRangeChange(e.target.value)}
                        className="px-3 py-2 border border-gray-300 rounded-md text-sm bg-white"
                      >
                        <option value="day">Day View</option>
                        <option value="week">Week View</option>
                        <option value="month">Month View</option>
                      </select>
                    </div>
                  )}
                </div>

                {viewType === "chart" ? (
                  // Chart view with Chart.js
                  <div className="space-y-6">
                    {/* Chart container */}
                    <div className="bg-white border border-gray-200 rounded-lg p-6">
                      {(bookingData.chart_data || []).length === 0 ? (
                        // Empty state
                        <div className="flex flex-col items-center justify-center py-20">
                          <div className="text-6xl text-gray-300 mb-4">📊</div>
                          <h3 className="text-lg font-medium text-gray-900 mb-2">No booking data available</h3>
                          <p className="text-sm text-gray-500">
                            Try adjusting your filters or date range to see booking utilization data.
                          </p>
                        </div>
                      ) : (
                        // Chart.js Chart
                        <div style={{ height: '400px' }}>
                          <Chart 
                            type="bar" 
                            data={prepareChartData()} 
                            options={chartOptions} 
                          />
                        </div>
                      )}
                      
                      {/* Summary stats below chart */}
                      {(bookingData.chart_data || []).length > 0 && bookingData.summary && (
                        <div className="mt-6 pt-6 border-t border-gray-200">
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <div className="text-center">
                              <div className="text-2xl font-bold text-blue-600">
                                {bookingData.summary.overall_utilization || 0}%
                              </div>
                              <div className="text-sm text-gray-500">Overall Utilization</div>
                            </div>
                            <div className="text-center">
                              <div className="text-2xl font-bold text-gray-900">
                                {Math.round(bookingData.summary.total_available_hours || 0)}
                              </div>
                              <div className="text-sm text-gray-500">Hours Available</div>
                            </div>
                            <div className="text-center">
                              <div className="text-2xl font-bold text-red-600">
                                {Math.round(bookingData.summary.total_booked_hours || 0)}
                              </div>
                              <div className="text-sm text-gray-500">Hours Booked</div>
                            </div>
                            <div className="text-center">
                              <div className="text-2xl font-bold text-green-600">
                                {bookingData.summary.active_employees || 0}
                              </div>
                              <div className="text-sm text-gray-500">Active Employees</div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ) : (
                  // Grid view - showing time period data
                  <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                    <table className="w-full">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                            Date
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                            Percentage
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                            Time Available
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                            Time Booked
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {(bookingData.chart_data || []).map((dataPoint, index) => (
                          <tr key={dataPoint.id || index} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">
                              {dataPoint.dateRange || 'Unknown Date'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                dataPoint.percentage >= 80 ? 'bg-green-100 text-green-800' :
                                dataPoint.percentage >= 60 ? 'bg-yellow-100 text-yellow-800' :
                                dataPoint.percentage >= 40 ? 'bg-orange-100 text-orange-800' :
                                'bg-red-100 text-red-800'
                              }`}>
                                {dataPoint.percentage || 0}%
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {dataPoint.timeAvailable || 0}hrs
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {dataPoint.timeBooked || 0}hrs
                            </td>
                          </tr>
                        ))}
                        {/* Show empty state if no data */}
                        {(!bookingData.chart_data || bookingData.chart_data.length === 0) && (
                          <tr>
                            <td colSpan="4" className="px-6 py-8 text-center text-gray-500">
                              <div className="text-4xl text-gray-300 mb-2">📊</div>
                              <div className="text-sm">No booking data available for the selected period.</div>
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Bottom buttons */}
      {hasRunReport && !filterLoading && !filterError && (
        <div className="flex justify-end gap-4 mt-6">
          <button
            onClick={handleExport}
            className="px-4 py-2 bg-white border border-gray-300 rounded-md hover:bg-gray-50 text-gray-700 flex items-center font-medium shadow-sm"
          >
            Export
            <span className="ml-1">▼</span>
          </button>
          <button
            onClick={handlePrint}
            className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 font-medium"
          >
            Print
          </button>
        </div>
      )}
    </div>
  );
}

export default BookingPercentage;
