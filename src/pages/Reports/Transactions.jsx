import { useState, useEffect, useCallback } from "react";
import { 
  ArrowDownTrayIcon,
  PrinterIcon,
  XMarkIcon,
  DocumentTextIcon,
  ChevronRightIcon,
  CurrencyDollarIcon
} from "@heroicons/react/24/outline";
import Button from "../../components/Button";
import ReportsFilterPanel from "../../features/reports/components/ReportsFilterPanel";
import useReportsFilter from "../../features/reports/hooks/useReportsFilter";
import { reportsApi } from "../../features/reports/services/reportsApi";

// Payment method icons and colors
const PAYMENT_METHODS = {
  cash: { label: "Cash", color: "bg-green-100 text-green-800", icon: "💵" },
  credit_card: { label: "Credit Card", color: "bg-blue-100 text-blue-800", icon: "💳" },
  debit_card: { label: "Debit Card", color: "bg-purple-100 text-purple-800", icon: "💳" },
  venmo: { label: "Venmo", color: "bg-indigo-100 text-indigo-800", icon: "📱" },
  zelle: { label: "Zelle", color: "bg-yellow-100 text-yellow-800", icon: "⚡" },
  check: { label: "Check", color: "bg-gray-100 text-gray-800", icon: "📝" },
  paypal: { label: "PayPal", color: "bg-blue-100 text-blue-800", icon: "🅿️" },
  other: { label: "Other", color: "bg-gray-100 text-gray-800", icon: "❓" },
};

function Transactions() {
  // ========== State Management with Shared Hook ==========
  const {
    filters,
    setFilters,
    filterOptions,
    loading: filterLoading,
    error: filterError,
    setLoading,
    setError,
    getApiParams,
    resetFilters
  } = useReportsFilter();

  // Main data
  const [transactions, setTransactions] = useState([]);
  const [summary, setSummary] = useState({
    money_earned: 0,
    drawer_balance: 0,
    total_transactions: 0,
    total_tips: 0,
    average_transaction: 0,
    cash_transactions: 0,
    card_transactions: 0,
    digital_transactions: 0,
    top_employee: null,
    top_employee_sales: 0,
  });

  const [hasRunReport, setHasRunReport] = useState(false);

  // Pagination
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 25,
    totalPages: 1,
    totalCount: 0,
  });

  // ========== API Functions ==========
  
  // Load transactions with filters
  const loadTransactions = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const apiParams = {
        ...getApiParams(),
        page: pagination.page,
        pageSize: pagination.pageSize,
      };
      const data = await reportsApi.getTransactions(apiParams);

      if (data && data.results) {
        console.log('📊 Pagination data from API:', {
          count: data.count,
          total_pages: data.total_pages,
          current_page: data.current_page,
          results_length: data.results.length
        });
        
        setTransactions(data.results);
        setPagination(prev => ({
          ...prev,
          totalCount: data.count || 0,
          totalPages: data.total_pages || Math.ceil((data.count || 0) / prev.pageSize),
        }));
      } else {
        console.log('❌ No data or results from API');
        setTransactions([]);
        setPagination(prev => ({
          ...prev,
          totalCount: 0,
          totalPages: 1,
        }));
      }
    } catch (error) {
      console.error("Failed to load transactions:", error);
      setError("Failed to load transactions. Please try again.");
      setTransactions([]);
      setPagination(prev => ({
        ...prev,
        totalCount: 0,
        totalPages: 1,
      }));
    } finally {
      setLoading(false);
    }
  }, [getApiParams, setLoading, setError, pagination.page, pagination.pageSize]);

  // Load summary data
  const loadSummary = useCallback(async () => {
    try {
      const apiParams = getApiParams();
      const summaryData = await reportsApi.getTransactionSummary(apiParams);
      setSummary(summaryData);
    } catch (error) {
      console.error("Failed to load summary:", error);
      setSummary({
        money_earned: 0,
        drawer_balance: 0,
      });
    }
  }, [getApiParams]);

  // ========== Event Handlers ==========
  
  const handleRunReport = () => {
    // Reset to page 1 and load data
    setPagination(prev => ({ ...prev, page: 1 }));
    // Don't call loadTransactions here - let the effect handle it
    loadSummary();
    setHasRunReport(true);
  };

  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, page: newPage }));
    // loadTransactions will be called by useEffect when page changes
  };

  // Export functionality
  const handleExport = async () => {
    try {
      setLoading(true); // Use setLoading for consistency
      const apiParams = getApiParams();
      const response = await reportsApi.exportTransactions(apiParams, 'csv');
      
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `transactions_${apiParams.startDate.toISOString().split('T')[0]}_${apiParams.endDate.toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
      } else {
        throw new Error('Export failed');
      }
    } catch (error) {
      console.error("Export failed:", error);
      setError("Export failed. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // ========== Effects ==========
  
  // Load transactions when page changes (including page 1) and report has been run
  useEffect(() => {
    if (hasRunReport) {
      loadTransactions();
    }
  }, [pagination.page, hasRunReport, loadTransactions]);

  useEffect(() => {
    loadSummary();
  }, [loadSummary]);

  // ========== Render Helpers ==========
  
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount || 0);
  };

  const formatDate = (dateString) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(new Date(dateString));
  };

  const getPaymentMethodDisplay = (method) => {
    const config = PAYMENT_METHODS[method] || PAYMENT_METHODS.other;
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        <span className="mr-1">{config.icon}</span>
        {config.label}
      </span>
    );
  };

  // ========== Render ==========

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Transaction Reports</h1>
            <p className="text-sm text-gray-600 mt-1">
              Track sales, payments, and performance metrics
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            <Button
              variant="outline"
              size="sm"
              onClick={handleExport}
              className="flex items-center"
              disabled={filterLoading}
            >
              <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.print()}
              className="flex items-center"
            >
              <PrinterIcon className="h-4 w-4 mr-2" />
              Print
            </Button>
          </div>
        </div>
      </div>

      {/* Shared Filter Panel */}
      <ReportsFilterPanel
        filters={filters}
        onFiltersChange={setFilters}
        filterOptions={filterOptions}
        loading={filterLoading}
        onRunReport={handleRunReport}
        showAdvancedFilters={true}
        showPaymentMethods={true}
        showCustomers={true}
        showServiceProviders={true}
        showSearch={true}
        showAmountRange={true}
      />

      {/* Error Display */}
      {filterError && (
        <div className="mx-6 mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex">
            <XMarkIcon className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <p className="mt-1 text-sm text-red-700">{filterError}</p>
            </div>
          </div>
        </div>
      )}

      {/* Summary Cards */}
      {hasRunReport && !filterLoading && (
        <div className="px-6 py-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Money Earned */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CurrencyDollarIcon className="h-8 w-8 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Money Earned</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(summary.money_earned)}
                  </p>
                </div>
              </div>
            </div>

            {/* Drawer Balance */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <DocumentTextIcon className="h-8 w-8 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Drawer Balance</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(summary.drawer_balance)}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Transaction List */}
      {hasRunReport && !filterLoading && (
        <div className="px-6 pb-6">
          <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
            {/* Table Header */}
            <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">
                  Transaction List
                </h3>
                <p className="text-sm text-gray-600">
                  {pagination.totalCount.toLocaleString()} transactions found
                </p>
              </div>
            </div>

            {/* Table */}
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Transaction ID
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Customer Name
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Checkout Date
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Appointment Date
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Checkout By
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Item Sold
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Sold By
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Service Name
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Payment Method
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Price
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Tax
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Tip
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Discount
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Amount Paid
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {transactions.map((transaction) => (
                    <tr 
                      key={transaction.id} 
                      className="hover:bg-gray-50 cursor-pointer"
                      onClick={() => {
                        // Handle row click for details view
                        console.log('Transaction details:', transaction);
                      }}
                    >
                      <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        #{transaction.id}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        {transaction.customer_name}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatDate(transaction.checkout_date)}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        {transaction.appointment_date ? (
                          formatDate(transaction.appointment_date)
                        ) : (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            Direct Sale
                          </span>
                        )}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        {transaction.checkout_by_name}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        {transaction.item_sold}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        {transaction.sold_by_name}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        {transaction.service_name}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        {getPaymentMethodDisplay(transaction.payment_method)}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                        {formatCurrency(transaction.price)}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                        {formatCurrency(transaction.tax)}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                        {formatCurrency(transaction.tip)}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                        {formatCurrency(transaction.discount)}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-right">
                        {formatCurrency(transaction.amount_paid)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {(pagination.totalPages > 1 || pagination.totalCount > pagination.pageSize) && (
              <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
                {console.log('🔢 Pagination state:', pagination)}
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-700">
                    Showing {((pagination.page - 1) * pagination.pageSize) + 1} to{' '}
                    {Math.min(pagination.page * pagination.pageSize, pagination.totalCount)} of{' '}
                    {pagination.totalCount} results
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      disabled={pagination.page === 1}
                      onClick={() => handlePageChange(pagination.page - 1)}
                    >
                      Previous
                    </Button>
                    {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                      const page = i + 1;
                      return (
                        <Button
                          key={page}
                          variant={page === pagination.page ? "primary" : "outline"}
                          size="sm"
                          onClick={() => handlePageChange(page)}
                        >
                          {page}
                        </Button>
                      );
                    })}
                    <Button
                      variant="outline"
                      size="sm"
                      disabled={pagination.page === pagination.totalPages}
                      onClick={() => handlePageChange(pagination.page + 1)}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* No Data State */}
      {hasRunReport && !filterLoading && transactions.length === 0 && (
        <div className="mx-6 mb-6">
          <div className="bg-white rounded-lg border border-gray-200 p-12 text-center">
            <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-4 text-lg font-medium text-gray-900">No transactions found</h3>
            <p className="mt-2 text-sm text-gray-600">
              Try adjusting your filters or date range to see more results.
            </p>
          </div>
        </div>
      )}

      {/* Initial State */}
      {!hasRunReport && !filterLoading && (
        <div className="mx-6 mb-6">
          <div className="bg-white rounded-lg border border-gray-200 p-12 text-center">
            <ChevronRightIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-4 text-lg font-medium text-gray-900">Ready to run your report</h3>
            <p className="mt-2 text-sm text-gray-600">
              Select your filters and click "Run Report" to view transaction data.
            </p>
          </div>
        </div>
      )}
    </div>
  );
}

export default Transactions;
