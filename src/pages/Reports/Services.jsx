import { useState, useEffect, useCallback } from "react";
import Button from "../../components/Button";
import ReportsFilterPanel from "../../features/reports/components/ReportsFilterPanel";
import useReportsFilter from "../../features/reports/hooks/useReportsFilter";
import { reportsApi } from "../../features/reports/services/reportsApi";

function Services() {
  // ========== State Management with Shared Hook ==========
  const {
    filters,
    setFilters,
    filterOptions,
    loading: filterLoading,
    error: filterError,
    setLoading,
    setError,
    getApiParams,
    resetFilters
  } = useReportsFilter();

  // Advanced filter conditions specific to services report
  const [advancedFilters, setAdvancedFilters] = useState({
    serviceType: "all",
    serviceCategory: "all",
    location: "all",
    priceRange: "all",
    duration: "all",
    includeRefunds: false,
    includeTax: true,
    pricesIncludePointDeduction: true,
    includePastEmployees: false,
  });

  // Services data
  const [servicesData, setServicesData] = useState({});
  const [hasRunReport, setHasRunReport] = useState(false);

  // Load services report data
  const loadServicesReport = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const apiParams = {
        ...getApiParams(),
        ...advancedFilters, // Include advanced filters specific to services
      };

      const data = await reportsApi.getServicesReport(apiParams);
      setServicesData(data);
      setHasRunReport(true);
    } catch (error) {
      console.error("Failed to load services report:", error);
      setError("Failed to load services report. Please try again.");
      // Set default/empty data on error
      setServicesData({
        services: [],
        summary: {
          total_services: 0,
          total_revenue: 0,
          average_rating: 0,
        },
        period_start: apiParams.startDate,
        period_end: apiParams.endDate,
      });
    } finally {
      setLoading(false);
    }
  }, [getApiParams, advancedFilters, setLoading, setError]);

  // ========== Utility Functions ==========

  // Format currency values
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount || 0);
  };

  // ========== Event Handlers ==========

  // Handle advanced filter condition changes
  const handleAdvancedFilterChange = (key, value) => {
    setAdvancedFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  // Run report function that calls the loadServicesReport
  const handleRunReport = () => {
    loadServicesReport();
  };

  // Export functionality
  const handleExport = async () => {
    try {
      setLoading(true);
      const apiParams = {
        ...getApiParams(),
        ...advancedFilters,
      };
      const response = await reportsApi.exportServicesReport(apiParams, 'csv');
      
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `services-report_${apiParams.startDate.toISOString().split('T')[0]}_${apiParams.endDate.toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
      } else {
        throw new Error('Export failed');
      }
    } catch (error) {
      console.error("Export failed:", error);
      setError("Export failed. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Print report
  const handlePrint = () => {
    window.print();
  };

  // Advanced filter fields configuration for the shared component
  const advancedFields = [
    {
      key: 'pricesIncludePointDeduction',
      label: 'Prices Include Point Deduction',
      type: 'checkbox',
      checkboxLabel: 'Prices Shown Include Point Deduction',
      defaultValue: true
    },
    {
      key: 'includePastEmployees',
      label: 'Include Past Employees',
      type: 'checkbox',
      checkboxLabel: 'Include Past Employees',
      defaultValue: false
    }
  ];

  // Merge advanced filters into the main filters for the shared hook
  const combinedFilters = { ...filters, ...advancedFilters };
  const setCombinedFilters = (newFilters) => {
    if (typeof newFilters === 'function') {
      const updated = newFilters(combinedFilters);
      setFilters(updated);
      setAdvancedFilters(updated);
    } else {
      setFilters(newFilters);
      setAdvancedFilters(newFilters);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Sticky Filter Panel */}
      <div className="sticky top-0 z-50 bg-white shadow-sm border-b border-gray-200">
        <div className="container mx-auto px-4 py-6">
          <ReportsFilterPanel
            filters={combinedFilters}
            onFiltersChange={setCombinedFilters}
            filterOptions={filterOptions}
            loading={filterLoading}
            onRunReport={handleRunReport}
            showAdvancedFilters={true}
            showPaymentMethods={false}
            showCustomers={true}
            showServiceProviders={true}
            showSearch={false}
            showAmountRange={false}
            advancedFields={advancedFields}
            title="Services Report Filters"
          />
        </div>
      </div>

      {/* Main content container */}
      <div className="container mx-auto px-4 py-6">
        {/* Service/Class Sales Summary title */}
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold text-gray-800">
            Service/Class Sales Summary
          </h2>
        </div>

        {/* Main content area */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {!hasRunReport ? (
          // Placeholder content
          <div className="flex flex-col items-center justify-center py-20">
            <div className="text-6xl text-gray-400 mb-4">↑</div>
            <p className="text-lg text-gray-600 font-medium">
              Select filters above, then press Run Report.
            </p>
          </div>
        ) : (
          // Report results area
          <div className="p-6">
            {filterLoading ? (
              <div className="flex items-center justify-center py-20">
                <div className="text-lg text-gray-600">Loading...</div>
              </div>
            ) : filterError ? (
              <div className="flex items-center justify-center py-20">
                <div className="text-lg text-red-600">Error: {filterError}</div>
              </div>
            ) : (
              // Services report content
              <div>
                {/* Report period and summary info */}
                <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="text-lg font-semibold text-gray-800">
                      Services Performance Report
                    </h3>
                    <span className="text-sm text-gray-600">
                      {servicesData?.summary?.total_services || 0} services
                    </span>
                  </div>
                  {servicesData?.period_start && servicesData?.period_end && (
                    <p className="text-sm text-gray-600">
                      Period: {new Date(servicesData.period_start).toLocaleDateString()} - {new Date(servicesData.period_end).toLocaleDateString()}
                    </p>
                  )}
                  <p className="text-sm text-gray-600 mt-2">
                    * All sales figures are after discount
                  </p>
                </div>

                {/* Services table - Improved Design */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-8">
                  <table className="w-full">
                    <thead>
                      <tr className="bg-gray-50 border-b border-gray-200">
                        <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">
                          Service Name
                        </th>
                        <th className="px-6 py-4 text-center text-sm font-semibold text-gray-900">
                          No. Appointments
                        </th>
                        <th className="px-6 py-4 text-center text-sm font-semibold text-gray-900">
                          No. Attendance
                        </th>
                        <th className="px-6 py-4 text-right text-sm font-semibold text-gray-900">
                          Service Sales
                        </th>
                        <th className="px-6 py-4 text-right text-sm font-semibold text-gray-900">
                          Service Add-On Sales
                        </th>
                        <th className="px-6 py-4 text-right text-sm font-semibold text-gray-900">
                          Cost To Business
                        </th>
                        <th className="px-6 py-4 text-right text-sm font-semibold text-gray-900">
                          Average Sales
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-100">
                      {servicesData?.services && servicesData.services.length > 0 ? (
                        <>
                          {servicesData.services.map((service, index) => (
                            <tr key={index} className="hover:bg-gray-50 transition-colors">
                              <td className="px-6 py-4 text-sm font-medium text-gray-900">
                                {service.name || 'Unknown Service'}
                              </td>
                              <td className="px-6 py-4 text-sm text-gray-600 text-center">
                                {service.appointments_count || 0}
                              </td>
                              <td className="px-6 py-4 text-sm text-gray-600 text-center">
                                {service.attendance_count || 0}
                              </td>
                              <td className="px-6 py-4 text-sm text-gray-900 text-right font-medium">
                                {formatCurrency(service.service_sales || 0)}
                              </td>
                              <td className="px-6 py-4 text-sm text-gray-900 text-right font-medium">
                                {formatCurrency(service.addon_sales || 0)}
                              </td>
                              <td className="px-6 py-4 text-sm text-gray-900 text-right">
                                {formatCurrency(service.business_cost || 0)}
                              </td>
                              <td className="px-6 py-4 text-sm text-gray-900 text-right font-medium">
                                {formatCurrency(service.average_sale || 0)}
                              </td>
                            </tr>
                          ))}
                          
                          {/* Totals Row */}
                          <tr className="bg-blue-50 border-t-2 border-blue-200">
                            <td className="px-6 py-4 text-sm font-bold text-gray-900">
                              Total
                            </td>
                            <td className="px-6 py-4 text-sm font-bold text-gray-900 text-center">
                              {servicesData?.summary?.total_appointments || 0}
                            </td>
                            <td className="px-6 py-4 text-sm font-bold text-gray-900 text-center">
                              {servicesData?.summary?.total_attendance || 0}
                            </td>
                            <td className="px-6 py-4 text-sm font-bold text-gray-900 text-right">
                              {formatCurrency(servicesData?.summary?.total_service_sales || 0)}
                            </td>
                            <td className="px-6 py-4 text-sm font-bold text-gray-900 text-right">
                              {formatCurrency(servicesData?.summary?.total_addon_sales || 0)}
                            </td>
                            <td className="px-6 py-4 text-sm font-bold text-gray-900 text-right">
                              {formatCurrency(servicesData?.summary?.total_business_cost || 0)}
                            </td>
                            <td className="px-6 py-4 text-sm font-bold text-blue-600 text-right">
                              {formatCurrency(servicesData?.summary?.average_of_averages || 0)}
                            </td>
                          </tr>
                        </>
                      ) : (
                        <tr>
                          <td
                            className="px-6 py-12 text-center text-gray-500 text-sm"
                            colSpan="7"
                          >
                            <div className="flex flex-col items-center">
                              <svg className="w-12 h-12 text-gray-300 mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                              </svg>
                              <p className="font-medium">No services data to display</p>
                              <p className="text-xs mt-1">Try adjusting your filters or date range</p>
                            </div>
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        )}
        </div>

        {/* Bottom buttons */}
        {hasRunReport && !filterLoading && !filterError && (
          <div className="flex justify-end gap-4 mt-6">
            <button
              onClick={handleExport}
              className="px-4 py-2 bg-white border border-gray-300 rounded-md hover:bg-gray-50 text-gray-700 flex items-center font-medium shadow-sm"
            >
              Export
              <span className="ml-1">▼</span>
            </button>
            <button
              onClick={handlePrint}
              className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 font-medium"
            >
              Print
            </button>
          </div>
        )}
      </div>
    </div>
  );
}

export default Services;
