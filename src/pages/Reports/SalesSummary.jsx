import { useState, useEffect, useCallback } from "react";
import Button from "../../components/Button";
import ReportsFilterPanel from "../../features/reports/components/ReportsFilterPanel";
import useReportsFilter from "../../features/reports/hooks/useReportsFilter";
import { reportsApi } from "../../features/reports/services/reportsApi";

function SalesSummary() {
  // ========== State Management with Shared Hook ==========
  const {
    filters,
    setFilters,
    filterOptions,
    loading: filterLoading,
    error: filterError,
    setLoading,
    setError,
    getApiParams,
    resetFilters
  } = useReportsFilter();

  // Advanced filter conditions specific to sales summary
  const [advancedFilters, setAdvancedFilters] = useState({
    tip: true,
    includePastEmployees: true,
    appointmentDate: "",
  });

  // Sales data
  const [salesData, setSalesData] = useState({});
  const [hasRunReport, setHasRunReport] = useState(false);

  // ========== Utility Functions ==========

  // Format currency values
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount || 0);
  };

  // ========== Event Handlers ==========

  // Handle advanced filter condition changes
  const handleAdvancedFilterChange = (key, value) => {
    setAdvancedFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  // Load sales summary data
  const loadSalesSummary = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const apiParams = {
        ...getApiParams(),
        ...advancedFilters, // Include advanced filters specific to sales summary
      };

      const data = await reportsApi.getSalesSummary(apiParams);
      
      // ✅ FIX: Add original filter dates to response to avoid backend date discrepancy
      data.filter_start_date = apiParams.startDate;
      data.filter_end_date = apiParams.endDate;
      
      setSalesData(data);
      setHasRunReport(true);
    } catch (error) {
      console.error("Failed to load sales summary:", error);
      setError("Failed to load sales summary. Please try again.");
      
      // Get current filter params for error fallback
      const apiParams = {
        ...getApiParams(),
        ...advancedFilters,
      };
      
      // Set default/empty data on error
      setSalesData({
        breakdown: {
          service_sales_revenue: 0,
          service_sales_cost: 0,
          addon_sales_revenue: 0,
          addon_sales_cost: 0,
        },
        aggregates: {
          sales_tax: 0,
          tips: 0,
          service_refunds: 0,
          tip_refunds: 0,
        },
        totals: {
          revenue_total: 0,
          business_cost_total: 0,
          tax_total: 0,
          profit_total: 0,
        },
        period_start: apiParams.startDate,
        period_end: apiParams.endDate,
        filter_start_date: apiParams.startDate,
        filter_end_date: apiParams.endDate,
        total_transactions: 0,
      });
    } finally {
      setLoading(false);
    }
  }, [getApiParams, advancedFilters, setLoading, setError]);

  // Export functionality
  const handleExport = async () => {
    try {
      setLoading(true);
      const apiParams = {
        ...getApiParams(),
        ...advancedFilters,
      };
      const response = await reportsApi.exportSalesSummary(apiParams, 'csv');
      
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        
        // ✅ FIX: Use filter dates for filename to match what user actually selected
        const startDateStr = apiParams.startDate.toISOString().split('T')[0];
        const endDateStr = apiParams.endDate.toISOString().split('T')[0];
        a.download = `sales-summary_${startDateStr}_${endDateStr}.csv`;
        
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
      } else {
        throw new Error('Export failed');
      }
    } catch (error) {
      console.error("Export failed:", error);
      setError("Export failed. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Print report
  const handlePrint = () => {
    window.print();
  };

  // Advanced filter fields configuration for the shared component
  const advancedFields = [
    {
      key: 'tip',
      label: 'Tip',
      type: 'checkbox',
      checkboxLabel: 'Include tips in calculation',
      defaultValue: true
    },
    {
      key: 'includePastEmployees',
      label: 'Include Past Employees',
      type: 'checkbox',
      checkboxLabel: 'Include past employees in report',
      defaultValue: true
    },
    {
      key: 'appointmentDate',
      label: 'Appointment Date',
      type: 'date',
      placeholder: 'Select specific appointment date'
    }
  ];

  // Merge advanced filters into the main filters for the shared hook
  const combinedFilters = { ...filters, ...advancedFilters };
  const setCombinedFilters = (newFilters) => {
    if (typeof newFilters === 'function') {
      const updated = newFilters(combinedFilters);
      setFilters(updated);
      setAdvancedFilters(updated);
    } else {
      setFilters(newFilters);
      setAdvancedFilters(newFilters);
    }
  };

  return (
    <div className="container mx-auto px-4 py-6">
      {/* Shared Filter Panel */}
      <ReportsFilterPanel
        filters={combinedFilters}
        onFiltersChange={setCombinedFilters}
        filterOptions={filterOptions}
        loading={filterLoading}
        onRunReport={loadSalesSummary}
        showAdvancedFilters={true}
        showPaymentMethods={false}
        showCustomers={true}
        showServiceProviders={true}
        showSearch={false}
        showAmountRange={false}
        advancedFields={advancedFields}
        title="Sales Summary Filters"
      />

      {/* Sales summary title */}
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold text-gray-800">Sales Summary</h2>
      </div>

      {/* Main content area */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {!hasRunReport ? (
          // Placeholder content
          <div className="flex flex-col items-center justify-center py-20">
            <div className="text-6xl text-gray-400 mb-4">↑</div>
            <p className="text-lg text-gray-600 font-medium">
              Select filters above, then press Run Report.
            </p>
          </div>
        ) : (
          // Report results area
          <div className="p-6">
            {filterLoading ? (
              <div className="flex items-center justify-center py-20">
                <div className="text-lg text-gray-600">Loading...</div>
              </div>
            ) : filterError ? (
              <div className="flex items-center justify-center py-20">
                <div className="text-lg text-red-600">Error: {filterError}</div>
              </div>
            ) : (
              // Sales summary report content
              <div>
                {/* Report period and summary info */}
                <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="text-lg font-semibold text-gray-800">
                      Sales Summary Report
                    </h3>
                    <span className="text-sm text-gray-600">
                      {salesData?.total_transactions || 0} transactions
                    </span>
                  </div>
                  {/* ✅ FIX: Use filter dates instead of backend response to prevent discrepancy */}
                  {salesData?.filter_start_date && salesData?.filter_end_date && (
                    <p className="text-sm text-gray-600">
                      Period: {new Date(salesData.filter_start_date).toLocaleDateString()} - {new Date(salesData.filter_end_date).toLocaleDateString()}
                    </p>
                  )}
                  <p className="text-sm text-gray-600 mt-2">
                    *Total sales numbers are based on transaction date and do not
                    include items paid for by points.
                  </p>
                </div>

                {/* Sales summary table - Minimalist Design */}
                <div className="mb-8">
                  <table className="w-full">
                    {/* Clean header */}
                    <thead>
                      <tr className="border-b border-gray-200">
                        <th className="text-left py-3 text-sm font-medium text-gray-700">
                          Type
                        </th>
                        <th className="text-right py-3 text-sm font-medium text-gray-700">
                          Business Cost
                        </th>
                        <th className="text-right py-3 text-sm font-medium text-gray-700">
                          Total Sale*
                        </th>
                      </tr>
                    </thead>

                    {/* Table body */}
                    <tbody className="divide-y divide-gray-100">
                      {/* Type - Services with Business Cost */}
                      <tr className="hover:bg-gray-50">
                        <td className="py-3 text-sm text-gray-900">
                          <div className="flex items-center">
                            Service Sales
                            <span className="ml-2 text-xs text-gray-400 cursor-help" title="Information">ⓘ</span>
                          </div>
                        </td>
                        <td className="py-3 text-sm text-gray-900 text-right">
                          {formatCurrency(salesData?.breakdown?.service_sales_cost || 0)}
                        </td>
                        <td className="py-3 text-sm text-gray-900 text-right">
                          {formatCurrency(salesData?.breakdown?.service_sales_revenue || 0)}
                        </td>
                      </tr>
                      <tr className="hover:bg-gray-50">
                        <td className="py-3 text-sm text-gray-900">
                          <div className="flex items-center">
                            Service Add-On Sales
                            <span className="ml-2 text-xs text-gray-400 cursor-help" title="Information">ⓘ</span>
                          </div>
                        </td>
                        <td className="py-3 text-sm text-gray-900 text-right">
                          {formatCurrency(salesData?.breakdown?.addon_sales_cost || 0)}
                        </td>
                        <td className="py-3 text-sm text-gray-900 text-right">
                          {formatCurrency(salesData?.breakdown?.addon_sales_revenue || 0)}
                        </td>
                      </tr>
                      
                      {/* Aggregate Results */}
                      <tr className="hover:bg-gray-50">
                        <td className="py-3 text-sm text-gray-900">Sales Tax</td>
                        <td className="py-3 text-sm text-gray-900 text-right">—</td>
                        <td className="py-3 text-sm text-gray-900 text-right">
                          {formatCurrency(salesData?.aggregates?.sales_tax || 0)}
                        </td>
                      </tr>
                      <tr className="hover:bg-gray-50">
                        <td className="py-3 text-sm text-gray-900">Tips</td>
                        <td className="py-3 text-sm text-gray-900 text-right">—</td>
                        <td className="py-3 text-sm text-gray-900 text-right">
                          {formatCurrency(salesData?.aggregates?.tips || 0)}
                        </td>
                      </tr>
                      <tr className="hover:bg-gray-50">
                        <td className="py-3 text-sm text-gray-900">Service Refunds</td>
                        <td className="py-3 text-sm text-gray-900 text-right">—</td>
                        <td className="py-3 text-sm text-gray-900 text-right">
                          {formatCurrency(salesData?.aggregates?.service_refunds || 0)}
                        </td>
                      </tr>
                      <tr className="hover:bg-gray-50">
                        <td className="py-3 text-sm text-gray-900">Tip Refunds</td>
                        <td className="py-3 text-sm text-gray-900 text-right">—</td>
                        <td className="py-3 text-sm text-gray-900 text-right">
                          {formatCurrency(salesData?.aggregates?.tip_refunds || 0)}
                        </td>
                      </tr>
                      
                      {/* Results section with subtle separation */}
                      <tr className="border-t border-gray-200 bg-gray-50">
                        <td className="py-3 text-sm font-semibold text-gray-900">Revenue Total</td>
                        <td className="py-3 text-sm font-semibold text-gray-900 text-right">—</td>
                        <td className="py-3 text-sm font-semibold text-gray-900 text-right">
                          {formatCurrency(salesData?.totals?.revenue_total || 0)}
                        </td>
                      </tr>
                      <tr className="hover:bg-gray-50">
                        <td className="py-3 text-sm text-gray-900">Business Cost</td>
                        <td className="py-3 text-sm text-gray-900 text-right">
                          {formatCurrency(salesData?.totals?.business_cost_total || 0)}
                        </td>
                        <td className="py-3 text-sm text-gray-900 text-right">—</td>
                      </tr>
                      <tr className="hover:bg-gray-50">
                        <td className="py-3 text-sm text-gray-900">Tax Total</td>
                        <td className="py-3 text-sm text-gray-900 text-right">—</td>
                        <td className="py-3 text-sm text-gray-900 text-right">
                          {formatCurrency(salesData?.totals?.tax_total || 0)}
                        </td>
                      </tr>
                      <tr className="border-t border-gray-200 bg-gray-50">
                        <td className="py-3 text-sm font-semibold text-gray-900">Profit (Revenue - Tax)</td>
                        <td className="py-3 text-sm font-semibold text-gray-900 text-right">—</td>
                        <td className="py-3 text-sm font-semibold text-gray-900 text-right">
                          {formatCurrency(salesData?.totals?.profit_total || 0)}
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>

                {/* Bottom statistics circles */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                  {/* Points Summary */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">
                      Points Summary
                    </h3>
                    <div className="flex gap-6">
                      <div className="text-center">
                        <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center text-white text-xl font-bold mb-2">
                          0
                        </div>
                        <div className="text-sm text-gray-600 flex items-center justify-center">
                          Points Given
                          <span className="ml-1 inline-flex items-center justify-center w-4 h-4 bg-blue-100 text-blue-600 rounded-full text-xs font-medium">
                            i
                          </span>
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center text-white text-xl font-bold mb-2">
                          0
                        </div>
                        <div className="text-sm text-gray-600 flex items-center justify-center">
                          Points Redeemed
                          <span className="ml-1 inline-flex items-center justify-center w-4 h-4 bg-blue-100 text-blue-600 rounded-full text-xs font-medium">
                            i
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Total IOU Outstanding */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">
                      Total IOU Outstanding
                    </h3>
                    <div className="text-center">
                      <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-bold mb-2">
                        $0.00
                      </div>
                      <div className="text-sm text-gray-600 flex items-center justify-center">
                        IOUs Outstanding
                        <span className="ml-1 inline-flex items-center justify-center w-4 h-4 bg-blue-100 text-blue-600 rounded-full text-xs font-medium">
                          i
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Bottom buttons */}
      {hasRunReport && !filterLoading && !filterError && (
        <div className="flex justify-end gap-4 mt-6">
          <button
            onClick={handleExport}
            className="px-4 py-2 bg-white border border-gray-300 rounded-md hover:bg-gray-50 text-gray-700 flex items-center font-medium shadow-sm"
          >
            Export
            <span className="ml-1">▼</span>
          </button>
          <button
            onClick={handlePrint}
            className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 font-medium"
          >
            Print
          </button>
        </div>
      )}
    </div>
  );
}

export default SalesSummary;
