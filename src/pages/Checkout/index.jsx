import { useState, useEffect } from "react";
import Button from "../../components/Button";
import useCheckout from "../../features/checkout/hooks/useCheckout";

/**
 * Checkout/POS Page Component
 *
 * Page Layout:
 * - Left Side: Main content area (customer selection, shopping cart, service type buttons, barcode scanner)
 * - Right Side: Price breakdown sidebar (price details, payment methods, checkout buttons)
 */
function Checkout() {
  // ========== Checkout Hook Integration ==========
  const {
    // Data
    services,
    serviceCategories,
    staff,
    customers,
    cartItems,
    bookingTotal,
    currentBusiness,
    
    // Selection states
    selectedCustomer,
    selectedEmployee,
    selectedDate,
    selectedTime,
    availableTimeSlots,
    
    // Loading states
    loading,
    error,
    
    // API operations
    loadServices,
    loadServiceCategories,
    loadStaff,
    loadCustomers,
    getEmployeeServices,
    loadAvailableTimeSlots,
    
    // Cart management
    addServiceToCart,
    updateCartItem,
    removeFromCart,
    clearCart,
    
    // Selection methods
    selectCustomer,
    selectEmployee,
    selectDate,
    selectTime,
    
    // Booking processing
    createAppointment,
    completeBooking,
    
    // Utilities
    setError,
  } = useCheckout();

  // ========== Component State Management ==========

  // Payment method state
  const [paymentMethod, setPaymentMethod] = useState("cash");

  // Barcode input state
  const [barcodeInput, setBarcodeInput] = useState("");

  // ========== Modal State Management ==========

  // Service modal visibility state
  const [showServiceModal, setShowServiceModal] = useState(false);

  // Customer required alert modal state
  const [showCustomerRequiredAlert, setShowCustomerRequiredAlert] = useState(false);

  // Track which price input is being edited
  const [editingPriceId, setEditingPriceId] = useState(null);
  const [editingPriceValue, setEditingPriceValue] = useState("");

  // Track which discount input is being edited
  const [editingDiscountId, setEditingDiscountId] = useState(null);
  const [editingDiscountValue, setEditingDiscountValue] = useState("");

  // Payment amounts state
  const [paymentAmounts, setPaymentAmounts] = useState({
    cash: 0,
    check: 0,
    iou: 0,
    creditCard: 0,
    giftCard: 0,
    other: 0,
  });

  // Service modal form state
  const [serviceForm, setServiceForm] = useState({
    service: null,
    provider: null,
    startTime: null,
  });

  // ========== Data Loading Effects ==========

  // Load initial data on component mount
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        await Promise.all([
          loadServices({ show_online: true }),
          loadServiceCategories(),
          loadStaff({ is_active: true }),
          loadCustomers({ fields: 'id,first_name,last_name,email,phone,point_balance' }),
        ]);
      } catch (err) {
        console.error('Failed to load initial data:', err);
        setError('Failed to load checkout data. Please refresh the page.');
      }
    };

    loadInitialData();
  }, []);

  // Load available services when employee is selected
  useEffect(() => {
    if (selectedEmployee) {
      getEmployeeServices(selectedEmployee.id)
        .then(employeeServices => {
          console.log('Employee services loaded:', employeeServices);
        })
        .catch(err => {
          console.error('Failed to load employee services:', err);
        });
    }
  }, [selectedEmployee]);

  // Load available time slots when employee, date, and service are selected
  useEffect(() => {
    if (selectedEmployee && selectedDate && serviceForm.service) {
      loadAvailableTimeSlots({
        date: selectedDate,
        service_id: serviceForm.service.id,
        employee_id: selectedEmployee.id,
      });
    }
  }, [selectedEmployee, selectedDate, serviceForm.service]);

  // ========== Service Type Configuration ==========

  // Service type button configuration (displayed at page bottom)
  const serviceTypes = [
    { id: "service", label: "Service", icon: "📅" },
    { id: "product", label: "Product", icon: "🧴" },
    { id: "giftcard", label: "Gift Card", icon: "🎁" },
    { id: "package", label: "Package", icon: "📦" },
    { id: "membership", label: "Membership", icon: "👥" },
  ];

  // ========== Helper Functions ==========

  // Format customer display name
  const formatCustomerName = (customer) => {
    if (customer.first_name && customer.last_name) {
      return `${customer.first_name} ${customer.last_name}`;
    }
    return customer.first_name || customer.last_name || customer.email || 'Unknown Customer';
  };

  // Format employee display name
  const formatEmployeeName = (employee) => {
    if (employee.user?.first_name && employee.user?.last_name) {
      return `${employee.user.first_name} ${employee.user.last_name}`;
    }
    return `Employee #${employee.id}`;
  };

  // Get customer initials for avatar
  const getCustomerInitials = (customer) => {
    const name = formatCustomerName(customer);
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  // Generate time slots for today (simplified)
  const generateTimeSlots = () => {
    const slots = [];
    for (let hour = 9; hour <= 20; hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
        const displayString = hour >= 12 
          ? `${hour === 12 ? 12 : hour - 12}:${minute.toString().padStart(2, '0')} PM`
          : `${hour}:${minute.toString().padStart(2, '0')} AM`;
        slots.push({ value: timeString, label: displayString });
      }
    }
    return slots;
  };

  // ========== Calculation Functions ==========

  // Calculate total amount
  const calculateTotal = () => {
    return bookingTotal.total.toFixed(2);
  };

  // Calculate subtotal for services
  const calculateServiceTotal = () => {
    return bookingTotal.subtotal.toFixed(2);
  };

  // Calculate total amount paid
  const calculateAmountPaid = () => {
    return Object.values(paymentAmounts)
      .reduce((sum, amount) => sum + (parseFloat(amount) || 0), 0)
      .toFixed(2);
  };

  // Calculate change due
  const calculateChangeDue = () => {
    const amountPaid = parseFloat(calculateAmountPaid()) || 0;
    const amountDue = parseFloat(calculateTotal()) || 0;
    const changeDue = amountPaid - amountDue;
    return changeDue.toFixed(2);
  };

  // ========== Event Handlers ==========

  // Handle customer selection
  const handleCustomerSelect = (customerId) => {
    const customer = customers.find(c => c.id === parseInt(customerId));
    selectCustomer(customer);
  };

  // Handle service type button click
  const handleServiceTypeClick = (typeId) => {
    if (typeId === "service") {
      // Check if customer is selected first
      if (!selectedCustomer) {
        setShowCustomerRequiredAlert(true);
        return;
      }
      
      // Initialize service form with first available options
      setServiceForm({
        service: services.length > 0 ? services[0] : null,
        provider: staff.length > 0 ? staff[0] : null,
        startTime: null,
      });
      
      setShowServiceModal(true);
    }
    // Handle other service types later
  };

  // Handle service form input change
  const handleServiceFormChange = (field, value) => {
    setServiceForm((prev) => {
      const newForm = { ...prev, [field]: value };
      
      // If service or provider changed, reset time
      if (field === 'service' || field === 'provider') {
        newForm.startTime = null;
        
        // Update selected employee for availability checking
        if (field === 'provider') {
          selectEmployee(value);
        }
      }
      
      return newForm;
    });
  };

  // Handle add service
  const handleAddService = async () => {
    if (!serviceForm.service || !serviceForm.provider || !serviceForm.startTime) {
      setError('Please fill in all required fields');
      return;
    }

    try {
      // Add service to cart
      addServiceToCart(
        serviceForm.service,
        serviceForm.provider,
        serviceForm.startTime
      );

      setShowServiceModal(false);

      // Reset form
      setServiceForm({
        service: services.length > 0 ? services[0] : null,
        provider: staff.length > 0 ? staff[0] : null,
        startTime: null,
      });
    } catch (err) {
      setError('Failed to add service to cart');
    }
  };

  // Handle cancel service modal
  const handleCancelService = () => {
    setShowServiceModal(false);
  };

  // Handle close customer required alert
  const handleCloseCustomerRequiredAlert = () => {
    setShowCustomerRequiredAlert(false);
  };

  // Handle price input focus - clear input for editing
  const handlePriceFocus = (itemId, currentPrice) => {
    setEditingPriceId(itemId);
    setEditingPriceValue("");
  };

  // Handle price input change during editing
  const handlePriceChange = (itemId, newValue) => {
    setEditingPriceValue(newValue);
  };

  // Handle price save (Enter key only)
  const handlePriceSave = (itemId) => {
    const numericValue = parseFloat(editingPriceValue) || 0;

    updateCartItem(itemId, {
      price: numericValue,
    });

    setEditingPriceId(null);
    setEditingPriceValue("");
  };

  // Handle price key press (Enter to save)
  const handlePriceKeyPress = (e, itemId) => {
    if (e.key === "Enter") {
      handlePriceSave(itemId);
    }
  };

  // Handle remove item
  const handleRemoveItem = (itemId) => {
    removeFromCart(itemId);
  };

  // Handle payment amount change
  const handlePaymentAmountChange = (paymentType, amount) => {
    setPaymentAmounts((prev) => ({
      ...prev,
      [paymentType]: amount,
    }));
  };

  // Handle discount input focus - clear input for editing
  const handleDiscountFocus = (itemId, currentDiscount) => {
    setEditingDiscountId(itemId);
    setEditingDiscountValue("");
  };

  // Handle discount input change during editing
  const handleDiscountChange = (itemId, newValue) => {
    setEditingDiscountValue(newValue);
  };

  // Handle discount save (Enter key only)
  const handleDiscountSave = (itemId) => {
    const numericValue = parseFloat(editingDiscountValue) || 0;

    updateCartItem(itemId, {
      discount: numericValue,
    });

    setEditingDiscountId(null);
    setEditingDiscountValue("");
  };

  // Handle discount key press (Enter to save)
  const handleDiscountKeyPress = (e, itemId) => {
    if (e.key === "Enter") {
      handleDiscountSave(itemId);
    }
  };

  // Handle checkout
  const handleCheckout = async () => {
    if (!selectedCustomer) {
      setShowCustomerRequiredAlert(true);
      return;
    }

    if (cartItems.length === 0) {
      setError('Please add at least one service to checkout');
      return;
    }

    try {
      // Prepare payment methods
      const paymentMethods = Object.entries(paymentAmounts)
        .filter(([type, amount]) => amount > 0)
        .map(([type, amount]) => ({
          type,
          amount: parseFloat(amount),
        }));

      await completeBooking(selectedCustomer.id, paymentMethods, {
        notes: 'Checkout from POS system',
      });

      // Reset payment amounts
      setPaymentAmounts({
        cash: 0,
        check: 0,
        iou: 0,
        creditCard: 0,
        giftCard: 0,
        other: 0,
      });

      // Success message could be added here
      console.log('Checkout completed successfully');
    } catch (err) {
      setError('Checkout failed: ' + err.message);
    }
  };

  // ========== Page Rendering ==========

  // Show loading state
  if (loading.services || loading.staff || loading.customers) {
    return (
      <div className="flex bg-gray-50 items-center justify-center" style={{ height: "calc(100vh - 64px)" }}>
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading checkout data...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="flex bg-gray-50 items-center justify-center" style={{ height: "calc(100vh - 64px)" }}>
        <div className="text-center max-w-md">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-800 mb-2">Error Loading Checkout</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <Button 
            onClick={() => window.location.reload()} 
            className="!bg-blue-600 !hover:bg-blue-700 text-white px-4 py-2 rounded"
          >
            Retry
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex bg-gray-50" style={{ height: "calc(100vh - 64px)" }}>
      {/* ========== Left Side Main Content Area ========== */}
      <div className="flex-1 flex flex-col">
        {/* ---------- Header Section: Customer Selection and Action Buttons ---------- */}
        <div className="bg-white p-3 border-b border-gray-200">
          <div className="flex justify-between items-center">
            {/* Customer Selection Area - Displayed at top left */}
            <div className="flex-1 mr-4">
              <div className="border border-gray-300 rounded px-4 py-3 bg-white relative">
                <div className="flex items-center space-x-4">
                  {/* Customer Avatar */}
                  <div className="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center flex-shrink-0">
                    {selectedCustomer ? (
                      <span className="text-sm font-semibold text-gray-700">
                        {getCustomerInitials(selectedCustomer)}
                      </span>
                    ) : (
                      <svg
                        className="w-6 h-6 text-gray-500"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                          clipRule="evenodd"
                        />
                      </svg>
                    )}
                  </div>

                  {/* Customer Information Display Area */}
                  <div className="flex-1 relative">
                    {selectedCustomer ? (
                      /* Customer Selected - Show customer details */
                      <div>
                        <div className="flex items-center space-x-2">
                          <h3 className="font-semibold text-gray-800 text-base">
                            {formatCustomerName(selectedCustomer)} {selectedCustomer.phone && `- ${selectedCustomer.phone}`}
                          </h3>
                        </div>
                        <p className="text-sm text-blue-500">
                          {selectedCustomer.email}
                        </p>
                      </div>
                    ) : (
                      /* No Customer Selected - Show selection prompt */
                      <div className="text-gray-600 text-base">
                        Select a customer
                      </div>
                    )}

                    {/* Customer Selection Dropdown (hidden select element) */}
                    <select
                      className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                      value={selectedCustomer?.id || ""}
                      onChange={(e) => handleCustomerSelect(e.target.value)}
                    >
                      <option value="">Select a customer</option>
                      {customers.map((customer) => (
                        <option key={customer.id} value={customer.id}>
                          {formatCustomerName(customer)}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Points Selector - Only shown when customer is selected */}
                  {selectedCustomer && (
                    <div className="flex-shrink-0 relative z-10">
                      <select className="bg-blue-500 text-white rounded px-3 py-1 text-sm focus:outline-none">
                        <option>{selectedCustomer.point_balance || 0} pt</option>
                      </select>
                    </div>
                  )}

                  {/* Dropdown Arrow Indicator */}
                  <div className="flex-shrink-0 relative">
                    <svg
                      className="w-4 h-4 fill-current text-gray-400"
                      viewBox="0 0 20 20"
                    >
                      <path d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
                    </svg>

                    {/* Dropdown Arrow Click Area */}
                    <select
                      className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                      value={selectedCustomer?.id || ""}
                      onChange={(e) => handleCustomerSelect(e.target.value)}
                    >
                      <option value="">Select a customer</option>
                      {customers.map((customer) => (
                        <option key={customer.id} value={customer.id}>
                          {formatCustomerName(customer)}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
              </div>
            </div>

            {/* Action Button Group - Displayed at top right */}
            <div className="flex space-x-2 flex-shrink-0">
              <Button className="!bg-green-600 !hover:bg-green-700 text-white px-3 py-2 rounded text-sm">
                Create New
              </Button>
              {selectedCustomer && (
                <>
                  <Button className="!bg-white border border-gray-300 !text-gray-700 px-3 py-2 rounded hover:bg-gray-50 text-sm">
                    ✏️ Edit
                  </Button>
                  <Button className="!bg-white border border-gray-300 !text-gray-700 px-3 py-2 rounded hover:bg-gray-50 text-sm">
                    🕐 History
                  </Button>
                </>
              )}
              <Button className="!bg-white border border-gray-300 !text-red-600 px-3 py-2 rounded hover:bg-red-50 text-sm">
                🔄 Refund
              </Button>
            </div>
          </div>
        </div>

        {/* ---------- Customer Details Section - Only shown when customer is selected ---------- */}
        {selectedCustomer && (
          <div className="bg-white border-b border-gray-200 p-4">
            <div className="grid grid-cols-4 gap-4">
              {/* Customer Registration Date */}
              <div className="text-center border border-gray-200 rounded p-3">
                <div className="font-semibold text-gray-800 mb-1">
                  {selectedCustomer.created_at ? new Date(selectedCustomer.created_at).toLocaleDateString() : 'N/A'}
                </div>
                <div className="text-gray-600 text-sm">Customer Since</div>
              </div>

              {/* Last Visit Date */}
              <div className="text-center border border-gray-200 rounded p-3">
                <div className="font-semibold text-gray-800 mb-1">
                  {selectedCustomer.last_visited || 'Never'}
                </div>
                <div className="text-gray-600 text-sm">Last Visit</div>
              </div>

              {/* Birthday Information */}
              <div className="text-center border border-gray-200 rounded p-3">
                <div className="font-semibold text-gray-800 mb-1">
                  {selectedCustomer.birthday ? new Date(selectedCustomer.birthday).toLocaleDateString() : 'Not available'}
                </div>
                <div className="text-gray-600 text-sm">Birthday</div>
              </div>

              {/* Membership Level */}
              <div className="text-center border border-gray-200 rounded p-3">
                <div className="font-semibold text-gray-800 mb-1">
                  {selectedCustomer.membership || 'Standard'}
                </div>
                <div className="text-gray-600 text-sm">Membership</div>
              </div>
            </div>
          </div>
        )}

        {/* ---------- Main Content Area - Displayed at page center ---------- */}
        <div className="flex-1 flex flex-col p-4">
          {!selectedCustomer ? (
            /* No Customer Selected State */
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <div className="mb-4">
                  <div className="w-16 h-16 bg-gray-300 rounded-full flex items-center justify-center mx-auto mb-3">
                    <svg
                      className="w-8 h-8 text-gray-500"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <h2 className="text-xl font-semibold text-gray-800 mb-2">
                    Select a Customer
                  </h2>
                  <p className="text-gray-600 text-sm">
                    Get started by selecting a customer above.
                  </p>
                </div>
              </div>
            </div>
          ) : cartItems.length === 0 ? (
            /* Customer Selected but Cart Empty */
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <div className="mb-4">
                  <div className="w-20 h-20 bg-gray-300 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg
                      className="w-12 h-12 text-gray-500"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={1.5}
                        d="M3 3h2l.4 2M7 13h10l4-8H5.4m-.4-2L3 3m4 10v6a1 1 0 001 1h6a1 1 0 001-1v-6M9 19h6"
                      />
                    </svg>
                  </div>
                  <h2 className="text-2xl font-semibold text-gray-800 mb-2">
                    Shopping Cart Empty
                  </h2>
                  <p className="text-gray-600">
                    Get started by adding an item from below.
                  </p>
                </div>
              </div>
            </div>
          ) : (
            /* Customer Selected and Cart Has Items - Show Cart Table */
            <div className="flex-1 overflow-auto">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                {/* Table Header */}
                <div className="bg-gray-100 grid grid-cols-11 gap-2 p-3 text-sm font-medium text-gray-700 border-b border-gray-200">
                  <div className="col-span-2">Item</div>
                  <div>Code</div>
                  <div>Service Provider</div>
                  <div>Qty</div>
                  <div>Price</div>
                  <div>Discount</div>
                  <div>Deposit</div>
                  <div>Sub Total</div>
                  <div>Pts</div>
                  <div>Use Pts</div>
                  <div></div>
                </div>

                {/* Table Body */}
                {cartItems.map((item) => (
                  <div
                    key={item.id}
                    className="grid grid-cols-11 gap-2 p-3 border-b border-gray-100 hover:bg-gray-50"
                  >
                    {/* Item Name */}
                    <div className="col-span-2 text-blue-600 font-medium">
                      {item.service_name}
                    </div>

                    {/* Code */}
                    <div className="text-gray-600">SRVC</div>

                    {/* Service Provider */}
                    <div>
                      <select
                        value={item.employee_id || ''}
                        onChange={(e) => {
                          const employeeId = parseInt(e.target.value);
                          const employee = staff.find(s => s.id === employeeId);
                          updateCartItem(item.id, {
                            employee_id: employeeId,
                            employee_name: employee ? formatEmployeeName(employee) : '',
                          });
                        }}
                        className="w-full px-2 py-1 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                      >
                        <option value="">Select Provider</option>
                        {staff.map((employee) => (
                          <option key={employee.id} value={employee.id}>
                            {formatEmployeeName(employee)}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Quantity */}
                    <div className="text-center">1</div>

                    {/* Price - Editable */}
                    <div>
                      <input
                        type="text"
                        value={
                          editingPriceId === item.id
                            ? editingPriceValue
                            : `$${(parseFloat(item.price) || 0).toFixed(2)}`
                        }
                        onChange={(e) =>
                          handlePriceChange(item.id, e.target.value)
                        }
                        onFocus={() => handlePriceFocus(item.id, item.price)}
                        onKeyPress={(e) => handlePriceKeyPress(e, item.id)}
                        className="w-full px-2 py-1 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                        placeholder="$0.00"
                      />
                    </div>

                    {/* Discount */}
                    <div>
                      <input
                        type="text"
                        value={
                          editingDiscountId === item.id
                            ? editingDiscountValue
                            : `$${(parseFloat(item.discount) || 0).toFixed(2)}`
                        }
                        onChange={(e) =>
                          handleDiscountChange(item.id, e.target.value)
                        }
                        onFocus={() =>
                          handleDiscountFocus(item.id, item.discount)
                        }
                        onKeyPress={(e) => handleDiscountKeyPress(e, item.id)}
                        className="w-full px-2 py-1 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                        placeholder="$0.00"
                      />
                    </div>

                    {/* Deposit */}
                    <div>
                      <input
                        type="number"
                        value={item.deposit || 0}
                        onChange={(e) => {
                          updateCartItem(item.id, {
                            deposit: parseFloat(e.target.value) || 0,
                          });
                        }}
                        className="w-full px-2 py-1 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                        placeholder="0"
                        step="0.01"
                        min="0"
                      />
                    </div>

                    {/* Sub Total */}
                    <div className="font-medium">
                      ${item.subtotal.toFixed(2)}
                    </div>

                    {/* Points */}
                    <div className="text-center">{item.points || 0}</div>

                    {/* Use Points */}
                    <div className="text-center">
                      <input
                        type="checkbox"
                        checked={item.usePoints || false}
                        onChange={(e) => {
                          updateCartItem(item.id, {
                            usePoints: e.target.checked,
                          });
                        }}
                        className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                      />
                    </div>

                    {/* Actions Menu */}
                    <div className="text-center">
                      <button
                        onClick={() => handleRemoveItem(item.id)}
                        className="text-gray-400 hover:text-red-600 text-lg"
                      >
                        ⋮
                      </button>
                    </div>
                  </div>
                ))}

                {/* Total Row */}
                <div className="bg-gray-50 grid grid-cols-11 gap-2 p-3 text-sm font-bold border-t border-gray-200">
                  <div className="col-span-8">Total</div>
                  <div>${calculateServiceTotal()}</div>
                  <div className="col-span-2"></div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* ---------- Service Type Button Group - Displayed at page bottom ---------- */}
        <div className="bg-white border-t border-gray-200 p-3">
          <div className="flex justify-center space-x-6">
            {serviceTypes.map((type) => (
              <button
                key={type.id}
                onClick={() => handleServiceTypeClick(type.id)}
                className="flex flex-col items-center p-3 hover:bg-gray-50 rounded-lg transition-colors"
                disabled={type.id !== 'service'} // Only service is implemented
              >
                <div className="text-xl mb-1">{type.icon}</div>
                <span className="text-xs text-gray-700">{type.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* ---------- Barcode Scanner - Displayed at page bottom ---------- */}
        <div className="bg-white border-t border-gray-200 p-3">
          <div className="max-w-md mx-auto">
            <input
              type="text"
              placeholder="Scan barcode or type it (not implemented)"
              className="w-full px-3 py-2 border-2 border-green-300 rounded-lg focus:outline-none focus:border-green-500 bg-green-50 text-sm"
              value={barcodeInput}
              onChange={(e) => setBarcodeInput(e.target.value)}
              disabled
            />
          </div>
        </div>
      </div>

      {/* ========== Right Side Price Breakdown Sidebar ========== */}
      <div className="w-72 bg-white border-l border-gray-200 flex flex-col">
        {/* ---------- Price Breakdown Section ---------- */}
        <div className="flex-1 p-3">
          <div className="space-y-2">
            {/* Service Charges */}
            <div className="flex justify-between items-center">
              <span className="text-gray-700 text-sm">Services</span>
              <span className="font-medium text-sm">
                ${calculateServiceTotal()}
              </span>
            </div>

            {/* Product Charges */}
            <div className="flex justify-between items-center">
              <span className="text-gray-700 text-sm">Products</span>
              <span className="font-medium text-sm">
                $0.00
              </span>
            </div>

            {/* Gift Card/Package Charges */}
            <div className="flex justify-between items-center">
              <span className="text-gray-700 text-sm">Gift Card/Packages</span>
              <span className="font-medium text-sm">
                $0.00
              </span>
            </div>

            {/* Discount */}
            <div className="flex justify-between items-center">
              <span className="text-gray-700 text-sm">Discount</span>
              <span className="font-medium text-sm">
                ${bookingTotal.discount.toFixed(2)}
              </span>
            </div>

            {/* Deposit */}
            <div className="flex justify-between items-center">
              <span className="text-gray-700 text-sm">Deposit</span>
              <span className="font-medium text-red-600 text-sm">
                ($0.00)
              </span>
            </div>

            {/* Tax */}
            <div className="flex justify-between items-center">
              <span className="text-gray-700 text-sm">Tax (8%)</span>
              <span className="font-medium text-sm">
                ${bookingTotal.tax.toFixed(2)}
              </span>
            </div>

            {/* Fee */}
            <div className="flex justify-between items-center">
              <span className="text-gray-700 text-sm">Fee</span>
              <span className="font-medium text-sm">
                $0.00
              </span>
            </div>

            {/* Tip */}
            <div className="flex justify-between items-center">
              <span className="text-gray-700 text-sm">Tip</span>
              <span className="font-medium text-sm">
                ${bookingTotal.tip.toFixed(2)}
              </span>
            </div>

            {/* Total Amount Due */}
            <div className="border-t pt-2">
              <div className="flex justify-between items-center bg-green-100 px-2 py-2 rounded">
                <span className="text-base font-semibold text-green-800">
                  Amount Due
                </span>
                <span className="text-base font-bold text-green-800">
                  ${calculateTotal()}
                </span>
              </div>
            </div>
          </div>

          {/* ---------- Payment Methods Section ---------- */}
          <div className="mt-4 space-y-1">
            {/* Cash, Check, IOU, Credit Card */}
            {[
              { label: "Cash", key: "cash" },
              { label: "Check", key: "check" },
              { label: "IOU", key: "iou" },
              { label: "Credit Card", key: "creditCard" },
            ].map((method) => (
              <div
                key={method.key}
                className="flex justify-between items-center"
              >
                <span className="text-gray-700 text-sm">{method.label}</span>
                <input
                  type="number"
                  value={paymentAmounts[method.key]}
                  onChange={(e) =>
                    handlePaymentAmountChange(method.key, e.target.value)
                  }
                  className="w-16 text-right px-1 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                  placeholder="0.00"
                  step="0.01"
                  min="0"
                />
              </div>
            ))}

            {/* Gift Card Payment */}
            <div className="flex justify-between items-center">
              <span className="text-gray-700 text-sm">Gift Card</span>
              <input
                type="number"
                value={paymentAmounts.giftCard}
                onChange={(e) =>
                  handlePaymentAmountChange("giftCard", e.target.value)
                }
                className="w-16 text-right px-1 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                placeholder="0.00"
                step="0.01"
                min="0"
              />
            </div>

            {/* Other Payment Methods */}
            <div className="flex justify-between items-center">
              <span className="text-gray-700 text-sm">Other</span>
              <input
                type="number"
                value={paymentAmounts.other}
                onChange={(e) =>
                  handlePaymentAmountChange("other", e.target.value)
                }
                className="w-16 text-right px-1 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                placeholder="0.00"
                step="0.01"
                min="0"
              />
            </div>
          </div>
        </div>

        {/* ---------- Payment Summary Section ---------- */}
        <div className="mt-4 space-y-1 px-3 pb-3">
          {/* Amount Paid */}
          <div className="flex justify-between items-center bg-gray-100 px-3 py-2 rounded">
            <span className="text-green-600 font-semibold text-sm">
              Amount Paid
            </span>
            <span className="text-green-600 font-bold text-sm">
              ${calculateAmountPaid()}
            </span>
          </div>

          {/* Change Due */}
          <div className="flex justify-between items-center bg-gray-100 px-3 py-2 rounded">
            <span className="text-gray-800 font-semibold text-sm">
              Change Due
            </span>
            <span
              className={`font-bold text-sm ${
                parseFloat(calculateChangeDue()) >= 0
                  ? "text-green-600"
                  : "text-red-600"
              }`}
            >
              ${calculateChangeDue()}
            </span>
          </div>
        </div>

        {/* ---------- Bottom Action Buttons ---------- */}
        <div className="border-t border-gray-200 p-3 space-y-2">
          {/* Connect Button */}
          <button className="w-full bg-gray-800 hover:bg-gray-900 text-white py-2 rounded-lg font-medium text-sm">
            🔗 Connect
          </button>

          {/* Checkout Button Group */}
          <div className="relative">
            <button 
              onClick={handleCheckout}
              disabled={loading.appointment || !selectedCustomer || cartItems.length === 0}
              className="w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white py-2 rounded-lg font-medium text-sm transition-colors"
            >
              {loading.appointment ? 'Processing...' : 'Checkout'}
            </button>
            <button className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-green-700 hover:bg-green-800 px-2 py-1 rounded text-white text-xs">
              ▼
            </button>
          </div>
        </div>
      </div>

      {/* ========== Customer Required Alert Modal ========== */}
      {showCustomerRequiredAlert && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            {/* Alert Icon */}
            <div className="flex items-center justify-center mb-4">
              <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center">
                <svg
                  className="w-8 h-8 text-orange-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                  />
                </svg>
              </div>
            </div>

            {/* Alert Message */}
            <div className="text-center mb-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-2">
                Customer Required
              </h3>
              <p className="text-gray-600">
                Please add customers first before adding services.
              </p>
            </div>

            {/* Alert Actions */}
            <div className="flex justify-center">
              <button
                onClick={handleCloseCustomerRequiredAlert}
                className="px-6 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
              >
                OK
              </button>
            </div>
          </div>
        </div>
      )}

      {/* ========== Service Modal ========== */}
      {showServiceModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl mx-4">
            {/* Modal Header */}
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-semibold text-gray-800">
                Add Service
              </h2>
              <button
                onClick={handleCancelService}
                className="text-gray-500 hover:text-gray-700 text-2xl"
              >
                ×
              </button>
            </div>

            {/* Modal Content */}
            <div className="grid grid-cols-3 gap-4 mb-6">
              {/* Service Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <span className="text-red-500">*</span> Service
                </label>
                <select
                  value={serviceForm.service?.id || ''}
                  onChange={(e) => {
                    const service = services.find(s => s.id === parseInt(e.target.value));
                    handleServiceFormChange("service", service);
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select Service</option>
                  {services.map((service) => (
                    <option key={service.id} value={service.id}>
                      {service.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Service Provider Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Service Provider
                </label>
                <select
                  value={serviceForm.provider?.id || ''}
                  onChange={(e) => {
                    const provider = staff.find(s => s.id === parseInt(e.target.value));
                    handleServiceFormChange("provider", provider);
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select Provider</option>
                  {staff.map((employee) => (
                    <option key={employee.id} value={employee.id}>
                      {formatEmployeeName(employee)}
                    </option>
                  ))}
                </select>
              </div>

              {/* Start Time Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <span className="text-red-500">*</span> Start Time:
                </label>
                <div className="relative">
                  <select
                    value={serviceForm.startTime || ''}
                    onChange={(e) =>
                      handleServiceFormChange("startTime", e.target.value)
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 appearance-none"
                  >
                    <option value="">Select Time</option>
                    {generateTimeSlots().map((slot) => (
                      <option key={slot.value} value={slot.value}>
                        {slot.label}
                      </option>
                    ))}
                  </select>
                  <div className="absolute inset-y-0 right-3 flex items-center pointer-events-none">
                    <svg
                      className="w-4 h-4 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                  </div>
                </div>
              </div>
            </div>

            {/* Modal Actions */}
            <div className="flex justify-end space-x-3">
              <button
                onClick={handleCancelService}
                className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleAddService}
                disabled={!serviceForm.service || !serviceForm.provider || !serviceForm.startTime}
                className="px-6 py-2 bg-green-600 disabled:bg-gray-400 text-white rounded-lg hover:bg-green-700 disabled:hover:bg-gray-400 transition-colors"
              >
                Add Service
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default Checkout;
