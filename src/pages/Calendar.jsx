import { useState, useCallback, useEffect } from 'react'

// Import enhanced calendar components and hooks with iOS-like functionality
import { useAdvancedCalendar } from '../features/calendar/hooks/useAdvancedCalendar'
import { useActionSheet } from '../features/calendar/hooks/useActionSheet'
import { 
  CalendarSidebar,
  CalendarContainer,
  Calendar,
  CalendarActionSheet,
  CalendarSettingsPanel,
  NavigationFeedback
} from '../features/calendar/components'
import AppointmentModal from '../features/calendar/components/modals/AppointmentModal'

// Import appointment management services
import AppointmentStatusService from '../features/calendar/services/appointmentStatusService'
import { appointmentService } from '../features/calendar/services/appointmentService'
import { APPOINTMENT_STATUSES } from '../features/calendar/constants/calendarConfig'

/**
 * Enhanced Calendar Component with iOS-like functionality
 * 
 * FEATURES INCLUDED:
 * Working Hours Management - Visual display and editing
 * Action Sheets - Context menus for time slots and appointments
 * Calendar Configuration - Time resolution, display hours, week start
 * Advanced State Management - Real-time updates, performance tracking
 * Employee Profile Display - Smart single/multi employee selection
 * Appointment Status Tracking - Visual status indicators and management
 * Working Hours Highlighting - Visual working hours in calendar grid
 * Scroll Position Memory - Remembers scroll position
 * Auto Scroll to Current Time - Smart navigation
 */
const CalendarPage = () => {
  // Disable global scrollbar for calendar page
  useEffect(() => {
    // Store original overflow style
    const originalOverflow = document.body.style.overflow
    
    // Disable scrolling
    document.body.style.overflow = 'hidden'
    
    // Cleanup function to restore scrolling when component unmounts
    return () => {
      document.body.style.overflow = originalOverflow
    }
  }, [])

  // Enhanced calendar state management with iOS-like features
  const {
    selectedDate,
    displayMode,
    employees,
    selectedEmployees,
    selectedEmployeeIDs,
    filteredAppointments,
    appointments,
    isLoading,
    error,
    config,
    serviceCategories,
    scrollPosition,
    setScrollPosition,
    
    // Configuration
    updateConfig,
    
    // Navigation methods
    selectDate,
    setViewMode,
    
    // Employee management
    updateSelectedEmployeeIDs,
    
    // Appointment management
    createAppointment,
    updateAppointment,
    deleteAppointment,
    refreshAppointments,
    
    // Working hours functions
    getWorkingHoursForEmployee,
    getWorkingHoursForEmployeeSync,
    isEmployeeWorking,
    isWorkingHourSlot,
    scrollToCurrentTime
  } = useAdvancedCalendar()

  // Action sheet state for iOS-like context menus
  const {
    actionSheet,
    hideActionSheet,
    showTimeSlotActionSheet,
    showAppointmentActionSheet
  } = useActionSheet()

  // UI state
  const [isSidebarOpen, setIsSidebarOpen] = useState(false)
  const [isSettingsOpen, setIsSettingsOpen] = useState(false)
  const [selectedAppointment, setSelectedAppointment] = useState(null)
  const [isAppointmentDetailsOpen, setIsAppointmentDetailsOpen] = useState(false)

  const [navigationFeedback, setNavigationFeedback] = useState({
    message: '',
    type: 'info',
    isVisible: false
  })
  
  // Keep compatibility with existing sidebar
  const currentView = displayMode

  // Show toast notifications
  const showToast = useCallback((message, type = 'info') => {
    setNavigationFeedback({
      message,
      type,
      isVisible: true
    })
    
    // Auto-clear after 3 seconds
    setTimeout(() => {
      setNavigationFeedback(prev => ({ ...prev, isVisible: false }))
    }, 3000)
  }, [])

  // Enhanced appointment event handlers for iOS-like functionality
  const handleAppointmentCreate = useCallback(async (dateTime, employee) => {
    try {
      console.log('🆕 Creating appointment:', { dateTime, employee })
      showToast(`Opening appointment creation for ${employee?.name || 'Selected employee'} at ${new Date(dateTime).toLocaleString()}`, 'info')
      
      // TODO: Open appointment creation modal
      // For now, create a basic appointment
      const appointmentData = {
        customerId: `customer-${Date.now()}`,
        employeeId: employee?.id || selectedEmployees[0]?.id,
        startTime: dateTime,
        duration: 60,
        serviceName: 'Service',
        price: 100,
        status: 'pending'
      }
      
      const newAppointment = await createAppointment(appointmentData)
      console.log('✅ New appointment created:', newAppointment)
      
      // Immediately refresh appointments to show the new one
      await refreshAppointments()
      
      showToast('Appointment created successfully', 'success')
      
    } catch (error) {
      console.error('Failed to create appointment:', error)
      showToast('Failed to create appointment', 'error')
    }
  }, [createAppointment, selectedEmployees, refreshAppointments, showToast])

  const handleAppointmentEdit = useCallback(async (appointment) => {
    try {
      console.log('✏️ Editing appointment:', appointment)
      setSelectedAppointment(appointment)
      setIsAppointmentDetailsOpen(true) // Open unified modal (will start in view mode with edit button)
      
    } catch (error) {
      console.error('Failed to edit appointment:', error)
      showToast('Failed to open appointment edit modal', 'error')
    }
  }, [showToast])

  const handleAppointmentUpdate = useCallback(async (updatedAppointment) => {
    try {
      console.log('✅ Appointment updated:', updatedAppointment)
      
      // Refresh appointments to get the latest data
      await refreshAppointments()
      
      // Show success message
      showToast('Appointment updated successfully', 'success')
      
    } catch (error) {
      console.error('Failed to handle appointment update:', error)
      showToast('Failed to refresh appointments', 'error')
    }
  }, [refreshAppointments, showToast])

  const handleAppointmentCancel = useCallback(async (appointment) => {
    try {
      const reason = prompt('Please provide a reason for cancellation:')
      if (!reason) return
      
      await AppointmentStatusService.cancelAppointment(appointment.id, reason)
      await refreshAppointments()
      showToast(`Cancelled appointment: ${appointment.title}`, 'success')
    } catch (error) {
      console.error('Failed to cancel appointment:', error)
      showToast('Failed to cancel appointment', 'error')
    }
  }, [refreshAppointments, showToast])

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed': return '✅'
      case 'confirmed': return '🔵'
      case 'pending': return '⚪'
      case 'in-progress': return '🟡'
      case 'no-show': return '🔴'
      default: return '⚪'
    }
  }

  const handleAppointmentStatusChange = useCallback(async (appointmentId, newStatus, reason = null) => {
    try {
      console.log(`🔄 Status change requested: ${appointmentId} -> ${newStatus}`)
      
      // Use the dedicated status update API method that handles mapping
      const updated = await appointmentService.updateAppointmentStatus(appointmentId, newStatus, reason)
      
      console.log('✅ Status updated successfully:', updated)
      
      // First, refresh appointments to get the latest state from API
      await refreshAppointments()
      
              // Then, fetch the fresh detailed appointment data for the modal
        if (selectedAppointment && selectedAppointment.id === appointmentId) {
          try {
            const freshAppointmentData = await appointmentService.fetchAppointmentDetails(appointmentId)
            console.log('🔄 Fetched fresh appointment data for modal:', freshAppointmentData)
            
            // The data is already transformed by fetchAppointmentDetails
            setSelectedAppointment(freshAppointmentData)
          } catch (fetchError) {
            console.warn('Failed to fetch fresh appointment data, using API response:', fetchError)
            // Fallback to updating with the API response
            setSelectedAppointment(prev => ({ ...prev, ...updated }))
          }
        }
      
      showToast('Status updated successfully', 'success')
    } catch (error) {
      console.error('❌ Failed to update status:', error)
      showToast(error.message || 'Failed to update appointment status', 'error')
    }
  }, [appointmentService, refreshAppointments, selectedAppointment, showToast])

  // Enhanced event handlers that support both clicking and dragging
  const handleEventClick = useCallback((calendarEvent, event) => {
    // Only handle click if it wasn't a drag operation
    // The drag system will set a flag to prevent click after drag
    if (event && event.wasDragged) {
      return // Don't open modal if this was a drag operation
    }
    
    console.log('📅 Event clicked:', calendarEvent)
    
    if (calendarEvent.type === 'appointment') {
      // Do nothing - appointment details only accessible through hover overlay edit button
      console.log('📅 Appointment clicked, but modal access disabled. Use hover overlay edit button.')
      return
    } else {
      const eventType = calendarEvent.type === 'break' ? 'Break' : 'Event'
      const message = `${eventType}: ${calendarEvent.title}`
      showToast(message, 'info')
    }
  }, [showToast])

  // Enhanced date selection handler for mini calendar 
  const handleDateSelect = useCallback((date) => {
    // Ensure we have a proper Date object
    const dateObj = date instanceof Date ? date : new Date(date)
    selectDate(dateObj)
    showToast(`Selected ${dateObj.toLocaleDateString()}`, 'info')
    
    // Auto-close sidebar on mobile after selection
    if (window.innerWidth < 1024) {
      setIsSidebarOpen(false)
    }
  }, [selectDate, showToast])

  // Double-click handler for mini calendar - zoom to day view
  const handleDateDoubleClick = useCallback((date) => {
    // Ensure we have a proper Date object
    const dateObj = date instanceof Date ? date : new Date(date)
    
    // Switch to day view and select the date
    setViewMode('day')
    selectDate(dateObj)
    showToast(`Zoomed to ${dateObj.toLocaleDateString()}`, 'info')
    
    // Auto-close sidebar on mobile after selection
    if (window.innerWidth < 1024) {
      setIsSidebarOpen(false)
    }
  }, [setViewMode, selectDate, showToast])

  // Enhanced employee selection with view-aware logic (iOS-like behavior)
  const handleToggleEmployee = useCallback((employeeId) => {
    // Get current selection as array
    const currentSelection = Array.from(selectedEmployeeIDs)
    
    if (displayMode === 'day') {
      // Day view allows multiple employees
      if (currentSelection.includes(employeeId)) {
        if (currentSelection.length === 1) {
          showToast('At least one staff member must be selected', 'warning')
          return
        }
        // Remove employee
        const newSelection = currentSelection.filter(id => id !== employeeId)
        updateSelectedEmployeeIDs(newSelection)
      } else {
        // Add employee
        updateSelectedEmployeeIDs([...currentSelection, employeeId])
      }
    } else {
      // Week view only allows single employee (iOS behavior)
      updateSelectedEmployeeIDs([employeeId])
      showToast(`Switched to ${selectedEmployees.find(emp => emp.id === employeeId)?.name || 'employee'}`, 'info')
    }
  }, [displayMode, selectedEmployeeIDs, selectedEmployees, updateSelectedEmployeeIDs, showToast])



  const handleSelectAllEmployees = useCallback(() => {
    if (displayMode === 'week') {
      showToast('Week view can only display one employee at a time', 'warning')
      return
    }
    
    const allEmployeeIds = selectedEmployees.map(emp => emp.id)
    updateSelectedEmployeeIDs(allEmployeeIds)
    showToast('Selected all employees', 'info')
  }, [displayMode, selectedEmployees, updateSelectedEmployeeIDs, showToast])

  const handleDeselectAllEmployees = useCallback(() => {
    showToast('At least one staff member must be selected', 'warning')
  }, [showToast])



  // Error handling
  useEffect(() => {
    if (error) {
      showToast(error, 'error')
    }
  }, [error, showToast])

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex h-full items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading enhanced calendar...</p>
        </div>
      </div>
    )
  }

    return (
    <div className="calendar-container flex h-screen bg-gray-50 relative" style={{ minHeight: '100vh' }}>
      {/* Sidebar - Responsive */}
      <div className={`
        ${isSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
        fixed lg:relative inset-y-0 left-0 z-[1500] lg:z-auto
        w-80 lg:w-80 xl:w-96
        bg-white border-r border-gray-200
        transition-transform duration-300 ease-in-out lg:transition-none
        flex flex-col flex-shrink-0 h-full
        ${isSidebarOpen ? 'shadow-xl lg:shadow-none' : ''}
      `}>
        {/* Sidebar Header - Mobile Only */}
        <div className="lg:hidden bg-gray-50 px-4 py-3 border-b border-gray-200 flex items-center justify-between flex-shrink-0">
          <h2 className="text-sm font-semibold text-gray-900">Calendar Controls</h2>
          <button
            onClick={() => setIsSidebarOpen(false)}
            className="p-1 text-gray-500 hover:text-gray-700 rounded"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Enhanced Sidebar Content */}
        <div className="flex-1 min-h-0 overflow-hidden">
          <CalendarSidebar
            employees={employees}
            selectedEmployees={Array.from(selectedEmployeeIDs)}
            selectedDate={selectedDate}
            filteredAppointments={filteredAppointments}
            onToggleEmployee={handleToggleEmployee}
            onSelectAllEmployees={handleSelectAllEmployees}
            onDeselectAllEmployees={handleDeselectAllEmployees}
            onDateSelect={handleDateSelect}
            onDateDoubleClick={handleDateDoubleClick}
            currentView={currentView}
            serviceCategories={serviceCategories}
          />
        </div>
      </div>

      {/* Sidebar Overlay for Mobile */}
      {isSidebarOpen && (
        <div 
          className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-[1400]"
          onClick={() => setIsSidebarOpen(false)}
        />
      )}

      {/* Enhanced Main Calendar Area with iOS Features */}
      <div className="flex-1 min-w-0 relative" style={{ minHeight: '100vh' }}>
        {/* Calendar Container with Enhanced Features */}
        <div className="w-full h-full" style={{ minHeight: 'calc(100vh - 60px)' }}>
          <CalendarContainer isLoading={isLoading}>
            <Calendar
              className="h-full"
              selectedDate={selectedDate}
              displayMode={displayMode}
              selectedEmployees={selectedEmployees}
              selectedEmployeeIDs={selectedEmployeeIDs}
              filteredAppointments={filteredAppointments}
              config={config}
              updateConfig={updateConfig}
              selectDate={selectDate}
              setViewMode={setViewMode}
              onAppointmentCreate={handleAppointmentCreate}
              onAppointmentEdit={handleAppointmentEdit}
              onAppointmentCancel={handleAppointmentCancel}
              onAppointmentStatusChange={handleAppointmentStatusChange}
              onAppointmentClick={handleEventClick}
              
              createAppointment={createAppointment}
              updateAppointment={updateAppointment}
              deleteAppointment={deleteAppointment}
              refreshAppointments={refreshAppointments}
              getWorkingHoursForEmployee={getWorkingHoursForEmployee}
              getWorkingHoursForEmployeeSync={getWorkingHoursForEmployeeSync}
              isEmployeeWorking={isEmployeeWorking}
              isWorkingHourSlot={isWorkingHourSlot}
              scrollPosition={scrollPosition}
              setScrollPosition={setScrollPosition}
              scrollToCurrentTime={scrollToCurrentTime}
              currentEmployee={selectedEmployees[0]}
            />
          </CalendarContainer>
        </div>

        {/* Enhanced Navigation Feedback */}
        <NavigationFeedback 
          message={navigationFeedback.message}
          type={navigationFeedback.type}
          isVisible={navigationFeedback.isVisible}
        />
      </div>

      {/* iOS-style Action Sheet */}
      <CalendarActionSheet
        isOpen={actionSheet.isOpen}
        onClose={hideActionSheet}
        date={actionSheet.date}
        appointment={actionSheet.appointment}
        employee={actionSheet.employee}
        position={actionSheet.position}
        onNewAppointment={handleAppointmentCreate}
        onAddToWaitlist={() => {
          showToast('Waitlist functionality coming soon', 'info')
          hideActionSheet()
        }}
        onPersonalTask={() => {
          showToast('Personal task functionality coming soon', 'info') 
          hideActionSheet()
        }}
        onEditWorkingHours={() => {
          setIsSettingsOpen(true)
          hideActionSheet()
        }}
        onEditAppointment={(appointment) => {
          setSelectedAppointment(appointment)
          setIsAppointmentDetailsOpen(true)
          hideActionSheet()
        }}
        onCancelAppointment={handleAppointmentCancel}
        onMarkNoShow={async () => {
          if (actionSheet.appointment) {
            try {
              await AppointmentStatusService.markNoShow(actionSheet.appointment.id)
              await refreshAppointments()
              showToast(`Marked ${actionSheet.appointment.title} as no-show`, 'success')
            } catch (error) {
              showToast('Failed to mark as no-show', 'error')
            }
          }
          hideActionSheet()
        }}
        onMarkCompleted={async () => {
          if (actionSheet.appointment) {
            try {
              await AppointmentStatusService.completeAppointment(actionSheet.appointment.id)
              await refreshAppointments()
              showToast(`Marked ${actionSheet.appointment.title} as completed`, 'success')
            } catch (error) {
              showToast('Failed to mark as completed', 'error')
            }
          }
          hideActionSheet()
        }}
      />

      {/* Calendar Settings Panel */}
      <CalendarSettingsPanel
        isOpen={isSettingsOpen}
        onClose={() => setIsSettingsOpen(false)}
        config={config}
        updateConfig={updateConfig}
        selectedEmployees={selectedEmployees}
        refreshAppointments={refreshAppointments}
      />

      {/* Appointment Modal - Combined Details & Edit */}
      <AppointmentModal
        isOpen={isAppointmentDetailsOpen}
        onClose={() => {
          setIsAppointmentDetailsOpen(false)
          setSelectedAppointment(null)
        }}
        appointment={selectedAppointment}
        onUpdateStatus={handleAppointmentStatusChange}
        onAppointmentUpdate={handleAppointmentUpdate}
        onCancelAppointment={async (appointment) => {
          await handleAppointmentCancel(appointment)
          setIsAppointmentDetailsOpen(false)
          setSelectedAppointment(null)
        }}
      />

      {/* Mobile FAB for Quick Actions */}
      <div className="lg:hidden fixed bottom-6 right-6 z-[50] flex flex-col gap-3">
        {/* Sidebar Toggle FAB */}
        <button
          onClick={() => setIsSidebarOpen(!isSidebarOpen)}
          className="bg-gray-600 text-white p-3 rounded-full shadow-lg hover:bg-gray-700 transition-colors"
          aria-label="Toggle calendar controls"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>

        {/* Quick Refresh FAB */}
        <button
          onClick={refreshAppointments}
          className="bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-colors"
          aria-label="Refresh appointments"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
        </button>
      </div>

    </div>
  )
}

export default CalendarPage 