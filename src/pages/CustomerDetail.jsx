import { use<PERSON>ara<PERSON>, <PERSON> } from 'react-router-dom'
import { useState, useEffect } from 'react'
import ProfileHeader from '../features/customers/components/ProfileHeader'
import ProfileDetails from '../features/customers/components/ProfileDetails'
import CreditCardSection from '../features/customers/components/CreditCardSection'
import CustomerAppointments from '../features/customers/components/CustomerAppointments'
import CustomerForms from '../features/customers/components/CustomerForms'
import { customerApi } from '../features/customers/services/customerApi'

function CustomerDetail() {
  const { id } = useParams()
  const [activeTab, setActiveTab] = useState('profile')
  const [customer, setCustomer] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  // Fetch customer from backend
  useEffect(() => {
    const fetchCustomer = async () => {
      try {
        setLoading(true)
        setError(null)
        
        // Fetch specific customer from backend
        const response = await customerApi.getCustomer(id)
        console.log('📊 Fetched customer from backend:', response)
        
        // Backend now returns flat structure via BusinessCustomerFlatSerializer
        // No transformation needed - use response directly with some fallbacks
        const transformedCustomer = {
          // Use serializer fields directly
          id: response.id,
          name: response.name || 'Unknown Customer',
          email: response.email || '',
          phone: response.phone || '',
          avatar: response.avatar || 'https://ui-avatars.com/api/?name=' + encodeURIComponent(response.name || 'Unknown'),
          status: response.status || 'active',
          gender: response.gender || 'unknown',
          joinDate: response.joinDate,
          lastOrder: response.lastOrder,
          totalOrders: response.totalOrders || 0,
          address: response.address || {
            street: '',
            postalCode: '',
            city: '',
            state: '',
            country: ''
          },
          
          // Additional imported data fields
          customerSince: response.customerSince,
          lastVisited: response.lastVisited,
          membership: response.membership,
          birthdate: response.birthdate,
          dayPhone: response.dayPhone,
          nightPhone: response.nightPhone,
          referredBy: response.referredBy,
          onlineBooking: response.onlineBooking,
          creditCard: response.creditCard,
          bank: response.bank,
          appointmentsBooked: response.appointmentsBooked || 0,
          classesBooked: response.classesBooked || 0,
          checkIns: response.checkIns || 0,
          pointsEarned: response.pointsEarned || 0,
          amountPaid: response.amountPaid || 0,
          noShowsCancellations: response.noShowsCancellations || 0,
          employeeSeen: response.employeeSeen,
          
          // Keep any other response fields
          ...response
        }
        
        setCustomer(transformedCustomer)
        
      } catch (error) {
        console.error('❌ Failed to fetch customer:', error)
        setError(error.message)
        
        // Fallback to mock data if backend fails
        console.log('📝 Using fallback mock data for customer ID:', id)
        const mockCustomers = [
    {
      id: 1,
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '+****************',
      avatar: 'https://via.placeholder.com/80',
      status: 'active',
      gender: 'male',
      joinDate: '2023-01-15',
      lastOrder: '2024-05-20',
      totalOrders: 15,
      address: {
        street: '123 Main Street',
        postalCode: '10001',
        city: 'New York',
        country: 'USA'
      }
    },
    {
      id: 2,
      name: 'Jane Smith',
      email: '<EMAIL>',
      phone: '+****************',
      avatar: 'https://via.placeholder.com/80',
      status: 'active',
      gender: 'female',
      joinDate: '2023-03-22',
      lastOrder: '2024-06-01',
      totalOrders: 8,
      address: {
        street: '456 Oak Avenue',
        postalCode: '90210',
        city: 'Los Angeles',
        country: 'USA'
      }
    },
    {
      id: 3,
      name: 'Bob Johnson',
      email: '<EMAIL>',
      phone: '+****************',
      avatar: 'https://via.placeholder.com/80',
      status: 'inactive',
      gender: 'male',
      joinDate: '2022-11-10',
      lastOrder: '2024-01-15',
      totalOrders: 3,
      address: {
        street: '789 Pine Road',
        postalCode: '75001',
        city: 'Dallas',
        country: 'USA'
      }
    },
    {
      id: 4,
      name: 'Alice Brown',
      email: '<EMAIL>',
      phone: '+****************',
      avatar: 'https://via.placeholder.com/80',
      status: 'active',
      gender: 'female',
      joinDate: '2023-08-05',
      lastOrder: '2024-06-05',
      totalOrders: 22,
      address: {
        street: '321 Beach Boulevard',
        postalCode: '33101',
        city: 'Miami',
        country: 'USA'
      }
    },
    {
      id: 5,
      name: 'Charlie Wilson',
      email: '<EMAIL>',
      phone: '+****************',
      avatar: 'https://via.placeholder.com/80',
      status: 'pending',
      gender: 'male',
      joinDate: '2024-01-20',
      lastOrder: null,
      totalOrders: 0,
      address: {
        street: '654 Capitol Hill',
        postalCode: '98101',
        city: 'Seattle',
        country: 'USA'
      }
    },
    {
      id: 6,
      name: 'Diana Martinez',
      email: '<EMAIL>',
      phone: '+****************',
      avatar: 'https://via.placeholder.com/80',
      status: 'active',
      gender: 'female',
      joinDate: '2023-05-12',
      lastOrder: '2024-05-28',
      totalOrders: 11,
      address: {
        street: '987 Sunset Strip',
        postalCode: '90028',
        city: 'Hollywood',
        country: 'USA'
      }
    },
    {
      id: 7,
      name: 'Michael Chen',
      email: '<EMAIL>',
      phone: '+****************',
      avatar: 'https://via.placeholder.com/80',
      status: 'active',
      gender: 'male',
      joinDate: '2023-02-14',
      lastOrder: '2024-06-10',
      totalOrders: 18,
      address: {
        street: '147 Chinatown Street',
        postalCode: '94108',
        city: 'San Francisco',
        country: 'USA'
      }
    },
    {
      id: 8,
      name: 'Sarah Davis',
      email: '<EMAIL>',
      phone: '+****************',
      avatar: 'https://via.placeholder.com/80',
      status: 'inactive',
      gender: 'female',
      joinDate: '2022-09-30',
      lastOrder: '2024-02-15',
      totalOrders: 5,
      address: {
        street: '258 Music Row',
        postalCode: '37203',
        city: 'Nashville',
        country: 'USA'
      }
    },
    {
      id: 9,
      name: 'James Rodriguez',
      email: '<EMAIL>',
      phone: '+****************',
      avatar: 'https://via.placeholder.com/80',
      status: 'active',
      gender: 'male',
      joinDate: '2023-07-18',
      lastOrder: '2024-06-08',
      totalOrders: 13,
      address: {
        street: '369 River Walk',
        postalCode: '78205',
        city: 'San Antonio',
        country: 'USA'
      }
    },
    {
      id: 10,
      name: 'Emma Thompson',
      email: '<EMAIL>',
      phone: '+44 20 7946 0958',
      avatar: 'https://via.placeholder.com/80',
      status: 'active',
      gender: 'female',
      joinDate: '2023-04-03',
      lastOrder: '2024-05-25',
      totalOrders: 9,
      address: {
        street: '42 Baker Street',
        postalCode: 'NW1 6XE',
        city: 'London',
        country: 'UK'
      }
    },
    {
      id: 11,
      name: 'Oliver Garcia',
      email: '<EMAIL>',
      phone: '+34 91 123 4567',
      avatar: 'https://via.placeholder.com/80',
      status: 'pending',
      gender: 'male',
      joinDate: '2024-02-28',
      lastOrder: null,
      totalOrders: 0,
      address: {
        street: 'Calle Gran Via 15',
        postalCode: '28013',
        city: 'Madrid',
        country: 'Spain'
      }
    },
    {
      id: 12,
      name: 'Sophie Dubois',
      email: '<EMAIL>',
      phone: '+33 1 42 34 56 78',
      avatar: 'https://via.placeholder.com/80',
      status: 'active',
      gender: 'female',
      joinDate: '2023-06-11',
      lastOrder: '2024-06-02',
      totalOrders: 16,
      address: {
        street: '125 Champs-Élysées',
        postalCode: '75008',
        city: 'Paris',
        country: 'France'
      }
    },
    {
      id: 13,
      name: 'Lucas Mueller',
      email: '<EMAIL>',
      phone: '+49 30 12345678',
      avatar: 'https://via.placeholder.com/80',
      status: 'active',
      gender: 'male',
      joinDate: '2023-01-28',
      lastOrder: '2024-05-30',
      totalOrders: 12,
      address: {
        street: 'Unter den Linden 77',
        postalCode: '10117',
        city: 'Berlin',
        country: 'Germany'
      }
    },
    {
      id: 14,
      name: 'Isabella Rossi',
      email: '<EMAIL>',
      phone: '+39 06 1234 5678',
      avatar: 'https://via.placeholder.com/80',
      status: 'inactive',
      gender: 'female',
      joinDate: '2022-12-05',
      lastOrder: '2024-03-10',
      totalOrders: 7,
      address: {
        street: 'Via del Corso 234',
        postalCode: '00187',
        city: 'Rome',
        country: 'Italy'
      }
    },
    {
      id: 15,
      name: 'Hiroshi Tanaka',
      email: '<EMAIL>',
      phone: '+81 3 1234 5678',
      avatar: 'https://via.placeholder.com/80',
      status: 'active',
      gender: 'male',
      joinDate: '2023-03-15',
      lastOrder: '2024-06-07',
      totalOrders: 20,
      address: {
        street: '1-1-1 Shibuya',
        postalCode: '150-0002',
        city: 'Tokyo',
        country: 'Japan'
      }
    },
    {
      id: 16,
      name: 'Priya Sharma',
      email: '<EMAIL>',
      phone: '+91 11 2345 6789',
      avatar: 'https://via.placeholder.com/80',
      status: 'active',
      gender: 'female',
      joinDate: '2023-05-20',
      lastOrder: '2024-06-03',
      totalOrders: 14,
      address: {
        street: 'Connaught Place 45',
        postalCode: '110001',
        city: 'New Delhi',
        country: 'India'
      }
    },
    {
      id: 17,
      name: 'Alex Kim',
      email: '<EMAIL>',
      phone: '+82 2 1234 5678',
      avatar: 'https://via.placeholder.com/80',
      status: 'pending',
      gender: 'other',
      joinDate: '2024-03-10',
      lastOrder: null,
      totalOrders: 0,
      address: {
        street: 'Gangnam-gu 123',
        postalCode: '06028',
        city: 'Seoul',
        country: 'South Korea'
      }
    },
    {
      id: 18,
      name: 'Maria Silva',
      email: '<EMAIL>',
      phone: '+55 11 9876 5432',
      avatar: 'https://via.placeholder.com/80',
      status: 'active',
      gender: 'female',
      joinDate: '2023-04-25',
      lastOrder: '2024-05-22',
      totalOrders: 10,
      address: {
        street: 'Avenida Paulista 1578',
        postalCode: '01310-200',
        city: 'São Paulo',
        country: 'Brazil'
      }
    },
    {
      id: 19,
      name: 'Ahmed Hassan',
      email: '<EMAIL>',
      phone: '+971 4 123 4567',
      avatar: 'https://via.placeholder.com/80',
      status: 'active',
      gender: 'male',
      joinDate: '2023-02-08',
      lastOrder: '2024-06-09',
      totalOrders: 17,
      address: {
        street: 'Sheikh Zayed Road 789',
        postalCode: '12345',
        city: 'Dubai',
        country: 'UAE'
      }
    },
    {
      id: 20,
      name: 'Rachel Green',
      email: '<EMAIL>',
      phone: '+****************',
      avatar: 'https://via.placeholder.com/80',
      status: 'inactive',
      gender: 'female',
      joinDate: '2022-08-14',
      lastOrder: '2024-01-30',
      totalOrders: 4,
      address: {
        street: '90 Bedford Street',
        postalCode: '10014',
        city: 'New York',
        country: 'USA'
      }
    },
    {
      id: 21,
      name: 'Connor O\'Brien',
      email: '<EMAIL>',
      phone: '+353 1 234 5678',
      avatar: 'https://via.placeholder.com/80',
      status: 'active',
      gender: 'male',
      joinDate: '2023-09-12',
      lastOrder: '2024-06-04',
      totalOrders: 6,
      address: {
        street: 'Temple Bar 12',
        postalCode: 'D02 YD30',
        city: 'Dublin',
        country: 'Ireland'
      }
    },
    {
      id: 22,
      name: 'Zara Al-Rashid',
      email: '<EMAIL>',
      phone: '+966 11 234 5678',
      avatar: 'https://via.placeholder.com/80',
      status: 'active',
      gender: 'female',
      joinDate: '2023-07-30',
      lastOrder: '2024-05-15',
      totalOrders: 8,
      address: {
        street: 'King Fahd Road 456',
        postalCode: '11564',
        city: 'Riyadh',
        country: 'Saudi Arabia'
      }
    },
    {
      id: 23,
      name: 'Nicolas Petrov',
      email: '<EMAIL>',
      phone: '****** 123 4567',
      avatar: 'https://via.placeholder.com/80',
      status: 'pending',
      gender: 'male',
      joinDate: '2024-04-15',
      lastOrder: null,
      totalOrders: 0,
      address: {
        street: 'Red Square 1',
        postalCode: '109012',
        city: 'Moscow',
        country: 'Russia'
      }
    },
    {
      id: 24,
      name: 'Amelia Watson',
      email: '<EMAIL>',
      phone: '+61 2 9876 5432',
      avatar: 'https://via.placeholder.com/80',
      status: 'active',
      gender: 'female',
      joinDate: '2023-10-22',
      lastOrder: '2024-06-06',
      totalOrders: 19,
      address: {
        street: '88 Harbour Bridge Road',
        postalCode: '2000',
        city: 'Sydney',
        country: 'Australia'
      }
    },
    {
      id: 25,
      name: 'Erik Andersson',
      email: '<EMAIL>',
      phone: '+46 8 123 456 78',
      avatar: 'https://via.placeholder.com/80',
      status: 'active',
      gender: 'male',
      joinDate: '2023-01-05',
      lastOrder: '2024-05-18',
      totalOrders: 21,
      address: {
        street: 'Gamla Stan 15',
        postalCode: '111 29',
        city: 'Stockholm',
        country: 'Sweden'
      }
    }
  ]
        
        const fallbackCustomer = mockCustomers.find((c) => c.id === Number(id))
        setCustomer(fallbackCustomer)
      } finally {
        setLoading(false)
      }
    }
    
    fetchCustomer()
  }, [id])

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full bg-white rounded-xl shadow-sm p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h1 className="text-xl font-semibold text-gray-900 mb-2">Loading customer...</h1>
          <p className="text-gray-600">Fetching customer profile from backend</p>
        </div>
      </div>
    )
  }

  // Customer not found
  if (!customer) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full bg-white rounded-xl shadow-sm p-8 text-center">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </div>
          <h1 className="text-xl font-semibold text-gray-900 mb-2">Customer not found</h1>
          <p className="text-gray-600 mb-2">The customer profile you're looking for doesn't exist or has been removed.</p>
          {error && (
            <p className="text-sm text-red-600 mb-4">Error: {error}</p>
          )}
          <Link 
            to="/customers/management" 
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Back to Customer Management
        </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Navigation */}
        <nav className="mb-6">
          <Link 
            to="/customers/management" 
            className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Back to Customer Management
      </Link>
        </nav>

        {/* Profile Header */}
        <ProfileHeader customer={customer} />

        {/* Tab Navigation */}
        <div className="bg-white rounded-xl shadow-sm mb-6">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-8">
              <button
                onClick={() => setActiveTab('profile')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'profile'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Profile
              </button>
              <button
                onClick={() => setActiveTab('appointments')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'appointments'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Appointments
              </button>
              <button
                onClick={() => setActiveTab('forms')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'forms'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Forms
              </button>
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === 'profile' && (
          <div className="space-y-6">
            {/* Profile Details */}
            <ProfileDetails customer={customer} />

            {/* Credit Card Section */}
            <CreditCardSection customer={customer} />

            {/* Quick Stats */}

      </div>
        )}

        {activeTab === 'appointments' && (
          <CustomerAppointments customer={customer} />
        )}

        {activeTab === 'forms' && (
          <CustomerForms customer={customer} />
        )}
      </div>
    </div>
  )
}

export default CustomerDetail