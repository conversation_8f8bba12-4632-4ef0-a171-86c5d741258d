import { Link } from 'react-router-dom'
import EmailTextMarketing from '../features/marketing/components/EmailTextMarketing'

function Marketing() {
  return (
    <div className="container py-10">
      <h1 className="text-3xl mb-6">Marketing</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Email & Text Marketing */}
        <Link to="/marketing/campaigns" className="block">
          <EmailTextMarketing />
        </Link>
        
        {/* Other marketing tools placeholder */}
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center justify-center mb-6">
            <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
              <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
              </svg>
            </div>
          </div>
          <h3 className="text-xl font-semibold text-center mb-2">Social Media</h3>
          <p className="text-gray-600 text-center mb-6">
            Connect and manage your social media accounts.
          </p>
          <div className="flex justify-center">
            <button className="px-4 py-2 bg-gray-200 text-gray-600 rounded-md hover:bg-gray-300 transition-colors w-full sm:w-auto">
              Coming Soon
            </button>
          </div>
        </div>
        
        {/* More marketing tools placeholder */}
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center justify-center mb-6">
            <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
              <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
          </div>
          <h3 className="text-xl font-semibold text-center mb-2">Analytics</h3>
          <p className="text-gray-600 text-center mb-6">
            Track campaign performance and customer engagement.
          </p>
          <div className="flex justify-center">
            <button className="px-4 py-2 bg-gray-200 text-gray-600 rounded-md hover:bg-gray-300 transition-colors w-full sm:w-auto">
              Coming Soon
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Marketing 