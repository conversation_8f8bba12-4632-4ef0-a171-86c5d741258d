import { useState } from 'react'
import { Link } from 'react-router-dom'

function CustomerList() {
  // dummy data
  const [customers] = useState([
    { id: 1, name: '<PERSON>', email: '<EMAIL>', phone: '************', avatar: 'https://via.placeholder.com/150' },
    { id: 2, name: '<PERSON>', email: '<EMAIL>', phone: '************', avatar: 'https://via.placeholder.com/150' },
  ])

  return (
    <div className="container py-10">
      <h1 className="text-3xl mb-6">Customer List</h1>
      
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                Customer
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                Email
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                Phone
              </th>
              <th className="px-6 py-3" />
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {customers.map((c) => (
              <tr key={c.id}>
                <td className="px-6 py-4 whitespace-nowrap flex items-center">
                  <img
                    src={c.avatar}
                    alt={c.name}
                    className="w-10 h-10 rounded-full mr-3"
                  />
                  {c.name}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">{c.email}</td>
                <td className="px-6 py-4 whitespace-nowrap">{c.phone}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-primary-600">
                  <Link to={`/customers/${c.id}`}>View</Link>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}

export default CustomerList 