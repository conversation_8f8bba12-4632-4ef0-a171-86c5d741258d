import React, { useState, useEffect } from 'react'
import { useAgentBehavior } from '../hooks/useAgentBehavior'
import { AGENT_BEHAVIOR_SETTINGS, AGENT_TYPES } from '../../../features/ai-agents/constants/agentTypes'

const BehaviorConfig = ({ agentType }) => {
  const {
    behavior,
    isLoading,
    updateBehavior,
    resetToDefault,
    isUpdating,
    isResetting
  } = useAgentBehavior(agentType)

  const [localBehavior, setLocalBehavior] = useState({})
  const [hasChanges, setHasChanges] = useState(false)

  const agent = Object.values(AGENT_TYPES).find(a => a.id === agentType)

  // Update local state when behavior data loads
  useEffect(() => {
    if (behavior) {
      setLocalBehavior(behavior)
      setHasChanges(false)
    }
  }, [behavior])

  const handleUpdate = (field, value) => {
    setLocalBehavior(prev => ({ ...prev, [field]: value }))
    setHasChanges(true)
  }

  const handleEscalationTriggerChange = (trigger, checked) => {
    const triggers = localBehavior.escalationTriggers || []
    const newTriggers = checked
      ? [...triggers, trigger]
      : triggers.filter(t => t !== trigger)
    handleUpdate('escalationTriggers', newTriggers)
  }

  const handleSave = async () => {
    await updateBehavior(localBehavior)
    setHasChanges(false)
  }

  const handleReset = async () => {
    if (window.confirm('Are you sure you want to reset to default settings? This will discard all custom configurations.')) {
      await resetToDefault()
      setHasChanges(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
        <span className="ml-3 text-gray-600">Loading behavior settings...</span>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Description */}
      <div>
        <p className="text-gray-600 text-sm">
          Configure how this agent responds to customers
        </p>
      </div>

      {/* Personality Settings */}
      <div className="space-y-6">
        <div>
          <h3 className="text-sm font-medium text-gray-900 mb-4">Communication Style</h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm text-gray-700 mb-2">
                Tone
              </label>
              <select
                value={localBehavior.tone || 'professional'}
                onChange={(e) => handleUpdate('tone', e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-gray-900 focus:border-gray-900"
              >
                {AGENT_BEHAVIOR_SETTINGS.TONE.map(tone => (
                  <option key={tone} value={tone}>
                    {tone.charAt(0).toUpperCase() + tone.slice(1)}
                  </option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="block text-sm text-gray-700 mb-2">
                Response Length
              </label>
              <select
                value={localBehavior.responseLength || 'detailed'}
                onChange={(e) => handleUpdate('responseLength', e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-gray-900 focus:border-gray-900"
              >
                {AGENT_BEHAVIOR_SETTINGS.RESPONSE_LENGTH.map(length => (
                  <option key={length} value={length}>
                    {length.charAt(0).toUpperCase() + length.slice(1)}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        <div>
          <label className="block text-sm text-gray-700 mb-2">
            Greeting Message
          </label>
          <textarea
            value={localBehavior.greetingMessage || ''}
            onChange={(e) => handleUpdate('greetingMessage', e.target.value)}
            placeholder={`Hi! I'm your ${agent?.name.toLowerCase()}. How can I help you today?`}
            rows={3}
            className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-gray-900 focus:border-gray-900"
          />
        </div>
      </div>

      {/* Escalation Settings */}
      <div className="space-y-4">
        <h3 className="text-sm font-medium text-gray-900">Escalation</h3>
        
        <div className="space-y-2">
          {AGENT_BEHAVIOR_SETTINGS.ESCALATION_TRIGGERS.map(trigger => (
            <label key={trigger} className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={localBehavior.escalationTriggers?.includes(trigger) || false}
                onChange={(e) => handleEscalationTriggerChange(trigger, e.target.checked)}
                className="w-4 h-4 text-gray-900 border-gray-300 rounded focus:ring-gray-900"
              />
              <span className="text-sm text-gray-700">
                {trigger.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </span>
            </label>
          ))}
        </div>

        <div>
          <label className="block text-sm text-gray-700 mb-2">
            Escalation Message
          </label>
          <textarea
            value={localBehavior.escalationMessage || ''}
            onChange={(e) => handleUpdate('escalationMessage', e.target.value)}
            placeholder="Let me connect you with one of our team members who can better assist you with this request."
            rows={2}
            className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-gray-900 focus:border-gray-900"
          />
        </div>
      </div>

      {/* Context Settings */}
      <div className="space-y-4">
        <h3 className="text-sm font-medium text-gray-900">Context</h3>
        
        <div>
          <label className="block text-sm text-gray-700 mb-2">
            Business Name Override
          </label>
          <input
            type="text"
            value={localBehavior.businessName || ''}
            onChange={(e) => handleUpdate('businessName', e.target.value)}
            placeholder="Leave empty to use default business name"
            className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-gray-900 focus:border-gray-900"
          />
        </div>

        <div>
          <label className="block text-sm text-gray-700 mb-2">
            Additional Instructions
          </label>
          <textarea
            value={localBehavior.contextInstructions || ''}
            onChange={(e) => handleUpdate('contextInstructions', e.target.value)}
            placeholder="Additional context for this agent..."
            rows={3}
            className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-gray-900 focus:border-gray-900"
          />
        </div>
      </div>

      {/* Save Actions */}
      <div className="flex justify-between items-center pt-6 border-t border-gray-200">
        <button
          onClick={handleReset}
          disabled={isResetting || isUpdating}
          className="text-sm text-gray-600 hover:text-gray-900 disabled:opacity-50"
        >
          {isResetting ? 'Resetting...' : 'Reset to defaults'}
        </button>
        
        <div className="flex items-center space-x-3">
          {hasChanges && (
            <span className="text-xs text-gray-500">Unsaved changes</span>
          )}
          <button
            onClick={handleSave}
            disabled={!hasChanges || isUpdating || isResetting}
            className={`px-4 py-2 rounded-md text-sm font-medium ${
              hasChanges && !isUpdating && !isResetting
                ? 'bg-gray-900 text-white hover:bg-gray-800'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }`}
          >
            {isUpdating ? 'Saving...' : 'Save'}
          </button>
        </div>
      </div>
    </div>
  )
}

export default BehaviorConfig
