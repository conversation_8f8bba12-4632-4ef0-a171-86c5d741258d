import React, { useState } from 'react'
import { PlusIcon, TrashIcon, PencilIcon } from '@heroicons/react/24/outline'
import { useAgentFAQ } from '../hooks/useAgentFAQ'
import { AGENT_TYPES, FAQ_CATEGORIES } from '../../../features/ai-agents/constants/agentTypes'

const FAQEditor = ({ agentType }) => {
  const {
    faqs,
    isLoading,
    createFAQ,
    updateFAQ,
    deleteFAQ,
    isCreating,
    isUpdating,
    isDeleting
  } = useAgentFAQ(agentType)
  
  const [editingFAQ, setEditingFAQ] = useState(null)
  const [newFAQ, setNewFAQ] = useState({ question: '', answer: '', category: '' })
  const [showNewFAQForm, setShowNewFAQForm] = useState(false)

  const agent = Object.values(AGENT_TYPES).find(a => a.id === agentType)

  const handleCreateFAQ = async () => {
    if (newFAQ.question.trim() && newFAQ.answer.trim()) {
      await createFAQ(newFAQ)
      setNewFAQ({ question: '', answer: '', category: '' })
      setShowNewFAQForm(false)
    }
  }

  const handleUpdateFAQ = async (id, updates) => {
    await updateFAQ(id, updates)
    setEditingFAQ(null)
  }

  const handleDeleteFAQ = async (id) => {
    if (window.confirm('Are you sure you want to delete this FAQ?')) {
      await deleteFAQ(id)
    }
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
        <span className="ml-3 text-gray-600">Loading FAQs...</span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Action Bar */}
      <div className="flex justify-between items-center">
        <div>
          <p className="text-gray-600 text-sm">
            Add questions and responses for this agent
          </p>
        </div>
        <button
          onClick={() => setShowNewFAQForm(true)}
          disabled={isCreating}
          className="bg-gray-900 text-white px-4 py-2 rounded-md hover:bg-gray-800 disabled:opacity-50 text-sm font-medium"
        >
          {isCreating ? 'Adding...' : 'Add knowledge'}
        </button>
      </div>

      {/* Minimal New FAQ Form */}
      {showNewFAQForm && (
        <div className="border border-gray-200 rounded-lg p-6 bg-white">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-900 mb-2">
                Category
              </label>
              <select
                value={newFAQ.category}
                onChange={(e) => setNewFAQ({...newFAQ, category: e.target.value})}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-gray-900 focus:border-gray-900"
              >
                <option value="">Select category</option>
                {FAQ_CATEGORIES.map(category => (
                  <option key={category} value={category}>
                    {category.charAt(0).toUpperCase() + category.slice(1)}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-900 mb-2">
                Question
              </label>
              <input
                type="text"
                value={newFAQ.question}
                onChange={(e) => setNewFAQ({...newFAQ, question: e.target.value})}
                placeholder="What question will customers ask?"
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-gray-900 focus:border-gray-900"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-900 mb-2">
                Response
              </label>
              <textarea
                value={newFAQ.answer}
                onChange={(e) => setNewFAQ({...newFAQ, answer: e.target.value})}
                placeholder="How should the AI agent respond?"
                rows={4}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-gray-900 focus:border-gray-900"
              />
            </div>
            <div className="flex justify-end space-x-2 pt-2">
              <button
                onClick={() => {
                  setShowNewFAQForm(false)
                  setNewFAQ({ question: '', answer: '', category: '' })
                }}
                className="px-4 py-2 text-sm text-gray-600 hover:text-gray-900"
              >
                Cancel
              </button>
              <button
                onClick={handleCreateFAQ}
                disabled={!newFAQ.question.trim() || !newFAQ.answer.trim() || isCreating}
                className="bg-gray-900 text-white px-4 py-2 rounded-md hover:bg-gray-800 disabled:opacity-50 text-sm font-medium"
              >
                {isCreating ? 'Adding...' : 'Add'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* FAQ List */}
      <div className="space-y-3">
        {faqs.length === 0 ? (
          <div className="text-center py-16 text-gray-500">
            <p className="text-sm">No knowledge base entries yet</p>
            <p className="text-xs mt-1 text-gray-400">Add your first entry to get started</p>
          </div>
        ) : (
          faqs.map((faq, index) => (
            <FAQItem
              key={faq.id}
              faq={faq}
              index={index}
              onEdit={setEditingFAQ}
              onUpdate={handleUpdateFAQ}
              onDelete={handleDeleteFAQ}
              isEditing={editingFAQ === faq.id}
              isUpdating={isUpdating}
              isDeleting={isDeleting}
              categories={FAQ_CATEGORIES}
            />
          ))
        )}
      </div>
    </div>
  )
}

// FAQ Item Component
const FAQItem = ({ faq, index, onEdit, onUpdate, onDelete, isEditing, isUpdating, isDeleting, categories }) => {
  const [editData, setEditData] = useState(faq)

  const handleSave = () => {
    onUpdate(faq.id, editData)
  }

  const handleCancel = () => {
    setEditData(faq)
    onEdit(null)
  }

  if (isEditing) {
    return (
      <div className="border border-gray-200 rounded-lg p-4 bg-white">
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-900 mb-2">Category</label>
            <select
              value={editData.category}
              onChange={(e) => setEditData({...editData, category: e.target.value})}
              className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-gray-900 focus:border-gray-900"
            >
              {categories.map(category => (
                <option key={category} value={category}>
                  {category.charAt(0).toUpperCase() + category.slice(1)}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-900 mb-2">Question</label>
            <input
              type="text"
              value={editData.question}
              onChange={(e) => setEditData({...editData, question: e.target.value})}
              className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-gray-900 focus:border-gray-900"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-900 mb-2">Response</label>
            <textarea
              value={editData.answer}
              onChange={(e) => setEditData({...editData, answer: e.target.value})}
              rows={4}
              className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-gray-900 focus:border-gray-900"
            />
          </div>
          <div className="flex justify-end space-x-2 pt-2">
            <button
              onClick={handleCancel}
              disabled={isUpdating}
              className="px-4 py-2 text-sm text-gray-600 hover:text-gray-900 disabled:opacity-50"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              disabled={isUpdating}
              className="bg-gray-900 text-white px-4 py-2 rounded-md hover:bg-gray-800 disabled:opacity-50 text-sm font-medium"
            >
              {isUpdating ? 'Saving...' : 'Save'}
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors group">
      <div className="flex justify-between items-start">
        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-2">
            <span className="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded">
              {faq.category}
            </span>
          </div>
          <h4 className="font-medium text-gray-900 mb-2 text-sm">{faq.question}</h4>
          <p className="text-gray-600 text-sm leading-relaxed">{faq.answer}</p>
        </div>
        <div className="flex space-x-1 ml-4 opacity-0 group-hover:opacity-100 transition-opacity">
          <button
            onClick={() => onEdit(faq.id)}
            disabled={isUpdating || isDeleting}
            className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50"
            title="Edit"
          >
            <PencilIcon className="w-4 h-4" />
          </button>
          <button
            onClick={() => onDelete(faq.id)}
            disabled={isUpdating || isDeleting}
            className="p-1 text-gray-400 hover:text-red-500 disabled:opacity-50"
            title="Delete"
          >
            <TrashIcon className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  )
}

export default FAQEditor
