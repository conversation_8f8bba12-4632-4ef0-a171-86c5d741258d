import React from 'react'
import { AGENT_TYPES } from '../../../features/ai-agents/constants/agentTypes'

const AgentSelector = ({ selectedAgent, onAgentChange }) => {
  return (
    <div>
      <h2 className="text-lg font-medium text-gray-900 mb-4">Select AI Agent Type</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {Object.values(AGENT_TYPES).map(agent => (
          <div
            key={agent.id}
            onClick={() => onAgentChange(agent.id)}
            className={`relative p-6 rounded-lg border-2 cursor-pointer transition-all duration-200 hover:shadow-md ${
              selectedAgent === agent.id
                ? 'border-primary-500 bg-primary-50 shadow-lg'
                : 'border-gray-200 hover:border-gray-300 bg-white'
            }`}
          >
            {/* Selection indicator */}
            {selectedAgent === agent.id && (
              <div className="absolute top-4 right-4">
                <div className="w-6 h-6 bg-primary-500 rounded-full flex items-center justify-center">
                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
              </div>
            )}

            <div className="flex items-center space-x-4 mb-4">
              <div className="text-4xl">{agent.icon}</div>
              <div className="flex-1">
                <h3 className="text-xl font-semibold text-gray-900">{agent.name}</h3>
                <p className="text-gray-600 mt-1">{agent.description}</p>
              </div>
            </div>
            
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-3">Key Capabilities:</h4>
              <div className="grid grid-cols-2 gap-2">
                {agent.capabilities.slice(0, 6).map(capability => (
                  <div
                    key={capability}
                    className="flex items-center space-x-2 text-sm text-gray-600"
                  >
                    <div className="w-1.5 h-1.5 bg-gray-400 rounded-full"></div>
                    <span>{capability}</span>
                  </div>
                ))}
              </div>
              {agent.capabilities.length > 6 && (
                <p className="text-sm text-gray-500 mt-2">
                  +{agent.capabilities.length - 6} more capabilities
                </p>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export default AgentSelector
