import { useState, useEffect } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { agentBehaviorService } from '../../../features/ai-agents/services/agentBehaviorService'
import toast from 'react-hot-toast'

export const useAgentBehavior = (agentType) => {
  const queryClient = useQueryClient()
  
  const { data: behavior, isLoading, error } = useQuery({
    queryKey: ['agent-behavior', agentType],
    queryFn: () => agentBehaviorService.getBehavior(agentType),
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!agentType
  })

  const updateMutation = useMutation({
    mutationFn: (behaviorData) => agentBehaviorService.updateBehavior(agentType, behaviorData),
    onSuccess: () => {
      queryClient.invalidateQueries(['agent-behavior', agentType])
      toast.success('Agent behavior updated successfully!')
    },
    onError: (error) => {
      toast.error('Failed to update agent behavior. Please try again.')
      console.error('Update behavior error:', error)
    }
  })

  const resetMutation = useMutation({
    mutationFn: () => agentBehaviorService.resetToDefault(agentType),
    onSuccess: () => {
      queryClient.invalidateQueries(['agent-behavior', agentType])
      toast.success('Agent behavior reset to default!')
    },
    onError: (error) => {
      toast.error('Failed to reset agent behavior. Please try again.')
      console.error('Reset behavior error:', error)
    }
  })

  return {
    behavior,
    isLoading,
    error,
    updateBehavior: updateMutation.mutate,
    resetToDefault: resetMutation.mutate,
    isUpdating: updateMutation.isPending,
    isResetting: resetMutation.isPending
  }
}
