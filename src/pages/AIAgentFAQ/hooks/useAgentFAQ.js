import { useState, useEffect } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { agentFAQService } from '../../../features/ai-agents/services/agentFAQService'
import toast from 'react-hot-toast'

export const useAgentFAQ = (agentType) => {
  const queryClient = useQueryClient()
  
  const { data: faqs = [], isLoading, error } = useQuery({
    queryKey: ['agent-faqs', agentType],
    queryFn: () => agentFAQService.getFAQs(agentType),
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!agentType
  })

  const createMutation = useMutation({
    mutationFn: (faqData) => agentFAQService.createFAQ(agentType, faqData),
    onSuccess: () => {
      queryClient.invalidateQueries(['agent-faqs', agentType])
      toast.success('FAQ created successfully!')
    },
    onError: (error) => {
      toast.error('Failed to create FAQ. Please try again.')
      console.error('Create FAQ error:', error)
    }
  })

  const updateMutation = useMutation({
    mutationFn: ({ id, updates }) => agentFAQService.updateFAQ(agentType, id, updates),
    onSuccess: () => {
      queryClient.invalidateQueries(['agent-faqs', agentType])
      toast.success('FAQ updated successfully!')
    },
    onError: (error) => {
      toast.error('Failed to update FAQ. Please try again.')
      console.error('Update FAQ error:', error)
    }
  })

  const deleteMutation = useMutation({
    mutationFn: (id) => agentFAQService.deleteFAQ(agentType, id),
    onSuccess: () => {
      queryClient.invalidateQueries(['agent-faqs', agentType])
      toast.success('FAQ deleted successfully!')
    },
    onError: (error) => {
      toast.error('Failed to delete FAQ. Please try again.')
      console.error('Delete FAQ error:', error)
    }
  })

  return {
    faqs,
    isLoading,
    error,
    createFAQ: createMutation.mutate,
    updateFAQ: (id, updates) => updateMutation.mutate({ id, updates }),
    deleteFAQ: deleteMutation.mutate,
    isCreating: createMutation.isPending,
    isUpdating: updateMutation.isPending,
    isDeleting: deleteMutation.isPending,
    reorderFAQs: (newOrder) => {
      // TODO: Implement reordering logic if needed
      console.log('Reordering FAQs:', newOrder)
    }
  }
}
