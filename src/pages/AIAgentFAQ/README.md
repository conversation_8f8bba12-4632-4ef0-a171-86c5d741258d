# AI Agent FAQ Module

This module provides a comprehensive interface for managing AI Agent FAQs and behavior configuration for the Chatbook Business Frontend.

## 🏗️ Structure

```
AIAgentFAQ/
├── index.jsx                    # Main page component
├── components/
│   ├── AgentSelector.jsx        # Agent type selection interface
│   ├── FAQEditor.jsx           # FAQ management with CRUD operations
│   ├── BehaviorConfig.jsx      # Agent behavior configuration
│   └── TabNavigation.jsx       # Tab navigation between FAQ and Behavior
├── hooks/
│   ├── useAgentFAQ.js          # Custom hook for FAQ data management
│   └── useAgentBehavior.js     # Custom hook for behavior settings
└── README.md                   # This file
```

## 🎯 Features

### Agent Types
- **Front Desk Agent** (🏪): Handles general inquiries, customer service, and consultation
- **Booking Agent** (📅): Manages appointment scheduling and booking-related queries

### FAQ Management
- ✅ Create, read, update, delete FAQs
- 📂 Categorize FAQs (general, services, pricing, policies, technical, booking, emergency)
- 🔍 Search and filter capabilities
- 📝 Rich text editing for answers
- 🎯 Agent-specific FAQ organization

### Behavior Configuration
- 🎭 Personality settings (tone: professional, friendly, casual, formal)
- 📏 Response length control (brief, detailed, comprehensive)
- 💬 Custom greeting messages
- ⚠️ Escalation rules and triggers
- 🏢 Business-specific context instructions

## 🔌 API Integration

The module connects to the AI Agent microservice through the Django backend:

### Endpoints Used
- `GET /api/v1/ai-agents/{agentType}/faqs/` - Fetch FAQs
- `POST /api/v1/ai-agents/{agentType}/faqs/` - Create FAQ
- `PATCH /api/v1/ai-agents/{agentType}/faqs/{id}/` - Update FAQ
- `DELETE /api/v1/ai-agents/{agentType}/faqs/{id}/` - Delete FAQ
- `GET /api/v1/ai-agents/{agentType}/behavior/` - Fetch behavior settings
- `PATCH /api/v1/ai-agents/{agentType}/behavior/` - Update behavior settings

### Mock Data
During development, the services include mock data for both agent types to enable frontend development without backend dependencies.

## 🎨 UI/UX Features

- 📱 Responsive design for mobile and desktop
- ⚡ Real-time updates with React Query
- 🎨 Consistent design with existing Chatbook UI
- 🔄 Loading states and error handling
- 📊 Quick stats dashboard
- 💡 Help section with usage instructions

## 🚀 Usage

1. Navigate to "AI Agent FAQ" in the main navigation
2. Select an agent type (Front Desk or Booking)
3. Use the tabs to switch between FAQ Management and Behavior Settings
4. Add/edit FAQs with questions and answers
5. Configure agent behavior and personality
6. Save changes to update the AI agents

## 🛠️ Development Notes

- Uses React Query for server state management
- Follows existing Chatbook patterns and architecture
- Implements proper error handling and user feedback
- Includes TypeScript-style JSDoc comments
- Uses Tailwind CSS for styling consistency

## 🔄 Integration Points

- **Main Navigation**: Added to Header.jsx navigation
- **Routing**: Integrated into App.jsx routing structure
- **Features Module**: Constants and services in `/features/ai-agents/`
- **API Layer**: Uses existing useApi hook and axios configuration
