import React, { useState, useEffect } from 'react'
import AgentSelector from './components/AgentSelector'
import FAQEditor from './components/FAQEditor'
import BehaviorConfig from './components/BehaviorConfig'
import TabNavigation from './components/TabNavigation'
import { AGENT_TYPES } from '../../features/ai-agents/constants/agentTypes'

const AIAgentFAQ = () => {
  const [selectedAgent, setSelectedAgent] = useState(AGENT_TYPES.FRONT_DESK.id)
  const [activeTab, setActiveTab] = useState('faq')

  // Disable global scrollbar for this page
  useEffect(() => {
    // Store original overflow style
    const originalOverflow = document.body.style.overflow
    
    // Disable global scrolling
    document.body.style.overflow = 'hidden'
    
    // Cleanup function to restore scrolling when component unmounts
    return () => {
      document.body.style.overflow = originalOverflow
    }
  }, [])

  return (
    <div className="fixed inset-0 bg-gray-50 overflow-hidden flex" style={{ top: '64px' }}>
        {/* Sidebar - Fixed Position */}
        <div className="w-64 bg-white border-r border-gray-200 flex flex-col flex-shrink-0 h-full">
          {/* Sidebar Header - Fixed */}
          <div className="p-4 border-b border-gray-200 flex-shrink-0">
            <h1 className="text-sm font-medium text-gray-900">AI Agents</h1>
          </div>
          
          {/* Agent List - Scrollable with hidden scrollbar */}
          <div className="flex-1 overflow-y-auto scrollbar-none">
            <style jsx>{`
              .scrollbar-none::-webkit-scrollbar {
                display: none;
              }
              .scrollbar-none {
                -ms-overflow-style: none;
                scrollbar-width: none;
              }
            `}</style>
            <div className="p-2">
              {Object.values(AGENT_TYPES).map(agent => (
                <button
                  key={agent.id}
                  onClick={() => setSelectedAgent(agent.id)}
                  className={`w-full text-left p-3 rounded-lg mb-2 transition-colors ${
                    selectedAgent === agent.id
                      ? 'bg-gray-100 text-gray-900 border border-gray-200'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  }`}
                >
                  <div className="font-medium text-sm">{agent.name}</div>
                  <div className="text-xs text-gray-500 mt-1">{agent.description}</div>
                </button>
              ))}
            </div>
          </div>
          
          {/* Sidebar Footer - Fixed */}
          <div className="p-4 border-t border-gray-200 flex-shrink-0">
            <div className="text-xs text-gray-500">
              Configure agent responses and behavior
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex flex-col bg-white min-w-0 h-full">
          {/* Content Header - Fixed */}
          <div className="bg-white flex-shrink-0">
            <div className="px-6 py-4">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-medium text-gray-900">
                  {Object.values(AGENT_TYPES).find(a => a.id === selectedAgent)?.name}
                </h2>
                
                {/* Tab Navigation */}
                <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
                  <button
                    onClick={() => setActiveTab('faq')}
                    className={`px-3 py-1.5 text-sm font-medium rounded-md transition-colors ${
                      activeTab === 'faq'
                        ? 'bg-white text-gray-900 shadow-sm'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    Knowledge Base
                  </button>
                  <button
                    onClick={() => setActiveTab('behavior')}
                    className={`px-3 py-1.5 text-sm font-medium rounded-md transition-colors ${
                      activeTab === 'behavior'
                        ? 'bg-white text-gray-900 shadow-sm'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    Behavior
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Content Area - Scrollable with hidden scrollbar */}
          <div className="flex-1 overflow-y-auto scrollbar-none">
            <style jsx>{`
              .scrollbar-none::-webkit-scrollbar {
                display: none;
              }
              .scrollbar-none {
                -ms-overflow-style: none;
                scrollbar-width: none;
              }
            `}</style>
            <div className="max-w-4xl mx-auto p-6">
              {activeTab === 'faq' && (
                <FAQEditor agentType={selectedAgent} />
              )}
              
              {activeTab === 'behavior' && (
                <BehaviorConfig agentType={selectedAgent} />
              )}
            </div>
          </div>
        </div>
      </div>
  )
}

export default AIAgentFAQ
