import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import Button from '../../components/Button'
import { useApi } from '../../hooks/useApi'

function BusinessRegister() {
  const navigate = useNavigate()
  const [form, setForm] = useState({
    // Business info
    business_name: '',
    business_description: '',
    business_phone: '',
    business_email: '',
    business_website: '',
    // Admin info
    first_name: '',
    last_name: '',
    email: '',
    phone_number: '',
    password: '',
    password_confirm: '',
    terms_accepted: false,
  })

  const [error, setError] = useState(null)
  const registerApi = useApi('/business/register/')

  const handleChange = (e) => {
    const { name, type, value, checked } = e.target
    setForm((prev) => ({ ...prev, [name]: type === 'checkbox' ? checked : value }))
  }

  const validate = () => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    const phoneRegex = /^\+?[0-9]{7,15}$/

    if (!form.business_name.trim()) return 'Business name is required'
    if (!form.first_name.trim() || !form.last_name.trim()) return 'First and last name are required'
    if (!emailRegex.test(form.email)) return 'Admin email is invalid'
    if (!phoneRegex.test(form.phone_number)) return 'Admin phone number is invalid'
    if (form.password.length < 8) return 'Password must be at least 8 characters'
    if (form.password !== form.password_confirm) return 'Passwords do not match'
    if (!form.terms_accepted) return 'You must accept the terms to continue'
    return null
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    const validationError = validate()
    if (validationError) {
      setError(validationError)
      return
    }

    // Build payload as per spec
    const payload = {
      business: {
        name: form.business_name,
        description: form.business_description,
        phone: form.business_phone || null,
        email: form.business_email || null,
        website: form.business_website || null,
      },
      admin_user: {
        first_name: form.first_name,
        last_name: form.last_name,
        email: form.email,
        phone_number: form.phone_number,
        password: form.password,
      },
    }

    const res = await registerApi.createData(payload)
    if (!res || res.error) {
      setError(res?.detail || 'Failed to register business')
    } else {
      // store tokens if returned
      if (res.access) localStorage.setItem('auth_token', res.access)
      if (res.refresh) localStorage.setItem('refresh_token', res.refresh)
      navigate('/business/register/success')
    }
  }

  return (
    <div className="container flex justify-center items-center py-10">
      <div className="w-full max-w-3xl bg-white rounded-lg shadow-md p-8 space-y-8">
        <h1 className="text-2xl font-bold text-gray-900 text-center">Register Your Business</h1>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-2 rounded text-sm">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Business Information */}
          <section>
            <h2 className="text-lg font-semibold text-gray-800 mb-4">Business Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="col-span-1 md:col-span-2">
                <label className="block text-sm font-medium text-gray-700">Business Name <span className="text-red-500">*</span></label>
                <input
                  name="business_name"
                  type="text"
                  className="mt-1 w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500"
                  value={form.business_name}
                  onChange={handleChange}
                  required
                />
              </div>
              <div className="col-span-1 md:col-span-2">
                <label className="block text-sm font-medium text-gray-700">Business Description</label>
                <textarea
                  name="business_description"
                  rows="3"
                  className="mt-1 w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500"
                  value={form.business_description}
                  onChange={handleChange}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Business Phone</label>
                <input
                  name="business_phone"
                  type="tel"
                  className="mt-1 w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500"
                  value={form.business_phone}
                  onChange={handleChange}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Business Email</label>
                <input
                  name="business_email"
                  type="email"
                  className="mt-1 w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500"
                  value={form.business_email}
                  onChange={handleChange}
                />
              </div>
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700">Business Website</label>
                <input
                  name="business_website"
                  type="url"
                  className="mt-1 w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500"
                  value={form.business_website}
                  onChange={handleChange}
                />
              </div>
            </div>
          </section>

          {/* Administrator Information */}
          <section>
            <h2 className="text-lg font-semibold text-gray-800 mb-4">Your Information (Administrator)</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700">First Name <span className="text-red-500">*</span></label>
                <input
                  name="first_name"
                  type="text"
                  className="mt-1 w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500"
                  value={form.first_name}
                  onChange={handleChange}
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Last Name <span className="text-red-500">*</span></label>
                <input
                  name="last_name"
                  type="text"
                  className="mt-1 w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500"
                  value={form.last_name}
                  onChange={handleChange}
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Email <span className="text-red-500">*</span></label>
                <input
                  name="email"
                  type="email"
                  className="mt-1 w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500"
                  value={form.email}
                  onChange={handleChange}
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Phone Number <span className="text-red-500">*</span></label>
                <input
                  name="phone_number"
                  type="tel"
                  className="mt-1 w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="+14255550123"
                  value={form.phone_number}
                  onChange={handleChange}
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Password <span className="text-red-500">*</span></label>
                <input
                  name="password"
                  type="password"
                  className="mt-1 w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500"
                  value={form.password}
                  onChange={handleChange}
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Confirm Password <span className="text-red-500">*</span></label>
                <input
                  name="password_confirm"
                  type="password"
                  className="mt-1 w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500"
                  value={form.password_confirm}
                  onChange={handleChange}
                  required
                />
              </div>
            </div>
          </section>

          <div className="flex items-start">
            <input
              name="terms_accepted"
              type="checkbox"
              className="mt-1 mr-2"
              checked={form.terms_accepted}
              onChange={handleChange}
              required
            />
            <label className="text-sm text-gray-700">I agree to the <a href="#" className="text-primary-600 underline">Terms and Conditions</a> <span className="text-red-500">*</span></label>
          </div>

          <Button type="submit" className="w-full" disabled={registerApi.loading}>
            {registerApi.loading ? 'Registering...' : 'Register Business'}
          </Button>
        </form>
      </div>
    </div>
  )
}

export default BusinessRegister 