import { Link } from 'react-router-dom'
import Button from '../../components/Button'

function RegisterSuccess() {
  return (
    <div className="container flex justify-center items-center py-16">
      <div className="w-full max-w-lg bg-white rounded-lg shadow-md p-8 text-center space-y-6">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
          <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </div>
        <h2 className="text-2xl font-bold text-gray-900">Business Registered Successfully!</h2>
        <p className="text-gray-700">Your administrator account is ready, and your business profile has been created.</p>

        <div className="space-y-4 text-left">
          <h3 className="text-lg font-semibold text-gray-800">Next Steps</h3>
          <ul className="list-disc pl-5 space-y-2 text-sm text-gray-700">
            <li>
              <Link to="/business/settings" className="text-primary-600 hover:underline">Complete your profile</Link>
            </li>
            <li>
              <Link to="/services/create" className="text-primary-600 hover:underline">Add services</Link>
            </li>
            <li>
              <Link to="/employees/invite" className="text-primary-600 hover:underline">Invite employees</Link>
            </li>
          </ul>
        </div>

        <div className="flex flex-col sm:flex-row gap-3 justify-center pt-4">
          <Button as={Link} to="/dashboard" className="px-6 py-2">Go to Dashboard</Button>
        </div>
      </div>
    </div>
  )
}

export default RegisterSuccess 