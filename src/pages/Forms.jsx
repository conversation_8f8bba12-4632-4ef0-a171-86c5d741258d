import { useParams } from 'react-router-dom'
import FormsTable from '../features/forms/FormsTable'
import FormEditor from '../features/forms/FormEditor'

function Forms() {
  const { id } = useParams()

  return (
    <div className="container py-10">
      <h1 className="text-3xl mb-6">Forms</h1>
      {id ? <FormEditor formId={parseInt(id)} /> : <FormsTable />}
    </div>
  )
}

export default Forms 