import { uploadPdf } from '../features/forms/services/pdfUploadApi';

/**
 * Format a date string to a readable format
 * @param {string} dateString - ISO date string
 * @returns {string} - Formatted date (e.g., "May 15, 2024")
 */
export const formatDate = (dateString) => {
  if (!dateString) return ''
  
  const options = { year: 'numeric', month: 'long', day: 'numeric' }
  return new Date(dateString).toLocaleDateString(undefined, options)
}

/**
 * Format a date and time string to a readable format
 * @param {string} dateString - ISO date string
 * @param {string} timeString - Time string (HH:MM)
 * @returns {string} - Formatted date and time
 */
export const formatDateTime = (dateString, timeString) => {
  if (!dateString) return ''
  
  const datePart = formatDate(dateString)
  
  if (!timeString) return datePart
  
  // Parse time string (HH:MM)
  const [hours, minutes] = timeString.split(':').map(Number)
  
  // Create date object with the time
  const date = new Date(dateString)
  date.setHours(hours, minutes)
  
  // Format time
  const timeOptions = { hour: 'numeric', minute: 'numeric', hour12: true }
  const timePart = date.toLocaleTimeString(undefined, timeOptions)
  
  return `${datePart} at ${timePart}`
}

/**
 * Truncate text with ellipsis if it exceeds max length
 * @param {string} text - Text to truncate
 * @param {number} maxLength - Maximum length before truncating
 * @returns {string} - Truncated text
 */
export const truncateText = (text, maxLength = 100) => {
  if (!text || text.length <= maxLength) return text
  return text.slice(0, maxLength) + '...'
}

/**
 * Convert camelCase to Title Case
 * @param {string} camelCase - camelCase string
 * @returns {string} - Title Case string
 */
export const camelToTitleCase = (camelCase) => {
  if (!camelCase) return ''
  
  // Add space before capital letters and uppercase the first character
  const result = camelCase
    .replace(/([A-Z])/g, ' $1')
    .replace(/^./, (str) => str.toUpperCase())
  
  return result
}

/**
 * Generate a unique ID
 * @returns {string} - Unique ID
 */
export const generateId = () => {
  return Math.random().toString(36).substring(2, 15) + 
    Math.random().toString(36).substring(2, 15)
}

/**
 * Deep clone an object
 * @param {object} obj - Object to clone
 * @returns {object} - Cloned object
 */
export const deepClone = (obj) => {
  return JSON.parse(JSON.stringify(obj))
}

/**
 * Group an array of objects by a key
 * @param {Array} array - Array of objects
 * @param {string} key - Key to group by
 * @returns {Object} - Grouped object
 */
export const groupBy = (array, key) => {
  return array.reduce((result, item) => {
    const groupKey = item[key]
    if (!result[groupKey]) {
      result[groupKey] = []
    }
    result[groupKey].push(item)
    return result
  }, {})
}

// Export class name utility
export { cn } from './cn' 
/**
 * Converts a signature data URL to a PDF and triggers download
 * @param {string} signatureDataUrl - The data URL of the signature (from canvas.toDataURL())
 * @param {string} filename - The name of the PDF file to save
 * @param {Object} options - Additional options
 * @param {string} options.title - PDF title
 * @param {string} options.customerName - Customer name to include in the PDF
 * @param {string} options.documentType - Type of document being signed
 * @param {boolean} options.skipUpload - Skip the upload to S3 (for debugging)
 */
export const saveSignatureToPdf = async (signatureDataUrl, filename = 'signature.pdf', options = {}) => {
  try {
    console.log('Starting PDF generation process...');
    
    // Import libraries dynamically to avoid bundling them unnecessarily
    const [jsPDF, html2canvas] = await Promise.all([
      import('jspdf').then(module => module.default),
      import('html2canvas').then(module => module.default)
    ]);
    
    console.log('Libraries loaded successfully');
    
    // Create a temporary container for the signature
    const container = document.createElement('div');
    container.style.position = 'absolute';
    container.style.left = '-9999px';
    container.style.top = '-9999px';
    container.style.padding = '20px';
    container.style.width = '800px'; // Updated from 600px to match new canvas width
    
    // Create the signature document with metadata
    container.innerHTML = `
      <div style="font-family: Arial, sans-serif;">
        <h2 style="text-align: center; margin-bottom: 20px;">${options.title || 'Signature Document'}</h2>
        ${options.customerName ? `<p><strong>Customer:</strong> ${options.customerName}</p>` : ''}
        ${options.documentType ? `<p><strong>Document Type:</strong> ${options.documentType}</p>` : ''}
        <p><strong>Date:</strong> ${new Date().toLocaleDateString()}</p>
        <div style="margin: 30px 0; border-bottom: 1px solid #ccc; padding-bottom: 10px;">
          <p><strong>Customer Signature:</strong></p>
          <img src="${signatureDataUrl}" style="max-width: 100%; max-height: 250px;" />
        </div>
      </div>
    `;
    
    document.body.appendChild(container);
    console.log('Signature container created');
    
    // Render the container to canvas
    const canvas = await html2canvas(container, {
      scale: 2, // Increase quality
      useCORS: true,
      allowTaint: true,
      logging: false
    });
    console.log('Canvas rendered successfully');
    
    // Create PDF with appropriate dimensions
    const imgData = canvas.toDataURL('image/png');
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });
    
    const imgWidth = 210; // A4 width in mm
    const imgHeight = (canvas.height * imgWidth) / canvas.width;
    
    pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);
    
    // Save locally
    pdf.save(filename);
    console.log('PDF saved locally');
    
    // Clean up the temporary container
    document.body.removeChild(container);
    
    // Skip upload if skipUpload option is true
    if (options.skipUpload) {
      console.log('Skipping upload to AWS S3 (debug mode)');
      return { 
        success: true, 
        message: 'PDF saved locally only (upload skipped)',
        debug: true
      };
    }
    
    // Upload to AWS S3 using our dedicated service
    console.log('Preparing to upload PDF to AWS S3...');
    
    // Get the PDF as blob
    const pdfBlob = pdf.output('blob');
    
    try {
      // Upload using our API service
      console.log('Calling uploadPdf with blob size:', pdfBlob.size);
      const result = await uploadPdf(pdfBlob, filename, {
        customerName: options.customerName,
        documentType: options.documentType,
        category: 'signatures',
      });
      
      console.log('Upload successful:', result);
      return result;
    } catch (uploadError) {
      console.error('Upload failed:', uploadError);
      console.error('Error details:', uploadError.stack);
      throw new Error(`PDF saved locally but upload failed: ${uploadError.message}`);
    }
  } catch (error) {
    console.error('Error in PDF process:', error);
    throw error;
  }
}; 

/**
 * Generates a PDF from a full form definition (array of questions) and saves
 * it locally as well as uploads it to AWS S3 via the same upload service
 * used for signatures. Behaviour mirrors saveSignatureToPdf so that callers
 * only need to await the returned Promise.
 *
 * @param {string} formTitle       – Title of the form to appear at top of PDF
 * @param {Array}  questions       – Array of question objects from the builder
 * @param {string} filename        – Name of the PDF file to download/save
 * @param {Object} options         – Extra options { documentType, skipUpload }
 *
 * Usage:
 *   await saveFormToPdf('Client Intake', questions, 'client_intake.pdf')
 */
export const saveFormToPdf = async (
  formTitle = 'Form',
  questions = [],
  filename = 'form.pdf',
  options = {}
) => {
  try {
    // Dynamically import heavy libs so they are only loaded when needed
    const [jsPDF, html2canvas] = await Promise.all([
      import('jspdf').then(m => m.default),
      import('html2canvas').then(m => m.default),
    ])

    /* -------------------------------------------------------------- */
    /* Build a hidden DOM fragment that mirrors the public preview     */
    /* -------------------------------------------------------------- */
    const container = document.createElement('div')
    Object.assign(container.style, {
      position: 'absolute',
      left: '-9999px',
      top: '-9999px',
      padding: '20px',
      width: '800px',
      fontFamily: 'Arial, sans-serif',
      background: '#ffffff',
      color: '#000000',
    })

    const buildQuestionHtml = (q, idx) => {
      if (!q) return ''
      switch (q.type) {
        case 'text': {
          const tag = q.textStyle === 'Heading' ? 'h2' : q.textStyle === 'Subheading' ? 'h3' : 'p'
          return `<${tag} style="margin:12px 0;">${q.textContent || ''}</${tag}>`
        }
        case 'short':
        case 'long': {
          const height = q.type === 'short' ? 30 : 60
          return `
            <div style="margin:14px 0;">
              <p style="margin:0 0 6px;"><strong>Q${idx + 1}: ${q.label || ''}</strong></p>
              <div style="height:${height}px;border-bottom:1px solid #bbb;"></div>
            </div>
          `
        }
        case 'multiple': {
          const opts = (q.options || []).map(opt => `<li>${opt}</li>`).join('')
          return `
            <div style="margin:14px 0;">
              <p style="margin:0 0 6px;"><strong>Q${idx + 1}: ${q.label || ''}</strong></p>
              <ul style="margin:0 0 0 18px;padding:0;list-style-type:disc;">${opts}</ul>
            </div>
          `
        }
        case 'signature': {
          const imgHtml = q.signatureData?.customer
            ? `<img src="${q.signatureData.customer}" style="max-width:100%;max-height:200px;" />`
            : '<div style="height:100px;border:1px dashed #bbb;"></div>'
          return `
            <div style="margin:18px 0;">
              <p style="margin:0 0 6px;"><strong>Customer Signature</strong></p>
              ${imgHtml}
            </div>
          `
        }
        default:
          return ''
      }
    }

    const questionsHtml = questions.map((q, idx) => buildQuestionHtml(q, idx)).join('')

    container.innerHTML = `
      <div>
        <h1 style="text-align:center;margin-bottom:24px;">${formTitle}</h1>
        ${questionsHtml}
        <p style="margin-top:24px;text-align:right;">Date: ${new Date().toLocaleDateString()}</p>
      </div>
    `

    document.body.appendChild(container)

    /* -------------------------------------------------------------- */
    /* Render to canvas then PDF                                       */
    /* -------------------------------------------------------------- */
    const canvas = await html2canvas(container, {
      scale: 2,
      useCORS: true,
      allowTaint: true,
      logging: false,
    })

    const imgData = canvas.toDataURL('image/png')
    const pdf = new jsPDF({ orientation: 'portrait', unit: 'mm', format: 'a4' })
    const imgWidth = 210 // A4 width in mm
    const imgHeight = (canvas.height * imgWidth) / canvas.width
    pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight)

    // Save locally first so the user always gets a copy
    pdf.save(filename)

    // Clean up DOM
    document.body.removeChild(container)

    // Optionally skip upload (useful for local testing)
    if (options.skipUpload) {
      return { success: true, message: 'PDF saved locally (upload skipped)', debug: true }
    }

    /* -------------------------------------------------------------- */
    /* Upload blob to S3 via backend endpoint                           */
    /* -------------------------------------------------------------- */
    const pdfBlob = pdf.output('blob')
    const uploadResult = await uploadPdf(pdfBlob, filename, {
      documentType: options.documentType || 'Form',
      description: `Form PDF for ${formTitle}`,
      category: 'forms',
    })

    return uploadResult
  } catch (error) {
    console.error('Error in saveFormToPdf:', error)
    throw error
  }
} 
