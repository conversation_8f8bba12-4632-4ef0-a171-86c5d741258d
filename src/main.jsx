import React from 'react'
import <PERSON>actD<PERSON> from 'react-dom/client'
import { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import App from './App'
import './styles/index.css'
import { preloadServices } from './features/calendar/services/serviceCache.js'
import { preloadCustomerData } from './features/calendar/services/customerLookupService.js'

// ✅ iOS PATTERN: Create QueryClient with iOS-style cache settings
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes like iOS
      cacheTime: 30 * 60 * 1000, // 30 minutes like iOS
      refetchOnWindowFocus: false, // iOS doesn't refetch on focus
      refetchOnReconnect: true, // iOS does refetch on reconnect
      retry: 2 // iOS-style retry logic
    }
  }
})

// ✅ iOS PATTERN: Preload all necessary data on app startup for faster performance
console.log('🚀 [iOS Pattern] Preloading data on app startup...')
// Only preload if user is authenticated
if (localStorage.getItem('auth_token')) {
  preloadServices()
  preloadCustomerData()
} else {
  console.log('🔒 [iOS Pattern] User not authenticated, skipping data preload')
}

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <App />
      </BrowserRouter>
    </QueryClientProvider>
  </React.StrictMode>,
)
