// transactionModel.js - 交易数据模型定义

// 交易记录数据结构
export const TransactionModel = {
  id: null,
  transaction_id: "",
  checkout_date: "",
  checkout_by: "",
  app_date: "",
  customer: "",
  item_sold: "",
  sold_by: "",
  source: "",
  qty: 0,
  price: 0,
  tax: 0,
  tip: 0,
  discount: 0,
  amount_paid: 0,
  cash: 0,
  check: 0,
  gift_card: 0,
  package: 0,
  membership: 0,
  credit_card: 0,
  bank_account: 0,
  vagaro: 0,
  pay_later: 0,
  other: 0,
  iou_invoice: 0,
  points: "0(0)",
  merchant_account: 0,
  change_due: 0,
  created_at: "",
  updated_at: "",
};

// 交易统计数据结构
export const TransactionSummaryModel = {
  total_transactions: 0,
  total_earned: 0,
  total_tax: 0,
  total_tips: 0,
  total_discounts: 0,
  payment_methods: {
    cash: 0,
    credit_card: 0,
    check: 0,
    gift_card: 0,
    vagaro: 0,
    pay_later: 0,
    other: 0,
    iou_invoice: 0,
    points: 0,
    merchant_account: 0,
    change_due: 0,
  },
  average_transaction: 0,
  date_range: {
    start: "",
    end: "",
  },
};

// 收银机余额数据结构
export const DrawerBalanceModel = {
  date: "",
  balance: 0,
  transactions: [],
  opening_balance: 0,
  closing_balance: 0,
};

// 销售统计数据结构
export const SalesStatsModel = {
  total_sales: 0,
  total_orders: 0,
  average_order_value: 0,
  top_services: [],
  sales_by_period: [],
};

// 支付方式统计数据结构
export const PaymentMethodStatsModel = {
  payment_methods: [],
  total_amount: 0,
  total_transactions: 0,
};

// 员工销售统计数据结构
export const StaffSalesModel = {
  staff_sales: [],
  total_staff_sales: 0,
  period: {
    start: "",
    end: "",
  },
};

// 交易筛选条件
export const TransactionFilters = {
  startDate: "",
  endDate: "",
  serviceProvider: "all",
  customer: "all",
  page: 1,
  limit: 50,
};

// 高级筛选条件
export const AdvancedFilters = {
  transaction_types: [], // ['service', 'product', 'package']
  payment_methods: [], // ['cash', 'credit_card', 'check', 'gift_card', 'vagaro', 'pay_later', 'other', 'iou_invoice', 'points', 'merchant_account', 'change_due']
  amount_range: {
    min: 0,
    max: 0,
  },
  staff_members: [],
  service_categories: [],
  product_categories: [],
  discount_types: [],
  order_sources: [], // ['pos', 'online', 'app']
};

// 报告生成参数
export const ReportParameters = {
  type: "", // 'daily', 'weekly', 'monthly', 'custom'
  period: {
    start: "",
    end: "",
  },
  format: "csv", // 'csv', 'xlsx', 'pdf'
  include_details: true,
  include_summary: true,
  group_by: "date", // 'date', 'staff', 'service', 'payment_method'
};

// 数据验证函数
export const validateTransactionData = (transaction) => {
  const errors = [];

  if (!transaction.transaction_id) {
    errors.push("Transaction ID is required");
  }

  if (!transaction.checkout_date) {
    errors.push("Checkout date is required");
  }

  if (!transaction.customer) {
    errors.push("Customer is required");
  }

  if (!transaction.amount_paid || transaction.amount_paid <= 0) {
    errors.push("Amount paid must be greater than 0");
  }

  return errors;
};

// 数据格式化函数
export const formatTransactionData = (transaction) => {
  return {
    ...transaction,
    price: parseFloat(transaction.price) || 0,
    tax: parseFloat(transaction.tax) || 0,
    tip: parseFloat(transaction.tip) || 0,
    discount: parseFloat(transaction.discount) || 0,
    amount_paid: parseFloat(transaction.amount_paid) || 0,
    cash: parseFloat(transaction.cash) || 0,
    check: parseFloat(transaction.check) || 0,
    gift_card: parseFloat(transaction.gift_card) || 0,
    package: parseFloat(transaction.package) || 0,
    membership: parseFloat(transaction.membership) || 0,
    credit_card: parseFloat(transaction.credit_card) || 0,
    bank_account: parseFloat(transaction.bank_account) || 0,
    vagaro: parseFloat(transaction.vagaro) || 0,
    pay_later: parseFloat(transaction.pay_later) || 0,
    other: parseFloat(transaction.other) || 0,
    iou_invoice: parseFloat(transaction.iou_invoice) || 0,
    points: transaction.points || "0(0)",
    merchant_account: parseFloat(transaction.merchant_account) || 0,
    change_due: parseFloat(transaction.change_due) || 0,
    qty: parseInt(transaction.qty) || 0,
  };
};

// 计算总计的工具函数
export const calculateTransactionTotals = (transactions) => {
  return transactions.reduce(
    (totals, tx) => ({
      qty: totals.qty + (tx.qty || 0),
      price: totals.price + (tx.price || 0),
      tax: totals.tax + (tx.tax || 0),
      tip: totals.tip + (tx.tip || 0),
      discount: totals.discount + (tx.discount || 0),
      amount_paid: totals.amount_paid + (tx.amount_paid || 0),
      cash: totals.cash + (tx.cash || 0),
      check: totals.check + (tx.check || 0),
      gift_card: totals.gift_card + (tx.gift_card || 0),
      package: totals.package + (tx.package || 0),
      membership: totals.membership + (tx.membership || 0),
      credit_card: totals.credit_card + (tx.credit_card || 0),
      bank_account: totals.bank_account + (tx.bank_account || 0),
      vagaro: totals.vagaro + (tx.vagaro || 0),
      pay_later: totals.pay_later + (tx.pay_later || 0),
      other: totals.other + (tx.other || 0),
      iou_invoice: totals.iou_invoice + (tx.iou_invoice || 0),
      points: totals.points + (parseInt(tx.points) || 0),
      merchant_account: totals.merchant_account + (tx.merchant_account || 0),
      change_due: totals.change_due + (tx.change_due || 0),
    }),
    {
      qty: 0,
      price: 0,
      tax: 0,
      tip: 0,
      discount: 0,
      amount_paid: 0,
      cash: 0,
      check: 0,
      gift_card: 0,
      package: 0,
      membership: 0,
      credit_card: 0,
      bank_account: 0,
      vagaro: 0,
      pay_later: 0,
      other: 0,
      iou_invoice: 0,
      points: 0,
      merchant_account: 0,
      change_due: 0,
    }
  );
};

// 格式化货币显示
export const formatCurrency = (amount) => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 2,
  }).format(amount);
};

// 格式化日期显示
export const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
};

// 格式化时间显示
export const formatTime = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleTimeString("en-US", {
    hour: "2-digit",
    minute: "2-digit",
  });
};

// 导出默认模型
export default TransactionModel;
