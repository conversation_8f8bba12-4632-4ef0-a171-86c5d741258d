import { useState, useRef, useEffect } from "react";
import {
  CalendarIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
} from "@heroicons/react/24/outline";

function DateRangePicker({
  startDate,
  endDate,
  onChange,
  placeholder = "Select date range",
}) {
  const [isOpen, setIsOpen] = useState(false);
  const [currentMonth, setCurrentMonth] = useState(new Date().getMonth());
  const [currentYear, setCurrentYear] = useState(new Date().getFullYear());
  const [selectingStart, setSelectingStart] = useState(true);
  const [tempStartDate, setTempStartDate] = useState(startDate);
  const [tempEndDate, setTempEndDate] = useState(endDate);
  const [hoveredDate, setHoveredDate] = useState(null);

  const dropdownRef = useRef(null);

  const months = [
    "January", "February", "March", "April", "May", "June",
    "July", "August", "September", "October", "November", "December",
  ];

  const weekDays = ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"];

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Format date for display
  const formatDate = (date) => {
    if (!date) return '';
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  // Get display text for input
  const getDisplayText = () => {
    if (!startDate || !endDate) return placeholder;
    if (startDate.toDateString() === endDate.toDateString()) {
      return formatDate(startDate);
    }
    return `${formatDate(startDate)} - ${formatDate(endDate)}`;
  };

  // Generate calendar days for current month
  const generateCalendarDays = () => {
    const firstDay = new Date(currentYear, currentMonth, 1);
    const lastDay = new Date(currentYear, currentMonth + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());

    const days = [];
    const currentDate = new Date(startDate);

    for (let i = 0; i < 42; i++) {
      days.push(new Date(currentDate));
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return days;
  };

  // Navigate to previous month
  const goToPreviousMonth = () => {
    if (currentMonth === 0) {
      setCurrentMonth(11);
      setCurrentYear(currentYear - 1);
    } else {
      setCurrentMonth(currentMonth - 1);
    }
  };

  // Navigate to next month
  const goToNextMonth = () => {
    if (currentMonth === 11) {
      setCurrentMonth(0);
      setCurrentYear(currentYear + 1);
    } else {
      setCurrentMonth(currentMonth + 1);
    }
  };

  // Handle date selection
  const handleDateClick = (date) => {
    console.log('Date clicked:', date, 'selectingStart:', selectingStart, 'tempStartDate:', tempStartDate);
    
    // Auto-navigate to the correct month if clicking on adjacent month date
    const clickedMonth = date.getMonth();
    const clickedYear = date.getFullYear();
    if (clickedMonth !== currentMonth || clickedYear !== currentYear) {
      setCurrentMonth(clickedMonth);
      setCurrentYear(clickedYear);
    }
    
    if (selectingStart || !tempStartDate) {
      setTempStartDate(date);
      setTempEndDate(null);
      setSelectingStart(false);
      console.log('Set start date:', date);
    } else {
      if (date >= tempStartDate) {
        setTempEndDate(date);
        console.log('Set end date:', date);
      } else {
        setTempStartDate(date);
        setTempEndDate(tempStartDate);
        console.log('Swapped dates - new start:', date, 'new end:', tempStartDate);
      }
      setSelectingStart(true);
    }
  };

  // Handle quick select options
  const handleQuickSelect = (type) => {
    const today = new Date();
    let newStartDate, newEndDate;

    switch (type) {
      case "today":
        newStartDate = newEndDate = new Date(today);
        break;
      case "yesterday":
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);
        newStartDate = newEndDate = yesterday;
        break;
      case "last7":
        newStartDate = new Date(today);
        newStartDate.setDate(today.getDate() - 6);
        newEndDate = new Date(today);
        break;
      case "last30":
        newStartDate = new Date(today);
        newStartDate.setDate(today.getDate() - 29);
        newEndDate = new Date(today);
        break;
      case "thisMonth":
        newStartDate = new Date(today.getFullYear(), today.getMonth(), 1);
        newEndDate = new Date(today);
        break;
      case "lastMonth":
        newStartDate = new Date(today.getFullYear(), today.getMonth() - 1, 1);
        newEndDate = new Date(today.getFullYear(), today.getMonth(), 0);
        break;
      case "thisYear":
        newStartDate = new Date(today.getFullYear(), 0, 1);
        newEndDate = new Date(today);
        break;
      case "lastYear":
        newStartDate = new Date(today.getFullYear() - 1, 0, 1);
        newEndDate = new Date(today.getFullYear() - 1, 11, 31);
        break;
      default:
        return;
    }

    setTempStartDate(newStartDate);
    setTempEndDate(newEndDate);
    setSelectingStart(true);
  };

  // Apply selection
  const handleApply = () => {
    console.log('Apply clicked - tempStartDate:', tempStartDate, 'tempEndDate:', tempEndDate);
    if (tempStartDate && tempEndDate) {
      console.log('Calling onChange with:', { startDate: tempStartDate, endDate: tempEndDate });
      onChange({ startDate: tempStartDate, endDate: tempEndDate });
      setIsOpen(false);
    } else {
      console.log('Cannot apply - missing dates');
    }
  };

  // Clear selection
  const handleClear = () => {
    setTempStartDate(null);
    setTempEndDate(null);
    setSelectingStart(true);
  };

  // Check if date is today
  const isToday = (date) => {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  };

  // Check if date is in range
  const isInRange = (date) => {
    if (!tempStartDate || !tempEndDate) return false;
    return date >= tempStartDate && date <= tempEndDate;
  };

  // Check if date is range start
  const isRangeStart = (date) => {
    return tempStartDate && date.toDateString() === tempStartDate.toDateString();
  };

  // Check if date is range end
  const isRangeEnd = (date) => {
    return tempEndDate && date.toDateString() === tempEndDate.toDateString();
  };

  // Check if date is in hover range (for preview)
  const isInHoverRange = (date) => {
    if (!tempStartDate || !hoveredDate || tempEndDate) return false;
    const start = tempStartDate;
    const end = hoveredDate;
    const minDate = start <= end ? start : end;
    const maxDate = start <= end ? end : start;
    return date >= minDate && date <= maxDate;
  };

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Input Button */}
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-left flex items-center justify-between group"
      >
        <span className={`text-sm font-medium ${startDate && endDate ? "text-gray-900" : "text-gray-500"}`}>
          {getDisplayText()}
        </span>
        <CalendarIcon className="h-5 w-5 text-gray-400 group-hover:text-gray-600 transition-colors" />
      </button>

      {/* Calendar Dropdown */}
      {isOpen && (
        <div className="absolute z-50 mt-2 bg-white rounded-xl shadow-xl border border-gray-100 overflow-hidden w-80">
          <div className="p-4">
            {/* Calendar Header */}
            <div className="flex items-center justify-between mb-4">
              <button
                onClick={goToPreviousMonth}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ChevronLeftIcon className="h-4 w-4 text-gray-600" />
              </button>

              <div className="flex gap-2">
                <select
                  value={currentMonth}
                  onChange={(e) => setCurrentMonth(parseInt(e.target.value))}
                  className="bg-gray-50 border-0 rounded-lg px-3 py-2 text-sm font-medium text-gray-700 focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  {months.map((month, index) => (
                    <option key={index} value={index}>
                      {month}
                    </option>
                  ))}
                </select>
                <select
                  value={currentYear}
                  onChange={(e) => setCurrentYear(parseInt(e.target.value))}
                  className="bg-gray-50 border-0 rounded-lg px-3 py-2 text-sm font-medium text-gray-700 focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  {Array.from({ length: 10 }, (_, i) => new Date().getFullYear() - 5 + i).map((year) => (
                    <option key={year} value={year}>
                      {year}
                    </option>
                  ))}
                </select>
              </div>

              <button
                onClick={goToNextMonth}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ChevronRightIcon className="h-4 w-4 text-gray-600" />
              </button>
            </div>

            {/* Week Days Header */}
            <div className="grid grid-cols-7 gap-1 mb-2">
              {weekDays.map((day) => (
                <div key={day} className="text-center text-xs font-medium text-gray-500 py-2">
                  {day}
                </div>
              ))}
            </div>

            {/* Calendar Grid */}
            <div className="grid grid-cols-7 gap-1 mb-4">
              {generateCalendarDays().map((date, index) => {
                const isCurrentMonth = date.getMonth() === currentMonth;
                const isTodayDate = isToday(date);
                const isSelected = isInRange(date);
                const isStart = isRangeStart(date);
                const isEnd = isRangeEnd(date);
                const isHovered = isInHoverRange(date);

                return (
                  <button
                    key={index}
                    onClick={() => handleDateClick(date)}
                    onMouseEnter={() => setHoveredDate(date)}
                    onMouseLeave={() => setHoveredDate(null)}
                    className={`
                      h-10 w-10 text-sm font-medium rounded-lg transition-all duration-200 relative cursor-pointer
                      ${!isCurrentMonth ? "text-gray-400 hover:text-gray-600 hover:bg-gray-50" : "text-gray-700 hover:bg-gray-100"}
                      ${isSelected ? (
                        isStart || isEnd 
                          ? "bg-primary-600 text-white hover:bg-primary-700 shadow-md" 
                          : "bg-primary-100 text-primary-800"
                      ) : ""}
                      ${isHovered && !isSelected ? "bg-primary-50 text-primary-600" : ""}
                      ${isTodayDate && !isSelected ? "bg-blue-50 text-blue-600 font-bold" : ""}
                      hover:scale-105
                    `}
                  >
                    {date.getDate()}
                    {isTodayDate && !isSelected && (
                      <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-blue-600 rounded-full"></div>
                    )}
                  </button>
                );
              })}
            </div>

            {/* Quick Select Buttons */}
            <div className="border-t border-gray-100 pt-4 mb-4">
              <div className="grid grid-cols-2 gap-2">
                <button
                  onClick={() => handleQuickSelect("today")}
                  className="px-3 py-2 text-xs font-medium text-gray-700 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  Today
                </button>
                <button
                  onClick={() => handleQuickSelect("yesterday")}
                  className="px-3 py-2 text-xs font-medium text-gray-700 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  Yesterday
                </button>
                <button
                  onClick={() => handleQuickSelect("last7")}
                  className="px-3 py-2 text-xs font-medium text-gray-700 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  Last 7 Days
                </button>
                <button
                  onClick={() => handleQuickSelect("last30")}
                  className="px-3 py-2 text-xs font-medium text-gray-700 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  Last 30 Days
                </button>
                <button
                  onClick={() => handleQuickSelect("thisMonth")}
                  className="px-3 py-2 text-xs font-medium text-gray-700 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  This Month
                </button>
                <button
                  onClick={() => handleQuickSelect("lastMonth")}
                  className="px-3 py-2 text-xs font-medium text-gray-700 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  Last Month
                </button>
                <button
                  onClick={() => handleQuickSelect("thisYear")}
                  className="px-3 py-2 text-xs font-medium text-gray-700 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  This Year
                </button>
                <button
                  onClick={() => handleQuickSelect("lastYear")}
                  className="px-3 py-2 text-xs font-medium text-gray-700 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  Last Year
                </button>
              </div>
            </div>

            {/* Selection Status */}
            {tempStartDate && (
              <div className="bg-gray-50 rounded-lg p-3 mb-4 text-sm">
                <div className="text-gray-600">
                  {tempEndDate ? (
                    <span>
                      <span className="font-medium">{formatDate(tempStartDate)}</span>
                      {" → "}
                      <span className="font-medium">{formatDate(tempEndDate)}</span>
                    </span>
                  ) : (
                    <span>
                      Start: <span className="font-medium">{formatDate(tempStartDate)}</span>
                      <span className="text-primary-600 ml-2">Select end date</span>
                    </span>
                  )}
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex gap-2">
              <button
                onClick={handleClear}
                className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Clear
              </button>
              <button
                onClick={handleApply}
                disabled={!tempStartDate || !tempEndDate}
                className="flex-1 px-4 py-2 text-sm font-medium text-white bg-primary-600 rounded-lg hover:bg-primary-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
              >
                Apply
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default DateRangePicker; 