import { useState, useEffect } from "react";
import { 
  ChevronDownIcon, 
  MagnifyingGlassIcon,
  FunnelIcon,
  XMarkIcon
} from "@heroicons/react/24/outline";
import Button from "../../../components/Button";
import DateRangePicker from "./DateRangePicker";

// Payment method icons and colors
const PAYMENT_METHODS = {
  cash: { label: "Cash", color: "bg-green-100 text-green-800", icon: "💵" },
  credit_card: { label: "Credit Card", color: "bg-blue-100 text-blue-800", icon: "💳" },
  debit_card: { label: "Debit Card", color: "bg-purple-100 text-purple-800", icon: "💳" },
  venmo: { label: "Venmo", color: "bg-indigo-100 text-indigo-800", icon: "📱" },
  zelle: { label: "Zelle", color: "bg-yellow-100 text-yellow-800", icon: "⚡" },
  check: { label: "Check", color: "bg-gray-100 text-gray-800", icon: "📝" },
  paypal: { label: "PayPal", color: "bg-blue-100 text-blue-800", icon: "🅿️" },
  other: { label: "Other", color: "bg-gray-100 text-gray-800", icon: "❓" },
};

function ReportsFilterPanel({
  filters,
  onFiltersChange,
  filterOptions,
  loading,
  onRunReport,
  showAdvancedFilters = true,
  showPaymentMethods = false,
  showCustomers = true,
  showServiceProviders = true,
  showSearch = true,
  showAmountRange = false,
  advancedFields = [],
  customFields = [],
  title = "Filter Options"
}) {
  // UI state
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [showServiceProvidersDropdown, setShowServiceProvidersDropdown] = useState(false);
  const [showCustomersDropdown, setShowCustomersDropdown] = useState(false);

  // Handle filter changes
  const handleFilterChange = (key, value) => {
    onFiltersChange(prev => ({ ...prev, [key]: value }));
  };

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      const isClickInsideServiceProviders = event.target.closest('[data-dropdown="service-providers"]');
      const isClickInsideCustomers = event.target.closest('[data-dropdown="customers"]');
      
      if (!isClickInsideServiceProviders) {
        setShowServiceProvidersDropdown(false);
      }
      if (!isClickInsideCustomers) {
        setShowCustomersDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Clear all filters
  const clearAllFilters = () => {
    onFiltersChange({
      startDate: null,
      endDate: null,
      serviceProviders: [],
      customers: [],
      paymentMethods: [],
      search: '',
      minAmount: '',
      maxAmount: '',
      ...Object.fromEntries(advancedFields.map(field => [field.key, field.defaultValue || '']))
    });
  };

  // Check if any filters are active
  const hasActiveFilters = () => {
    return (
      filters.startDate || 
      filters.endDate || 
      filters.serviceProviders?.length > 0 || 
      filters.customers?.length > 0 || 
      filters.paymentMethods?.length > 0 || 
      filters.search || 
      filters.minAmount || 
      filters.maxAmount ||
      advancedFields.some(field => filters[field.key] && filters[field.key] !== field.defaultValue)
    );
  };

  return (
    <div className="bg-white border-b border-gray-200 px-6 py-4">
      {/* Main Filters Row */}
      <div className="grid grid-cols-1 lg:grid-cols-5 gap-4 items-end">
        {/* Date Range */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Transaction Date
          </label>
          <DateRangePicker
            startDate={filters.startDate}
            endDate={filters.endDate}
            onChange={({ startDate, endDate }) => {
              // Update both dates at once to avoid state update issues
              onFiltersChange(prev => ({ 
                ...prev, 
                startDate: startDate, 
                endDate: endDate 
              }));
            }}
            className="w-full"
          />
        </div>

        {/* Service Providers */}
        {showServiceProviders && (
          <div className="relative" data-dropdown="service-providers">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Service Providers
            </label>
            <div className="relative">
              <button
                type="button"
                onClick={() => setShowServiceProvidersDropdown(!showServiceProvidersDropdown)}
                className="w-full px-3 py-2 text-left bg-white border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm"
              >
                <span className="block truncate">
                  {filters.serviceProviders?.length === 0 
                    ? "Select providers..." 
                    : filters.serviceProviders?.length === filterOptions.service_providers?.length
                    ? "All providers selected"
                    : `${filters.serviceProviders?.length || 0} provider(s) selected`
                  }
                </span>
                <ChevronDownIcon 
                  className={`absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 transition-transform ${
                    showServiceProvidersDropdown ? 'rotate-180' : ''
                  }`} 
                />
              </button>
              
              {showServiceProvidersDropdown && (
                <div className="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-auto">
                  {/* Select All */}
                  <div className="px-3 py-2 border-b border-gray-200">
                    <label className="flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                        checked={filters.serviceProviders?.length === filterOptions.service_providers?.length}
                        onChange={(e) => {
                          if (e.target.checked) {
                            handleFilterChange('serviceProviders', filterOptions.service_providers?.map(p => p.id.toString()) || []);
                          } else {
                            handleFilterChange('serviceProviders', []);
                          }
                        }}
                      />
                      <span className="ml-2 text-sm font-medium text-gray-900">Select All</span>
                    </label>
                  </div>
                  
                  {/* Options */}
                  {filterOptions.service_providers?.map((provider) => (
                    <div key={provider.id} className="px-3 py-2 hover:bg-gray-50">
                      <label className="flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                          checked={filters.serviceProviders?.includes(provider.id.toString())}
                          onChange={(e) => {
                            if (e.target.checked) {
                              handleFilterChange('serviceProviders', [...(filters.serviceProviders || []), provider.id.toString()]);
                            } else {
                              handleFilterChange('serviceProviders', (filters.serviceProviders || []).filter(id => id !== provider.id.toString()));
                            }
                          }}
                        />
                        <span className="ml-2 text-sm text-gray-900">{provider.name}</span>
                      </label>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Customers */}
        {showCustomers && (
          <div className="relative" data-dropdown="customers">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Customers
            </label>
            <div className="relative">
              <button
                type="button"
                onClick={() => setShowCustomersDropdown(!showCustomersDropdown)}
                className="w-full px-3 py-2 text-left bg-white border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm"
              >
                <span className="block truncate">
                  {filters.customers?.length === 0 
                    ? "Select customers..." 
                    : filters.customers?.length === filterOptions.customers?.length
                    ? "All customers selected"
                    : `${filters.customers?.length || 0} customer(s) selected`
                  }
                </span>
                <ChevronDownIcon 
                  className={`absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 transition-transform ${
                    showCustomersDropdown ? 'rotate-180' : ''
                  }`} 
                />
              </button>
              
              {showCustomersDropdown && (
                <div className="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-auto">
                  {/* Select All */}
                  <div className="px-3 py-2 border-b border-gray-200">
                    <label className="flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                        checked={filters.customers?.length === filterOptions.customers?.length}
                        onChange={(e) => {
                          if (e.target.checked) {
                            handleFilterChange('customers', filterOptions.customers?.map(c => c.id.toString()) || []);
                          } else {
                            handleFilterChange('customers', []);
                          }
                        }}
                      />
                      <span className="ml-2 text-sm font-medium text-gray-900">Select All</span>
                    </label>
                  </div>
                  
                  {/* Options */}
                  {filterOptions.customers?.map((customer) => (
                    <div key={customer.id} className="px-3 py-2 hover:bg-gray-50">
                      <label className="flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                          checked={filters.customers?.includes(customer.id.toString())}
                          onChange={(e) => {
                            if (e.target.checked) {
                              handleFilterChange('customers', [...(filters.customers || []), customer.id.toString()]);
                            } else {
                              handleFilterChange('customers', (filters.customers || []).filter(id => id !== customer.id.toString()));
                            }
                          }}
                        />
                        <span className="ml-2 text-sm text-gray-900">{customer.name}</span>
                      </label>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Search */}
        {showSearch && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Search
            </label>
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search transactions..."
                className="block w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-primary-500 focus:border-primary-500 focus:outline-none text-sm"
                value={filters.search || ''}
                onChange={(e) => handleFilterChange('search', e.target.value)}
              />
            </div>
          </div>
        )}

        {/* Custom Fields */}
        {customFields.map((field) => (
          <div key={field.key}>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {field.label}
            </label>
            {field.type === 'select' ? (
              <select
                value={filters[field.key] || field.defaultValue || ''}
                onChange={(e) => handleFilterChange(field.key, e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-primary-500 focus:border-primary-500 text-sm"
              >
                {field.options.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            ) : (
              <input
                type={field.type || 'text'}
                placeholder={field.placeholder}
                className="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-primary-500 focus:border-primary-500 text-sm"
                value={filters[field.key] || ''}
                onChange={(e) => handleFilterChange(field.key, e.target.value)}
              />
            )}
          </div>
        ))}

        {/* Run Report Button */}
        <div>
          <Button
            onClick={onRunReport}
            className="w-full bg-primary-600 hover:bg-primary-700 text-white font-medium py-2.5"
            disabled={loading}
          >
            {loading ? "Loading..." : "Run Report"}
          </Button>
        </div>
      </div>

      {/* Advanced Filters Toggle */}
      {showAdvancedFilters && (
        <div className="mt-4 flex items-center justify-between">
          <button
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="flex items-center text-sm text-gray-600 hover:text-gray-900 transition-colors"
          >
            <FunnelIcon className="h-4 w-4 mr-2" />
            Advanced Filters
            <ChevronDownIcon 
              className={`h-4 w-4 ml-1 transform transition-transform ${
                showAdvanced ? 'rotate-180' : ''
              }`} 
            />
          </button>

          {/* Clear All Filters */}
          {hasActiveFilters() && (
            <button
              onClick={clearAllFilters}
              className="text-sm text-red-600 hover:text-red-700 transition-colors"
            >
              Clear All Filters
            </button>
          )}
        </div>
      )}

      {/* Advanced Filters Panel */}
      {showAdvanced && (
        <div className="mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Payment Methods */}
            {showPaymentMethods && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Payment Methods
                </label>
                <div className="space-y-2">
                  {Object.entries(PAYMENT_METHODS).map(([key, config]) => (
                    <label key={key} className="flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                        checked={filters.paymentMethods?.includes(key)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            handleFilterChange('paymentMethods', [...(filters.paymentMethods || []), key]);
                          } else {
                            handleFilterChange('paymentMethods', (filters.paymentMethods || []).filter(m => m !== key));
                          }
                        }}
                      />
                      <span className="ml-2 text-sm text-gray-700 flex items-center">
                        <span className="mr-1">{config.icon}</span>
                        {config.label}
                      </span>
                    </label>
                  ))}
                </div>
              </div>
            )}

            {/* Amount Range */}
            {showAmountRange && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Amount Range
                </label>
                <div className="space-y-2">
                  <input
                    type="number"
                    placeholder="Min amount"
                    className="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-primary-500 focus:border-primary-500 text-sm"
                    value={filters.minAmount || ''}
                    onChange={(e) => handleFilterChange('minAmount', e.target.value)}
                  />
                  <input
                    type="number"
                    placeholder="Max amount"
                    className="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-primary-500 focus:border-primary-500 text-sm"
                    value={filters.maxAmount || ''}
                    onChange={(e) => handleFilterChange('maxAmount', e.target.value)}
                  />
                </div>
              </div>
            )}

            {/* Advanced Fields */}
            {advancedFields.map((field) => (
              <div key={field.key}>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  {field.label}
                </label>
                {field.type === 'checkbox' ? (
                  <label className="flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                      checked={filters[field.key] || false}
                      onChange={(e) => handleFilterChange(field.key, e.target.checked)}
                    />
                    <span className="ml-2 text-sm text-gray-700">{field.checkboxLabel}</span>
                  </label>
                ) : field.type === 'select' ? (
                  <select
                    value={filters[field.key] || field.defaultValue || ''}
                    onChange={(e) => handleFilterChange(field.key, e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-primary-500 focus:border-primary-500 text-sm"
                  >
                    {field.options.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                ) : (
                  <input
                    type={field.type || 'text'}
                    placeholder={field.placeholder}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-primary-500 focus:border-primary-500 text-sm"
                    value={filters[field.key] || ''}
                    onChange={(e) => handleFilterChange(field.key, e.target.value)}
                  />
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

export default ReportsFilterPanel; 