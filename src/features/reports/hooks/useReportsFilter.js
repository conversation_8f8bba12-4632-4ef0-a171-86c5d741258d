import { useState, useEffect, useCallback } from 'react';
import { reportsApi } from '../services/reportsApi';

export function useReportsFilter(initialFilters = {}) {
  // Default filter state
  const defaultFilters = {
    startDate: null,
    endDate: null,
    serviceProviders: [],
    customers: [],
    paymentMethods: [],
    search: '',
    minAmount: '',
    maxAmount: '',
    ...initialFilters
  };

  // State management
  const [filters, setFilters] = useState(defaultFilters);
  const [filterOptions, setFilterOptions] = useState({
    service_providers: [],
    customers: [],
    services: [],
    payment_methods: []
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Load filter options from the shared API endpoint
  const loadFilterOptions = useCallback(async () => {
    try {
      setLoading(true);
      const options = await reportsApi.getFilterOptions();
      setFilterOptions(options);
      setError(null);
    } catch (error) {
      console.error('Failed to load filter options:', error);
      setError('Failed to load filter options');
      // Set empty arrays as fallback
      setFilterOptions({
        service_providers: [],
        customers: [],
        services: [],
        payment_methods: []
      });
    } finally {
      setLoading(false);
    }
  }, []);

  // Load filter options on mount
  useEffect(() => {
    loadFilterOptions();
  }, [loadFilterOptions]);

  // Helper function to get API-ready parameters
  const getApiParams = useCallback((customFilters = {}) => {
    const currentFilters = { ...filters, ...customFilters };
    
    // Set default dates if not provided (today)
    const today = new Date();
    const defaultStartDate = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const defaultEndDate = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);
    
    return {
      startDate: currentFilters.startDate || defaultStartDate,
      endDate: currentFilters.endDate || defaultEndDate,
      serviceProviders: currentFilters.serviceProviders?.length > 0 ? currentFilters.serviceProviders : undefined,
      customers: currentFilters.customers?.length > 0 ? currentFilters.customers : undefined,
      paymentMethods: currentFilters.paymentMethods?.length > 0 ? currentFilters.paymentMethods : undefined,
      search: currentFilters.search || undefined,
      minAmount: currentFilters.minAmount || undefined,
      maxAmount: currentFilters.maxAmount || undefined,
    };
  }, [filters]);

  // Reset filters to default
  const resetFilters = useCallback(() => {
    setFilters(defaultFilters);
  }, []);

  // Update filters
  const updateFilters = useCallback((newFilters) => {
    if (typeof newFilters === 'function') {
      setFilters(newFilters);
    } else {
      setFilters(prev => ({ ...prev, ...newFilters }));
    }
  }, []);

  // Check if filters have values
  const hasActiveFilters = useCallback(() => {
    return (
      filters.startDate ||
      filters.endDate ||
      filters.serviceProviders?.length > 0 ||
      filters.customers?.length > 0 ||
      filters.paymentMethods?.length > 0 ||
      filters.search ||
      filters.minAmount ||
      filters.maxAmount
    );
  }, [filters]);

  return {
    // Filter state
    filters,
    setFilters: updateFilters,
    filterOptions,
    loading,
    error,
    setLoading,
    setError,

    // Helper functions
    getApiParams,
    resetFilters,
    hasActiveFilters,
    loadFilterOptions,
  };
}

export default useReportsFilter; 