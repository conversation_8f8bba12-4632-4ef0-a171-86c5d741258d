// transactionApi.js - Modern API service for transaction reports
// Updated to work with new Django backend /api/v1/reports/transactions/ endpoints

const API_BASE_URL = import.meta.env.VITE_API_URL || "http://localhost:8000/api/v1";

class TransactionApiService {
  constructor() {
    this.baseURL = API_BASE_URL;
  }

  // Helper method to get auth token
  getAuthToken() {
    return localStorage.getItem("auth_token") || sessionStorage.getItem("auth_token");
  }

  // Helper method to make API requests with improved error handling
  async makeRequest(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const authToken = this.getAuthToken();
    
    const defaultOptions = {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
      },
      ...options,
    };

    try {
      const response = await fetch(url, defaultOptions);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || 
          errorData.detail || 
          errorData.error ||
          `HTTP error! status: ${response.status}`
        );
      }

      return await response.json();
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error);
      throw error;
    }
  }

  // Format date for API (YYYY-MM-DD)
  formatDate(date) {
    if (!date) return null;
    const d = new Date(date);
    return d.toISOString().split('T')[0];
  }

  // ========== Transaction Endpoints ==========

  // GET /api/v1/reports/transactions/ - List all transactions with filtering and pagination
  async getTransactions(params = {}) {
    const queryParams = new URLSearchParams();

    // Date filtering
    if (params.startDate) {
      queryParams.append("start_date", this.formatDate(params.startDate));
    }
    if (params.endDate) {
      queryParams.append("end_date", this.formatDate(params.endDate));
    }

    // Service provider filtering (employee who sold the service)
    if (params.serviceProviders && Array.isArray(params.serviceProviders)) {
      params.serviceProviders.forEach(id => {
        if (id !== "all") queryParams.append("service_providers", id);
      });
    } else if (params.serviceProvider && params.serviceProvider !== "all") {
      queryParams.append("service_providers", params.serviceProvider);
    }

    // Customer filtering
    if (params.customers && Array.isArray(params.customers)) {
      params.customers.forEach(id => {
        if (id !== "all") queryParams.append("customers", id);
      });
    } else if (params.customer && params.customer !== "all") {
      queryParams.append("customers", params.customer);
    }

    // Payment method filtering
    if (params.paymentMethods && Array.isArray(params.paymentMethods)) {
      params.paymentMethods.forEach(method => {
        queryParams.append("payment_methods", method);
      });
    }

    // Search functionality
    if (params.search) {
      queryParams.append("search", params.search);
    }

    // Amount filtering
    if (params.minAmount) {
      queryParams.append("min_amount", params.minAmount);
    }
    if (params.maxAmount) {
      queryParams.append("max_amount", params.maxAmount);
    }

    // Pagination
    if (params.page) queryParams.append("page", params.page);
    if (params.pageSize) queryParams.append("page_size", params.pageSize);

    const queryString = queryParams.toString();
    const endpoint = `/reports/transactions/${queryString ? `?${queryString}` : ""}`;

    return await this.makeRequest(endpoint);
  }

  // GET /api/v1/reports/transactions/summary/ - Get transaction summary statistics with filtering
  async getTransactionSummary(params = {}) {
    const queryParams = new URLSearchParams();

    // Apply same filtering as transaction list
    if (params.startDate) {
      queryParams.append("start_date", this.formatDate(params.startDate));
    }
    if (params.endDate) {
      queryParams.append("end_date", this.formatDate(params.endDate));
    }

    if (params.serviceProviders && Array.isArray(params.serviceProviders)) {
      params.serviceProviders.forEach(id => {
        if (id !== "all") queryParams.append("service_providers", id);
      });
    } else if (params.serviceProvider && params.serviceProvider !== "all") {
      queryParams.append("service_providers", params.serviceProvider);
    }

    if (params.customers && Array.isArray(params.customers)) {
      params.customers.forEach(id => {
        if (id !== "all") queryParams.append("customers", id);
      });
    } else if (params.customer && params.customer !== "all") {
      queryParams.append("customers", params.customer);
    }

    if (params.search) {
      queryParams.append("search", params.search);
    }

    // Amount filtering
    if (params.minAmount) {
      queryParams.append("min_amount", params.minAmount);
    }
    if (params.maxAmount) {
      queryParams.append("max_amount", params.maxAmount);
    }

    const queryString = queryParams.toString();
    const endpoint = `/reports/transactions/summary/${queryString ? `?${queryString}` : ""}`;

    return await this.makeRequest(endpoint);
  }

  // GET /api/v1/reports/transactions/filter_options/ - Get dropdown options for service providers and customers
  async getFilterOptions() {
    const endpoint = "/reports/transactions/filter_options/";
    return await this.makeRequest(endpoint);
  }

  // GET /api/v1/reports/transactions/daily_summary/ - Get daily transaction totals for charts and graphs
  async getDailySummary(params = {}) {
    const queryParams = new URLSearchParams();

    if (params.startDate) {
      queryParams.append("start_date", this.formatDate(params.startDate));
    }
    if (params.endDate) {
      queryParams.append("end_date", this.formatDate(params.endDate));
    }

    const queryString = queryParams.toString();
    const endpoint = `/reports/transactions/daily_summary/${queryString ? `?${queryString}` : ""}`;

    return await this.makeRequest(endpoint);
  }

  // GET /api/v1/reports/transactions/{id}/ - Get single transaction details
  async getTransactionDetails(transactionId) {
    return await this.makeRequest(`/reports/transactions/${transactionId}/`);
  }

  // ========== Legacy Support Methods (for backward compatibility) ==========

  // Keep these methods for any components still using the old API structure
  async getServiceProviders() {
    const options = await this.getFilterOptions();
    return options.service_providers || [];
  }

  async getCustomers(params = {}) {
    const options = await this.getFilterOptions();
    return { results: options.customers || [] };
  }

  // ========== Export Functionality (Future Enhancement) ==========
  
  // GET /api/v1/reports/transactions/export/?format=csv
  async exportTransactions(params = {}, format = 'csv') {
    const queryParams = new URLSearchParams();
    
    // Add all the same filters as getTransactions
    if (params.startDate) {
      queryParams.append("start_date", this.formatDate(params.startDate));
    }
    if (params.endDate) {
      queryParams.append("end_date", this.formatDate(params.endDate));
    }
    
    queryParams.append("format", format);
    
    const queryString = queryParams.toString();
    const endpoint = `/reports/transactions/export/${queryString ? `?${queryString}` : ""}`;

    // For exports, we might want to handle this differently to download files
    const url = `${this.baseURL}${endpoint}`;
    const authToken = this.getAuthToken();
    
    return fetch(url, {
      headers: {
        Authorization: `Bearer ${authToken}`,
      },
    });
  }
}

// Export singleton instance
export const transactionApi = new TransactionApiService();
export default transactionApi;
