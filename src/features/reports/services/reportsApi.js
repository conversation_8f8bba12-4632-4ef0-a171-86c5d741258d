// reportsApi.js - Generic API service for all report types
// Works with the redesigned Django backend /api/v1/reports/ endpoints

const API_BASE_URL = import.meta.env.VITE_API_URL || "http://localhost:8000/api/v1";

class ReportsApiService {
  constructor() {
    this.baseURL = API_BASE_URL;
  }

  // Helper method to get auth token
  getAuthToken() {
    return localStorage.getItem("auth_token") || sessionStorage.getItem("auth_token");
  }

  // Helper method to make API requests with improved error handling
  async makeRequest(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const authToken = this.getAuthToken();
    
    const defaultOptions = {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
      },
      ...options,
    };

    try {
      const response = await fetch(url, defaultOptions);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || 
          errorData.detail || 
          errorData.error ||
          `HTTP error! status: ${response.status}`
        );
      }

      return await response.json();
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error);
      throw error;
    }
  }

  // Format date for API (YYYY-MM-DD) - Fix timezone issues
  formatDate(date) {
    if (!date) return null;
    const d = new Date(date);
    
    // ✅ FIX: Use local date instead of UTC to prevent timezone shifts
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    
    return `${year}-${month}-${day}`;
  }

  // Build common query parameters for all report types
  buildCommonQueryParams(params = {}) {
    const queryParams = new URLSearchParams();

    // Date filtering
    if (params.startDate) {
      queryParams.append("start_date", this.formatDate(params.startDate));
    }
    if (params.endDate) {
      queryParams.append("end_date", this.formatDate(params.endDate));
    }

    // Service provider filtering (employees)
    if (params.serviceProviders && Array.isArray(params.serviceProviders)) {
      params.serviceProviders.forEach(id => {
        if (id !== "all") queryParams.append("service_providers", id);
      });
    }

    // Customer filtering
    if (params.customers && Array.isArray(params.customers)) {
      params.customers.forEach(id => {
        if (id !== "all") queryParams.append("customers", id);
      });
    }

    // Payment method filtering
    if (params.paymentMethods && Array.isArray(params.paymentMethods)) {
      params.paymentMethods.forEach(method => {
        queryParams.append("payment_methods", method);
      });
    }

    // Search functionality
    if (params.search) {
      queryParams.append("search", params.search);
    }

    // Amount filtering
    if (params.minAmount) {
      queryParams.append("min_amount", params.minAmount);
    }
    if (params.maxAmount) {
      queryParams.append("max_amount", params.maxAmount);
    }

    return queryParams;
  }

  // ========== Shared Filter Options ==========

  // GET /api/v1/reports/filter-options/ - Get dropdown options for all reports
  async getFilterOptions() {
    const endpoint = "/reports/filter-options/";
    return await this.makeRequest(endpoint);
  }

  // ========== Transaction Reports ==========

  // GET /api/v1/reports/transactions/ - List all transactions with filtering and pagination
  async getTransactions(params = {}) {
    const queryParams = this.buildCommonQueryParams(params);

    // Transaction-specific filters
    if (params.transactionTypes && Array.isArray(params.transactionTypes)) {
      params.transactionTypes.forEach(type => {
        queryParams.append("transaction_types", type);
      });
    }

    // Pagination
    if (params.page) {
      queryParams.append("page", params.page);
    }
    if (params.pageSize) {
      queryParams.append("page_size", params.pageSize);
    }

    const queryString = queryParams.toString();
    const endpoint = `/reports/transactions/${queryString ? `?${queryString}` : ""}`;

    return await this.makeRequest(endpoint);
  }

  // GET /api/v1/reports/transactions/summary/ - Get transaction summary statistics
  async getTransactionSummary(params = {}) {
    const queryParams = this.buildCommonQueryParams(params);

    const queryString = queryParams.toString();
    const endpoint = `/reports/transactions/summary/${queryString ? `?${queryString}` : ""}`;

    return await this.makeRequest(endpoint);
  }

  // ========== Sales Summary Reports ==========

  // GET /api/v1/reports/sales-summary/ - Get sales summary with business costs and profit analysis
  async getSalesSummary(params = {}) {
    const queryParams = this.buildCommonQueryParams(params);

    // Sales summary specific filters
    if (params.tip !== undefined) {
      queryParams.append("tip", params.tip);
    }
    if (params.includePastEmployees !== undefined) {
      queryParams.append("include_past_employees", params.includePastEmployees);
    }
    if (params.appointmentDate) {
      queryParams.append("appointment_date", this.formatDate(params.appointmentDate));
    }

    const queryString = queryParams.toString();
    const endpoint = `/reports/sales-summary/${queryString ? `?${queryString}` : ""}`;

    return await this.makeRequest(endpoint);
  }

  // ========== Services Performance Reports ==========

  // GET /api/v1/reports/services/ - Get services performance data
  async getServicesReport(params = {}) {
    const queryParams = this.buildCommonQueryParams(params);

    // Services report specific filters
    if (params.serviceType && params.serviceType !== "all") {
      queryParams.append("service_type", params.serviceType);
    }
    if (params.serviceCategory && params.serviceCategory !== "all") {
      queryParams.append("service_category", params.serviceCategory);
    }
    if (params.location && params.location !== "all") {
      queryParams.append("location", params.location);
    }
    if (params.priceRange && params.priceRange !== "all") {
      queryParams.append("price_range", params.priceRange);
    }
    if (params.duration && params.duration !== "all") {
      queryParams.append("duration", params.duration);
    }
    if (params.includeRefunds !== undefined) {
      queryParams.append("include_refunds", params.includeRefunds);
    }
    if (params.includeTax !== undefined) {
      queryParams.append("include_tax", params.includeTax);
    }
    if (params.pricesIncludePointDeduction !== undefined) {
      queryParams.append("prices_include_point_deduction", params.pricesIncludePointDeduction);
    }
    if (params.includePastEmployees !== undefined) {
      queryParams.append("include_past_employees", params.includePastEmployees);
    }

    const queryString = queryParams.toString();
    const endpoint = `/reports/services/${queryString ? `?${queryString}` : ""}`;

    return await this.makeRequest(endpoint);
  }

  // ========== Booking Percentage Reports ==========

  // GET /api/v1/reports/booking-percentage/ - Get booking percentage and utilization data
  async getBookingPercentage(params = {}) {
    const queryParams = this.buildCommonQueryParams(params);

    // Booking percentage specific filters
    if (params.timeRange) {
      queryParams.append("time_range", params.timeRange);
    }
    if (params.includeBlockedTime !== undefined) {
      queryParams.append("include_blocked_time", params.includeBlockedTime);
    }
    if (params.includeBreaks !== undefined) {
      queryParams.append("include_breaks", params.includeBreaks);
    }
    if (params.showOnlyAvailable !== undefined) {
      queryParams.append("show_only_available", params.showOnlyAvailable);
    }
    if (params.includePastEmployees !== undefined) {
      queryParams.append("include_past_employees", params.includePastEmployees);
    }

    const queryString = queryParams.toString();
    const endpoint = `/reports/booking-percentage/${queryString ? `?${queryString}` : ""}`;

    return await this.makeRequest(endpoint);
  }

  // ========== Export Functionality ==========
  
  // Generic export method for any report type
  async exportReport(reportType, params = {}, format = 'csv') {
    const queryParams = this.buildCommonQueryParams(params);
    queryParams.append("format", format);
    
    const queryString = queryParams.toString();
    const endpoint = `/reports/${reportType}/export/${queryString ? `?${queryString}` : ""}`;

    // For exports, return the fetch response for download handling
    const url = `${this.baseURL}${endpoint}`;
    const authToken = this.getAuthToken();
    
    return fetch(url, {
      headers: {
        Authorization: `Bearer ${authToken}`,
      },
    });
  }

  // Specific export methods
  async exportTransactions(params = {}, format = 'csv') {
    return this.exportReport('transactions', params, format);
  }

  async exportSalesSummary(params = {}, format = 'csv') {
    return this.exportReport('sales-summary', params, format);
  }

  async exportServicesReport(params = {}, format = 'csv') {
    return this.exportReport('services', params, format);
  }

  async exportBookingPercentage(params = {}, format = 'csv') {
    return this.exportReport('booking-percentage', params, format);
  }

  // ========== Legacy Support Methods (for backward compatibility) ==========

  // Keep these methods for any components still using the old API structure
  async getServiceProviders() {
    const options = await this.getFilterOptions();
    return options.service_providers || [];
  }

  async getCustomers(params = {}) {
    const options = await this.getFilterOptions();
    return { results: options.customers || [] };
  }
}

// Export singleton instance
export const reportsApi = new ReportsApiService();
export default reportsApi; 