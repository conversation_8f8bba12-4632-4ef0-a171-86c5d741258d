// fileUploadApi.js - API service for file upload functionality

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api/v1'

class FileUploadApiService {
  constructor() {
    this.baseURL = API_BASE_URL
  }

  // Helper method to get auth token
  getAuthToken() {
    return localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token')
  }

  // Helper method to make file upload requests
  async makeFileRequest(endpoint, formData, options = {}) {
    const url = `${this.baseURL}${endpoint}`
    
    const authToken = this.getAuthToken()
    const defaultOptions = {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${authToken}`
        // Don't set Content-Type for FormData - browser will set it with boundary
      },
      body: formData,
      ...options
    }

    try {
      const response = await fetch(url, defaultOptions)
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.message || errorData.error || `HTTP error! status: ${response.status}`)
      }
      
      return await response.json()
    } catch (error) {
      console.error(`File upload failed for ${endpoint}:`, error)
      throw error
    }
  }

  // Upload raw file to backend for processing
  async uploadImportFile(file, options = {}) {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('file_type', 'customer_import')
    
    // Add optional metadata
    if (options.description) {
      formData.append('description', options.description)
    }
    if (options.skipDuplicates !== undefined) {
      formData.append('skip_duplicates', options.skipDuplicates)
    }
    if (options.updateExisting !== undefined) {
      formData.append('update_existing', options.updateExisting)
    }

    return await this.makeFileRequest('/files/upload/', formData)
  }

  // Get file processing status
  async getFileStatus(fileId) {
    const url = `${this.baseURL}/files/${fileId}/status/`
    const authToken = this.getAuthToken()
    
    const response = await fetch(url, {
      headers: {
        Authorization: `Bearer ${authToken}`
      }
    })
    
    if (!response.ok) {
      throw new Error(`Failed to get file status: ${response.status}`)
    }
    
    return await response.json()
  }

  // Start processing uploaded file
  async startFileProcessing(fileId, processingOptions = {}) {
    const url = `${this.baseURL}/files/${fileId}/process/`
    const authToken = this.getAuthToken()
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${authToken}`
      },
      body: JSON.stringify(processingOptions)
    })
    
    if (!response.ok) {
      throw new Error(`Failed to start processing: ${response.status}`)
    }
    
    return await response.json()
  }

  // Get processing results
  async getProcessingResults(fileId) {
    const url = `${this.baseURL}/files/${fileId}/results/`
    const authToken = this.getAuthToken()
    
    const response = await fetch(url, {
      headers: {
        Authorization: `Bearer ${authToken}`
      }
    })
    
    if (!response.ok) {
      throw new Error(`Failed to get results: ${response.status}`)
    }
    
    return await response.json()
  }

  // Delete uploaded file
  async deleteFile(fileId) {
    const url = `${this.baseURL}/files/${fileId}/`
    const authToken = this.getAuthToken()
    
    const response = await fetch(url, {
      method: 'DELETE',
      headers: {
        Authorization: `Bearer ${authToken}`
      }
    })
    
    if (!response.ok) {
      throw new Error(`Failed to delete file: ${response.status}`)
    }
    
    return response.status === 204
  }

  // Download sample import file
  async downloadSampleFile() {
    const url = `${this.baseURL}/files/sample-import/`
    const authToken = this.getAuthToken()
    
    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${authToken}`,
          'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel, text/csv, */*'
        }
      })
      
      if (!response.ok) {
        const errorText = await response.text().catch(() => 'Unknown error')
        throw new Error(`Failed to download sample file (${response.status}): ${errorText}`)
      }
      
      // Get the filename from the Content-Disposition header or use a default
      const contentDisposition = response.headers.get('Content-Disposition')
      const contentType = response.headers.get('Content-Type')
      
      let filename = 'customer-import-sample.xlsx'
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename[*]?=['"]?([^'"\s]+)['"]?/i)
        if (filenameMatch) {
          filename = filenameMatch[1]
        }
      }
      
      // Get the blob data with proper content type
      const blob = await response.blob()
      
      // Ensure we have a proper blob
      if (!blob || blob.size === 0) {
        throw new Error('Downloaded file is empty')
      }
      
      // Create download link and trigger download
      const downloadUrl = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = filename
      link.style.display = 'none'
      document.body.appendChild(link)
      link.click()
      
      // Cleanup
      setTimeout(() => {
        document.body.removeChild(link)
        window.URL.revokeObjectURL(downloadUrl)
      }, 100)
      
      console.log(`✅ Sample file downloaded: ${filename} (${blob.size} bytes)`)
      return { success: true, filename, size: blob.size }
      
    } catch (error) {
      console.error('❌ Sample file download failed:', error)
      throw error
    }
  }
}

// Export singleton instance
export const fileUploadApi = new FileUploadApiService()

// Export individual methods for easier importing
export const {
  uploadImportFile,
  getFileStatus,
  startFileProcessing,
  getProcessingResults,
  deleteFile,
  downloadSampleFile
} = fileUploadApi 