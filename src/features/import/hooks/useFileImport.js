import { useState } from 'react'
import { fileUploadApi } from '../services/fileUploadApi'
import { handleImportError } from '../utils/errorHandler'

export function useFileImport() {
  const [activeStep, setActiveStep] = useState('upload') // upload, processing, complete
  const [uploadedFile, setUploadedFile] = useState(null)
  const [importProgress, setImportProgress] = useState(0)
  const [importStatus, setImportStatus] = useState('idle') // idle, processing, success, error
  const [importErrors, setImportErrors] = useState([])
  const [validatedRecords, setValidatedRecords] = useState([])
  const [importJobId, setImportJobId] = useState(null)
  const [importResults, setImportResults] = useState(null)
  const [uploadResult, setUploadResult] = useState(null)

  // Reset state to start new import
  const resetImport = () => {
    setActiveStep('upload')
    setUploadedFile(null)
    setUploadResult(null)
    setImportProgress(0)
    setImportStatus('idle')
    setImportErrors([])
    setValidatedRecords([])
    setImportJobId(null)
    setImportResults(null)
  }

  // Handle file upload success - file uploaded to backend for processing
  const handleFileUpload = (file, uploadResult) => {
    setUploadedFile(file)
    setUploadResult(uploadResult)
    setActiveStep('processing')
    startFileProcessing(uploadResult.fileId)
  }

  // Start file processing on backend
  const startFileProcessing = async (fileId) => {
    setImportStatus('processing')
    setImportProgress(0)
    setImportErrors([])
    
    try {
      console.log('🚀 Starting file processing for fileId:', fileId)
      
      // Start processing the uploaded file
      const processingResult = await fileUploadApi.startFileProcessing(fileId, {
        skipDuplicates: true,
        updateExisting: false,
        validateEmails: true
      })
      
      console.log('✅ Processing started:', processingResult)
      
      const jobId = processingResult.job_id || processingResult.id
      setImportJobId(jobId)
      
      // Poll for processing updates
      pollFileProcessing(fileId, jobId)
      
    } catch (error) {
      console.error('❌ Failed to start processing:', error)
      setImportStatus('error')
      const errorInfo = handleImportError(error, { type: 'queue' })
      setImportErrors([errorInfo.fullMessage])
    }
  }

  // Poll file processing progress
  const pollFileProcessing = async (fileId, jobId) => {
    const pollInterval = setInterval(async () => {
      try {
        const status = await fileUploadApi.getFileStatus(fileId)
        
        setImportProgress(status.progress || 0)
        
        if (status.status === 'completed' || status.status === 'processed') {
          clearInterval(pollInterval)
          setImportStatus('success')
          
          // Get final results
          const results = await fileUploadApi.getProcessingResults(fileId)
          console.log('🔍 Processing results:', results)
          
          setImportResults(results)
          
          // Handle different possible response structures
          let importedCount = 0
          let successfulRecords = []
          
          if (results.successful) {
            successfulRecords = Array.isArray(results.successful) ? results.successful : []
            importedCount = successfulRecords.length
          } else if (results.imported) {
            successfulRecords = Array.isArray(results.imported) ? results.imported : []
            importedCount = successfulRecords.length
          } else if (results.success_count || results.successCount) {
            importedCount = results.success_count || results.successCount
            successfulRecords = Array(importedCount).fill({}) // Create placeholder array
          } else if (results.total_imported || results.totalImported) {
            importedCount = results.total_imported || results.totalImported
            successfulRecords = Array(importedCount).fill({}) // Create placeholder array
          } else if (results.imported_count || results.importedCount) {
            importedCount = results.imported_count || results.importedCount
            successfulRecords = Array(importedCount).fill({}) // Create placeholder array
          } else if (typeof results.count === 'number') {
            importedCount = results.count
            successfulRecords = Array(importedCount).fill({}) // Create placeholder array
          }
          
          console.log('✅ Extracted import count:', importedCount)
          console.log('✅ Successful records:', successfulRecords)
          
          setValidatedRecords(successfulRecords)
          setImportErrors(results.errors || results.failed || [])
          
          setActiveStep('complete')
        } else if (status.status === 'failed' || status.status === 'error') {
          clearInterval(pollInterval)
          setImportStatus('error')
          const errorInfo = handleImportError(status.errors || 'Processing failed', { type: 'processing' })
          setImportErrors([errorInfo.fullMessage])
        } else if (status.status === 'cancelled') {
          clearInterval(pollInterval)
          setImportStatus('error')
          const errorInfo = handleImportError('Processing cancelled', { type: 'processing' })
          setImportErrors([errorInfo.fullMessage])
        }
        
        // Update progress if available
        if (status.processed_rows && status.total_rows) {
          setImportProgress(Math.round((status.processed_rows / status.total_rows) * 100))
        }
        
      } catch (error) {
        console.error('Failed to get file status:', error)
        clearInterval(pollInterval)
        setImportStatus('error')
        const errorInfo = handleImportError(error, { type: 'processing' })
        setImportErrors([errorInfo.fullMessage])
      }
    }, 2000) // Poll every 2 seconds

    // Cleanup interval after 10 minutes (timeout)
    setTimeout(() => {
      clearInterval(pollInterval)
      if (importStatus === 'processing') {
        setImportStatus('error')
        const errorInfo = handleImportError('Processing timeout', { type: 'timeout' })
        setImportErrors([errorInfo.fullMessage])
      }
    }, 600000)
  }

  return {
    // State
    activeStep,
    uploadedFile,
    importProgress,
    importStatus,
    importErrors,
    validatedRecords,
    importResults,
    uploadResult,
    
    // Actions
    resetImport,
    handleFileUpload
  }
} 