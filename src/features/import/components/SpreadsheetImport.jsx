import { useState, useEffect } from 'react'
import { useLocation } from 'react-router-dom'
import FileDropZone from './FileDropZone'
import { useFileImport } from '../hooks/useFileImport'

function SpreadsheetImport({ onBack }) {
  const location = useLocation()
  const isFromVagaro = location.state?.fromVagaro
  const [processingState, setProcessingState] = useState('upload') // 'upload', 'processing', 'success'
  
  const {
    activeStep,
    uploadedFile,
    importStatus,
    importErrors,
    uploadResult,
    resetImport,
    handleFileUpload
  } = useFileImport()

  // Handle state changes from the hook - NO REDIRECTION
  useEffect(() => {
    if (activeStep === 'processing') {
      setProcessingState('processing')
    }
  }, [activeStep])

  const handleReset = () => {
    setProcessingState('upload')
    resetImport()
  }

  const handleNewUpload = () => {
    setProcessingState('upload')
    resetImport()
  }

  return (
    <div className="container py-10">
      <div className="flex items-center justify-between mb-8">
        <div>
          <button
            onClick={onBack}
            className="inline-flex items-center text-gray-600 hover:text-gray-900 mb-4"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            Back to Import Options
          </button>
                      <h1 className="text-3xl font-bold text-gray-900 mb-2">
              {isFromVagaro ? 'Vagaro Import' : 'Spreadsheet Import'}
            </h1>
            <p className="text-gray-600">
              {isFromVagaro 
                ? 'Import your exported Vagaro customer data' 
                : 'Import customer data from CSV and Excel files'
              }
            </p>
        </div>
        <div className="flex items-center space-x-3">
          {processingState !== 'upload' && (
            <button
              onClick={handleNewUpload}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
              Upload Another File
            </button>
          )}

        </div>
      </div>



      {/* Main Content */}
      <div className="space-y-8">
        {processingState === 'upload' && (
          <div className="bg-white rounded-xl shadow-sm">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">
                {isFromVagaro ? 'Upload Vagaro Export File' : 'Upload Customer Data'}
              </h2>
              <p className="text-sm text-gray-600 mt-1">
                {isFromVagaro 
                  ? 'Upload the Excel or CSV file exported from your Vagaro account'
                  : 'Upload Excel files (.xlsx, .xls) or CSV files containing customer information'
                }
              </p>
            </div>
            <div className="p-6">
              <FileDropZone onFileUpload={handleFileUpload} />
            </div>
          </div>
        )}

        {processingState === 'processing' && (
          <div className="bg-white rounded-xl shadow-sm">
            <div className="p-6 text-center py-12">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                {isFromVagaro ? 'Vagaro Import Started!' : 'File Processing Started!'}
              </h3>
              <p className="text-gray-600 mb-6 max-w-md mx-auto">
                {isFromVagaro 
                  ? 'Your Vagaro customer data has been submitted and is being processed asynchronously in the background. You can upload additional files or navigate away from this page.'
                  : 'Your file has been submitted and is being processed asynchronously in the background. You can upload additional files or navigate away from this page.'
                }
              </p>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <div className="flex items-start">
                  <svg className="w-5 h-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                  <div className="text-sm text-blue-800">
                    <strong>Email Notification:</strong> You'll receive an email confirmation once all customers have been successfully imported into your database.
                  </div>
                </div>
              </div>
              <div className="flex justify-center space-x-4">
                <button
                  onClick={handleNewUpload}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
                >
                  Upload Another File
                </button>
                <button
                  onClick={onBack}
                  className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium"
                >
                  Back to Import Options
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Error State */}
        {importErrors && importErrors.length > 0 && (
          <div className="bg-white rounded-xl shadow-sm">
            <div className="p-6">
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <h4 className="text-sm font-medium text-red-900 mb-2">Upload Error:</h4>
                <ul className="text-sm text-red-800 list-disc list-inside space-y-1">
                  {importErrors.map((error, index) => (
                    <li key={index}>{typeof error === 'string' ? error : JSON.stringify(error)}</li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default SpreadsheetImport 