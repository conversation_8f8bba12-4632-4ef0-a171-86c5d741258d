import { useState } from 'react'
import { fileUploadApi } from '../services/fileUploadApi'

function ImportMethodSelector({ onSelectMethod, onShowVagaroModal }) {
  const [isDownloading, setIsDownloading] = useState(false)

  const handleDownloadSample = async () => {
    setIsDownloading(true)
    try {
      const result = await fileUploadApi.downloadSampleFile()
      console.log('Sample file download completed:', result)
    } catch (error) {
      console.error('Failed to download sample file:', error)
      
      // Show user-friendly error message based on error type
      let errorMessage = 'Failed to download sample file. Please try again.'
      if (error.message.includes('404')) {
        errorMessage = 'Sample file not found. Please contact support.'
      } else if (error.message.includes('403')) {
        errorMessage = 'Access denied. Please check your permissions.'
      } else if (error.message.includes('empty')) {
        errorMessage = 'Sample file is empty. Please contact support.'
      }
      
      alert(errorMessage)
    } finally {
      setIsDownloading(false)
    }
  }
  return (
    <div className="container py-10">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Import Management</h1>
        <p className="text-gray-600">Choose your preferred method to import customer data</p>
      </div>

      <div className="grid md:grid-cols-2 gap-6 max-w-4xl mx-auto">
        {/* Spreadsheet Import Option */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 hover:border-gray-300 transition-colors">
          <div className="p-8 text-center">
            <div className="w-16 h-16 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-6">
              <svg className="w-10 h-10 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M19,3H5C3.9,3 3,3.9 3,5V19C3,20.1 3.9,21 5,21H19C20.1,21 21,20.1 21,19V5C21,3.9 20.1,3 19,3M19,19H5V5H19V19Z" />
                <path d="M7,7H9V9H7V7M11,7H13V9H11V7M15,7H17V9H15V7M7,11H9V13H7V11M11,11H13V13H11V11M15,11H17V13H15V11M7,15H9V17H7V15M11,15H13V17H11V15M15,15H17V17H15V17Z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-3">Spreadsheet</h3>
            <p className="text-gray-600 mb-6 leading-relaxed">
              Upload a CSV or Excel file of your customer list.<br />
              Download a <button 
                onClick={handleDownloadSample}
                disabled={isDownloading}
                className="text-blue-600 hover:text-blue-700 underline font-medium disabled:text-gray-400 disabled:no-underline"
              >
                {isDownloading ? 'Downloading...' : 'sample file'}
              </button>.
            </p>
            <button
              onClick={() => onSelectMethod('excel')}
              className="w-full px-6 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium"
            >
              Import
            </button>
          </div>
        </div>

        {/* Vagaro Account Import Option */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 hover:border-gray-300 transition-colors">
          <div className="p-8 text-center">
            <div className="w-16 h-16 bg-red-100 rounded-xl flex items-center justify-center mx-auto mb-6">
              <svg className="w-10 h-10 text-red-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M7.07,18.28C7.5,17.38 10.12,16.5 12,16.5C13.88,16.5 16.5,17.38 16.93,18.28C15.57,19.36 13.86,20 12,20C10.14,20 8.43,19.36 7.07,18.28M18.36,16.83C16.93,15.09 13.46,14.5 12,14.5C10.54,14.5 7.07,15.09 5.64,16.83C4.62,15.5 4,13.82 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20,12C20,13.82 19.38,15.5 18.36,16.83M12,6C10.06,6 8.5,7.56 8.5,9.5C8.5,11.44 10.06,13 12,13C13.94,13 15.5,11.44 15.5,9.5C15.5,7.56 13.94,6 12,6M12,11A1.5,1.5 0 0,1 10.5,9.5A1.5,1.5 0 0,1 12,8A1.5,1.5 0 0,1 13.5,9.5A1.5,1.5 0 0,1 12,11Z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-3">Vagaro Account</h3>
            <p className="text-gray-600 mb-6 leading-relaxed">
              Add contacts from another Vagaro<br />
              account.
            </p>
            <button
              onClick={onShowVagaroModal}
              className="w-full px-6 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium"
            >
              Import
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ImportMethodSelector 