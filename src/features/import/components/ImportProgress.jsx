import { useEffect, useState } from 'react'

function ImportProgress({ progress, status, recordCount, errors = [] }) {
  const [currentRecord, setCurrentRecord] = useState(0)
  const [timeElapsed, setTimeElapsed] = useState(0)
  const [startTime] = useState(Date.now())

  // Update time elapsed
  useEffect(() => {
    if (status === 'processing') {
      const interval = setInterval(() => {
        setTimeElapsed(Math.floor((Date.now() - startTime) / 1000))
      }, 1000)
      
      return () => clearInterval(interval)
    }
  }, [status, startTime])

  // Update current record based on progress
  useEffect(() => {
    setCurrentRecord(Math.floor((progress / 100) * recordCount))
  }, [progress, recordCount])

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return mins > 0 ? `${mins}m ${secs}s` : `${secs}s`
  }

  const getEstimatedTimeRemaining = () => {
    if (progress === 0 || timeElapsed === 0) return null
    const totalEstimatedTime = (timeElapsed / progress) * 100
    const remaining = Math.max(0, Math.floor(totalEstimatedTime - timeElapsed))
    return remaining
  }

  const getStatusIcon = () => {
    switch (status) {
      case 'processing':
        return (
          <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        )
      case 'success':
        return (
          <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
            <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
        )
      case 'error':
        return (
          <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
            <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>
        )
      default:
        return (
          <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
            <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        )
    }
  }

  const getStatusMessage = () => {
    switch (status) {
      case 'processing':
        return `Processing record ${currentRecord} of ${recordCount}...`
      case 'success':
        return `Successfully imported ${recordCount} customers`
      case 'error':
        return 'Import failed due to errors'
      default:
        return 'Preparing to import...'
    }
  }

  const getStatusColor = () => {
    switch (status) {
      case 'processing':
        return 'text-blue-600'
      case 'success':
        return 'text-green-600'
      case 'error':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }

  return (
    <div className="space-y-6">
      {/* Main Progress Section */}
      <div className="text-center">
        <div className="flex items-center justify-center mb-4">
          {getStatusIcon()}
        </div>
        
        <h3 className={`text-lg font-medium mb-2 ${getStatusColor()}`}>
          {getStatusMessage()}
        </h3>
        
        <div className="max-w-md mx-auto">
          {/* Progress Bar */}
          <div className="bg-gray-200 rounded-full h-3 mb-4">
            <div 
              className={`h-3 rounded-full transition-all duration-300 ${
                status === 'success' ? 'bg-green-500' : 
                status === 'error' ? 'bg-red-500' : 'bg-blue-500'
              }`}
              style={{ width: `${progress}%` }}
            ></div>
          </div>
          
          {/* Progress Percentage */}
          <div className="flex justify-between text-sm text-gray-600 mb-2">
            <span>{progress}% Complete</span>
            <span>{currentRecord} / {recordCount} records</span>
          </div>
        </div>
      </div>

      {/* Statistics Grid */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-gray-50 rounded-lg p-4 text-center">
          <div className="text-lg font-bold text-gray-900">{recordCount}</div>
          <div className="text-sm text-gray-600">Total Records</div>
        </div>
        
        <div className="bg-blue-50 rounded-lg p-4 text-center">
          <div className="text-lg font-bold text-blue-600">{currentRecord}</div>
          <div className="text-sm text-blue-800">Processed</div>
        </div>
        
        <div className="bg-green-50 rounded-lg p-4 text-center">
          <div className="text-lg font-bold text-green-600">
            {status === 'success' ? recordCount : Math.max(0, currentRecord - errors.length)}
          </div>
          <div className="text-sm text-green-800">Successful</div>
        </div>
        
        <div className="bg-red-50 rounded-lg p-4 text-center">
          <div className="text-lg font-bold text-red-600">{errors.length}</div>
          <div className="text-sm text-red-800">Errors</div>
        </div>
      </div>

      {/* Time Information */}
      <div className="bg-gray-50 rounded-lg p-4">
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
          <div>
            <span className="text-gray-500">Time Elapsed:</span>
            <div className="font-medium">{formatTime(timeElapsed)}</div>
          </div>
          
          {status === 'processing' && getEstimatedTimeRemaining() !== null && (
            <div>
              <span className="text-gray-500">Estimated Remaining:</span>
              <div className="font-medium">{formatTime(getEstimatedTimeRemaining())}</div>
            </div>
          )}
          
          <div>
            <span className="text-gray-500">Status:</span>
            <div className={`font-medium capitalize ${getStatusColor()}`}>
              {status}
            </div>
          </div>
        </div>
      </div>

      {/* Process Steps */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-gray-900">Import Steps</h4>
        
        <div className="space-y-2">
          <div className={`flex items-center p-3 rounded-lg ${
            progress >= 10 ? 'bg-green-50 border border-green-200' : 'bg-gray-50 border border-gray-200'
          }`}>
            <div className={`w-5 h-5 rounded-full mr-3 flex items-center justify-center ${
              progress >= 10 ? 'bg-green-500' : 'bg-gray-300'
            }`}>
              {progress >= 10 && (
                <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
                </svg>
              )}
            </div>
            <span className={`text-sm ${progress >= 10 ? 'text-green-800' : 'text-gray-600'}`}>
              Validating data format and structure
            </span>
          </div>
          
          <div className={`flex items-center p-3 rounded-lg ${
            progress >= 30 ? 'bg-green-50 border border-green-200' : 'bg-gray-50 border border-gray-200'
          }`}>
            <div className={`w-5 h-5 rounded-full mr-3 flex items-center justify-center ${
              progress >= 30 ? 'bg-green-500' : 'bg-gray-300'
            }`}>
              {progress >= 30 && (
                <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
                </svg>
              )}
            </div>
            <span className={`text-sm ${progress >= 30 ? 'text-green-800' : 'text-gray-600'}`}>
              Checking for duplicates and conflicts
            </span>
          </div>
          
          <div className={`flex items-center p-3 rounded-lg ${
            progress >= 60 ? 'bg-green-50 border border-green-200' : 'bg-gray-50 border border-gray-200'
          }`}>
            <div className={`w-5 h-5 rounded-full mr-3 flex items-center justify-center ${
              progress >= 60 ? 'bg-green-500' : 'bg-gray-300'
            }`}>
              {progress >= 60 && (
                <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
                </svg>
              )}
            </div>
            <span className={`text-sm ${progress >= 60 ? 'text-green-800' : 'text-gray-600'}`}>
              Creating customer records
            </span>
          </div>
          
          <div className={`flex items-center p-3 rounded-lg ${
            progress >= 90 ? 'bg-green-50 border border-green-200' : 'bg-gray-50 border border-gray-200'
          }`}>
            <div className={`w-5 h-5 rounded-full mr-3 flex items-center justify-center ${
              progress >= 90 ? 'bg-green-500' : 'bg-gray-300'
            }`}>
              {progress >= 90 && (
                <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
                </svg>
              )}
            </div>
            <span className={`text-sm ${progress >= 90 ? 'text-green-800' : 'text-gray-600'}`}>
              Finalizing import and updating indexes
            </span>
          </div>
        </div>
      </div>

      {/* Error Messages */}
      {errors.length > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h4 className="text-sm font-medium text-red-900 mb-3">
            Import Errors ({errors.length})
          </h4>
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {errors.slice(0, 5).map((error, index) => (
              <div key={index} className="text-sm text-red-700">
                • {error}
              </div>
            ))}
            {errors.length > 5 && (
              <div className="text-sm text-red-600 font-medium">
                ... and {errors.length - 5} more errors
              </div>
            )}
          </div>
        </div>
      )}

      {/* Real-time Activity Log */}
      {status === 'processing' && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="text-sm font-medium text-blue-900 mb-3">Current Activity</h4>
          <div className="space-y-1 text-sm text-blue-800">
            {progress < 10 && <div className="animate-pulse">• Validating file structure...</div>}
            {progress >= 10 && progress < 30 && <div className="animate-pulse">• Checking for duplicate records...</div>}
            {progress >= 30 && progress < 60 && <div className="animate-pulse">• Processing customer data...</div>}
            {progress >= 60 && progress < 90 && <div className="animate-pulse">• Creating database records...</div>}
            {progress >= 90 && <div className="animate-pulse">• Finalizing import process...</div>}
          </div>
        </div>
      )}
    </div>
  )
}

export default ImportProgress 