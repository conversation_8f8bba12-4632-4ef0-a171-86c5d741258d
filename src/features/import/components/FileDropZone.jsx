import { useState, useRef, useCallback } from 'react'
import { validateFileFormat } from '../utils/fileUtils'
import { fileUploadApi } from '../services/fileUploadApi'
import { handleImportError } from '../utils/errorHandler'

function FileDropZone({ onFileUpload, acceptedFormats = ['.xlsx', '.xls', '.csv'], maxSize = 10 * 1024 * 1024 }) {
  const [isDragActive, setIsDragActive] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [error, setError] = useState(null)
  const fileInputRef = useRef(null)

  const handleFiles = useCallback(async (files) => {
    if (!files || files.length === 0) return
    
    const file = files[0]
    setError(null)
    setIsUploading(true)
    setUploadProgress(0)

    try {
      // Validate file format and size
      const validation = validateFileFormat(file, acceptedFormats, maxSize)
      if (!validation.isValid) {
        const errorInfo = handleImportError(validation.error, { type: 'validation' })
        throw new Error(errorInfo.fullMessage)
      }

      // Upload raw file to backend
      console.log('📤 Uploading file to backend:', file.name)
      
      const uploadResult = await fileUploadApi.uploadImportFile(file, {
        description: `Customer import file: ${file.name}`,
        skipDuplicates: true,
        updateExisting: false
      })

      console.log('✅ File uploaded successfully:', uploadResult)

      // Call callback with upload result instead of parsed data
      onFileUpload(file, {
        fileId: uploadResult.file_id || uploadResult.id,
        fileName: uploadResult.file_name || file.name,
        filePath: uploadResult.file_path,
        fileSize: uploadResult.file_size || file.size,
        uploadedAt: uploadResult.uploaded_at || new Date().toISOString(),
        status: uploadResult.status || 'uploaded',
        isUploaded: true // Flag to indicate this is uploaded, not parsed
      })

    } catch (err) {
      console.error('❌ File upload failed:', err)
      const errorInfo = handleImportError(err, { type: 'upload' })
      setError(errorInfo.fullMessage)
    } finally {
      setIsUploading(false)
      setUploadProgress(0)
    }
  }, [onFileUpload, acceptedFormats, maxSize])

  const handleDrop = useCallback((e) => {
    e.preventDefault()
    setIsDragActive(false)
    
    const files = Array.from(e.dataTransfer.files)
    handleFiles(files)
  }, [handleFiles])

  const handleDragOver = useCallback((e) => {
    e.preventDefault()
  }, [])

  const handleDragEnter = useCallback((e) => {
    e.preventDefault()
    setIsDragActive(true)
  }, [])

  const handleDragLeave = useCallback((e) => {
    e.preventDefault()
    if (e.currentTarget.contains(e.relatedTarget)) return
    setIsDragActive(false)
  }, [])

  const handleFileSelect = useCallback((e) => {
    const files = Array.from(e.target.files)
    handleFiles(files)
    // Reset input value to allow selecting the same file again
    e.target.value = ''
  }, [handleFiles])

  const openFileDialog = () => {
    fileInputRef.current?.click()
  }

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div className="space-y-6">
      {/* Main Drop Zone */}
      <div
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        className={`relative border-2 border-dashed rounded-xl p-8 text-center transition-all duration-200 ${
          isDragActive
            ? 'border-blue-500 bg-blue-50 scale-[1.02]'
            : error
            ? 'border-red-300 bg-red-50'
            : 'border-gray-300 bg-gray-50 hover:border-gray-400 hover:bg-gray-100'
        } ${isUploading ? 'pointer-events-none opacity-75' : 'cursor-pointer'}`}
        onClick={openFileDialog}
      >
        {isUploading ? (
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mb-4"></div>
            <p className="text-lg font-medium text-gray-900 mb-2">Uploading File...</p>
            <p className="text-sm text-gray-600">Sending file to server for processing</p>
            {uploadProgress > 0 && (
              <div className="w-full max-w-xs mt-3">
                <div className="bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${uploadProgress}%` }}
                  ></div>
                </div>
                <p className="text-xs text-gray-500 mt-1">{uploadProgress}% uploaded</p>
              </div>
            )}
          </div>
        ) : (
          <div className="flex flex-col items-center">
            <div className={`w-16 h-16 rounded-full flex items-center justify-center mb-4 ${
              isDragActive ? 'bg-blue-100' : error ? 'bg-red-100' : 'bg-gray-100'
            }`}>
              {isDragActive ? (
                <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                </svg>
              ) : error ? (
                <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              ) : (
                <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
              )}
            </div>
            
            <h3 className={`text-lg font-medium mb-2 ${
              isDragActive ? 'text-blue-900' : error ? 'text-red-900' : 'text-gray-900'
            }`}>
              {isDragActive ? 'Drop your file here' : error ? 'Upload Failed' : 'Upload Customer Data'}
            </h3>
            
            <p className={`text-sm mb-4 ${
              isDragActive ? 'text-blue-700' : error ? 'text-red-700' : 'text-gray-600'
            }`}>
              {isDragActive 
                ? 'Release to upload your file' 
                : error 
                  ? error
                  : 'Drag and drop your file here, or click to browse'
              }
            </p>

            {!error && (
              <button
                type="button"
                className="inline-flex items-center px-6 py-3 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
                Choose File
              </button>
            )}
          </div>
        )}

        <input
          ref={fileInputRef}
          type="file"
          accept={acceptedFormats.join(',')}
          onChange={handleFileSelect}
          className="hidden"
        />
      </div>

      {/* File Format Info */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start">
          <svg className="w-5 h-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div className="flex-1">
            <h4 className="text-sm font-medium text-blue-900 mb-2">Supported File Formats</h4>
            <div className="text-sm text-blue-800 space-y-1">
              <div className="flex items-center justify-between">
                <span>Excel Files (.xlsx, .xls)</span>
                <span className="text-xs bg-blue-100 px-2 py-1 rounded">Recommended</span>
              </div>
              <div className="flex items-center justify-between">
                <span>CSV Files (.csv)</span>
                <span className="text-xs bg-blue-100 px-2 py-1 rounded">Supported</span>
              </div>
              <div className="text-xs text-blue-600 mt-2">
                Maximum file size: {formatFileSize(maxSize)}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Updated Guidelines */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h4 className="text-sm font-medium text-gray-900 mb-3">File Processing</h4>
        <div className="text-sm text-gray-600 space-y-2">
          <p>Files are uploaded to the server and processed there. Your file should contain customer information with columns like:</p>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mt-3">
            {['Name', 'Email', 'Phone', 'Address', 'Birthday', 'Gender', 'City', 'Postal Code'].map(field => (
              <div key={field} className="bg-white px-3 py-1 rounded border text-xs font-medium text-center">
                {field}
              </div>
            ))}
          </div>
          <p className="text-xs text-gray-500 mt-3">
            ✓ Files are securely stored on the server<br/>
            ✓ Processing happens server-side for better performance<br/>
            ✓ Large files are supported
          </p>
        </div>
      </div>
    </div>
  )
}

export default FileDropZone 