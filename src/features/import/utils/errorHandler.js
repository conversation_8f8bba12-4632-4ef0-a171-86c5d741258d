// Generate a unique reference number for errors
const generateErrorReference = () => {
  const timestamp = Date.now().toString().slice(-6)
  const random = Math.random().toString(36).substring(2, 5).toUpperCase()
  return `REF-${timestamp}${random}`
}

// Map error types to user-friendly messages
const getErrorMessage = (error, type = 'general') => {
  const reference = generateErrorReference()
  
  // Log the actual error for debugging (server-side logging would be better)
  console.error(`Error ${reference}:`, error)
  
  switch (type) {
    case 'upload':
      return {
        message: 'Failed to upload file. Please try again.',
        reference
      }
    
    case 'processing':
      return {
        message: 'Failed to process your file. Our team has been notified.',
        reference
      }
    
    case 'server':
      return {
        message: 'Service temporarily unavailable. Please try again later.',
        reference
      }
    
    case 'timeout':
      return {
        message: 'Request timed out. Please try uploading a smaller file or try again later.',
        reference
      }
    
    case 'validation':
      return {
        message: 'File format validation failed. Please check your file and try again.',
        reference
      }
    
    case 'queue':
      return {
        message: 'Failed to add file to processing queue. Please try again.',
        reference
      }
    
    default:
      return {
        message: 'An unexpected error occurred. Please try again or contact support.',
        reference
      }
  }
}

// Format error for display
export const formatError = (error, type = 'general') => {
  const { message, reference } = getErrorMessage(error, type)
  return {
    userMessage: message,
    reference,
    fullMessage: `${message} (Reference: ${reference})`
  }
}

// Handle different types of errors
export const handleImportError = (error, context = {}) => {
  let errorType = 'general'
  
  // Determine error type based on error content or context
  if (error?.status === 500 || error?.message?.includes('500')) {
    errorType = 'server'
  } else if (error?.status === 408 || error?.message?.includes('timeout')) {
    errorType = 'timeout'
  } else if (context.type === 'upload') {
    errorType = 'upload'
  } else if (context.type === 'processing') {
    errorType = 'processing'
  } else if (context.type === 'validation') {
    errorType = 'validation'
  } else if (context.type === 'queue') {
    errorType = 'queue'
  }
  
  return formatError(error, errorType)
} 