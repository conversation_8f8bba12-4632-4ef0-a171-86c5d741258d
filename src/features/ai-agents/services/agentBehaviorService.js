import { api } from '../../../hooks/useApi'

export const agentBehaviorService = {
  // Get agent behavior configuration
  async getBehavior(agentType) {
    try {
      const response = await api.get(`/ai-agents/${agentType}/behavior/`)
      return response.data
    } catch (error) {
      console.error('Error fetching behavior:', error)
      // Return mock data for development
      return this.getMockBehavior(agentType)
    }
  },

  // Update agent behavior
  async updateBehavior(agentType, behaviorData) {
    try {
      const response = await api.patch(`/ai-agents/${agentType}/behavior/`, behaviorData)
      return response.data
    } catch (error) {
      console.error('Error updating behavior:', error)
      // Return mock success for development
      return { ...behaviorData, updated_at: new Date().toISOString() }
    }
  },

  // Reset agent behavior to defaults
  async resetToDefault(agentType) {
    try {
      const response = await api.post(`/ai-agents/${agentType}/behavior/reset/`)
      return response.data
    } catch (error) {
      console.error('Error resetting behavior:', error)
      // Return mock default behavior
      return this.getMockBehavior(agentType)
    }
  },

  // Mock data for development
  getMockBehavior(agentType) {
    const mockBehaviors = {
      front_desk: {
        tone: 'professional',
        responseLength: 'detailed',
        greetingMessage: "Hello! I'm your front desk assistant. I'm here to help you with general inquiries about our services, pricing, and policies. How can I assist you today?",
        escalationTriggers: ['complaint_handling', 'complex_requests'],
        escalationMessage: "Let me connect you with one of our team members who can better assist you with this request.",
        businessName: "",
        contextInstructions: "Always be helpful and professional. Provide accurate information about services and pricing. If unsure about specific details, offer to connect the customer with a team member.",
        updated_at: "2024-01-15T10:00:00Z"
      },
      booking: {
        tone: 'friendly',
        responseLength: 'detailed',
        greetingMessage: "Hi there! I'm your booking assistant. I can help you schedule appointments, check availability, and answer questions about our booking process. What can I help you with?",
        escalationTriggers: ['pricing_negotiation', 'emergency_situations'],
        escalationMessage: "I'll transfer you to our booking team who can provide more personalized assistance.",
        businessName: "",
        contextInstructions: "Be efficient and helpful with booking requests. Always confirm appointment details clearly. Guide customers through the booking process step by step.",
        updated_at: "2024-01-15T10:00:00Z"
      }
    }

    return mockBehaviors[agentType] || {}
  }
}
