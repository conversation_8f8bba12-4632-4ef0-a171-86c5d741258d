import { api } from '../../../hooks/useApi'

export const agentFAQService = {
  // Get all FAQs for an agent type
  async getFAQs(agentType) {
    try {
      const response = await api.get(`/ai-agents/${agentType}/faqs/`)
      return response.data.results || response.data
    } catch (error) {
      console.error('Error fetching FAQs:', error)
      // Return mock data for development
      return this.getMockFAQs(agentType)
    }
  },

  // Create a new FAQ
  async createFAQ(agentType, faqData) {
    try {
      const response = await api.post(`/ai-agents/${agentType}/faqs/`, {
        ...faqData,
        agent_type: agentType
      })
      return response.data
    } catch (error) {
      console.error('Error creating FAQ:', error)
      // Return mock success for development
      return {
        id: Date.now(),
        ...faqData,
        agent_type: agentType,
        created_at: new Date().toISOString()
      }
    }
  },

  // Update an existing FAQ
  async updateFAQ(agentType, id, updates) {
    try {
      const response = await api.patch(`/ai-agents/${agentType}/faqs/${id}/`, updates)
      return response.data
    } catch (error) {
      console.error('Error updating FAQ:', error)
      // Return mock success for development
      return { id, ...updates }
    }
  },

  // Delete an FAQ
  async deleteFAQ(agentType, id) {
    try {
      await api.delete(`/ai-agents/${agentType}/faqs/${id}/`)
      return { success: true }
    } catch (error) {
      console.error('Error deleting FAQ:', error)
      // Return mock success for development
      return { success: true }
    }
  },

  // Mock data for development
  getMockFAQs(agentType) {
    const mockFAQs = {
      front_desk: [
        {
          id: 1,
          question: "What are your business hours?",
          answer: "We are open Monday to Friday from 9:00 AM to 6:00 PM, and Saturday from 9:00 AM to 4:00 PM. We are closed on Sundays.",
          category: "general",
          agent_type: "front_desk",
          created_at: "2024-01-15T10:00:00Z"
        },
        {
          id: 2,
          question: "What services do you offer?",
          answer: "We offer a full range of salon services including haircuts, hair coloring, styling, treatments, and consultations. Please check our service menu for detailed pricing.",
          category: "services",
          agent_type: "front_desk",
          created_at: "2024-01-15T10:00:00Z"
        },
        {
          id: 3,
          question: "What payment methods do you accept?",
          answer: "We accept cash, credit cards (Visa, MasterCard, American Express), and digital payments through our app.",
          category: "policies",
          agent_type: "front_desk",
          created_at: "2024-01-15T10:00:00Z"
        }
      ],
      booking: [
        {
          id: 4,
          question: "How far in advance can I book an appointment?",
          answer: "You can book appointments up to 3 months in advance. We recommend booking at least 1 week ahead for popular services and time slots.",
          category: "booking",
          agent_type: "booking",
          created_at: "2024-01-15T10:00:00Z"
        },
        {
          id: 5,
          question: "What is your cancellation policy?",
          answer: "You can cancel or reschedule your appointment up to 24 hours in advance without any fees. Cancellations with less than 24 hours notice may incur a cancellation fee.",
          category: "policies",
          agent_type: "booking",
          created_at: "2024-01-15T10:00:00Z"
        },
        {
          id: 6,
          question: "Can I request a specific stylist?",
          answer: "Yes! You can request a specific stylist when booking your appointment. Please note that popular stylists may have limited availability.",
          category: "booking",
          agent_type: "booking",
          created_at: "2024-01-15T10:00:00Z"
        }
      ]
    }

    return mockFAQs[agentType] || []
  }
}
