export const AGENT_TYPES = {
  FRONT_DESK: {
    id: 'front_desk',
    name: 'Front Desk Agent',
    description: 'Handles general inquiries, customer service, and consultation',
    icon: '🏪',
    color: '#3b82f6',
    capabilities: [
      'General business information',
      'Service descriptions',
      'Pricing inquiries',
      'Operating hours',
      'Location and contact info',
      'Staff information',
      'Appointment policies',
      'Payment methods'
    ]
  },
  BOOKING: {
    id: 'booking',
    name: 'Booking Agent',
    description: 'Manages appointment scheduling and booking-related queries',
    icon: '📅',
    color: '#10b981',
    capabilities: [
      'Appointment scheduling',
      'Availability checking',
      'Service selection',
      'Staff preferences',
      'Cancellation policies',
      'Rescheduling assistance',
      'Booking confirmations',
      'Reminder management'
    ]
  }
}

export const AGENT_BEHAVIOR_SETTINGS = {
  TONE: ['professional', 'friendly', 'casual', 'formal'],
  RESPONSE_LENGTH: ['brief', 'detailed', 'comprehensive'],
  ESCALATION_TRIGGERS: [
    'pricing_negotiation',
    'complaint_handling',
    'complex_requests',
    'emergency_situations'
  ]
}

export const FAQ_CATEGORIES = [
  'general',
  'services',
  'pricing',
  'policies',
  'technical',
  'booking',
  'emergency'
]
