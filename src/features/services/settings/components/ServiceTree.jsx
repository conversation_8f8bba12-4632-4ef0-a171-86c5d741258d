import React, { useState } from 'react'
import {
  ChevronDownIcon,
  ChevronRightIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  FolderIcon,
  FolderOpenIcon,
  DocumentIcon,
  CurrencyDollarIcon,
  ClockIcon,
  EyeSlashIcon,
  TagIcon
} from '@heroicons/react/24/outline'
import { formatPrice, formatDuration } from '../utils/formatters'

const ServiceTree = ({
  categories,
  services,
  getCategoryServices,
  selectedCategory,
  selectedService,
  onCategorySelect,
  onServiceSelect,
  onAddCategory,
  onEditCategory,
  onDeleteCategory,
  onAddService,
  onEditService,
  onDeleteService
}) => {
  const [expandedCategories, setExpandedCategories] = useState(() => {
    // Expand first category and uncategorized section by default
    const initialExpanded = new Set(['uncategorized'])
    if (categories.length > 0) {
      initialExpanded.add(categories[0].id)
    }
    return initialExpanded
  })

  const toggleCategory = (categoryId) => {
    setExpandedCategories(prev => {
      const newSet = new Set(prev)
      if (newSet.has(categoryId)) {
        newSet.delete(categoryId)
      } else {
        newSet.add(categoryId)
      }
      return newSet
    })
  }

  // Get services without categories
  const uncategorizedServices = services.filter(service => !service.category)

  if (categories.length === 0 && uncategorizedServices.length === 0) {
    return (
      <div className="p-8 text-center text-gray-500">
        <FolderIcon className="w-12 h-12 mx-auto mb-4 text-gray-300" />
        <p className="text-sm">No categories or services found</p>
        <button
          onClick={onAddCategory}
          className="mt-2 text-sm text-blue-600 hover:text-blue-800"
        >
          Create your first category
        </button>
      </div>
    )
  }

  const renderServiceItem = (service) => (
    <div
      key={service.id}
      className={`flex items-center justify-between p-2 hover:bg-gray-50 group rounded-lg cursor-pointer ${
        selectedService && selectedService.id === service.id ? 'bg-blue-50 border-l-4 border-blue-500' : ''
      }`}
      onClick={() => onServiceSelect && onServiceSelect(service)}
    >
      <div className="flex items-center flex-1">
        <DocumentIcon className="w-4 h-4 text-gray-400 mr-2" />
        <div className="flex-1">
          <div className="text-sm font-medium text-gray-900 truncate">
            {service.name}
          </div>
          <div className="text-xs text-gray-500 flex items-center space-x-2">
            <span className="flex items-center">
              <CurrencyDollarIcon className="w-3 h-3 mr-1" />
              {formatPrice(service.price)}
            </span>
            <span className="flex items-center">
              <ClockIcon className="w-3 h-3 mr-1" />
              {formatDuration(service.duration)}
            </span>
            {!service.is_active && (
              <EyeSlashIcon className="w-3 h-3 text-gray-400" title="Hidden" />
            )}
          </div>
        </div>
      </div>
      
      <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
        <button
          onClick={(e) => {
            e.stopPropagation()
            onEditService(service)
          }}
          className="p-1 text-gray-500 hover:text-blue-600"
          title="Edit Service"
        >
          <PencilIcon className="w-3 h-3" />
        </button>
        <button
          onClick={(e) => {
            e.stopPropagation()
            onDeleteService(service.id)
          }}
          className="p-1 text-gray-500 hover:text-red-600"
          title="Delete Service"
        >
          <TrashIcon className="w-3 h-3" />
        </button>
      </div>
    </div>
  )

  return (
    <div className="space-y-0">
      {/* Regular Categories */}
      {categories.map((category) => {
        const categoryServices = getCategoryServices(category.id)
        const isExpanded = expandedCategories.has(category.id)
        
        return (
          <div key={category.id} className="border-b border-gray-100 last:border-b-0">
            {/* Category Header */}
            <div className={`flex items-center justify-between p-3 hover:bg-gray-50 group ${
              selectedCategory && selectedCategory.id === category.id ? 'bg-blue-50 border-l-4 border-blue-500' : ''
            }`}>
              <div className="flex items-center flex-1">
                <button 
                  className="mr-2"
                  onClick={() => toggleCategory(category.id)}
                >
                  {isExpanded ? (
                    <ChevronDownIcon className="w-4 h-4 text-gray-500" />
                  ) : (
                    <ChevronRightIcon className="w-4 h-4 text-gray-500" />
                  )}
                </button>
                
                <div 
                  className="flex items-center flex-1 cursor-pointer"
                  onClick={() => onCategorySelect && onCategorySelect(category)}
                >
                  {isExpanded ? (
                    <FolderOpenIcon className="w-5 h-5 text-gray-500 mr-2" />
                  ) : (
                    <FolderIcon className="w-5 h-5 text-gray-500 mr-2" />
                  )}
                  
                  <div className="flex items-center flex-1">
                    <div 
                      className="w-3 h-3 rounded-full mr-2"
                      style={{ backgroundColor: category.color }}
                    />
                    <span className="text-sm font-medium text-gray-900 truncate">
                      {category.name}
                    </span>
                    <span className="text-xs text-gray-500 ml-2">
                      ({categoryServices.length})
                    </span>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    onAddService(category.id)
                  }}
                  className="p-1 text-gray-500 hover:text-green-600"
                  title="Add Service"
                >
                  <PlusIcon className="w-4 h-4" />
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    onEditCategory(category)
                  }}
                  className="p-1 text-gray-500 hover:text-blue-600"
                  title="Edit Category"
                >
                  <PencilIcon className="w-4 h-4" />
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    onDeleteCategory(category.id)
                  }}
                  className="p-1 text-gray-500 hover:text-red-600"
                  title="Delete Category"
                >
                  <TrashIcon className="w-4 h-4" />
                </button>
              </div>
            </div>

            {/* Category Services */}
            {isExpanded && (
              <div className="pl-8 pb-2">
                {categoryServices.map((service) => renderServiceItem(service))}
                
                {categoryServices.length === 0 && (
                  <div className="text-xs text-gray-500 italic p-2">
                    No services in this category
                  </div>
                )}
              </div>
            )}
          </div>
        )
      })}

      {/* Uncategorized Services Section */}
      {uncategorizedServices.length > 0 && (
        <div className="border-b border-gray-100 last:border-b-0">
          {/* Uncategorized Header */}
          <div className="flex items-center justify-between p-3 hover:bg-gray-50 group">
            <div className="flex items-center flex-1">
              <button 
                className="mr-2"
                onClick={() => toggleCategory('uncategorized')}
              >
                {expandedCategories.has('uncategorized') ? (
                  <ChevronDownIcon className="w-4 h-4 text-gray-500" />
                ) : (
                  <ChevronRightIcon className="w-4 h-4 text-gray-500" />
                )}
              </button>
              
              <div className="flex items-center flex-1">
                {expandedCategories.has('uncategorized') ? (
                  <FolderOpenIcon className="w-5 h-5 text-gray-500 mr-2" />
                ) : (
                  <FolderIcon className="w-5 h-5 text-gray-500 mr-2" />
                )}
                
                <div className="flex items-center flex-1">
                  <TagIcon className="w-3 h-3 text-gray-400 mr-2" />
                  <span className="text-sm font-medium text-gray-600 truncate">
                    Uncategorized Services
                  </span>
                  <span className="text-xs text-gray-500 ml-2">
                    ({uncategorizedServices.length})
                  </span>
                </div>
              </div>
            </div>
            
            <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  onAddService(null) // No category
                }}
                className="p-1 text-gray-500 hover:text-green-600"
                title="Add Service"
              >
                <PlusIcon className="w-4 h-4" />
              </button>
            </div>
          </div>

          {/* Uncategorized Services */}
          {expandedCategories.has('uncategorized') && (
            <div className="pl-8 pb-2">
              {uncategorizedServices.map((service) => renderServiceItem(service))}
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default ServiceTree 