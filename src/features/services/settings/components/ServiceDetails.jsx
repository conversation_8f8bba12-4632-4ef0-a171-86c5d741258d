import React, { useState, useEffect, useRef } from 'react'
import { 
  DocumentIcon, 
  PhotoIcon, 
  XMarkIcon,
  CheckIcon,
  PencilIcon,
  TrashIcon,
  CloudArrowUpIcon,
  ClockIcon,
  GlobeAltIcon,
  UserGroupIcon
} from '@heroicons/react/24/outline'
import apiClient from '../../../employees/services/apiClient'
import { formatPrice, formatDuration } from '../utils/formatters'

const ServiceDetails = ({ selectedCategory, selectedService, onServiceUpdate }) => {
  const [serviceData, setServiceData] = useState(null)
  const [employees, setEmployees] = useState([])
  const [employeeServices, setEmployeeServices] = useState([])
  const [isLoading, setIsLoading] = useState(false)
  const [isEditing, setIsEditing] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [editForm, setEditForm] = useState({
    name: '',
    description: '',
    show_online: true
  })
  const [dragActive, setDragActive] = useState(false)
  const fileInputRef = useRef(null)

  // Fetch service details when selectedService changes
  useEffect(() => {
    if (selectedService) {
      fetchServiceDetails()
      fetchEmployees()
    } else {
      setServiceData(null)
      setEmployees([])
      setEmployeeServices([])
    }
  }, [selectedService])

  const fetchServiceDetails = async () => {
    if (!selectedService?.id) return
    
    setIsLoading(true)
    try {
      const response = await apiClient.get(`/services/${selectedService.id}/`)
      const service = response.data
      setServiceData(service)
      setEditForm({
        name: service.name || '',
        description: service.description || '',
        show_online: service.show_online || false
      })
      setEmployeeServices(service.employee_services || [])
    } catch (error) {
      console.error('Failed to fetch service details:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const fetchEmployees = async () => {
    try {
      const response = await apiClient.get('/employees/')
      setEmployees(response.data.results || response.data || [])
    } catch (error) {
      console.error('Failed to fetch employees:', error)
    }
  }

  const handleEdit = () => {
    setIsEditing(true)
  }

  const handleSave = async () => {
    if (!serviceData?.id) return
    
    setIsSaving(true)
    try {
      const updateData = {
        name: editForm.name,
        description: editForm.description,
        show_online: editForm.show_online
      }

      const response = await apiClient.patch(`/services/${serviceData.id}/`, updateData)
      setServiceData(response.data)
      setIsEditing(false)
      
      // Notify parent component of update
      if (onServiceUpdate) {
        onServiceUpdate(response.data)
      }
    } catch (error) {
      console.error('Failed to update service:', error)
      alert('Failed to update service. Please try again.')
    } finally {
      setIsSaving(false)
    }
  }

  const handleCancel = () => {
    setEditForm({
      name: serviceData?.name || '',
      description: serviceData?.description || '',
      show_online: serviceData?.show_online || false
    })
    setIsEditing(false)
  }

  const handleImageUpload = async (file) => {
    if (!serviceData?.id || !file) return

    const formData = new FormData()
    formData.append('image', file)

    try {
      const response = await apiClient.put(`/services/${serviceData.id}/`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      setServiceData(response.data)
      
      if (onServiceUpdate) {
        onServiceUpdate(response.data)
      }
    } catch (error) {
      console.error('Failed to upload image:', error)
      alert('Failed to upload image. Please try again.')
    }
  }

  const handleImageDelete = async () => {
    if (!serviceData?.id) return

    try {
      const response = await apiClient.put(`/services/${serviceData.id}/`, {
        image: null
      })
      setServiceData(response.data)
      
      if (onServiceUpdate) {
        onServiceUpdate(response.data)
      }
    } catch (error) {
      console.error('Failed to delete image:', error)
      alert('Failed to delete image. Please try again.')
    }
  }

  const handleDrag = (e) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }

  const handleDrop = (e) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleImageUpload(e.dataTransfer.files[0])
    }
  }

  const handleFileSelect = (e) => {
    if (e.target.files && e.target.files[0]) {
      handleImageUpload(e.target.files[0])
    }
  }

  const toggleEmployeeService = async (employee) => {
    if (!serviceData?.id) return

    const existingEmployeeService = employeeServices.find(es => es.employee.id === employee.id)
    
    try {
      if (existingEmployeeService) {
        // Toggle the existing employee service
        const updateData = {
          is_active: !existingEmployeeService.is_active
        }
        await apiClient.put(`/employee-services/${existingEmployeeService.id}/`, updateData)
      } else {
        // Create new employee service relationship
        const createData = {
          employee: employee.id,
          service: serviceData.id,
          is_active: true
        }
        await apiClient.post('/employee-services/', createData)
      }
      
      // Refresh service details to get updated employee services
      await fetchServiceDetails()
    } catch (error) {
      console.error('Failed to toggle employee service:', error)
      alert('Failed to update employee service assignment.')
    }
  }

  const getEmployeeServiceStatus = (employee) => {
    const employeeService = employeeServices.find(es => es.employee.id === employee.id)
    return employeeService?.is_active || false
  }

  if (!selectedService) {
    return (
      <div className="text-center text-gray-500 mt-20">
        <DocumentIcon className="w-12 h-12 mx-auto mb-4 text-gray-300" />
        <p>Select a service to view details</p>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!serviceData) {
    return (
      <div className="text-center text-gray-500 mt-20">
        <DocumentIcon className="w-12 h-12 mx-auto mb-4 text-gray-300" />
        <p>Failed to load service details</p>
      </div>
    )
  }

  return (
    <div className="h-full overflow-y-auto">
      {/* Header */}
      <div className="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Service Details</h2>
          <p className="text-sm text-gray-600 mt-1">Manage service information and employee assignments</p>
        </div>
        <div className="flex items-center gap-2">
          {isEditing ? (
            <>
              <button
                onClick={handleCancel}
                className="px-3 py-2 text-sm text-gray-600 hover:text-gray-800"
                disabled={isSaving}
              >
                Cancel
              </button>
              <button
                onClick={handleSave}
                disabled={isSaving}
                className="px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                {isSaving ? 'Saving...' : 'Save Changes'}
              </button>
            </>
          ) : (
            <button
              onClick={handleEdit}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50"
            >
              <PencilIcon className="w-4 h-4 mr-2" />
              Edit
            </button>
          )}
        </div>
      </div>

      <div className="p-6 space-y-8">
        {/* Service Image */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">Service Image</h3>
          <div className="flex items-start gap-6">
            {serviceData.image ? (
              <div className="relative">
                <img
                  src={serviceData.image}
                  alt={serviceData.name}
                  className="w-32 h-32 object-cover rounded-lg border border-gray-200"
                />
                <button
                  onClick={handleImageDelete}
                  className="absolute -top-2 -right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600"
                  title="Remove image"
                >
                  <XMarkIcon className="w-4 h-4" />
                </button>
              </div>
            ) : (
              <div className="w-32 h-32 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
                <PhotoIcon className="w-8 h-8 text-gray-400" />
              </div>
            )}
            
            <div className="flex-1">
              <div
                className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
                  dragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'
                }`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
                onClick={() => fileInputRef.current?.click()}
              >
                <CloudArrowUpIcon className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-600">
                  Drag and drop an image here, or <span className="text-blue-600">click to browse</span>
                </p>
                <p className="text-xs text-gray-500 mt-1">PNG, JPG up to 10MB</p>
              </div>
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleFileSelect}
                className="hidden"
              />
            </div>
          </div>
        </div>

        {/* Service Information */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">Service Information</h3>
          <div className="space-y-4">
            {/* Service Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Service Name</label>
              {isEditing ? (
                <input
                  type="text"
                  value={editForm.name}
                  onChange={(e) => setEditForm({ ...editForm, name: e.target.value })}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter service name"
                />
              ) : (
                <div className="p-3 bg-gray-50 rounded-lg">
                  <p className="text-gray-900">{serviceData.name}</p>
                </div>
              )}
            </div>

            {/* Service Description */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
              {isEditing ? (
                <textarea
                  value={editForm.description}
                  onChange={(e) => setEditForm({ ...editForm, description: e.target.value })}
                  rows={4}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter service description"
                />
              ) : (
                <div className="p-3 bg-gray-50 rounded-lg">
                  <p className="text-gray-900">{serviceData.description || 'No description provided'}</p>
                </div>
              )}
            </div>

            {/* Service Settings */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Price & Duration (Read-only) */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Price & Duration</label>
                <div className="p-3 bg-gray-50 rounded-lg">
                  <p className="text-gray-900">{formatPrice(serviceData.price)} • {formatDuration(serviceData.duration)}</p>
                </div>
              </div>

              {/* Buffer Time (Read-only) */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <ClockIcon className="w-4 h-4 inline mr-1" />
                  Buffer Time (minutes)
                </label>
                <div className="p-3 bg-gray-50 rounded-lg">
                  <p className="text-gray-900">{serviceData.buffer_time} minutes</p>
                </div>
              </div>
            </div>

            {/* Online Booking Toggle */}
            <div>
              <label className="flex items-center justify-between">
                <div className="flex items-center">
                  <GlobeAltIcon className="w-5 h-5 text-gray-500 mr-2" />
                  <span className="text-sm font-medium text-gray-700">Show in Online Booking</span>
                </div>
                <button
                  type="button"
                  onClick={() => isEditing && setEditForm({ ...editForm, show_online: !editForm.show_online })}
                  disabled={!isEditing}
                  className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                    (isEditing ? editForm.show_online : serviceData.show_online) ? 'bg-blue-600' : 'bg-gray-200'
                  } ${!isEditing ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  <span
                    className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                      (isEditing ? editForm.show_online : serviceData.show_online) ? 'translate-x-5' : 'translate-x-0'
                    }`}
                  />
                </button>
              </label>
              <p className="text-xs text-gray-500 mt-1">
                When enabled, customers can book this service online
              </p>
            </div>
          </div>
        </div>

        {/* Employee Assignments */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            <UserGroupIcon className="w-5 h-5 inline mr-2" />
            Employee Assignments
          </h3>
          <p className="text-sm text-gray-600 mb-4">
            Select which employees can provide this service
          </p>
          
          <div className="space-y-3">
            {employees.map((employee) => {
              const isAssigned = getEmployeeServiceStatus(employee)
              return (
                <div
                  key={employee.id}
                  className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
                >
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                      <span className="text-sm font-medium text-blue-800">
                        {employee.full_name ? employee.full_name.split(' ').map(n => n[0]).join('').toUpperCase() : 'U'}
                      </span>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">{employee.full_name}</p>
                      <p className="text-xs text-gray-500">{employee.stylist_level_display}</p>
                    </div>
                  </div>
                  
                  <button
                    onClick={() => toggleEmployeeService(employee)}
                    className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                      isAssigned ? 'bg-blue-600' : 'bg-gray-200'
                    }`}
                  >
                    <span
                      className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                        isAssigned ? 'translate-x-5' : 'translate-x-0'
                      }`}
                    />
                  </button>
                </div>
              )
            })}
            
            {employees.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <UserGroupIcon className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>No employees found</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default ServiceDetails 