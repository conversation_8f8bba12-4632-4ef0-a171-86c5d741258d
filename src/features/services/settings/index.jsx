import React, { useState } from 'react'
import { PlusIcon, ChevronDoubleLeftIcon, ChevronDoubleRightIcon } from '@heroicons/react/24/outline'
import { useServiceData } from './hooks/useServiceData'
import ServiceTree from './components/ServiceTree'
import ServiceDetails from './components/ServiceDetails'
import CategoryModal from './components/CategoryModal'
import ServiceModal from './components/ServiceModal'
import AddItemDropdown from './components/AddItemDropdown'

const ServiceMenu = () => {
  const {
    categories,
    services,
    isLoading,
    error,
    fetchData,
    deleteCategory,
    deleteService,
    getCategoryServices
  } = useServiceData()

  const [selectedCategory, setSelectedCategory] = useState(null)
  const [selectedService, setSelectedService] = useState(null)
  const [showCategoryModal, setShowCategoryModal] = useState(false)
  const [showServiceModal, setShowServiceModal] = useState(false)
  const [editingItem, setEditingItem] = useState(null)
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [showAddDropdown, setShowAddDropdown] = useState(false)

  const handleGlobalAdd = () => {
    setShowAddDropdown(true)
  }

  const handleAddCategory = () => {
    setEditingItem(null)
    setShowCategoryModal(true)
  }

  const handleEditCategory = (category) => {
    setEditingItem(category)
    setShowCategoryModal(true)
  }

  const handleAddService = (categoryId = null) => {
    setEditingItem({ category: categoryId })
    setShowServiceModal(true)
  }

  const handleEditService = (service) => {
    setEditingItem(service)
    setShowServiceModal(true)
  }

  const handleModalClose = () => {
    setShowCategoryModal(false)
    setShowServiceModal(false)
    setEditingItem(null)
  }

  const handleDropdownClose = () => {
    setShowAddDropdown(false)
  }

  const handleServiceUpdate = (updatedService) => {
    // Refresh data to get updated service info
    fetchData()
    // Update the selected service if it matches
    if (selectedService && selectedService.id === updatedService.id) {
      setSelectedService(updatedService)
    }
  }

  const handleCategorySelect = (category) => {
    setSelectedCategory(category)
    setSelectedService(null) // Clear service selection when category is selected
  }

  const handleServiceSelect = (service) => {
    setSelectedService(service)
    setSelectedCategory(null) // Clear category selection when service is selected
  }

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  if (isLoading) {
    return (
      <div className="flex-1 h-full">
        <div className="flex items-center justify-center h-full">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex-1 h-full">
        <div className="flex items-center justify-center h-full">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-800">{error}</p>
            <button
              onClick={fetchData}
              className="mt-2 text-sm text-red-600 hover:text-red-800"
            >
              Try again
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 h-full relative">
      <div className="w-full h-full">
        <div className="flex h-full">
          {/* Sidebar - Categories and Services Tree */}
          {!sidebarCollapsed && (
            <div className="w-80 bg-white border-r border-gray-200 flex flex-col transition-all duration-300 ease-in-out">
              <div className="p-4 border-b border-gray-200 flex-shrink-0">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">Services</h3>
                  <div className="flex items-center gap-2 relative">
                    <button
                      onClick={handleGlobalAdd}
                      className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg"
                      title="Add Category or Service"
                    >
                      <PlusIcon className="w-5 h-5" />
                    </button>
                    {showAddDropdown && (
                      <AddItemDropdown
                        onAddCategory={handleAddCategory}
                        onAddService={() => handleAddService(null)}
                        onClose={handleDropdownClose}
                      />
                    )}
                    <button
                      onClick={toggleSidebar}
                      className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg"
                      title="Collapse sidebar"
                    >
                      <ChevronDoubleLeftIcon className="w-5 h-5" />
                    </button>
                  </div>
                </div>
              </div>

              <div className="flex-1 overflow-y-auto min-h-0">
                <ServiceTree
                  categories={categories}
                  services={services}
                  getCategoryServices={getCategoryServices}
                  selectedCategory={selectedCategory}
                  selectedService={selectedService}
                  onCategorySelect={handleCategorySelect}
                  onServiceSelect={handleServiceSelect}
                  onAddCategory={handleAddCategory}
                  onEditCategory={handleEditCategory}
                  onDeleteCategory={deleteCategory}
                  onAddService={handleAddService}
                  onEditService={handleEditService}
                  onDeleteService={deleteService}
                />
              </div>
            </div>
          )}

          {/* Main Content - Details Panel */}
          <div className="flex-1 bg-white">
            <ServiceDetails
              selectedCategory={selectedCategory}
              selectedService={selectedService}
              onServiceUpdate={handleServiceUpdate}
            />
          </div>
        </div>
      </div>

      {/* Floating Sidebar Toggle - Only shown when collapsed */}
      {sidebarCollapsed && (
        <div className="absolute left-0 top-1/2 transform -translate-y-1/2 z-10">
          <button
            onClick={toggleSidebar}
            className="bg-white border border-gray-300 rounded-r-lg shadow-lg hover:shadow-xl hover:bg-gray-50 transition-all duration-200 group px-1.5 py-4"
            title="Expand sidebar"
          >
            <ChevronDoubleRightIcon className="w-2 h-5 text-gray-600 group-hover:text-gray-800" />
          </button>
        </div>
      )}

      {/* Modals */}
      {showCategoryModal && (
        <CategoryModal
          category={editingItem}
          onClose={handleModalClose}
          onSave={fetchData}
        />
      )}
      
      {showServiceModal && (
        <ServiceModal
          service={editingItem}
          categories={categories}
          preselectedCategory={editingItem?.category}
          onClose={handleModalClose}
          onSave={fetchData}
        />
      )}
    </div>
  )
}

export default ServiceMenu 