// pdfUploadApi.js - API service for PDF upload functionality

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';

class PdfUploadApiService {
  constructor() {
    this.baseURL = API_BASE_URL;
  }

  // Helper method to get auth token
  getAuthToken() {
    const token = localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token');
    console.log(`Auth token available: ${Boolean(token)}, length: ${token?.length || 0}`);
    return token;
  }

  // Helper method to make file upload requests
  async makeFileRequest(endpoint, formData, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    
    const authToken = this.getAuthToken();
    const headers = {};
    // Add bearer token only if we actually have one
    if (authToken) {
      headers.Authorization = `Bearer ${authToken}`;
      console.log('Using Authorization header with Bearer token');
    } else {
      console.warn('No auth token available for request');
    }
    
    const defaultOptions = {
      method: 'POST',
      headers,
      body: formData,
      mode: 'cors',
      credentials: 'include',
      ...options,
    };

    console.log(`Making request to ${url} with headers:`, headers);

    const executeRequest = async () => {
      console.log('Executing fetch request...');
      const response = await fetch(url, defaultOptions);
      console.log(`Response status: ${response.status}, ok: ${response.ok}`);
      
      // Log response headers for debugging
      console.log('Response headers:');
      response.headers.forEach((value, key) => {
        console.log(`${key}: ${value}`);
      });
      
      // If success – parse JSON and return
      if (response.ok) {
        return response.json();
      }
      
      // If 401 and we still have a refresh token, attempt a silent refresh once
      if (response.status === 401) {
        console.log('Received 401 Unauthorized, attempting token refresh...');
        const refreshed = await this.refreshAccessToken();
        if (refreshed) {
          console.log('Token refreshed successfully, retrying request');
          // Update header with new token and retry once
          defaultOptions.headers.Authorization = `Bearer ${refreshed}`;
          const retry = await fetch(url, defaultOptions);
          console.log(`Retry response status: ${retry.status}, ok: ${retry.ok}`);
          if (retry.ok) return retry.json();
        } else {
          console.error('Token refresh failed');
        }
      }
      
      // For 404 errors, try alternative endpoint without /v1/
      if (response.status === 404 && endpoint.startsWith('/v1/')) {
        console.log('Endpoint not found, trying alternative path without /v1/');
        const altEndpoint = endpoint.replace('/v1/', '/');
        const altUrl = `${this.baseURL}${altEndpoint}`;
        console.log(`Trying alternative URL: ${altUrl}`);
        const altResponse = await fetch(altUrl, defaultOptions);
        console.log(`Alternative response status: ${altResponse.status}, ok: ${altResponse.ok}`);
        if (altResponse.ok) return altResponse.json();
      }
      
      // Otherwise build a helpful error
      let errorMsg = `HTTP error! status: ${response.status}`;
      try {
        const errorData = await response.json();
        console.error('Error response data:', errorData);
        errorMsg = errorData.message || errorData.error || errorMsg;
      } catch (_) {
        console.error('Could not parse error response as JSON');
        // Try to get text content as fallback
        try {
          const textContent = await response.text();
          console.log('Response text content:', textContent);
        } catch (err) {
          console.error('Could not get response text:', err);
        }
      }
      throw new Error(errorMsg);
    };

    try {
      console.log(`Making request to ${url}`);
      return await executeRequest();
    } catch (error) {
      console.error(`PDF upload failed for ${endpoint}:`, error);
      throw error;
    }
  }

  /**
   * Attempt to refresh the access token using the stored refresh token.
   * Returns the new access token string on success, or null on failure.
   */
  async refreshAccessToken() {
    const refreshToken = localStorage.getItem('refresh_token') || sessionStorage.getItem('refresh_token');
    console.log(`Refresh token available: ${Boolean(refreshToken)}, length: ${refreshToken?.length || 0}`);
    
    if (!refreshToken) {
      console.warn('No refresh token available');
      return null;
    }

    // Build URL to /auth/refresh/ **without** any version suffix
    let refreshUrl;
    try {
      const urlObj = new URL(this.baseURL);
      // Drop trailing / if present
      let path = urlObj.pathname.replace(/\/$/, '');
      // Remove /v1 if it is the final segment
      if (path.endsWith('/v1')) path = path.slice(0, -3);
      // Ensure trailing slash before auth segment
      refreshUrl = `${urlObj.origin}${path}/auth/refresh/`;
      console.log(`Constructed refresh URL: ${refreshUrl}`);
    } catch (_) {
      // Fallback – assume /api is root and prepend origin manually
      refreshUrl = '/api/auth/refresh/';
      console.log(`Using fallback refresh URL: ${refreshUrl}`);
    }

    try {
      console.log(`Sending refresh token request to ${refreshUrl}`);
      const response = await fetch(refreshUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refresh: refreshToken }),
      });

      console.log(`Refresh response status: ${response.status}, ok: ${response.ok}`);
      
      if (!response.ok) {
        console.warn('Token refresh failed');
        return null;
      }

      const data = await response.json();
      console.log('Refresh response data:', data);
      
      if (data.access) {
        localStorage.setItem('auth_token', data.access);
        console.log('New access token saved to localStorage');
        return data.access;
      }
      return null;
    } catch (err) {
      console.error('Token refresh error:', err);
      return null;
    }
  }

  // Upload PDF to backend for storage in S3
  async uploadPdf(pdfBlob, filename, options = {}) {
    console.log(`Preparing to upload PDF: ${filename}`);
    
    // First check if the API is accessible
    try {
      await this.checkApiConnection();
    } catch (error) {
      console.error('API connection check failed:', error);
      // Continue anyway, but log the error
    }
    
    // Ensure the blob is properly formatted with a filename
    const file = new File([pdfBlob], filename, { type: 'application/pdf' });
    console.log('Created File object:', file.name, file.size, file.type);
    
    const formData = new FormData();
    formData.append('file', file);
    
    // Use 'other' as file_type which is guaranteed to be valid
    formData.append('file_type', 'other');
    
    // Add optional metadata
    if (options.customerName) {
      formData.append('description', `Customer signature for ${options.customerName}`);
    }
    
    // Log formData contents for debugging
    console.log('FormData prepared with fields:');
    for (const pair of formData.entries()) {
      console.log(`- ${pair[0]}: ${pair[1] instanceof File ? `File: ${pair[1].name}, ${pair[1].size} bytes` : pair[1]}`);
    }
    
    console.log('Sending to backend...');
    
    // Try the direct upload method first for better error handling
    try {
      const result = await this.uploadWithXhr('/files/upload/', formData);
      console.log('Upload successful with XHR:', result);
      return result;
    } catch (error) {
      console.error('XHR upload failed, falling back to fetch:', error);
      // Fall back to the regular fetch method
      const result = await this.makeFileRequest('/files/upload/', formData);
      console.log('Upload result with fetch:', result);
      return result;
    }
  }
  
  /**
   * Check if the API is accessible
   */
  async checkApiConnection() {
    try {
      console.log('Checking API connection...');
      const url = `${this.baseURL}/health/`;
      const response = await fetch(url, { 
        method: 'GET',
        headers: {
          'Accept': 'application/json'
        }
      });
      
      console.log(`API health check status: ${response.status}`);
      
      if (response.ok) {
        const data = await response.json();
        console.log('API health check response:', data);
        return true;
      } else {
        console.error('API health check failed:', response.status);
        return false;
      }
    } catch (error) {
      console.error('API connection check error:', error);
      throw error;
    }
  }
  
  /**
   * Upload file using XMLHttpRequest for better error handling and progress tracking
   */
  uploadWithXhr(endpoint, formData) {
    return new Promise((resolve, reject) => {
      const url = `${this.baseURL}${endpoint}`;
      const xhr = new XMLHttpRequest();
      
      // Setup request
      xhr.open('POST', url, true);
      xhr.withCredentials = true; // Include cookies for CSRF
      
      // Add auth header if available
      const authToken = this.getAuthToken();
      if (authToken) {
        xhr.setRequestHeader('Authorization', `Bearer ${authToken}`);
      }
      
      // Add CSRF token from cookies if available
      const getCsrfToken = () => {
        const name = 'csrftoken=';
        const decodedCookie = decodeURIComponent(document.cookie);
        const cookieArray = decodedCookie.split(';');
        
        for (let i = 0; i < cookieArray.length; i++) {
          let cookie = cookieArray[i].trim();
          if (cookie.indexOf(name) === 0) {
            return cookie.substring(name.length, cookie.length);
          }
        }
        return null;
      };
      
      const csrfToken = getCsrfToken();
      if (csrfToken) {
        xhr.setRequestHeader('X-CSRFToken', csrfToken);
        console.log('Added CSRF token to request');
      } else {
        console.warn('No CSRF token found in cookies');
      }
      
      // Log all request headers for debugging
      console.log('Request headers:');
      if (authToken) console.log('- Authorization: Bearer [token]');
      if (csrfToken) console.log('- X-CSRFToken:', csrfToken);
      
      // Setup event handlers
      xhr.onload = function() {
        console.log(`XHR complete - status: ${xhr.status}`);
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const response = JSON.parse(xhr.responseText);
            resolve(response);
          } catch (e) {
            console.error('Error parsing response:', e);
            resolve({ success: true, message: 'Upload successful but could not parse response' });
          }
        } else {
          console.error('XHR error response:', xhr.responseText);
          
          // Try to parse error response
          try {
            const errorData = JSON.parse(xhr.responseText);
            console.log('Parsed error data:', errorData);
            
            // Check for validation errors
            if (errorData.details && typeof errorData.details === 'object') {
              console.log('Validation errors:', errorData.details);
              // Format validation errors for display
              const errorMessages = [];
              Object.keys(errorData.details).forEach(field => {
                const errors = errorData.details[field];
                if (Array.isArray(errors)) {
                  errors.forEach(error => errorMessages.push(`${field}: ${error}`));
                } else {
                  errorMessages.push(`${field}: ${errors}`);
                }
              });
              reject(new Error(`Validation failed: ${errorMessages.join(', ')}`));
            } else {
              reject(new Error(errorData.message || errorData.error || `HTTP error! status: ${xhr.status}`));
            }
          } catch (e) {
            reject(new Error(`HTTP error! status: ${xhr.status}, response: ${xhr.responseText}`));
          }
        }
      };
      
      xhr.onerror = function() {
        console.error('XHR network error');
        reject(new Error('Network error occurred during upload'));
      };
      
      xhr.upload.onprogress = function(e) {
        if (e.lengthComputable) {
          const percentComplete = (e.loaded / e.total) * 100;
          console.log(`Upload progress: ${percentComplete.toFixed(2)}%`);
        }
      };
      
      // Send the form data
      console.log(`Sending XHR request to ${url}`);
      xhr.send(formData);
    });
  }

  // Get PDF status
  async getPdfStatus(fileId) {
    const url = `${this.baseURL}/files/${fileId}/status/`;
    const authToken = this.getAuthToken();
    
    const response = await fetch(url, {
      headers: {
        Authorization: `Bearer ${authToken}`
      }
    });
    
    if (!response.ok) {
      throw new Error(`Failed to get PDF status: ${response.status}`);
    }
    
    return await response.json();
  }

  // Get PDF download URL
  async getPdfDownloadUrl(fileId) {
    const url = `${this.baseURL}/files/${fileId}/download/`;
    const authToken = this.getAuthToken();
    
    const response = await fetch(url, {
      headers: {
        Authorization: `Bearer ${authToken}`
      }
    });
    
    if (!response.ok) {
      throw new Error(`Failed to get PDF download URL: ${response.status}`);
    }
    
    return await response.json();
  }
}

// Export singleton instance
export const pdfUploadApi = new PdfUploadApiService();

// Convenience wrappers that preserve `this` context
export const uploadPdf = (...args) => pdfUploadApi.uploadPdf(...args);
export const getPdfStatus = (...args) => pdfUploadApi.getPdfStatus(...args);
export const getPdfDownloadUrl = (...args) => pdfUploadApi.getPdfDownloadUrl(...args); 