import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useForms } from '../FormsContext';
import { format } from 'date-fns';

const TemplateDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { fetchTemplateDetails, templateDetails, detailsLoading, detailsError } = useForms();
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    // Only fetch if not already loaded or if ID changed
    if (id && (!isLoaded || (templateDetails && templateDetails.id !== parseInt(id, 10)))) {
      const loadDetails = async () => {
        try {
          await fetchTemplateDetails(parseInt(id, 10));
          setIsLoaded(true);
        } catch (error) {
          console.error("Error loading template details:", error);
        }
      };
      
      loadDetails();
    }
  }, [id, fetchTemplateDetails, isLoaded, templateDetails]);

  if (detailsLoading && !isLoaded) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (detailsError) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
        <p className="font-medium">Error loading template details</p>
        <p className="text-sm">{detailsError}</p>
        <button 
          onClick={() => { setIsLoaded(false); }} 
          className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
        >
          Try Again
        </button>
      </div>
    );
  }

  if (!templateDetails) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded">
        <p>No template details found.</p>
        <button 
          onClick={() => navigate('/forms')} 
          className="mt-2 px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700"
        >
          Back to Forms
        </button>
      </div>
    );
  }

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    try {
      return format(new Date(dateString), 'MMM dd, yyyy HH:mm');
    } catch (e) {
      return dateString;
    }
  };

  return (
    <div className="bg-white shadow rounded-lg overflow-hidden">
      <div className="px-6 py-4 border-b">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold text-gray-800">{templateDetails.name}</h2>
          <div className="flex space-x-2">
            <button
              onClick={() => navigate(`/forms/edit/${id}`)}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Edit Template
            </button>
            <button
              onClick={() => navigate('/forms')}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded hover:bg-gray-50"
            >
              Back to List
            </button>
          </div>
        </div>
      </div>

      <div className="p-6">
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Template Information</h3>
              <div className="bg-gray-50 p-4 rounded border">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-sm font-medium text-gray-500">Name</div>
                  <div className="text-sm text-gray-900">{templateDetails.name}</div>
                  
                  <div className="text-sm font-medium text-gray-500">Document Type</div>
                  <div className="text-sm text-gray-900">{templateDetails.documentType}</div>
                  
                  <div className="text-sm font-medium text-gray-500">Status</div>
                  <div className="text-sm">
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      templateDetails.status === 'Published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {templateDetails.status}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Settings</h3>
              <div className="bg-gray-50 p-4 rounded border">
                <div className="space-y-2">
                  <div className="flex items-center">
                    <div className={`w-4 h-4 rounded ${templateDetails.settings?.mandatory ? 'bg-blue-500' : 'bg-gray-300'}`}></div>
                    <span className="ml-2 text-sm text-gray-700">Mandatory for all clients</span>
                  </div>
                  
                  <div className="flex items-center">
                    <div className={`w-4 h-4 rounded ${templateDetails.settings?.notify ? 'bg-blue-500' : 'bg-gray-300'}`}></div>
                    <span className="ml-2 text-sm text-gray-700">Notify on submit</span>
                  </div>
                  
                  <div className="flex items-center">
                    <span className="text-sm font-medium text-gray-500 mr-2">Expiration period:</span>
                    <span className="text-sm text-gray-900">
                      {templateDetails.settings?.expiration ? `${templateDetails.settings.expiration} months` : 'No expiration'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Creation & Modification</h3>
            <div className="bg-gray-50 p-4 rounded border">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <div className="text-sm font-medium text-gray-500">Created By</div>
                  <div className="text-sm text-gray-900">{templateDetails.createdBy || 'N/A'}</div>
                  
                  <div className="text-sm font-medium text-gray-500 mt-2">Created On</div>
                  <div className="text-sm text-gray-900">{formatDate(templateDetails.createdDate)}</div>
                </div>
                
                <div>
                  <div className="text-sm font-medium text-gray-500">Last Updated By</div>
                  <div className="text-sm text-gray-900">{templateDetails.updatedBy || 'N/A'}</div>
                  
                  <div className="text-sm font-medium text-gray-500 mt-2">Last Updated On</div>
                  <div className="text-sm text-gray-900">{formatDate(templateDetails.updatedDate)}</div>
                </div>
              </div>
            </div>
          </div>
          
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Questions</h3>
            <div className="bg-gray-50 p-4 rounded border">
              <div className="text-sm text-gray-700">
                This template contains {templateDetails.questions?.length || 0} questions.
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TemplateDetails; 