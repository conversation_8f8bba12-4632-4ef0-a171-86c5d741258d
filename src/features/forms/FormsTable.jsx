import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useForms } from './FormsContext'
import axios from 'axios';
import { api } from '../../hooks/useApi';
import SaveSuccessModal from '../../components/SaveSuccessModal'

function getStatusBadge(status) {
  const styles = {
    Published: 'bg-blue-100 text-blue-800',
    Draft: 'bg-gray-100 text-gray-800',
  }
  return (
    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${styles[status] || styles.Draft}`}>
      {status}
    </span>
  )
}

function FormsTable() {
  const { forms, loading, error, fetchTemplates, addForm, updateForm, deleteForm } = useForms()
  const [moreMenuOpen, setMoreMenuOpen] = useState(null)
  const [showAddModal, setShowAddModal] = useState(false)
  const [statusFilter, setStatusFilter] = useState('All')
  const [debugInfo, setDebugInfo] = useState('')
  const [deleteSuccess, setDeleteSuccess] = useState(false)
  const [deleteConfirm, setDeleteConfirm] = useState({ show: false, id: null })
  const navigate = useNavigate()

  const handleMoreClick = (id) => {
    setMoreMenuOpen(moreMenuOpen === id ? null : id)
  }

  const handleDelete = async (id) => {
    setDeleteConfirm({ show: true, id })
  }

  const confirmDelete = async () => {
    try {
      await deleteForm(deleteConfirm.id)
      setDeleteConfirm({ show: false, id: null })
      setDeleteSuccess(true)
    } catch (err) {
      alert('Failed to delete form: ' + err.message)
      setDeleteConfirm({ show: false, id: null })
    }
  }

  const handleEdit = (id) => {
    navigate(`/forms/edit/${id}`)
  }

  const handleDuplicate = (form) => {
    const newForm = {
      ...form,
      name: form.name + ' (Copy)',
      status: 'Draft',
    }
    addForm(newForm)
  }

  const handleRefresh = () => {
    fetchTemplates()
  }

  const handleDebug = async () => {
    try {
      setDebugInfo('Testing connection...')
      
      // Test direct connection to Django
      try {
        const djangoRes = await axios.get('http://localhost:8000/api/v1/forms/templates/', { 
          timeout: 5000,
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          }
        })
        setDebugInfo(prev => prev + '\nDjango API (8000): ' + JSON.stringify(djangoRes.data).substring(0, 100) + '...')
      } catch (err) {
        setDebugInfo(prev => prev + '\nDjango API (8000) error: ' + err.message + 
          (err.response ? ` (Status: ${err.response.status})` : ''))
      }
      
      // Test connection to frontend proxy
      try {
        const proxyRes = await api.get('/forms/templates/', { 
          timeout: 5000,
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          }
        })
        setDebugInfo(prev => prev + '\nProxy API: ' + JSON.stringify(proxyRes.data).substring(0, 100) + '...')
      } catch (err) {
        setDebugInfo(prev => prev + '\nProxy API error: ' + err.message + 
          (err.response ? ` (Status: ${err.response.status})` : ''))
      }
      
      // Check if Django server is running
      try {
        const adminRes = await axios.get('http://localhost:8000/admin/', { timeout: 5000 })
        setDebugInfo(prev => prev + '\nDjango Admin accessible: Yes')
      } catch (err) {
        setDebugInfo(prev => prev + '\nDjango Admin error: ' + err.message)
      }
      
    } catch (err) {
      setDebugInfo('Debug error: ' + err.message)
    }
  }

  const templates = [
    { id: 'blank', name: 'Blank Form', icon: (
      <svg className="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><rect x="5" y="4" width="14" height="16" rx="2" strokeWidth="2" /><path d="M9 2v4h6V2" strokeWidth="2" /></svg>
    ) },
    ...forms.map(f => ({ id: f.id, name: f.name, icon: (
      <svg className="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><rect x="5" y="4" width="14" height="16" rx="2" strokeWidth="2" /></svg>
    ) })),
  ]

  const handleTemplateSelect = (template) => {
    setShowAddModal(false)
    if (template.id === 'blank') {
      navigate('/forms/edit/new', { state: { template: 'blank' } })
    } else {
      navigate('/forms/edit/new', { state: { templateId: template.id } })
    }
  }

  const handleTemplateDetails = (form) => {
    navigate(`/forms/details/${form.id}`)
  }

  const filteredForms = statusFilter === 'All' ? forms : forms.filter(f => f.status === statusFilter)

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6 flex justify-center items-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading forms...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="text-center text-red-600 p-4">
          <p>{error}</p>
          <div className="flex justify-center gap-4 mt-4">
            <button 
              onClick={handleRefresh} 
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Try Again
            </button>
            <button 
              onClick={handleDebug} 
              className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
            >
              Debug Connection
            </button>
          </div>
          {debugInfo && (
            <div className="mt-4 p-4 bg-gray-100 text-left text-xs font-mono whitespace-pre-wrap rounded">
              {debugInfo}
            </div>
          )}
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold text-gray-900">Forms</h2>
        <div className="flex items-center gap-4">
          <select
            className="border border-gray-300 rounded px-3 py-2 text-sm"
            value={statusFilter}
            onChange={e => setStatusFilter(e.target.value)}
          >
            <option value="All">All</option>
            <option value="Draft">Draft</option>
            <option value="Published">Published</option>
          </select>
          <button className="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors shadow-sm" type="button" onClick={() => setShowAddModal(true)}>
            + Add New Form
          </button>
        </div>
      </div>
      {debugInfo && (
        <div className="mb-4 p-4 bg-gray-100 text-left text-xs font-mono whitespace-pre-wrap rounded">
          {debugInfo}
        </div>
      )}
      {forms.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <p>No forms found. Click "Add New Form" to create one.</p>
        </div>
      ) : (
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Form Name</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Document Type</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created Date</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredForms.map((form) => (
              <tr key={form.id} className="align-middle hover:bg-gray-50 transition-colors">
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-left align-middle">
                  <a 
                    href="#" 
                    className="text-blue-600 hover:underline" 
                    onClick={(e) => { e.preventDefault(); handleTemplateDetails(form); }}
                  >
                    {form.name}
                  </a>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-left align-middle">{form.documentType}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-left align-middle">
                  {form.createdDate ? new Date(form.createdDate).toLocaleDateString() : ''}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-left align-middle">{getStatusBadge(form.status)}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm align-middle">
                  <div className="flex items-center space-x-2 relative">
                    <button className="text-blue-600 hover:underline font-medium px-2 py-1 rounded" title="Edit" onClick={() => handleEdit(form.id)}>Edit</button>
                    <button className="text-red-600 hover:underline font-medium px-2 py-1 rounded" title="Delete" onClick={() => handleDelete(form.id)}>Delete</button>
                    <button className="text-gray-700 hover:underline font-medium px-2 py-1 rounded" title="Duplicate" onClick={() => handleDuplicate(form)}>Duplicate</button>
                    <div className="relative">
                      <button onClick={() => handleMoreClick(form.id)} className="p-1 text-gray-400 hover:text-gray-700 hover:bg-gray-100 rounded" title="More" type="button">
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <circle cx="12" cy="12" r="1.5" />
                          <circle cx="19" cy="12" r="1.5" />
                          <circle cx="5" cy="12" r="1.5" />
                        </svg>
                      </button>
                      {moreMenuOpen === form.id && (
                        <div className="absolute right-0 mt-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-20">
                          <button className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" type="button" onClick={() => handleTemplateDetails(form)}>Template Details</button>
                        </div>
                      )}
                    </div>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      )}
      {/* Add New Form Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-2xl">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium">Create New Form</h3>
              <button onClick={() => setShowAddModal(false)} className="text-gray-400 hover:text-gray-600">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="mb-4">
              <p className="text-gray-600 mb-4">Choose a template to get started or create a blank form.</p>
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                {templates.map((template) => (
                  <button
                    key={template.id}
                    className="bg-white border border-gray-200 rounded-lg p-4 hover:border-blue-500 hover:shadow-md transition-all text-center"
                    onClick={() => handleTemplateSelect(template)}
                  >
                    {template.icon}
                    <div className="text-sm font-medium">{template.name}</div>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
      {/* Delete success modal */}
      {deleteSuccess && (
        <SaveSuccessModal open={deleteSuccess} status="Deleted" onClose={() => setDeleteSuccess(false)} />
      )}
      {/* Delete confirmation modal */}
      {deleteConfirm.show && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40 backdrop-blur-sm">
          <div className="bg-white rounded-xl shadow-2xl w-11/12 max-w-md p-8 transform transition-all duration-300">
            <div className="flex items-center justify-center w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
            </div>
            <h3 className="text-2xl font-semibold text-center text-red-700 mb-2">Delete Form</h3>
            <p className="text-gray-600 text-center mb-6">Are you sure you want to delete this form?</p>
            <div className="flex justify-center gap-3">
              <button
                onClick={() => setDeleteConfirm({ show: false, id: null })}
                className="px-6 py-2 bg-gray-300 text-gray-700 rounded shadow hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-400"
              >
                Cancel
              </button>
              <button
                onClick={confirmDelete}
                className="px-6 py-2 bg-red-600 text-white rounded shadow hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-400"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default FormsTable