import { createContext, useContext, useState, useEffect, useCallback } from 'react'
import axios from 'axios'
import { api } from '../../hooks/useApi'

const FormsContext = createContext()

export function FormsProvider({ children }) {
  const [forms, setForms] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [templateDetails, setTemplateDetails] = useState(null)
  const [detailsLoading, setDetailsLoading] = useState(false)
  const [detailsError, setDetailsError] = useState(null)

  const fetchTemplates = async () => {
    try {
      setLoading(true)
      console.log('Fetching templates from API...');
      const res = await api.get('/forms/templates/');
      console.log('API response:', res.data);
      
      // Transform the data to match our frontend format
      const transformedForms = res.data.results ? res.data.results.map(template => ({
        id: template.id,
        name: template.name,
        documentType: template.document_type,
        createdDate: template.created_at,
        status: template.status.charAt(0).toUpperCase() + template.status.slice(1), // Capitalize status
        createdBy: template.created_by?.name || 'Admin', // Use created_by if available
        updatedDate: template.updated_at,
        updatedBy: template.updated_by?.name,
        questions: template.content && template.content.questions ? template.content.questions : [],
        mandatory: template.content?.settings?.mandatory || false,
        notify: template.content?.settings?.notify || false,
        expiration: template.content?.settings?.expiration || null
      })) : [];
      
      setForms(transformedForms);
      setLoading(false)
    } catch (err) {
      console.error("Error fetching templates:", err);
      console.error("Error details:", err.response ? err.response.data : 'No response data');
      console.error("Error status:", err.response ? err.response.status : 'No status code');
      setError(`Failed to load templates: ${err.message}`);
      setLoading(false)
    }
  };

  const fetchTemplateDetails = useCallback(async (id) => {
    try {
      setDetailsLoading(true);
      setDetailsError(null);
      
      console.log(`Fetching template details for ID: ${id}`);
      const res = await api.get(`/forms/templates/${id}/`);
      console.log('Template details response:', res.data);
      
      // Transform the data to include history information
      const details = {
        id: res.data.id,
        name: res.data.name,
        documentType: res.data.document_type,
        status: res.data.status.charAt(0).toUpperCase() + res.data.status.slice(1),
        createdDate: res.data.created_at,
        createdBy: res.data.created_by?.name || 'Admin',
        updatedDate: res.data.updated_at,
        updatedBy: res.data.updated_by?.name,
        questions: res.data.content?.questions || [],
        history: res.data.history || [],
        settings: {
          mandatory: res.data.content?.settings?.mandatory || false,
          notify: res.data.content?.settings?.notify || false,
          expiration: res.data.content?.settings?.expiration || null
        }
      };
      
      setTemplateDetails(details);
      setDetailsLoading(false);
      return details;
    } catch (err) {
      console.error(`Error fetching template details for ID ${id}:`, err);
      console.error("Error details:", err.response ? err.response.data : 'No response data');
      setDetailsError(`Failed to load template details: ${err.message}`);
      setDetailsLoading(false);
      throw err;
    }
  }, []);

  const createTemplate = async (data) => {
    try {
      console.log('Sending data to API:', JSON.stringify(data, null, 2));
      const response = await api.post('/forms/templates/', data);
      console.log('Template created successfully:', response.data);
      await fetchTemplates(); // Refresh the list and wait for it to complete
      return response.data;
    } catch (err) {
      console.error("Error creating template:", err);
      
      // Enhanced error logging for 400 errors
      if (err.response && err.response.status === 400) {
        console.error("Validation errors:", err.response.data);
        
        // Format validation errors for better debugging
        const errorDetails = Object.entries(err.response.data)
          .map(([field, errors]) => `${field}: ${Array.isArray(errors) ? errors.join(', ') : errors}`)
          .join('\n');
          
        throw new Error(`Validation failed: ${errorDetails}`);
      }
      
      console.error("Error details:", err.response ? err.response.data : 'No response data');
      console.error("Error status:", err.response ? err.response.status : 'No status code');
      throw err;
    }
  };

  const updateTemplate = async (id, data) => {
    try {
      console.log('Updating template with data:', JSON.stringify(data, null, 2));
      await api.put(`/forms/templates/${id}/`, data);
      console.log('Template updated successfully');
      await fetchTemplates(); // Refresh the list and wait for it to complete
    } catch (err) {
      console.error("Error updating template:", err);
      console.error("Error details:", err.response ? err.response.data : 'No response data');
      throw err;
    }
  };

  const deleteTemplate = async (id) => {
    try {
      console.log(`Deleting template with ID: ${id}`);
      await api.delete(`/forms/templates/${id}/`);
      console.log('Template deleted successfully');
      // Update the local state immediately to reflect the deletion
      setForms(forms.filter(form => form.id !== id));
      return true;
    } catch (err) {
      console.error("Error deleting template:", err);
      console.error("Error details:", err.response ? err.response.data : 'No response data');
      throw err;
    }
  };

  // Load templates when component mounts - only if user is authenticated
  useEffect(() => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      fetchTemplates();
    }
  }, []);

  const addForm = async (form) => {
    try {
      // Format data according to API requirements
      const data = {
        name: form.name,
        document_type: form.documentType,
        status: form.status, // API expects "Published" or "Draft", not lowercase
        business: 1, // Assuming business ID 1 for now
        content: {
          questions: form.questions || [],
          settings: {
            mandatory: form.mandatory || false,
            notify: form.notify || false,
            expiration: form.expiration || null
          }
        }
      };
      
      console.log('Creating new form with data:', data);
      const result = await createTemplate(data);
      console.log('Form added successfully:', result);
      return result;
    } catch (err) {
      console.error('Failed to add form:', err);
      throw err;
    }
  }

  const updateForm = (id, updated) => {
    updateTemplate(id, {
      name: updated.name,
      document_type: updated.documentType,
      status: updated.status || 'published', // API expects "published" or "draft"
      business: 1, // Assuming business ID 1 for now
      content: {
        questions: updated.questions || [],
        settings: {
          mandatory: updated.mandatory || false,
          notify: updated.notify || false,
          expiration: updated.expiration || null
        }
      }
    });
  }

  const deleteForm = async (id) => {
    try {
      const result = await deleteTemplate(id);
      return result;
    } catch (err) {
      console.error('Failed to delete form:', err);
      throw err;
    }
  }

  return (
    <FormsContext.Provider value={{ 
      forms, 
      loading, 
      error, 
      setForms, 
      addForm, 
      updateForm, 
      deleteForm,
      fetchTemplates,
      templateDetails,
      detailsLoading,
      detailsError,
      fetchTemplateDetails
    }}>
      {children}
    </FormsContext.Provider>
  )
}

export function useForms() {
  return useContext(FormsContext)
} 