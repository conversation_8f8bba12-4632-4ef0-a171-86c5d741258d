// Mock campaign data for marketing features
export const mockCampaigns = [
  {
    id: 1,
    date: 'Apr 23, 2023 - 8:40 PM',
    name: 'Clément Lash Parking Instruction',
    type: 'Email',
    status: 'Announced (Recurring Before Visit)',
    createdBy: '(Master <PERSON>ylist) <PERSON>',
    appointmentsBooked: '--',
    revenueGenerated: '$0',
    active: true,
    automated: true,
    category: 'automated'
  },
  {
    id: 2,
    date: 'Jul 25, 2023 - 9:38 PM',
    name: 'Clément Lash: Home Care and Refill Suggestions',
    type: 'Email',
    status: 'Announced (Recurring After Visit)',
    createdBy: '(Master Stylist) <PERSON>',
    appointmentsBooked: '--',
    revenueGenerated: '$0',
    active: true,
    automated: true,
    category: 'automated'
  },
  {
    id: 3,
    date: 'Feb 25, 2025 - 10:51 PM',
    name: 'Clément Lash New Stylist Discount',
    type: 'Email',
    status: 'Announced (on Feb 25, 2025)',
    createdBy: '(Master <PERSON>ylist) <PERSON>',
    appointmentsBooked: '--',
    revenueGenerated: '$0',
    active: true,
    automated: false,
    category: 'queue'
  },
  {
    id: 4,
    date: 'Jun 14, 2024 - 10:27 PM',
    name: 'Clément Lash Soft Opening Discount',
    type: 'Text',
    status: 'Announced (on Jun 14, 2024)',
    createdBy: '(Master Stylist) Serena Zhou',
    appointmentsBooked: '--',
    revenueGenerated: '$0',
    active: true,
    automated: false,
    category: 'draft'
  },
  {
    id: 5,
    date: 'Jun 9, 2024 - 9:28 PM',
    name: 'Clément Lash Promotion',
    type: 'Email',
    status: 'Announced (on Jun 09, 2024)',
    createdBy: '(Master Stylist) Serena Zhou',
    appointmentsBooked: '9',
    revenueGenerated: '$0',
    active: true,
    automated: false,
    category: 'sent'
  },
  {
    id: 6,
    date: 'Jul 21, 2023 - 8:12 PM',
    name: 'Promotion Ending',
    type: 'Email',
    status: 'Announced (on Jul 21, 2023)',
    createdBy: '(Master Stylist) Serena Zhou',
    appointmentsBooked: '--',
    revenueGenerated: '$0',
    active: false,
    automated: false,
    category: 'archive'
  }
];

// Marketing credits information
export const marketingCredits = {
  email: {
    used: 30,
    total: 1000
  },
  text: {
    used: 0,
    total: 0
  }
};

// Helper functions for campaign data
export const getCampaignsByCategory = (category) => {
  if (category === 'all') {
    return mockCampaigns;
  }
  return mockCampaigns.filter(campaign => campaign.category === category);
};

export const getCategoryCounts = () => {
  return {
    all: mockCampaigns.length,
    sent: mockCampaigns.filter(c => c.category === 'sent').length,
    draft: mockCampaigns.filter(c => c.category === 'draft').length,
    queue: mockCampaigns.filter(c => c.category === 'queue').length,
    automated: mockCampaigns.filter(c => c.category === 'automated').length,
    archive: mockCampaigns.filter(c => c.category === 'archive').length
  };
}; 