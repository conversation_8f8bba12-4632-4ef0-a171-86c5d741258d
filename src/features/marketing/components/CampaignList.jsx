import { useState, useEffect, useCallback } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { useApi } from '../../../hooks/useApi'

function CampaignList() {
  const location = useLocation()
  const [activeTab, setActiveTab] = useState('all')
  const [campaigns, setCampaigns] = useState([])
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedFilter, setSelectedFilter] = useState('All Campaigns')
  const [openMenuId, setOpenMenuId] = useState(null)
  const [showArchiveConfirm, setShowArchiveConfirm] = useState(false)
  const [campaignToArchive, setCampaignToArchive] = useState(null)
  const [isArchiving, setIsArchiving] = useState(false)

  // use useApi hook connect API
  const { data, loading, error, fetchData, updateData } = useApi('marketing/campaigns')

  const fetchCampaigns = useCallback(async () => {
    const params = {}
    if (activeTab !== 'all') {
      const statusMap = {
        'draft': 'draft',
        'queue': 'scheduled',
        'sent': 'completed',
        'automated': 'in_progress',
        'archive': 'cancelled'
      }
      if (statusMap[activeTab]) {
        params.status = statusMap[activeTab]
      }
    }

    try {
      await fetchData(params)
    } catch (err) {
      console.error('Error fetching campaigns:', err)
    }
  }, [activeTab, fetchData])

  useEffect(() => {
    fetchCampaigns()
  }, [activeTab, fetchCampaigns])

  useEffect(() => {
    if (data) {
      const formattedCampaigns = data.results ? data.results.map(campaign => ({
        id: campaign.id,
        date: new Date(campaign.created_at).toLocaleString(),
        name: campaign.name,
        type: campaign.channels && campaign.channels.length > 0 ? 
              campaign.channels[0].channel_type.charAt(0).toUpperCase() + campaign.channels[0].channel_type.slice(1) : 
              'Email',
        status: `${campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)} (${campaign.scheduled_time ? 
                new Date(campaign.scheduled_time).toLocaleDateString() : 'Not scheduled'})`,
        createdBy: campaign.created_by || 'System',
        appointmentsBooked: campaign.recipients_count || '--',
        revenueGenerated: '$0', 
        active: campaign.active,
        automated: campaign.campaign_type && campaign.campaign_type.supports_offset,
        category: mapStatusToCategory(campaign.status),
        // 保存原始数据，用于更新操作
        originalData: campaign
      })) : [];
      
      setCampaigns(formattedCampaigns)
    }
  }, [data])

  const mapStatusToCategory = (status) => {
    switch(status) {
      case 'draft': return 'draft';
      case 'scheduled': return 'queue';
      case 'in_progress': return 'automated';
      case 'completed': return 'sent';
      case 'cancelled': return 'archive';
      default: return 'all';
    }
  }

  // Filter options - simplified to only include All Campaigns, Email, and Text
  const filterOptions = ['All Campaigns', 'Email', 'Text']

  // Handle filtering
  const filteredCampaigns = campaigns.filter(campaign => {
    // Search filtering
    const matchesSearch = campaign.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         campaign.status.toLowerCase().includes(searchTerm.toLowerCase())
    
    // Type filtering from dropdown
    let matchesTypeFilter = true
    if (selectedFilter === 'Email') matchesTypeFilter = campaign.type === 'Email'
    else if (selectedFilter === 'Text') matchesTypeFilter = campaign.type === 'Text'
    
    return matchesSearch && matchesTypeFilter
  }).sort((a, b) => {
    // Sort by active status first (active campaigns first)
    if (a.active && !b.active) return -1;
    if (!a.active && b.active) return 1;
    
    // Then sort by date (newest first)
    return new Date(b.date) - new Date(a.date);
  });

  // Toggle dropdown menu
  const toggleMenu = (id) => {
    setOpenMenuId(openMenuId === id ? null : id)
  }

  // Handle archive button click
  const handleArchiveClick = (campaign) => {
    setCampaignToArchive(campaign)
    setShowArchiveConfirm(true)
    setOpenMenuId(null) // Close menu
  }

  // Confirm archive operation
  const confirmArchive = async () => {
    if (!campaignToArchive) return
    
    setIsArchiving(true)
    try {
      // Logical archive - change status to 'cancelled'
      const updatedData = {
        ...campaignToArchive.originalData,
        status: 'cancelled',
        active: false
      }
      
      await updateData(campaignToArchive.id, updatedData)
      
      // Update local state
      setCampaigns(prevCampaigns => 
        prevCampaigns.map(campaign => 
          campaign.id === campaignToArchive.id 
            ? {
                ...campaign,
                status: 'Cancelled (Not scheduled)',
                active: false,
                category: 'archive',
                originalData: {
                  ...campaign.originalData,
                  status: 'cancelled',
                  active: false
                }
              }
            : campaign
        ).sort((a, b) => {
          // Sort by active status first (active campaigns first)
          if (a.active && !b.active) return -1;
          if (!a.active && b.active) return 1;
          
          // Then sort by date (newest first)
          return new Date(b.date) - new Date(a.date);
        })
      )
      
      // Refresh data from server
      fetchCampaigns()
    } catch (err) {
      console.error('Error archiving campaign:', err)
    } finally {
      setIsArchiving(false)
      setShowArchiveConfirm(false)
      setCampaignToArchive(null)
    }
  }

  // Cancel archive operation
  const cancelArchive = () => {
    setShowArchiveConfirm(false)
    setCampaignToArchive(null)
  }

  // Update activeTab when path changes
  useEffect(() => {
    const path = location.pathname.split('/').pop()
    if (path && ['campaigns', 'sent', 'draft', 'queue', 'automated', 'archive'].includes(path)) {
      setActiveTab(path === 'campaigns' ? 'all' : path)
    } else {
      setActiveTab('all')
    }
  }, [location.pathname])

  // Get title based on active tab
  const getTabTitle = () => {
    switch(activeTab) {
      case 'all': return 'All Marketing Campaigns'
      case 'sent': return 'Sent Campaigns'
      case 'draft': return 'Draft Campaigns'
      case 'queue': return 'Sending Queue'
      case 'automated': return 'Automated Campaigns'
      case 'archive': return 'Archived Campaigns'
      default: return 'Marketing Campaigns'
    }
  }

  return (
    <div className="bg-white rounded-lg shadow h-full border border-gray-200 flex flex-col">
      <div className="p-4 border-b border-gray-200">
        <h2 className="text-xl font-semibold text-gray-900">{getTabTitle()}</h2>
      </div>
      
      <div className="p-4 border-b border-gray-200">
        <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
          <Link 
            to="/marketing/create-campaign"
            className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors whitespace-nowrap order-2 sm:order-1"
          >
            Create Campaign
          </Link>
          
          <div className="flex items-center gap-3 ml-auto order-1 sm:order-2">
            <div className="relative w-40 sm:w-48">
              <div className="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none">
                <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                type="text"
                className="block w-full pl-8 pr-2 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-xs"
                placeholder="Search"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            
            <div className="w-40">
              <select
                className="block w-full px-2 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-xs"
                value={selectedFilter}
                onChange={(e) => setSelectedFilter(e.target.value)}
              >
                {filterOptions.map(option => (
                  <option key={option} value={option}>{option}</option>
                ))}
              </select>
            </div>
          </div>
        </div>
      </div>
      
      {loading ? (
        <div className="p-8 text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-600"></div>
          <p className="mt-2 text-gray-500">Loading...</p>
        </div>
      ) : error ? (
        <div className="p-8 text-center text-red-500">
          <p>Load Fail: {error.message || 'Please try later'}</p>
          <button 
            onClick={fetchCampaigns} 
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Try again
          </button>
        </div>
      ) : (
        <div className="flex-1 overflow-hidden">
          {filteredCampaigns.length === 0 ? (
            <div className="p-8 text-center text-gray-500">
              <p>No Campaigns Now</p>
            </div>
          ) : (
            <div className="h-full overflow-y-auto overflow-x-auto">
              <table className="min-w-full text-xs">
                <thead className="bg-gray-50 border-b border-gray-200 sticky top-0 z-10">
                  <tr>
                    <th scope="col" className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th scope="col" className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Campaign Name
                    </th>
                    <th scope="col" className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Type
                    </th>
                    <th scope="col" className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th scope="col" className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Created By
                    </th>
                    <th scope="col" className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Recipients
                    </th>
                    <th scope="col" className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Revenue
                    </th>
                    <th scope="col" className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Active
                    </th>
                    <th scope="col" className="relative px-2 py-3">
                      <span className="sr-only">Actions</span>
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredCampaigns.map((campaign) => (
                    <tr key={campaign.id} className="hover:bg-gray-50">
                      <td className="px-2 py-4 text-xs text-gray-500">
                        {campaign.date}
                      </td>
                      <td className="px-2 py-4">
                        <div className="text-xs font-medium text-blue-600 hover:text-blue-800">
                          <Link to={`/marketing/create-campaign?id=${campaign.id}`}>
                            {campaign.name}
                          </Link>
                        </div>
                      </td>
                      <td className="px-2 py-4">
                        <div className="flex items-center">
                          {campaign.type === 'Email' ? (
                            <svg className="w-3 h-3 text-blue-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                          ) : (
                            <svg className="w-3 h-3 text-green-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                            </svg>
                          )}
                          <span className="text-xs text-gray-900">{campaign.type}</span>
                          {campaign.automated && (
                            <span className="ml-1 bg-purple-100 text-purple-800 text-xs px-1 py-0.5 rounded">Auto</span>
                          )}
                        </div>
                      </td>
                      <td className="px-2 py-4">
                        <div className="flex flex-col">
                          <div className="flex items-center">
                            <svg className="w-3 h-3 text-blue-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" />
                            </svg>
                            <span className="text-xs text-gray-900">{campaign.status}</span>
                          </div>
                        </div>
                      </td>
                      <td className="px-2 py-4 text-xs text-gray-500">
                        {campaign.createdBy}
                      </td>
                      <td className="px-2 py-4 text-xs text-gray-500">
                        {campaign.appointmentsBooked}
                      </td>
                      <td className="px-2 py-4 text-xs text-gray-500">
                        {campaign.revenueGenerated}
                      </td>
                      <td className="px-2 py-4">
                        <div className="relative">
                          <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${campaign.active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                            {campaign.active ? 'Active' : 'Inactive'}
                          </span>
                          
                          {openMenuId === campaign.id && (
                            <div className="origin-top-right absolute right-0 mt-1 w-40 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-10">
                              <div className="py-1" role="menu" aria-orientation="vertical" aria-labelledby="options-menu">
                                <Link to={`/marketing/create-campaign?id=${campaign.id}`} className="block px-3 py-1.5 text-xs text-gray-700 hover:bg-gray-100" role="menuitem">View</Link>
                                <button onClick={() => {/* Handle duplicate */}} className="block w-full text-left px-3 py-1.5 text-xs text-gray-700 hover:bg-gray-100" role="menuitem">Duplicate</button>
                                <button onClick={() => handleArchiveClick(campaign)} className="block w-full text-left px-3 py-1.5 text-xs text-gray-700 hover:bg-gray-100" role="menuitem">Archive</button>
                              </div>
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-2 py-4 text-left">
                        <div className="relative">
                          <button
                            onClick={() => toggleMenu(campaign.id)}
                            className="text-gray-400 hover:text-gray-500 focus:outline-none"
                          >
                            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                            </svg>
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      )}

      {/* Archive confirmation dialog */}
      {showArchiveConfirm && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md mx-auto">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Confirm Archive</h3>
            <p className="text-sm text-gray-500 mb-4">
              Are you sure you want to archive campaign "{campaignToArchive?.name}"? This action will move the campaign to the Archive section.
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={cancelArchive}
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
                disabled={isArchiving}
              >
                Cancel
              </button>
              <button
                onClick={confirmArchive}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center"
                disabled={isArchiving}
              >
                {isArchiving ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Processing...
                  </>
                ) : 'Archive'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default CampaignList 