import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import Button from '../../../components/Button'
import RecipientSelector from './RecipientSelector'

console.log('CreateCampaignForm module loaded');

function CreateCampaignForm() {
  console.log('CreateCampaignForm rendering');
  
  const navigate = useNavigate()
  const [campaignType, setCampaignType] = useState('email')
  const [currentStep, setCurrentStep] = useState(1)
  const [formData, setFormData] = useState({
    name: '',
    subject: '',
    message: '',
    recipients: 'all',
    schedule: 'now',
    scheduledDate: '',
    scheduledTime: '',
    selectedCustomers: [],
    selectedEmployees: []
  })
  const [errors, setErrors] = useState({})
  const [isValidating, setIsValidating] = useState(false)
  const [showRecipientSelector, setShowRecipientSelector] = useState(false)
  const [recipientSelectorType, setRecipientSelectorType] = useState('customers') // 'customers' or 'employees'
  
  // Debug mount
  useEffect(() => {
    console.log('CreateCampaignForm mounted');
    return () => {
      console.log('CreateCampaignForm unmounting');
    };
  }, []);
  
  // Debug recipient selector state
  useEffect(() => {
    console.log('Recipient selector state changed:', { 
      showRecipientSelector, 
      recipientSelectorType,
      selectedCustomers: formData.selectedCustomers.length,
      selectedEmployees: formData.selectedEmployees.length
    });
  }, [showRecipientSelector, recipientSelectorType, formData.selectedCustomers, formData.selectedEmployees]);
  
  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData({
      ...formData,
      [name]: value
    })
    
    // Clear errors
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: ''
      })
    }
    
    // If we're actively validating (user has clicked Next at least once),
    // validate the fields on each change
    if (isValidating) {
      validateStep1()
    }
  }
  
  const validateStep1 = () => {
    const newErrors = {}
    
    if (!formData.name.trim()) {
      newErrors.name = 'Please enter campaign name'
    }
    
    // Subject is required for all campaign types
    if (!formData.subject.trim()) {
      newErrors.subject = 'Please enter subject'
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }
  
  const validateStep2 = () => {
    const newErrors = {}
    
    if (!formData.message.trim()) {
      newErrors.message = 'Please enter message content'
    }
    
    if (formData.schedule === 'later') {
      if (!formData.scheduledDate) {
        newErrors.scheduledDate = 'Please select a date'
      }
      if (!formData.scheduledTime) {
        newErrors.scheduledTime = 'Please select a time'
      }
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }
  
  const handleNextStep = () => {
    // Set validating flag to true to enable real-time validation
    setIsValidating(true)
    
    // Validate form
    const isValid = validateStep1()
    
    // Only proceed if valid
    if (isValid) {
      setCurrentStep(2)
    } else {
      console.log('Form validation failed:', errors)
    }
  }
  
  const handlePrevStep = () => {
    setCurrentStep(1)
  }
  
  const handleSubmit = (e) => {
    e.preventDefault()
    
    if (validateStep2()) {
      // Mock submission
      console.log('Submitting campaign:', { ...formData, type: campaignType })
      
      // Navigate to campaigns list
      navigate('/marketing/campaigns')
    }
  }

  // handle edit recipients
  const handleEditRecipients = (type) => {
    console.log('handleEditRecipients called with type:', type);
    
    try {
      setRecipientSelectorType(type);
      console.log('Set recipientSelectorType to:', type);
      
      setShowRecipientSelector(true);
      console.log('Set showRecipientSelector to true');
      
      console.log('Current selected items:', type === 'customers' 
        ? formData.selectedCustomers 
        : formData.selectedEmployees);
    } catch (error) {
      console.error('Error in handleEditRecipients:', error);
    }
  }

  // save selected recipients
  const handleSaveRecipients = (selectedItems) => {
    console.log('handleSaveRecipients called with items:', selectedItems);
    
    try {
      if (recipientSelectorType === 'customers') {
        console.log('Updating selectedCustomers');
        setFormData(prev => ({
          ...prev,
          selectedCustomers: selectedItems
        }))
      } else {
        console.log('Updating selectedEmployees');
        setFormData(prev => ({
          ...prev,
          selectedEmployees: selectedItems
        }))
      }
      
      setShowRecipientSelector(false);
      console.log('Set showRecipientSelector to false');
    } catch (error) {
      console.error('Error in handleSaveRecipients:', error);
    }
  }
  
  // debug
  useEffect(() => {
    console.log('Form data:', formData)
    console.log('Errors:', errors)
    console.log('Is validating:', isValidating)
  }, [formData, errors, isValidating])
  
  return (
    <div className="bg-white rounded-lg shadow h-full">
      <div className="p-6 border-b border-gray-200">
        <h2 className="text-xl font-semibold text-gray-900">Create New Campaign</h2>
        <div className="mt-2 flex items-center">
          <div className={`flex items-center justify-center w-8 h-8 rounded-full ${currentStep === 1 ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700'}`}>
            1
          </div>
          <div className={`h-1 w-16 ${currentStep === 2 ? 'bg-blue-600' : 'bg-gray-200'}`}></div>
          <div className={`flex items-center justify-center w-8 h-8 rounded-full ${currentStep === 2 ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700'}`}>
            2
          </div>
        </div>
      </div>
      
      <form onSubmit={handleSubmit} className="p-6">
        {currentStep === 1 ? (
          <>
            {/* step 1: campaign type, name and subject */}
            {/* campaign type selection */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Campaign Type
              </label>
              <div className="flex space-x-4">
                <button
                  type="button"
                  className={`flex items-center px-4 py-2 rounded-md ${
                    campaignType === 'email' 
                      ? 'bg-blue-50 border border-blue-300 text-blue-700' 
                      : 'bg-gray-50 border border-gray-300 text-gray-700 hover:bg-gray-100'
                  }`}
                  onClick={() => setCampaignType('email')}
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                  Email
                </button>
                <button
                  type="button"
                  className={`flex items-center px-4 py-2 rounded-md ${
                    campaignType === 'text' 
                      ? 'bg-blue-50 border border-blue-300 text-blue-700' 
                      : 'bg-gray-50 border border-gray-300 text-gray-700 hover:bg-gray-100'
                  }`}
                  onClick={() => setCampaignType('text')}
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                  </svg>
                  Text
                </button>
              </div>
            </div>
            
            {/* campaign name */}
            <div className="mb-6">
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                Campaign Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-md ${
                  errors.name ? 'border-red-300 bg-red-50' : 'border-gray-300'
                }`}
                placeholder="e.g., Summer Promotion"
                required
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name}</p>
              )}
            </div>
            
            {/* subject (all campaign types need) */}
            <div className="mb-6">
              <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-1">
                Subject <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="subject"
                name="subject"
                value={formData.subject}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-md ${
                  errors.subject ? 'border-red-300 bg-red-50' : 'border-gray-300'
                }`}
                placeholder={campaignType === 'email' ? "e.g., Limited Time Offer - Don't Miss Out!" : "e.g., Special Discount"}
                required
              />
              {errors.subject && (
                <p className="mt-1 text-sm text-red-600">{errors.subject}</p>
              )}
            </div>
            
            {/* next button */}
            <div className="flex justify-end space-x-3 mt-8">
              <Button 
                variant="outline" 
                onClick={() => navigate('/marketing/campaigns')}
                type="button"
              >
                Cancel
              </Button>
              <Button 
                type="button" 
                onClick={handleNextStep}
                disabled={isValidating && (!!errors.name || !!errors.subject)}
              >
                Next
              </Button>
            </div>
          </>
        ) : (
          <>
            {/* step 2: message content, recipients and schedule */}
            {/* message content */}
            <div className="mb-6">
              <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
                Message Content <span className="text-red-500">*</span>
              </label>
              <textarea
                id="message"
                name="message"
                value={formData.message}
                onChange={handleInputChange}
                rows="5"
                className={`w-full px-3 py-2 border rounded-md ${
                  errors.message ? 'border-red-300 bg-red-50' : 'border-gray-300'
                }`}
                placeholder="Enter your message content here..."
                required
              ></textarea>
              {errors.message && (
                <p className="mt-1 text-sm text-red-600">{errors.message}</p>
              )}
            </div>
            
            {/* Recipients */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Select Recipients
              </label>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                <div 
                  className={`border p-3 rounded-md cursor-pointer ${
                    formData.recipients === 'all' 
                      ? 'border-blue-300 bg-blue-50' 
                      : 'border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <input
                        type="radio"
                        checked={formData.recipients === 'all'}
                        onChange={() => handleInputChange({ target: { name: 'recipients', value: 'all' } })}
                        className="h-4 w-4 text-blue-600"
                      />
                      <div className="ml-3">
                        <span className="block text-sm font-medium text-gray-900">All Customers</span>
                        <span className="block text-xs text-gray-500">Send to all customers</span>
                      </div>
                    </div>
                    <button 
                      type="button" 
                      className="text-blue-600 hover:text-blue-800 text-sm"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        console.log('Edit button clicked for All Customers');
                        handleEditRecipients('customers');
                      }}
                    >
                      Edit
                    </button>
                  </div>
                </div>
                
                <div 
                  className={`border p-3 rounded-md cursor-pointer ${
                    formData.recipients === 'active' 
                      ? 'border-blue-300 bg-blue-50' 
                      : 'border-gray-300 hover:bg-gray-50'
                  }`}
                  onClick={() => handleInputChange({ target: { name: 'recipients', value: 'active' } })}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <input
                        type="radio"
                        checked={formData.recipients === 'active'}
                        onChange={() => {}}
                        className="h-4 w-4 text-blue-600"
                      />
                      <div className="ml-3">
                        <span className="block text-sm font-medium text-gray-900">Active Customers</span>
                        <span className="block text-xs text-gray-500">Appointments in last 3 months</span>
                      </div>
                    </div>
                    <button 
                      type="button" 
                      className="text-blue-600 hover:text-blue-800 text-sm"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        console.log('Edit button clicked for Active Customers');
                        handleEditRecipients('customers');
                      }}
                    >
                      Edit
                    </button>
                  </div>
                </div>
                
                <div 
                  className={`border p-3 rounded-md cursor-pointer ${
                    formData.recipients === 'inactive' 
                      ? 'border-blue-300 bg-blue-50' 
                      : 'border-gray-300 hover:bg-gray-50'
                  }`}
                  onClick={() => handleInputChange({ target: { name: 'recipients', value: 'inactive' } })}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <input
                        type="radio"
                        checked={formData.recipients === 'inactive'}
                        onChange={() => {}}
                        className="h-4 w-4 text-blue-600"
                      />
                      <div className="ml-3">
                        <span className="block text-sm font-medium text-gray-900">Inactive Customers</span>
                        <span className="block text-xs text-gray-500">No appointments in last 3 months</span>
                      </div>
                    </div>
                    <button 
                      type="button" 
                      className="text-blue-600 hover:text-blue-800 text-sm"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        console.log('Edit button clicked for Inactive Customers');
                        handleEditRecipients('customers');
                      }}
                    >
                      Edit
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* specific recipients */}
            <div className="mb-6">
              <h3 className="text-sm font-medium text-gray-700 mb-2">Who Will Receive Your Campaign:</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* customers */}
                <div className="border rounded-md p-4 bg-gray-50">
                  <div className="flex justify-between items-center mb-2">
                    <h4 className="font-medium">{formData.selectedCustomers.length} Customers</h4>
                    <button 
                      type="button" 
                      className="text-blue-600 hover:text-blue-800 text-sm"
                      onClick={(e) => {
                        e.preventDefault();
                        console.log('Edit button clicked for Customers section');
                        handleEditRecipients('customers');
                      }}
                    >
                      Edit
                    </button>
                  </div>
                  <p className="text-xs text-gray-500">
                    {formData.selectedCustomers.length === 0 
                      ? 'No customers selected' 
                      : `${formData.selectedCustomers.length} customers will receive this campaign`}
                  </p>
                </div>
                
                {/* employees */}
                <div className="border rounded-md p-4 bg-gray-50">
                  <div className="flex justify-between items-center mb-2">
                    <h4 className="font-medium">{formData.selectedEmployees.length} Employees</h4>
                    <button 
                      type="button" 
                      className="text-blue-600 hover:text-blue-800 text-sm"
                      onClick={(e) => {
                        e.preventDefault();
                        console.log('Edit button clicked for Employees section');
                        handleEditRecipients('employees');
                      }}
                    >
                      Edit
                    </button>
                  </div>
                  <p className="text-xs text-gray-500">
                    {formData.selectedEmployees.length === 0 
                      ? 'No employees selected' 
                      : `${formData.selectedEmployees.length} employees will receive this campaign`}
                  </p>
                </div>
                
                {/* unsubscribed users */}
                <div className="border rounded-md p-4 bg-gray-50">
                  <div className="flex justify-between items-center mb-2">
                    <h4 className="font-medium">7 Opted Out</h4>
                    <button 
                      type="button" 
                      className="text-blue-600 hover:text-blue-800 text-sm"
                    >
                      See Who
                    </button>
                  </div>
                  <p className="text-xs text-gray-500">These contacts have opted out of marketing</p>
                </div>
              </div>
            </div>
            
            {/* schedule */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Schedule
              </label>
              <div className="space-y-3">
                <div 
                  className={`border p-3 rounded-md cursor-pointer ${
                    formData.schedule === 'now' 
                      ? 'border-blue-300 bg-blue-50' 
                      : 'border-gray-300 hover:bg-gray-50'
                  }`}
                  onClick={() => handleInputChange({ target: { name: 'schedule', value: 'now' } })}
                >
                  <div className="flex items-center">
                    <input
                      type="radio"
                      checked={formData.schedule === 'now'}
                      onChange={() => {}}
                      className="h-4 w-4 text-blue-600"
                    />
                    <span className="ml-3 block text-sm font-medium text-gray-900">Send Now</span>
                  </div>
                </div>
                
                <div 
                  className={`border p-3 rounded-md cursor-pointer ${
                    formData.schedule === 'later' 
                      ? 'border-blue-300 bg-blue-50' 
                      : 'border-gray-300 hover:bg-gray-50'
                  }`}
                  onClick={() => handleInputChange({ target: { name: 'schedule', value: 'later' } })}
                >
                  <div className="flex items-center mb-2">
                    <input
                      type="radio"
                      checked={formData.schedule === 'later'}
                      onChange={() => {}}
                      className="h-4 w-4 text-blue-600"
                    />
                    <span className="ml-3 block text-sm font-medium text-gray-900">Schedule for Later</span>
                  </div>
                  
                  {formData.schedule === 'later' && (
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mt-3 ml-7">
                      <div>
                        <label htmlFor="scheduledDate" className="block text-xs font-medium text-gray-700 mb-1">
                          Date
                        </label>
                        <input
                          type="date"
                          id="scheduledDate"
                          name="scheduledDate"
                          value={formData.scheduledDate}
                          onChange={handleInputChange}
                          className={`w-full px-3 py-2 border rounded-md ${
                            errors.scheduledDate ? 'border-red-300 bg-red-50' : 'border-gray-300'
                          }`}
                        />
                        {errors.scheduledDate && (
                          <p className="mt-1 text-xs text-red-600">{errors.scheduledDate}</p>
                        )}
                      </div>
                      
                      <div>
                        <label htmlFor="scheduledTime" className="block text-xs font-medium text-gray-700 mb-1">
                          Time
                        </label>
                        <input
                          type="time"
                          id="scheduledTime"
                          name="scheduledTime"
                          value={formData.scheduledTime}
                          onChange={handleInputChange}
                          className={`w-full px-3 py-2 border rounded-md ${
                            errors.scheduledTime ? 'border-red-300 bg-red-50' : 'border-gray-300'
                          }`}
                        />
                        {errors.scheduledTime && (
                          <p className="mt-1 text-xs text-red-600">{errors.scheduledTime}</p>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
            
            {/* action buttons */}
            <div className="flex justify-between mt-8">
              <Button 
                variant="outline" 
                onClick={handlePrevStep}
                type="button"
              >
                Back
              </Button>
              <div className="flex space-x-3">
                <Button 
                  variant="outline" 
                  onClick={() => navigate('/marketing/campaigns')}
                  type="button"
                >
                  Cancel
                </Button>
                <Button type="submit">
                  {formData.schedule === 'now' ? 'Send Now' : 'Create Campaign'}
                </Button>
              </div>
            </div>
          </>
        )}
      </form>

      {/* recipient selector modal */}
      {showRecipientSelector && (
        <div className="debug-info">
          <RecipientSelector 
            type={recipientSelectorType}
            onClose={() => {
              console.log('RecipientSelector onClose called');
              setShowRecipientSelector(false);
            }}
            onSave={handleSaveRecipients}
            initialSelected={recipientSelectorType === 'customers' 
              ? formData.selectedCustomers 
              : formData.selectedEmployees}
          />
        </div>
      )}
    </div>
  )
}

export default CreateCampaignForm 