import { useState, useEffect, useCallback } from 'react'
import Button from '../../../components/Button'
import { useMarketingApi } from '../services/marketingApi'

const RecipientSelector = ({ type, onClose, onSave, initialSelected = [] }) => {
  const marketingApi = useMarketingApi();
  const [loading, setLoading] = useState(true)
  const [items, setItems] = useState([])
  const [selectedItems, setSelectedItems] = useState([])
  const [searchQuery, setSearchQuery] = useState('')
  const [filteredItems, setFilteredItems] = useState([])
  const [dataFetched, setDataFetched] = useState(false)

  // 只在组件挂载时获取数据
  useEffect(() => {
    // 如果已经获取过数据，不再重复获取
    if (dataFetched) return;
    
    const fetchData = async () => {
      setLoading(true);
      
      try {
        console.log(`Fetching ${type} data for RecipientSelector`);
        console.log('Initial selected items:', initialSelected);
        
        let data;
        if (type === 'customers') {
          data = await marketingApi.getCustomers();
        } else {
          data = await marketingApi.getEmployees();
        }
        
        console.log(`${type} data fetched:`, data);
        
        // Ensure data is always an array
        const itemsArray = Array.isArray(data) ? data : (data?.results || []);
        setItems(itemsArray);
        console.log(`${type} items set:`, itemsArray);
        
        // Extract IDs from initialSelected
        const initialIds = [];
        if (initialSelected && initialSelected.length > 0) {
          initialSelected.forEach(item => {
            if (typeof item === 'object') {
              if (item.id) {
                initialIds.push(item.id);
              } else if (item.user_id) {
                initialIds.push(item.user_id);
              }
            } else if (typeof item === 'number' || typeof item === 'string') {
              initialIds.push(item);
            }
          });
        }
        
        console.log('Initial IDs extracted:', initialIds);
        setSelectedItems(initialIds);
        setDataFetched(true);
      } catch (error) {
        console.error(`Error fetching ${type}:`, error);
        setItems([]);
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [type, marketingApi, initialSelected, dataFetched]);
  
  // 使用 useCallback 缓存过滤函数，避免不必要的重新计算
  const filterItems = useCallback(() => {
    if (!Array.isArray(items)) {
      setFilteredItems([]);
      return;
    }

    if (searchQuery.trim() === '') {
      setFilteredItems(items);
      return;
    }
    
    const query = searchQuery.toLowerCase();
    try {
      const filtered = items.filter(item => 
        (item.first_name && item.first_name.toLowerCase().includes(query)) || 
        (item.last_name && item.last_name.toLowerCase().includes(query)) || 
        (item.email && item.email.toLowerCase().includes(query))
      );
      
      setFilteredItems(filtered);
    } catch (error) {
      console.error('Error filtering items:', error);
      setFilteredItems([]);
    }
  }, [searchQuery, items]);
  
  // 只在搜索查询或项目列表变化时重新过滤
  useEffect(() => {
    filterItems();
  }, [filterItems]);
  
  // Select all visible items
  const handleSelectAll = () => {
    if (!Array.isArray(filteredItems)) {
      return;
    }
    
    const allIds = filteredItems.map(item => item.id);
    setSelectedItems(allIds);
  }
  
  // Deselect all items
  const handleDeselectAll = () => {
    setSelectedItems([]);
  }
  
  // Toggle selection for a single item
  const handleToggleItem = (id) => {
    setSelectedItems(prevSelected => {
      if (prevSelected.includes(id)) {
        return prevSelected.filter(itemId => itemId !== id);
      } else {
        return [...prevSelected, id];
      }
    });
  }
  
  // Save selected items
  const handleSave = () => {
    if (!Array.isArray(items)) {
      onSave([]);
      return;
    }
    
    // Find full selected items information
    const selectedItemsData = items.filter(item => selectedItems.includes(item.id));
    try {
      onSave(selectedItemsData);
    } catch (error) {
      console.error('Error in onSave callback:', error);
    }
  }

  // Ensure filteredItems is always an array
  const safeFilteredItems = Array.isArray(filteredItems) ? filteredItems : [];
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] flex flex-col">
        {/* header */}
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            {type === 'customers' ? 'Select Customers' : 'Select Employees'}
          </h2>
          <p className="text-sm text-gray-500 mt-1">
            {type === 'customers' ? 'Choose which customers will receive this campaign' : 'Choose which employees will receive this campaign'}
          </p>
        </div>
        
        {/* main */}
        <div className="flex-1 overflow-hidden flex flex-col">
          {/* search and control */}
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center">
              <div className="relative flex-1">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <input
                  type="text"
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder={`Search ${type}...`}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              
              <div className="ml-4 flex space-x-2">
                <button 
                  type="button"
                  className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  onClick={handleSelectAll}
                >
                  Select All
                </button>
                <button 
                  type="button"
                  className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  onClick={handleDeselectAll}
                >
                  Deselect All
                </button>
              </div>
            </div>
            
            <div className="mt-2 text-sm text-gray-500">
              {selectedItems.length} {type} selected
            </div>
          </div>
          
          {/* list */}
          <div className="flex-1 overflow-y-auto p-1">
            {loading ? (
              <div className="flex items-center justify-center h-full">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <span className="ml-2">Loading...</span>
              </div>
            ) : safeFilteredItems.length === 0 ? (
              <div className="flex items-center justify-center h-full text-gray-500">
                <span>No {type} found</span>
              </div>
            ) : (
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-100">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      <span className="sr-only">Select</span>
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Name
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Email Address
                    </th>
                    {type === 'employees' && (
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Role
                      </th>
                    )}
                    {type === 'customers' && (
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Marketing Preferences
                      </th>
                    )}
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Action
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {safeFilteredItems.map((item) => (
                    <tr 
                      key={item.id} 
                      className={`hover:bg-gray-50 ${selectedItems.includes(item.id) ? 'bg-blue-50' : ''}`}
                      onClick={() => handleToggleItem(item.id)}
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="checkbox"
                          className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                          checked={selectedItems.includes(item.id)}
                          onChange={(e) => {
                            e.stopPropagation();
                            handleToggleItem(item.id);
                          }}
                          onClick={(e) => e.stopPropagation()}
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {item.first_name} {item.last_name}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">{item.email}</div>
                      </td>
                      {type === 'employees' && (
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-500">{item.employee_type || item.role || 'Employee'}</div>
                        </td>
                      )}
                      {type === 'customers' && (
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex space-x-2">
                            {item.opt_in_marketing ? (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Opted In
                              </span>
                            ) : (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                Opted Out
                              </span>
                            )}
                          </div>
                        </td>
                      )}
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          type="button"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleToggleItem(item.id);
                          }}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          {selectedItems.includes(item.id) ? 'Remove' : 'Select'}
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </div>
        </div>
        
        {/* footer */}
        <div className="p-4 border-t border-gray-200 flex justify-end space-x-3">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            Apply & Close
          </Button>
        </div>
      </div>
    </div>
  )
}

export default RecipientSelector 