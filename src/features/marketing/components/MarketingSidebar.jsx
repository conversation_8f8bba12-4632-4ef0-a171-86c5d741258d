import React, { useState, useEffect, useRef } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { useApi } from '../../../hooks/useApi'

function MarketingSidebar() {
  const location = useLocation()
  const [activeTab, setActiveTab] = useState('all')
  const [campaignCounts, setCampaignCounts] = useState({
    all: 0,
    sent: 0,
    draft: 0,
    queue: 0,
    automated: 0,
    archive: 0
  })
  const [marketingCredits, setMarketingCredits] = useState({
    email: { used: 0, total: 1000 },
    text: { used: 0, total: 0 }
  })
  
  // Track if component is mounted
  const isMounted = useRef(true);
  
  // Track if initial data load has happened
  const initialLoadDone = useRef(false);
  
  // Track current path to avoid redundant loads
  const currentPath = useRef(location.pathname);
  
  // Track current tab to detect tab changes even when path doesn't change
  const previousTab = useRef(activeTab);
  
  // Use API hook to get campaign statistics directly - with shorter cache time
  const { 
    data: campaignStats, 
    loading: statsLoading, 
    error: statsError, 
    fetchData: fetchStats,
    clearCache: clearStatsCache
  } = useApi('marketing/campaigns/stats', { cacheDuration: 30000, debounceTime: 300 }) // 30s cache
  
  // Use API hook to get marketing credits info
  const { 
    data: creditsData, 
    loading: creditsLoading, 
    error: creditsError, 
    fetchData: fetchCredits 
  } = useApi('marketing/credits', { cacheDuration: 300000, debounceTime: 500 }) // 5min cache
  
  // Load data function
  const loadData = () => {
    if (!isMounted.current) return;
    
    // Get campaign stats directly from API
    fetchStats().catch(error => {
      console.error('Failed to fetch campaign statistics:', error);
    });
    
    // Get credits info - ignore errors
    fetchCredits().catch(error => {
      console.error('Failed to fetch marketing credits:', error);
      // Use default values, won't affect UI display
    });
  };
  
  // Handle component mount/unmount
  useEffect(() => {
    isMounted.current = true;
    
    // Initial data load
    if (!initialLoadDone.current) {
      loadData();
      initialLoadDone.current = true;
    }
    
    return () => {
      isMounted.current = false;
    };
  }, []); // Empty dependency array - only run once
  
  // When API stats data updates, update counts
  useEffect(() => {
    if (!campaignStats) return;
    
    // API returns format: { all: 10, sent: 3, draft: 2, queue: 1, automated: 2, archive: 2 }
    setCampaignCounts(campaignStats);
  }, [campaignStats]);
  
  // When credits data updates, update local state
  useEffect(() => {
    if (!creditsData) return;
    
    // Transform API response to component format
    // API returns: { email_credits_used, email_credit_limit, text_credits_used, text_credit_limit, ... }
    // Component expects: { email: { used, total }, text: { used, total } }
    const transformedData = {
      email: {
        used: creditsData.email_credits_used || 0,
        total: creditsData.email_credit_limit || 1000
      },
      text: {
        used: creditsData.text_credits_used || 0,
        total: creditsData.text_credit_limit || 0
      }
    };
    
    setMarketingCredits(transformedData);
  }, [creditsData]);
  
  // Update activeTab when path changes
  useEffect(() => {
    const path = location.pathname.split('/').pop();
    
    // Determine new active tab based on path
    let newActiveTab = 'all';
    if (path && ['campaigns', 'sent', 'draft', 'queue', 'automated', 'archive'].includes(path)) {
      newActiveTab = path === 'campaigns' ? 'all' : path;
    }
    
    // Set the active tab
    setActiveTab(newActiveTab);
    
    // Check if we need to reload data:
    // 1. Path changed
    // 2. Tab changed (even if path didn't change, like clicking "All Messages" when already there)
    const pathChanged = currentPath.current !== location.pathname;
    const tabChanged = previousTab.current !== newActiveTab;
    
    if (pathChanged || tabChanged) {
      // Update refs
      currentPath.current = location.pathname;
      previousTab.current = newActiveTab;
      
      // Always clear cache and reload data when tab/path changes
      clearStatsCache();
      loadData();
    }
  }, [location.pathname]); // Only depend on location.pathname
  
  // Add periodic refresh - every 60 seconds
  useEffect(() => {
    // Set up interval for periodic refresh
    const intervalId = setInterval(() => {
      if (isMounted.current) {
        loadData();
      }
    }, 60000);
    
    // Clean up interval on unmount
    return () => clearInterval(intervalId);
  }, []); // Empty dependency array - only set up once
  
  // If credits request fails, use default values
  const emailCredits = creditsError ? { used: 0, total: 1000 } : (marketingCredits?.email || { used: 0, total: 1000 });
  const textCredits = creditsError ? { used: 0, total: 0 } : (marketingCredits?.text || { used: 0, total: 0 });
  
  // Check if in offline mode
  const isOfflineMode = creditsError && creditsError.message && creditsError.message.includes('ERR_CONNECTION_REFUSED');
  
  // Handle manual refresh
  const handleRefresh = (e) => {
    e.preventDefault();
    if (!isMounted.current) return;
    
    clearStatsCache();
    loadData();
  };
  
  return (
    <div className="bg-white rounded-lg shadow w-full h-full">
      {isOfflineMode && (
        <div className="p-2 bg-yellow-100 text-yellow-800 text-xs text-center">
          Offline mode - Some features may be unavailable
        </div>
      )}
      
      <div className="flex justify-between items-center p-3 border-b border-gray-200">
        <h3 className="text-sm font-medium text-gray-700">Campaign Categories</h3>
        <button 
          onClick={handleRefresh}
          className="text-xs text-blue-600 hover:text-blue-800 flex items-center"
          title="Refresh data"
        >
          <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          Refresh
        </button>
      </div>
      
      {/* Message Categories */}
      <div className="p-3">
        <ul className="space-y-1">
          <li>
            <Link
              to="/marketing/campaigns"
              className={`flex items-center px-3 py-2 rounded-md text-base ${
                activeTab === 'all' ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-100'
              }`}
            >
              <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
              </svg>
              <span>All Messages</span>
              <span className="ml-auto bg-gray-200 text-gray-700 px-2 py-0.5 rounded-full text-sm font-medium">
                {statsLoading ? '...' : campaignCounts.all}
              </span>
            </Link>
          </li>
          <li>
            <Link
              to="/marketing/sent"
              className={`flex items-center px-3 py-2 rounded-md text-base ${
                activeTab === 'sent' ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-100'
              }`}
            >
              <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
              <span>Sent</span>
              <span className="ml-auto bg-gray-200 text-gray-700 px-2 py-0.5 rounded-full text-sm font-medium">
                {statsLoading ? '...' : campaignCounts.sent}
              </span>
            </Link>
          </li>
          <li>
            <Link
              to="/marketing/draft"
              className={`flex items-center px-3 py-2 rounded-md text-base ${
                activeTab === 'draft' ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-100'
              }`}
            >
              <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
              <span>Draft</span>
              <span className="ml-auto bg-gray-200 text-gray-700 px-2 py-0.5 rounded-full text-sm font-medium">
                {statsLoading ? '...' : campaignCounts.draft}
              </span>
            </Link>
          </li>
          <li>
            <Link
              to="/marketing/queue"
              className={`flex items-center px-3 py-2 rounded-md text-base ${
                activeTab === 'queue' ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-100'
              }`}
            >
              <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span>Sending Queue</span>
              <span className="ml-auto bg-gray-200 text-gray-700 px-2 py-0.5 rounded-full text-sm font-medium">
                {statsLoading ? '...' : campaignCounts.queue}
              </span>
            </Link>
          </li>
          <li>
            <Link
              to="/marketing/automated"
              className={`flex items-center px-3 py-2 rounded-md text-base ${
                activeTab === 'automated' ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-100'
              }`}
            >
              <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 3.104v5.714a2.25 2.25 0 01-.659 1.591L5 14.5M9.75 3.104c-.251.023-.501.05-.75.082m.75-.082a24.301 24.301 0 014.5 0m0 0v5.714a2.25 2.25 0 001.5 2.25m0 0v2.628a2.25 2.25 0 01-.659 1.591L14.25 14.5M19.5 10.5c0 2.485-2.015 4.5-4.5 4.5s-4.5-2.015-4.5-4.5S12.515 6 15 6s4.5 2.015 4.5 4.5zM5.25 5.25h3m-3 3h3m-3 3h3M9 18h3m-3 3h3" />
              </svg>
              <span>Automated Sends</span>
              <span className="ml-auto bg-gray-200 text-gray-700 px-2 py-0.5 rounded-full text-sm font-medium">
                {statsLoading ? '...' : campaignCounts.automated}
              </span>
            </Link>
          </li>
          <li>
            <Link
              to="/marketing/archive"
              className={`flex items-center px-3 py-2 rounded-md text-base ${
                activeTab === 'archive' ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-100'
              }`}
            >
              <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
              </svg>
              <span>Archive</span>
              <span className="ml-auto bg-gray-200 text-gray-700 px-2 py-0.5 rounded-full text-sm font-medium">
                {statsLoading ? '...' : campaignCounts.archive}
              </span>
            </Link>
          </li>
        </ul>
      </div>
      
      <hr className="my-2 border-gray-200" />
      
      {/* Email Marketing */}
      <div className="p-3">
        <h3 className="text-base font-medium text-gray-900 mb-2">Email Marketing</h3>
        <div className="bg-blue-50 p-3 rounded-lg">
          <div className="flex items-center mb-1">
            <span className="text-xl font-bold text-gray-700">{emailCredits.used}</span>
            <span className="text-sm text-gray-500 ml-2">Credits Used</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full" 
              style={{ width: `${(emailCredits.used / emailCredits.total) * 100}%` }}
            ></div>
          </div>
          <div className="flex justify-between items-center mt-1">
            <span className="text-sm text-gray-500">Limit: {emailCredits.total.toLocaleString()}</span>
            <span className="text-sm text-gray-500">No extra</span>
          </div>
        </div>
      </div>
      
      {/* Text Marketing */}
      <div className="p-3">
        <h3 className="text-base font-medium text-gray-900 mb-2">Text Marketing</h3>
        <div className="bg-gray-50 p-3 rounded-lg">
          <div className="flex items-center mb-1">
            <span className="text-xl font-bold text-gray-700">{textCredits.used}</span>
            <span className="text-sm text-gray-500 ml-2">Credits Used</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-gray-400 h-2 rounded-full" 
              style={{ width: `${(textCredits.used / (textCredits.total || 1)) * 100}%` }}
            ></div>
          </div>
          <div className="mt-2">
            <Link
              to="/marketing/text-plan"
              className="text-sm text-blue-600 hover:text-blue-800 font-medium"
            >
              Select Text Marketing Plan
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}

// Use React.memo to prevent unnecessary re-renders
export default React.memo(MarketingSidebar) 