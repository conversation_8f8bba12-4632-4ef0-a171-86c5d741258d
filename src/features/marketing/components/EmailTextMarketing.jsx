import { useState } from 'react'
import Button from '../../../components/Button'

function EmailTextMarketing() {
  const [showModal, setShowModal] = useState(false)

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-center mb-6">
        <div className="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center">
          <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
        </div>
      </div>
      <h3 className="text-xl font-semibold text-center mb-2">Email & Text Marketing</h3>
      <p className="text-gray-600 text-center mb-6">
        Send automated emails and text messages to specific clients using filters.
      </p>
      <div className="flex justify-center">
        <Button 
          onClick={() => setShowModal(true)}
          className="w-full sm:w-auto"
        >
          Create Campaign
        </Button>
      </div>

      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto shadow-2xl">
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <div className="flex items-center space-x-3">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                <div className="flex-1 min-w-0">
                  <h3 className="text-lg font-medium text-gray-900">Create Email Campaign</h3>
                  <p className="text-sm text-gray-500 mt-1">Send targeted messages to your customers</p>
                </div>
              </div>
              <button
                onClick={() => setShowModal(false)}
                className="text-gray-400 hover:text-gray-600 transition-colors p-1"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <div className="p-6">
              <p className="text-gray-600 mb-6">
                This feature is coming soon. You'll be able to create and manage email and text campaigns from here.
              </p>
              <div className="flex justify-end">
                <Button 
                  variant="outline" 
                  onClick={() => setShowModal(false)}
                  className="mr-2"
                >
                  Close
                </Button>
                <Button disabled>
                  Create Campaign
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default EmailTextMarketing 