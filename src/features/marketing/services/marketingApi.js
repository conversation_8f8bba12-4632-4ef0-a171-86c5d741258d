import { useApi } from '../../../hooks/useApi';

/**
 * Marketing Campaign API
 * @returns {Object} Marketing campaign API methods
 */
export const useMarketingApi = () => {
  const businessCustomersApi = useApi('business-customers');
  const employeesApi = useApi('employees');
  const campaignsApi = useApi('marketing/campaigns');
  const campaignTypesApi = useApi('marketing/campaign-types');

  /**
   * Get campaign types list
   * @param {Object} params - Query parameters
   * @returns {Promise<Array>} Campaign types list
   */
  const getCampaignTypes = async (params = {}) => {
    try {
      console.log('Fetching campaign types with params:', params);
      const response = await campaignTypesApi.fetchData(params);
      console.log('Campaign types API response:', response);
      
      // Process different response data formats
      let campaignTypes = [];
      if (Array.isArray(response)) {
        campaignTypes = response;
      } else if (response && typeof response === 'object') {
        // Handle paginated response format
        if (response.results && Array.isArray(response.results)) {
          campaignTypes = response.results;
        }
      }
      
      return campaignTypes;
    } catch (error) {
      console.error('Error fetching campaign types:', error);
      return [];
    }
  };

  /**
   * Get customer list
   * @param {Object} params - Query parameters
   * @returns {Promise<Array>} Customer list
   */
  const getCustomers = async (params = {}) => {
    try {
      console.log('Fetching customers with params:', params);
      const response = await businessCustomersApi.fetchData(params);
      console.log('Customer API response:', response);
      
      // Process data based on actual backend response format
      let customers = [];
      if (response && typeof response === 'object') {
        // Handle paginated response format
        if (response.results && Array.isArray(response.results)) {
          customers = response.results;
        } else if (Array.isArray(response)) {
          customers = response;
        }
      }
      
      console.log('Processed customers data:', customers);
      return customers;
    } catch (error) {
      console.error('Error fetching customers:', error);
      return [];
    }
  };

  /**
   * Get employee list
   * @param {Object} params - Query parameters
   * @returns {Promise<Array>} Employee list
   */
  const getEmployees = async (params = {}) => {
    try {
      console.log('Fetching employees with params:', params);
      const response = await employeesApi.fetchData(params);
      console.log('Employee API response:', response);
      
      // Process data based on actual backend response format
      let employees = [];
      if (response && typeof response === 'object') {
        // Handle paginated response format
        if (response.results && Array.isArray(response.results)) {
          employees = response.results;
        } else if (Array.isArray(response)) {
          employees = response;
        }
      }
      
      console.log('Processed employees data:', employees);
      return employees;
    } catch (error) {
      console.error('Error fetching employees:', error);
      return [];
    }
  };

  /**
   * Get opted out users
   * @param {Object} params - Query parameters
   * @returns {Promise<Array>} Opted out users list
   */
  const getOptedOutUsers = async (params = {}) => {
    try {
      console.log('Fetching opted out users with params:', params);
      const response = await businessCustomersApi.fetchData(params);
      console.log('Opted out API response:', response);
      
      // Process data based on actual backend response format
      let allCustomers = [];
      if (response && typeof response === 'object') {
        // Handle paginated response format
        if (response.results && Array.isArray(response.results)) {
          allCustomers = response.results;
        } else if (Array.isArray(response)) {
          allCustomers = response;
        }
      }
      
      // Filter for opted out users on the client side
      const optedOutUsers = allCustomers.filter(customer => 
        customer.hasOwnProperty('opt_in_marketing') && !customer.opt_in_marketing
      );
      
      console.log('Processed opted out data:', optedOutUsers);
      return optedOutUsers;
    } catch (error) {
      console.error('Error fetching opted out users:', error);
      return [];
    }
  };

  /**
   * Create marketing campaign
   * @param {Object} campaignData - Campaign data
   * @param {boolean} isDraft - Whether to save as draft or announce
   * @returns {Promise<Object>} Created campaign
   */
  const createCampaign = async (campaignData, isDraft = true) => {
    try {
      // Set the appropriate status based on isDraft flag
      const status = isDraft ? 'draft' : 'scheduled';
      
      // Prepare campaign data with appropriate status
      const preparedData = {
        ...campaignData,
        status: status
      };
      
      console.log('Creating campaign with data:', preparedData);
      const response = await campaignsApi.createData(preparedData);
      console.log('Campaign created successfully:', response);
      return response;
    } catch (error) {
      console.error('Error creating campaign:', error);
      throw error;
    }
  };

  /**
   * Save campaign for later (as draft)
   * @param {Object} campaignData - Campaign data
   * @returns {Promise<Object>} Created campaign
   */
  const saveCampaignForLater = async (campaignData) => {
    return await createCampaign(campaignData, true);
  };

  /**
   * Announce campaign (set as scheduled)
   * @param {Object} campaignData - Campaign data
   * @returns {Promise<Object>} Created campaign
   */
  const announceCampaign = async (campaignData) => {
    return await createCampaign(campaignData, false);
  };

  /**
   * Update existing campaign
   * @param {string} campaignId - Campaign ID
   * @param {Object} campaignData - Updated campaign data
   * @returns {Promise<Object>} Updated campaign
   */
  const updateCampaign = async (campaignId, campaignData) => {
    try {
      console.log(`Updating campaign ${campaignId} with data:`, campaignData);
      const response = await campaignsApi.updateData(campaignId, campaignData);
      console.log('Campaign updated successfully:', response);
      return response;
    } catch (error) {
      console.error(`Error updating campaign ${campaignId}:`, error);
      throw error;
    }
  };

  /**
   * Get campaigns list
   * @param {Object} params - Query parameters
   * @returns {Promise<Array>} Campaigns list
   */
  const getCampaigns = async (params = {}) => {
    try {
      const response = await campaignsApi.fetchData(params);
      console.log('Campaigns API response:', response);
      
      // Process different response data formats
      let campaigns = [];
      if (Array.isArray(response)) {
        campaigns = response;
      } else if (response && typeof response === 'object') {
        // Handle paginated response format
        if (response.campaigns && Array.isArray(response.campaigns)) {
          campaigns = response.campaigns;
        } else if (response.results && Array.isArray(response.results)) {
          campaigns = response.results;
        }
      }
      
      return campaigns;
    } catch (error) {
      console.error('Error fetching campaigns:', error);
      return [];
    }
  };

  /**
   * Get campaign by ID
   * @param {string} id - Campaign ID
   * @returns {Promise<Object>} Campaign details
   */
  const getCampaign = async (id) => {
    try {
      console.log(`Fetching campaign with ID: ${id}`);
      const response = await campaignsApi.fetchData(id);
      console.log('Campaign API response:', response);
      
      // 确保recipients字段是数组
      if (response && response.recipients && !Array.isArray(response.recipients)) {
        console.warn('Recipients field is not an array, converting:', response.recipients);
        if (typeof response.recipients === 'object') {
          // 如果是对象，尝试提取results字段
          response.recipients = response.recipients.results || [];
        } else {
          // 否则设为空数组
          response.recipients = [];
        }
      }
      
      return response;
    } catch (error) {
      console.error(`Error fetching campaign ${id}:`, error);
      throw error;
    }
  };

  /**
   * Get campaign recipients
   * @param {string} campaignId - Campaign ID
   * @returns {Promise<Array>} Campaign recipients list
   */
  const getCampaignRecipients = async (campaignId) => {
    try {
      console.log(`Fetching recipients for campaign: ${campaignId}`);
      // Use the existing campaigns API with the recipients action endpoint
      const response = await campaignsApi.fetchData(`${campaignId}/recipients`);
      console.log('Campaign recipients API response:', response);
      
      let recipients = [];
      if (Array.isArray(response)) {
        recipients = response;
      } else if (response && response.results && Array.isArray(response.results)) {
        recipients = response.results;
      }
      
      return recipients;
    } catch (error) {
      console.error(`Error fetching campaign ${campaignId} recipients:`, error);
      return [];
    }
  };

  /**
   * 发送营销活动预览邮件
   * @param {string} campaignId - 营销活动ID
   * @param {Array<string>} emailAddresses - 接收预览邮件的邮箱地址数组
   * @returns {Promise<Object>} 预览邮件发送结果
   */
  const sendCampaignPreview = async (campaignId, emailAddresses) => {
    try {
      const response = await campaignsApi.createData(`${campaignId}/preview/`, {
        email_addresses: emailAddresses
      });
      return response;
    } catch (error) {
      console.error('Error sending campaign preview:', error);
      throw error;
    }
  };

  /**
   * 发送营销活动预览邮件 (无ID版本，使用请求体中的ID)
   * @param {string} campaignId - 营销活动ID
   * @param {Array<string>} emailAddresses - 接收预览邮件的邮箱地址数组
   * @returns {Promise<Object>} 预览邮件发送结果
   */
  const sendCampaignPreviewNoId = async (campaignId, emailAddresses) => {
    try {
      const response = await campaignsApi.createData('preview/', {
        campaign_id: campaignId,
        email_addresses: emailAddresses
      });
      return response;
    } catch (error) {
      console.error('Error sending campaign preview:', error);
      throw error;
    }
  };

  /**
   * Upload a base64 image to S3 and get URL
   * @param {string} imageData - Base64 image data
   * @param {string} filename - Optional filename
   * @returns {Promise<Object>} Upload result with URL
   */
  const uploadEmailImage = async (imageData, filename = null) => {
    try {
      console.log('Uploading email image...');

      const response = await fetch(`${API_BASE_URL}/marketing/upload-image/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getAuthToken()}`
        },
        body: JSON.stringify({
          image_data: imageData,
          filename: filename
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to upload image');
      }

      const result = await response.json();
      console.log('Image uploaded successfully:', result);
      return result;
    } catch (error) {
      console.error('Error uploading image:', error);
      throw error;
    }
  };

  /**
   * Process email content and convert base64 images to S3 URLs
   * @param {string} content - Email content HTML
   * @param {Array} components - Optional component data
   * @returns {Promise<Object>} Processed content and components
   */
  const processEmailContent = async (content, components = null) => {
    try {
      console.log('Processing email content for images...');

      const response = await fetch(`${API_BASE_URL}/marketing/process-content/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getAuthToken()}`
        },
        body: JSON.stringify({
          content: content,
          components: components
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to process content');
      }

      const result = await response.json();
      console.log('Content processed successfully:', result);
      return result;
    } catch (error) {
      console.error('Error processing content:', error);
      throw error;
    }
  };

  return {
    getCampaignTypes,
    getCustomers,
    getEmployees,
    getOptedOutUsers,
    createCampaign,
    saveCampaignForLater,
    announceCampaign,
    updateCampaign,
    getCampaigns,
    getCampaign, // 添加新方法
    getCampaignRecipients, // 获取特定营销活动的收件人
    sendCampaignPreview, // 添加新方法
    sendCampaignPreviewNoId, // 添加新方法
    uploadEmailImage, // 图片上传
    processEmailContent, // 内容处理
    loading: {
      campaignTypes: campaignTypesApi.loading,
      customers: businessCustomersApi.loading,
      employees: employeesApi.loading,
      optedOut: businessCustomersApi.loading,
      campaigns: campaignsApi.loading
    },
    error: {
      campaignTypes: campaignTypesApi.error,
      customers: businessCustomersApi.error,
      employees: employeesApi.error,
      optedOut: businessCustomersApi.error,
      campaigns: campaignsApi.error
    }
  };
};

export default useMarketingApi; 