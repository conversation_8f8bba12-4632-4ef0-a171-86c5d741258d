/**
 * Campaign Type Mappings
 * 
 * This file contains mappings between frontend campaign type strings and 
 * backend campaign type IDs.
 * 
 * Note: These mappings are based on the reset_campaign_types.py script
 * which ensures IDs start from 1.
 */

// Frontend string to backend ID mapping
export const CAMPAIGN_TYPE_MAP = {
  'email-blast': 1,     // Email Blast
  'text-blast': 2,      // Text Blast
  'email-text': 3,      // Campaign Blast (Email & Text)
  'birthday': 4,        // Birthday
  'lost-customer': 5,   // Lost Customer
  'before-visit': 6,    // Before Visit
  'after-visit': 7      // After Visit
};

// Backend ID to frontend string mapping
export const CAMPAIGN_TYPE_REVERSE_MAP = {
  1: 'email-blast',
  2: 'text-blast',
  3: 'email-text',
  4: 'birthday',
  5: 'lost-customer',
  6: 'before-visit',
  7: 'after-visit'
};

// Campaign type display names
export const CAMPAIGN_TYPE_DISPLAY_NAMES = {
  'email-blast': 'Email Blast',
  'text-blast': 'Text Blast',
  'email-text': 'Email & Text Blast',
  'birthday': 'Birthday',
  'lost-customer': 'Lost Customer',
  'before-visit': 'Before Visit',
  'after-visit': 'After Visit'
};

/**
 * Convert frontend campaign type string to backend ID
 * @param {string} typeString - Frontend campaign type string
 * @returns {number} Backend campaign type ID
 */
export const getCampaignTypeId = (typeString) => {
  return CAMPAIGN_TYPE_MAP[typeString] || null;
};

/**
 * Convert backend campaign type ID to frontend string
 * @param {number} typeId - Backend campaign type ID
 * @returns {string} Frontend campaign type string
 */
export const getCampaignTypeString = (typeId) => {
  console.log('getCampaignTypeString called with:', typeId, 'type:', typeof typeId);
  console.log('Available mappings:', CAMPAIGN_TYPE_REVERSE_MAP);
  
  // 确保typeId是数字
  const numericId = parseInt(typeId, 10);
  if (isNaN(numericId)) {
    console.error('Invalid campaign type ID:', typeId);
    return 'email-blast'; // 默认值
  }
  
  const result = CAMPAIGN_TYPE_REVERSE_MAP[numericId];
  console.log('Mapping result:', result);
  return result || 'email-blast';
};

/**
 * Get display name for campaign type
 * @param {string} typeString - Frontend campaign type string
 * @returns {string} Display name for the campaign type
 */
export const getCampaignTypeDisplayName = (typeString) => {
  return CAMPAIGN_TYPE_DISPLAY_NAMES[typeString] || 'Unknown';
};

export default {
  CAMPAIGN_TYPE_MAP,
  CAMPAIGN_TYPE_REVERSE_MAP,
  CAMPAIGN_TYPE_DISPLAY_NAMES,
  getCampaignTypeId,
  getCampaignTypeString,
  getCampaignTypeDisplayName
}; 