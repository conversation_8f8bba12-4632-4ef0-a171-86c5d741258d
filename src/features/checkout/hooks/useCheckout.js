import { useState, useEffect } from "react";
import { checkoutApi } from "../services/checkoutApi";

/**
 * Custom hook for managing checkout/POS functionality
 * Provides state management and API integration for the appointment booking system
 */
export function useCheckout() {
  // ========== State Management ==========

  // Data states
  const [services, setServices] = useState([]);
  const [serviceCategories, setServiceCategories] = useState([]);
  const [staff, setStaff] = useState([]);
  const [customers, setCustomers] = useState([]);
  const [checkoutSettings, setCheckoutSettings] = useState(null);
  const [currentBusiness, setCurrentBusiness] = useState(null);

  // Cart and booking states (updated for appointment booking)
  const [cartItems, setCartItems] = useState([]);
  const [currentAppointment, setCurrentAppointment] = useState(null);
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [selectedEmployee, setSelectedEmployee] = useState(null);
  const [selectedDate, setSelectedDate] = useState(null);
  const [selectedTime, setSelectedTime] = useState(null);
  const [availableTimeSlots, setAvailableTimeSlots] = useState({});
  
  const [bookingTotal, setBookingTotal] = useState({
    subtotal: 0,
    discount: 0,
    tax: 0,
    tip: 0,
    total: 0,
  });

  // Loading states
  const [loading, setLoading] = useState({
    services: false,
    staff: false,
    customers: false,
    appointment: false,
    payment: false,
    availability: false,
  });

  // Error states
  const [error, setError] = useState(null);

  // ========== API Operations ==========

  // Load services list
  const loadServices = async (params = {}) => {
    setLoading((prev) => ({ ...prev, services: true }));
    setError(null);

    try {
      const response = await checkoutApi.getServices(params);
      // Handle both paginated and direct array responses
      const servicesList = response.results || response;
      setServices(servicesList);
      return response;
    } catch (err) {
      setError(err.message);
      console.error("Failed to load services:", err);
    } finally {
      setLoading((prev) => ({ ...prev, services: false }));
    }
  };

  // Load service categories
  const loadServiceCategories = async () => {
    setLoading((prev) => ({ ...prev, services: true }));
    setError(null);

    try {
      const response = await checkoutApi.getServiceCategories();
      const categoriesList = response.results || response;
      setServiceCategories(categoriesList);
      return response;
    } catch (err) {
      setError(err.message);
      console.error("Failed to load service categories:", err);
    } finally {
      setLoading((prev) => ({ ...prev, services: false }));
    }
  };

  // Load staff/employees list
  const loadStaff = async (params = {}) => {
    setLoading((prev) => ({ ...prev, staff: true }));
    setError(null);

    try {
      const response = await checkoutApi.getStaff({ is_active: true, ...params });
      const staffList = response.results || response;
      setStaff(staffList);
      return response;
    } catch (err) {
      setError(err.message);
      console.error("Failed to load staff:", err);
    } finally {
      setLoading((prev) => ({ ...prev, staff: false }));
    }
  };

  // Load customers for current business
  const loadCustomers = async (params = {}) => {
    setLoading((prev) => ({ ...prev, customers: true }));
    setError(null);

    try {
      const response = await checkoutApi.getCustomers(params);
      const customersList = response.results || response;
      setCustomers(customersList);
      return response;
    } catch (err) {
      setError(err.message);
      console.error("Failed to load customers:", err);
    } finally {
      setLoading((prev) => ({ ...prev, customers: false }));
    }
  };

  // Load current business information
  const loadCurrentBusiness = async () => {
    try {
      const business = await checkoutApi.getCurrentBusiness();
      setCurrentBusiness(business);
      return business;
    } catch (err) {
      setError(err.message);
      console.error("Failed to load current business:", err);
    }
  };

  // Get employee services
  const getEmployeeServices = async (employeeId, params = {}) => {
    try {
      const response = await checkoutApi.getEmployeeServices(employeeId, params);
      return response.results || response;
    } catch (err) {
      setError(err.message);
      console.error("Failed to get employee services:", err);
      return [];
    }
  };

  // Get staff availability
  const getStaffAvailability = async (staffId, date, serviceId) => {
    setLoading((prev) => ({ ...prev, availability: true }));
    setError(null);

    try {
      const availability = await checkoutApi.getStaffAvailability(
        staffId,
        date,
        serviceId
      );
      return availability;
    } catch (err) {
      setError(err.message);
      console.error("Failed to get staff availability:", err);
      return null;
    } finally {
      setLoading((prev) => ({ ...prev, availability: false }));
    }
  };

  // Get available time slots for appointment booking
  const loadAvailableTimeSlots = async (params = {}) => {
    setLoading((prev) => ({ ...prev, availability: true }));
    setError(null);

    try {
      const availability = await checkoutApi.getAvailableTimeSlots(params);
      setAvailableTimeSlots(availability);
      return availability;
    } catch (err) {
      setError(err.message);
      console.error("Failed to load available time slots:", err);
      setAvailableTimeSlots({});
    } finally {
      setLoading((prev) => ({ ...prev, availability: false }));
    }
  };

  // ========== Cart Management (Updated for Services/Appointments) ==========

  // Add service to booking cart
  const addServiceToCart = (service, employee = null, timeSlot = null) => {
    const cartItem = {
      id: Date.now(),
      type: 'service',
      service_id: service.id,
      service_name: service.name,
      employee_id: employee?.id || null,
      employee_name: employee ? `${employee.user?.first_name} ${employee.user?.last_name}`.trim() : null,
      price: parseFloat(service.price || service.base_price || 0),
      duration: service.duration || 60, // Duration in minutes
      quantity: 1,
      start_time: timeSlot,
      notes: '',
      subtotal: parseFloat(service.price || service.base_price || 0),
    };

    setCartItems((prev) => [...prev, cartItem]);
    updateBookingTotal([...cartItems, cartItem]);
  };

  // Update cart item
  const updateCartItem = (itemId, updates) => {
    setCartItems((prev) =>
      prev.map((item) =>
        item.id === itemId
          ? {
              ...item,
              ...updates,
              subtotal: calculateItemSubtotal({ ...item, ...updates }),
            }
          : item
      )
    );
  };

  // Remove from cart
  const removeFromCart = (itemId) => {
    setCartItems((prev) => prev.filter((item) => item.id !== itemId));
  };

  // Clear cart
  const clearCart = () => {
    setCartItems([]);
    setSelectedCustomer(null);
    setSelectedEmployee(null);
    setSelectedDate(null);
    setSelectedTime(null);
    setAvailableTimeSlots({});
    setBookingTotal({
      subtotal: 0,
      discount: 0,
      tax: 0,
      tip: 0,
      total: 0,
    });
  };

  // Calculate item subtotal
  const calculateItemSubtotal = (item) => {
    const price = parseFloat(item.price) || 0;
    const discount = parseFloat(item.discount) || 0;
    const quantity = parseInt(item.quantity) || 1;
    return (price - discount) * quantity;
  };

  // Update booking total
  const updateBookingTotal = (items = cartItems) => {
    const subtotal = items.reduce((sum, item) => sum + item.subtotal, 0);

    // Get tax rate from settings or use default
    const taxRate = checkoutSettings?.tax_rate || 0.08;
    const tax = subtotal * taxRate;

    const total = subtotal + tax + bookingTotal.tip - bookingTotal.discount;

    setBookingTotal({
      subtotal,
      discount: bookingTotal.discount,
      tax,
      tip: bookingTotal.tip,
      total,
    });
  };

  // ========== Appointment Processing ==========

  // Create appointment (replaces order creation)
  const createAppointment = async (appointmentData) => {
    setLoading((prev) => ({ ...prev, appointment: true }));
    setError(null);

    try {
      // Prepare appointment data
      const appointment = await checkoutApi.createAppointment({
        customer: selectedCustomer?.id || appointmentData.customer_id,
        employee: selectedEmployee?.id || appointmentData.employee_id,
        start_time: selectedTime || appointmentData.start_time,
        notes_from_customer: appointmentData.notes || '',
        payment_status: 'pending',
        status: 'pending',
        appointment_services: cartItems.map((item) => ({
          service: item.service_id,
          quantity: item.quantity,
          base_price: item.price,
          duration: item.duration,
          buffer_time: 0,
          notes: item.notes || '',
        })),
        // Add any appointment add-ons if needed
        appointment_add_ons: [],
      });

      setCurrentAppointment(appointment);
      return appointment;
    } catch (err) {
      setError(err.message);
      console.error("Failed to create appointment:", err);
      throw err;
    } finally {
      setLoading((prev) => ({ ...prev, appointment: false }));
    }
  };

  // Process payment for appointment
  const processPayment = async (paymentData) => {
    setLoading((prev) => ({ ...prev, payment: true }));
    setError(null);

    try {
      const payment = await checkoutApi.processPayment(paymentData);
      return payment;
    } catch (err) {
      setError(err.message);
      console.error("Failed to process payment:", err);
      throw err;
    } finally {
      setLoading((prev) => ({ ...prev, payment: false }));
    }
  };

  // Complete booking process
  const completeBooking = async (customerId, paymentMethods, bookingOptions = {}) => {
    try {
      // 1. Create appointment
      const appointmentData = {
        customer_id: customerId,
        employee_id: selectedEmployee?.id,
        start_time: selectedTime,
        notes: bookingOptions.notes || "",
        ...bookingOptions,
      };

      const appointment = await createAppointment(appointmentData);

      // 2. Process payment if required
      let payment = null;
      if (paymentMethods && paymentMethods.length > 0) {
        const paymentData = {
          appointment_id: appointment.id,
          payment_methods: paymentMethods,
          customer_id: customerId,
          amount: bookingTotal.total,
        };

        payment = await processPayment(paymentData);
      }

      // 3. Clear cart after successful booking
      clearCart();

      return { appointment, payment };
    } catch (err) {
      console.error("Booking failed:", err);
      throw err;
    }
  };

  // ========== Settings Management ==========

  // Load checkout settings
  const loadCheckoutSettings = async () => {
    try {
      const settings = await checkoutApi.getCheckoutSettings();
      setCheckoutSettings(settings);
      return settings;
    } catch (err) {
      setError(err.message);
      console.error("Failed to load checkout settings:", err);
    }
  };

  // Update settings
  const updateSettings = async (settingsData) => {
    try {
      const settings = await checkoutApi.updateCheckoutSettings(settingsData);
      setCheckoutSettings(settings);
      return settings;
    } catch (err) {
      setError(err.message);
      console.error("Failed to update checkout settings:", err);
    }
  };

  // ========== Customer Selection Helpers ==========

  // Set selected customer
  const selectCustomer = (customer) => {
    setSelectedCustomer(customer);
  };

  // Set selected employee
  const selectEmployee = (employee) => {
    setSelectedEmployee(employee);
    // Clear time slots when employee changes
    setAvailableTimeSlots({});
    setSelectedTime(null);
  };

  // Set selected date
  const selectDate = (date) => {
    setSelectedDate(date);
    // Clear time slots when date changes
    setAvailableTimeSlots({});
    setSelectedTime(null);
  };

  // Set selected time
  const selectTime = (time) => {
    setSelectedTime(time);
  };

  // ========== Effects ==========

  // When cart items change, update total
  useEffect(() => {
    updateBookingTotal();
  }, [cartItems]);

  // Initialize data on mount
  useEffect(() => {
    loadCheckoutSettings();
    loadCurrentBusiness();
  }, []);

  // ========== Return Values ==========

  return {
    // Data
    services,
    serviceCategories,
    staff,
    customers,
    checkoutSettings,
    currentBusiness,
    cartItems,
    currentAppointment,
    bookingTotal,

    // Selection states
    selectedCustomer,
    selectedEmployee,
    selectedDate,
    selectedTime,
    availableTimeSlots,

    // Loading states
    loading,
    error,

    // API operations
    loadServices,
    loadServiceCategories,
    loadStaff,
    loadCustomers,
    loadCurrentBusiness,
    getEmployeeServices,
    getStaffAvailability,
    loadAvailableTimeSlots,

    // Cart management
    addServiceToCart,
    updateCartItem,
    removeFromCart,
    clearCart,

    // Selection methods
    selectCustomer,
    selectEmployee,
    selectDate,
    selectTime,

    // Appointment processing
    createAppointment,
    processPayment,
    completeBooking,

    // Settings
    loadCheckoutSettings,
    updateSettings,

    // Utilities
    setError,
    setBookingTotal,
  };
}

export default useCheckout;
