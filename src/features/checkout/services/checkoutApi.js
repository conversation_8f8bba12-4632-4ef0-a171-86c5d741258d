// checkoutApi.js - API service for checkout/POS system functionality

const API_BASE_URL = import.meta.env.VITE_API_URL || "http://localhost:8000/api/v1";

class CheckoutApiService {
  constructor() {
    this.baseURL = API_BASE_URL;
  }

  // Helper method to get auth token
  getAuthToken() {
    return (
      localStorage.getItem("auth_token") || sessionStorage.getItem("auth_token")
    );
  }

  // Helper method to make API requests
  async makeRequest(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;

    const authToken = this.getAuthToken();
    const defaultOptions = {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
      },
      ...options,
    };

    try {
      const response = await fetch(url, defaultOptions);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message ||
            errorData.error ||
            errorData.detail ||
            `HTTP error! status: ${response.status}`
        );
      }

      return await response.json();
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error);
      throw error;
    }
  }

  // ========== Services Management API ==========

  // Get services list
  async getServices(params = {}) {
    const queryParams = new URLSearchParams();

    if (params.category) queryParams.append("category", params.category);
    if (params.show_online !== undefined) queryParams.append("show_online", params.show_online);
    if (params.search) queryParams.append("search", params.search);
    if (params.ordering) queryParams.append("ordering", params.ordering);

    const queryString = queryParams.toString();
    const endpoint = `/services/${queryString ? `?${queryString}` : ""}`;

    return await this.makeRequest(endpoint);
  }

  // Get service details
  async getService(serviceId) {
    return await this.makeRequest(`/services/${serviceId}/`);
  }

  // Get service categories
  async getServiceCategories() {
    return await this.makeRequest("/service-categories/");
  }

  // ========== Staff/Employees Management API ==========

  // Get employees list
  async getStaff(params = {}) {
    const queryParams = new URLSearchParams();

    if (params.is_active !== undefined) queryParams.append("is_active", params.is_active);
    if (params.search) queryParams.append("search", params.search);

    const queryString = queryParams.toString();
    const endpoint = `/employees/${queryString ? `?${queryString}` : ""}`;

    return await this.makeRequest(endpoint);
  }

  // Get current employee info
  async getCurrentEmployee() {
    return await this.makeRequest("/employees/me/");
  }

  // Get employee services
  async getEmployeeServices(employeeId, params = {}) {
    const queryParams = new URLSearchParams();
    if (params.category) queryParams.append("category", params.category);

    const queryString = queryParams.toString();
    const endpoint = `/employees/${employeeId}/services/${queryString ? `?${queryString}` : ""}`;

    return await this.makeRequest(endpoint);
  }

  // Get staff availability (using actual endpoint)
  async getStaffAvailability(date, serviceId, employeeId = null) {
    const queryParams = new URLSearchParams();
    queryParams.append("date", date);
    queryParams.append("service_id", serviceId);
    if (employeeId) queryParams.append("employee_id", employeeId);

    const queryString = queryParams.toString();
    const endpoint = `/appointments/available-times/?${queryString}`;

    return await this.makeRequest(endpoint);
  }

  // ========== Customer Management API ==========

  // Get customers for current business
  async getCustomers(params = {}) {
    const queryParams = new URLSearchParams();

    if (params.search) queryParams.append("q", params.search);
    if (params.page) queryParams.append("page", params.page);
    if (params.fields) queryParams.append("fields", params.fields);

    const queryString = queryParams.toString();
    const endpoint = `/business-customers/me/${queryString ? `?${queryString}` : ""}`;

    return await this.makeRequest(endpoint);
  }

  // Get customer details
  async getCustomer(customerId) {
    return await this.makeRequest(`/business-customers/me/${customerId}/`);
  }

  // ========== Appointment/Booking Processing API ==========

  // Create appointment (matching backend data structure)
  async createAppointment(appointmentData) {
    // Transform data to match backend expectations
    const backendData = {
      customer: appointmentData.customer_id,
      employee: appointmentData.employee_id,
      start_time: appointmentData.start_time,
      notes_from_customer: appointmentData.notes || '',
      payment_status: appointmentData.payment_status || 'pending',
      status: appointmentData.status || 'pending',
      appointment_services: appointmentData.services || [],
      appointment_add_ons: appointmentData.add_ons || []
    };

    return await this.makeRequest("/appointments/", {
      method: "POST",
      body: JSON.stringify(backendData),
    });
  }

  // Get appointment details
  async getAppointment(appointmentId) {
    return await this.makeRequest(`/appointments/${appointmentId}/`);
  }

  // Update appointment
  async updateAppointment(appointmentId, appointmentData) {
    return await this.makeRequest(`/appointments/${appointmentId}/`, {
      method: "PUT",
      body: JSON.stringify(appointmentData),
    });
  }

  // Cancel appointment
  async cancelAppointment(appointmentId) {
    return await this.makeRequest(`/appointments/${appointmentId}/`, {
      method: "DELETE",
    });
  }

  // Get available time slots
  async getAvailableTimeSlots(params = {}) {
    const queryParams = new URLSearchParams();
    
    if (params.date) queryParams.append("date", params.date);
    if (params.service_id) queryParams.append("service_id", params.service_id);
    if (params.employee_id) queryParams.append("employee_id", params.employee_id);
    if (params.business_id) queryParams.append("business_id", params.business_id);

    const queryString = queryParams.toString();
    const endpoint = `/appointments/available-times/${queryString ? `?${queryString}` : ""}`;

    return await this.makeRequest(endpoint);
  }

  // ========== Payment Processing API ==========

  // Get customer payment methods
  async getCustomerPaymentMethods() {
    return await this.makeRequest("/payments/methods/");
  }

  // Process payment (placeholder - needs actual payment integration)
  async processPayment(paymentData) {
    console.warn("Payment processing not yet implemented - this is a placeholder");
    // Return mock success for now
    return {
      id: Date.now(),
      status: 'completed',
      amount: paymentData.amount,
      payment_method: paymentData.payment_method
    };
  }

  // ========== Business Information API ==========

  // Get business info
  async getBusinessInfo(businessId) {
    return await this.makeRequest(`/businesses/${businessId}/`);
  }

  // Get current user's business
  async getCurrentBusiness() {
    const employee = await this.makeRequest("/employees/me/");
    return employee.business;
  }

  // ========== Legacy Methods (Not Available in Backend) ==========
  
  // Products - NOT AVAILABLE (salon/spa business doesn't typically sell products through POS)
  async getProducts(params = {}) {
    console.warn("Product management is not available in the current backend");
    return { results: [], count: 0 };
  }

  async getProduct(productId) {
    console.warn("Product management is not available in the current backend");
    throw new Error("Product functionality not available");
  }

  async scanProduct(barcode) {
    console.warn("Product scanning is not available in the current backend");
    throw new Error("Product scanning not available");
  }

  // Invoices - NOT IMPLEMENTED YET
  async getInvoices(params = {}) {
    console.warn("Invoice management is not yet implemented in the backend");
    return { results: [], count: 0 };
  }

  async createInvoice(invoiceData) {
    console.warn("Invoice creation is not yet implemented in the backend");
    throw new Error("Invoice functionality not yet available");
  }

  // Settings - NOT AVAILABLE AS DEDICATED ENDPOINT
  async getCheckoutSettings() {
    console.warn("Dedicated checkout settings endpoint not available");
    // Return default settings
    return {
      tax_rate: 0.08,
      appointment_interval: 15,
      currency: "USD",
      allow_walk_ins: true,
      require_payment: false
    };
  }

  async updateCheckoutSettings(settingsData) {
    console.warn("Checkout settings update not available");
    throw new Error("Settings update not available");
  }

  // Tax calculation - NOT IMPLEMENTED YET
  async calculateTax(orderData) {
    console.warn("Tax calculation API not implemented - using default calculation");
    const taxRate = 0.08; // 8% default
    const subtotal = orderData.subtotal || 0;
    return {
      tax_rate: taxRate,
      tax_amount: subtotal * taxRate,
      total: subtotal + (subtotal * taxRate)
    };
  }

  // Gift cards and loyalty - NOT IMPLEMENTED YET
  async validateGiftCard(cardNumber) {
    console.warn("Gift card functionality not yet implemented");
    throw new Error("Gift card functionality not available");
  }

  async useGiftCard(cardNumber, amount) {
    console.warn("Gift card functionality not yet implemented");
    throw new Error("Gift card functionality not available");
  }

  async getCustomerPoints(customerId) {
    console.warn("Customer points system not yet implemented");
    return { points: 0, value: 0 };
  }

  async usePoints(customerId, points) {
    console.warn("Customer points system not yet implemented");
    throw new Error("Points system not available");
  }
}

// Export singleton instance
export const checkoutApi = new CheckoutApiService();

// Export individual methods for easier importing
export const {
  // Services management
  getServices,
  getService,
  getServiceCategories,
  
  // Staff management
  getStaff,
  getCurrentEmployee,
  getEmployeeServices,
  getStaffAvailability,
  
  // Customer management
  getCustomers,
  getCustomer,
  
  // Appointment processing (replaces orders)
  createAppointment,
  getAppointment,
  updateAppointment,
  cancelAppointment,
  getAvailableTimeSlots,
  
  // Payment processing
  getCustomerPaymentMethods,
  processPayment,
  
  // Business information
  getBusinessInfo,
  getCurrentBusiness,
  
  // Legacy/placeholder methods
  getProducts,
  getProduct,
  scanProduct,
  getInvoices,
  createInvoice,
  getCheckoutSettings,
  updateCheckoutSettings,
  calculateTax,
  validateGiftCard,
  useGiftCard,
  getCustomerPoints,
  usePoints,
} = checkoutApi;
