import React from 'react'
import { useEmployees } from '../hooks'

/**
 * Simple test component to verify Django API connection
 */
const EmployeeConnectionTest = () => {
  const { 
    employees, 
    currentEmployee, 
    loading, 
    error 
  } = useEmployees()

  if (loading) {
    return (
      <div className="bg-blue-50 border border-blue-200 rounded-md p-4 m-4">
        <div className="flex items-center">
          <div className="w-5 h-5 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mr-3"></div>
          <div className="text-sm">
            <span className="font-medium text-blue-800">Loading employees from Django API...</span>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4 m-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Django API Connection Failed</h3>
            <p className="mt-1 text-sm text-red-700">{error}</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-green-50 border border-green-200 rounded-md p-4 m-4">
      <div className="flex">
        <div className="flex-shrink-0">
          <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
        </div>
        <div className="ml-3">
          <h3 className="text-sm font-medium text-green-800">✅ Django API Connected Successfully!</h3>
          <div className="mt-2 text-sm text-green-700">
            <p><strong>Employees loaded:</strong> {employees.length}</p>
            {currentEmployee && (
              <p><strong>Current employee:</strong> {currentEmployee.name || currentEmployee.email}</p>
            )}
            
            <div className="mt-3">
              <p className="font-medium">Employee List:</p>
              <ul className="mt-1 space-y-1">
                {employees.map((emp, index) => (
                  <li key={emp.id} className="flex items-center space-x-2">
                    <span className="w-6 h-6 bg-blue-500 text-white text-xs rounded-full flex items-center justify-center">
                      {emp.avatar}
                    </span>
                    <span>{emp.name || emp.email}</span>
                    {emp.role && <span className="text-xs text-gray-500">({emp.role})</span>}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default EmployeeConnectionTest 