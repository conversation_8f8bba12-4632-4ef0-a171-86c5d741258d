import React from 'react'
import EmployeeAvatar from './EmployeeAvatar'

const EmployeeListItem = ({ employee, isSelected, onClick }) => {
  return (
    <div
      onClick={() => onClick(employee)}
      className={`flex items-center gap-3 p-4 cursor-pointer border-b border-gray-100 hover:bg-gray-50 transition-colors ${
        isSelected ? 'bg-blue-50 border-blue-200' : ''
      }`}
    >
      <EmployeeAvatar employee={employee} size="md" />
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2">
          <h3 className="font-medium text-gray-900 truncate">{employee.full_name}</h3>
          {!employee.is_active && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
              Inactive
            </span>
          )}
        </div>
        <p className="text-sm text-gray-600 truncate">{employee.user_details?.email}</p>
        <p className="text-xs text-gray-500">{employee.stylist_level_display}</p>
      </div>
      <div className="flex-shrink-0">
        <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
        </svg>
      </div>
    </div>
  )
}

export default EmployeeListItem 