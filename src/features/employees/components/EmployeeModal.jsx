import React, { useState, useEffect } from 'react'
import { employeeApiService } from '../../../features/employees/services'

const EmployeeModal = ({ isOpen, onClose, employee, onSave }) => {
  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    email: '',
    phone_number: '',
    employee_type: 'service_provider',
    is_active: true,
    accept_online_bookings: true,
    calendar_sync_enabled: false,
    calendar_provider: '',
    profile_image: null
  })
  const [errors, setErrors] = useState({})
  const [isLoading, setIsLoading] = useState(false)
  const [previewImage, setPreviewImage] = useState(null)

  // Populate form when editing existing employee
  useEffect(() => {
    if (employee) {
      setFormData({
        first_name: employee.user_details?.first_name || '',
        last_name: employee.user_details?.last_name || '',
        email: employee.user_details?.email || '',
        phone_number: employee.user_details?.phone_number || '',
        employee_type: employee.employee_type || 'service_provider',
        is_active: employee.is_active !== undefined ? employee.is_active : true,
        accept_online_bookings: employee.accept_online_bookings !== undefined ? employee.accept_online_bookings : true,
        calendar_sync_enabled: employee.calendar_sync_enabled || false,
        calendar_provider: employee.calendar_provider || '',
        profile_image: null
      })
      setPreviewImage(employee.profile_image)
    } else {
      // Reset form for new employee
      setFormData({
        first_name: '',
        last_name: '',
        email: '',
        phone_number: '',
        employee_type: 'service_provider',
        is_active: true,
        accept_online_bookings: true,
        calendar_sync_enabled: false,
        calendar_provider: '',
        profile_image: null
      })
      setPreviewImage(null)
    }
    setErrors({})
  }, [employee, isOpen])

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }))
    }
  }

  const handleImageChange = (e) => {
    const file = e.target.files[0]
    if (file) {
      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        setErrors(prev => ({ ...prev, profile_image: 'Image size must be less than 5MB' }))
        return
      }
      
      setFormData(prev => ({ ...prev, profile_image: file }))
      
      // Create preview
      const reader = new FileReader()
      reader.onload = (e) => setPreviewImage(e.target.result)
      reader.readAsDataURL(file)
      
      // Clear error
      if (errors.profile_image) {
        setErrors(prev => ({ ...prev, profile_image: '' }))
      }
    }
  }

  const validateForm = () => {
    const newErrors = {}
    
    if (!formData.first_name.trim()) newErrors.first_name = 'First name is required'
    if (!formData.last_name.trim()) newErrors.last_name = 'Last name is required'
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required'
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address'
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!validateForm()) return
    
    setIsLoading(true)
    try {
      // Prepare data for API call
      const submitData = {
        user_data: {
          first_name: formData.first_name,
          last_name: formData.last_name,
          email: formData.email,
          phone_number: formData.phone_number
        },
        employee_type: formData.employee_type,
        is_active: formData.is_active,
        accept_online_bookings: formData.accept_online_bookings,
        calendar_sync_enabled: formData.calendar_sync_enabled,
        calendar_provider: formData.calendar_provider
      }
      
      // Add profile image if present
      if (formData.profile_image) {
        submitData.profile_image = formData.profile_image
      }

      let result
      if (employee) {
        // Update existing employee
        result = await employeeApiService.updateEmployee(employee.id, submitData)
      } else {
        // Create new employee
        result = await employeeApiService.createEmployee(submitData)
      }

      onSave(result)
      onClose()
    } catch (error) {
      console.error('Failed to save employee:', error)
      setErrors({ submit: error.message || 'Failed to save employee. Please try again.' })
    } finally {
      setIsLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-2 sm:p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[95vh] sm:max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 sm:p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            {employee ? 'Edit Employee' : 'Add New Employee'}
          </h2>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-md"
            disabled={isLoading}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="flex-1 overflow-y-auto">
          <div className="p-4 sm:p-6 space-y-6">
            {/* Profile Image */}
            <div className="flex items-start gap-4">
              <div className="flex flex-col items-center">
                <div className="w-20 h-20 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                  {previewImage ? (
                    <img src={previewImage} alt="Preview" className="w-full h-full object-cover" />
                  ) : (
                    <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  )}
                </div>
                <label htmlFor="profile_image" className="mt-2 cursor-pointer text-xs text-blue-600 hover:text-blue-700">
                  {previewImage ? 'Change Photo' : 'Add Photo'}
                </label>
                <input
                  type="file"
                  id="profile_image"
                  name="profile_image"
                  accept="image/*"
                  onChange={handleImageChange}
                  className="hidden"
                  disabled={isLoading}
                />
              </div>
              <div className="flex-1">
                {errors.profile_image && (
                  <p className="text-red-600 text-sm">{errors.profile_image}</p>
                )}
                <p className="text-xs text-gray-500 mt-1">
                  Upload a profile photo (optional). Max size: 5MB. Supported formats: JPG, PNG, GIF.
                </p>
              </div>
            </div>

            {/* Personal Information */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <label htmlFor="first_name" className="block text-sm font-medium text-gray-700 mb-1">
                  First Name *
                </label>
                <input
                  type="text"
                  id="first_name"
                  name="first_name"
                  value={formData.first_name}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-md focus:ring-blue-500 focus:border-blue-500 ${
                    errors.first_name ? 'border-red-300' : 'border-gray-300'
                  }`}
                  disabled={isLoading}
                />
                {errors.first_name && <p className="text-red-600 text-sm mt-1">{errors.first_name}</p>}
              </div>

              <div>
                <label htmlFor="last_name" className="block text-sm font-medium text-gray-700 mb-1">
                  Last Name *
                </label>
                <input
                  type="text"
                  id="last_name"
                  name="last_name"
                  value={formData.last_name}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-md focus:ring-blue-500 focus:border-blue-500 ${
                    errors.last_name ? 'border-red-300' : 'border-gray-300'
                  }`}
                  disabled={isLoading}
                />
                {errors.last_name && <p className="text-red-600 text-sm mt-1">{errors.last_name}</p>}
              </div>
            </div>

            {/* Contact Information */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                  Email Address *
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-md focus:ring-blue-500 focus:border-blue-500 ${
                    errors.email ? 'border-red-300' : 'border-gray-300'
                  }`}
                  disabled={isLoading}
                />
                {errors.email && <p className="text-red-600 text-sm mt-1">{errors.email}</p>}
              </div>

              <div>
                <label htmlFor="phone_number" className="block text-sm font-medium text-gray-700 mb-1">
                  Phone Number
                </label>
                <input
                  type="tel"
                  id="phone_number"
                  name="phone_number"
                  value={formData.phone_number}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  disabled={isLoading}
                />
              </div>
            </div>

            {/* Employee Settings */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Employee Settings</h3>
              
              <div>
                <label htmlFor="employee_type" className="block text-sm font-medium text-gray-700 mb-1">
                  Employee Type
                </label>
                <select
                  id="employee_type"
                  name="employee_type"
                  value={formData.employee_type}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  disabled={isLoading}
                >
                  <option value="service_provider">Service Provider</option>
                  <option value="admin">Account Admin</option>
                </select>
              </div>

              <div className="space-y-3">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="is_active"
                    name="is_active"
                    checked={formData.is_active}
                    onChange={handleInputChange}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    disabled={isLoading}
                  />
                  <label htmlFor="is_active" className="ml-2 text-sm text-gray-700">
                    Active Employee
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="accept_online_bookings"
                    name="accept_online_bookings"
                    checked={formData.accept_online_bookings}
                    onChange={handleInputChange}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    disabled={isLoading}
                  />
                  <label htmlFor="accept_online_bookings" className="ml-2 text-sm text-gray-700">
                    Accept Online Bookings
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="calendar_sync_enabled"
                    name="calendar_sync_enabled"
                    checked={formData.calendar_sync_enabled}
                    onChange={handleInputChange}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    disabled={isLoading}
                  />
                  <label htmlFor="calendar_sync_enabled" className="ml-2 text-sm text-gray-700">
                    Enable Calendar Sync
                  </label>
                </div>

                {formData.calendar_sync_enabled && (
                  <div className="ml-6">
                    <label htmlFor="calendar_provider" className="block text-sm font-medium text-gray-700 mb-1">
                      Calendar Provider
                    </label>
                    <select
                      id="calendar_provider"
                      name="calendar_provider"
                      value={formData.calendar_provider}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                      disabled={isLoading}
                    >
                      <option value="">Select Provider</option>
                      <option value="google">Google Calendar</option>
                      <option value="outlook">Outlook Calendar</option>
                      <option value="apple">Apple Calendar</option>
                    </select>
                  </div>
                )}
              </div>
            </div>

            {/* Error Display */}
            {errors.submit && (
              <div className="p-4 bg-red-50 border border-red-200 rounded-md">
                <p className="text-red-600 text-sm">{errors.submit}</p>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="flex flex-col sm:flex-row justify-end gap-3 p-4 sm:p-6 border-t border-gray-200 bg-gray-50">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 w-full sm:w-auto"
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed w-full sm:w-auto"
              disabled={isLoading}
            >
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  {employee ? 'Updating...' : 'Creating...'}
                </div>
              ) : (
                employee ? 'Update Employee' : 'Create Employee'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default EmployeeModal 