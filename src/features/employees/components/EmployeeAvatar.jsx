import React from 'react'

const EmployeeAvatar = ({ employee, size = 'md' }) => {
  const sizeClasses = {
    sm: 'w-8 h-8 text-xs',
    md: 'w-12 h-12 text-sm',
    lg: 'w-20 h-20 text-xl',
    xl: 'w-32 h-32 text-3xl'
  }
  
  const avatar = employee.avatar || (employee.full_name ? employee.full_name.split(' ').map(n => n[0]).join('').toUpperCase() : 'U')
  const color = employee.color || '#3b82f6'
  
  if (employee.profile_image) {
    return (
      <img
        src={employee.profile_image}
        alt={employee.full_name}
        className={`${sizeClasses[size]} rounded-full object-cover border border-gray-200`}
      />
    )
  }
  
  return (
    <div
      className={`${sizeClasses[size]} rounded-full flex items-center justify-center text-white font-semibold`}
      style={{ backgroundColor: color }}
    >
      {avatar}
    </div>
  )
}

export default EmployeeAvatar 