import apiClient from './apiClient'

/**
 * Employee API Service
 * Connects to Django backend employee endpoints
 */
export class EmployeeApiService {
  
  /**
   * Get current employee info
   */
  async getCurrentEmployee() {
    try {
      const response = await apiClient.get('/employees/me/')
      return response.data
    } catch (error) {
      console.error('Failed to fetch current employee:', error)
      throw new Error(error.response?.data?.message || 'Failed to fetch employee information')
    }
  }

  /**
   * Get all employees (for calendar display)
   */
  async getAllEmployees() {
    try {
      const response = await apiClient.get('/employees/')
      // Handle paginated response from Django REST Framework
      const data = response.data
      return Array.isArray(data) ? data : (data.results || [])
    } catch (error) {
      console.error('Failed to fetch employees:', error)
      throw new Error(error.response?.data?.message || 'Failed to fetch employees')
    }
  }

  /**
   * Get employee working hours (only supports current user)
   */
  async getEmployeeWorkingHours(employeeId = 'me') {
    try {
      // Backend only supports current user's working hours
      if (employeeId !== 'me') {
        console.warn(`Working hours only available for current user, not employee ${employeeId}`)
        // Return default working hours for other employees
        return this.getDefaultWorkingHours()
      }
      
      const response = await apiClient.get('/employees/me/working-hours/')
      return this.transformWorkingHoursResponse(response.data)
    } catch (error) {
      console.error('Failed to fetch working hours:', error)
      // Return default working hours if API fails
      return this.getDefaultWorkingHours()
    }
  }

  /**
   * Get current employee working hours specifically 
   */
  async getCurrentEmployeeWorkingHours() {
    return this.getEmployeeWorkingHours('me')
  }

  /**
   * Transform frontend working hours format to backend format
   */
  transformWorkingHoursRequest(frontendWorkingHours) {
    // Frontend format: { monday: { isWorking: true, startTime: '09:00', endTime: '17:00' } }
    // Backend expects: { working_hours: { monday: { start: '09:00', end: '17:00', is_active: true } } }
    
    const backendWorkingHours = {}
    
    Object.entries(frontendWorkingHours).forEach(([day, hours]) => {
      backendWorkingHours[day] = {
        start: hours.startTime,
        end: hours.endTime,
        is_active: hours.isWorking
      }
    })
    
    return { working_hours: backendWorkingHours }
  }

  /**
   * Update employee working hours (only supports current user)
   */
  async updateEmployeeWorkingHours(workingHours, employeeId = 'me') {
    try {
      // Backend only supports updating current user's working hours
      if (employeeId !== 'me') {
        throw new Error('Can only update working hours for current user')
      }
      
      console.log('🔄 Transforming working hours from frontend to backend format:', workingHours)
      
      // Transform frontend format to backend format
      const backendFormat = this.transformWorkingHoursRequest(workingHours)
      console.log('📡 Sending to backend:', backendFormat)
      
      const response = await apiClient.post('/employees/me/working-hours/', backendFormat)
      console.log('✅ Backend response:', response.data)
      return response.data
    } catch (error) {
      console.error('Failed to update working hours:', error)
      throw new Error(error.response?.data?.message || 'Failed to update working hours')
    }
  }

  /**
   * Get default working hours for employees when actual hours aren't available
   */
  getDefaultWorkingHours() {
    return {
      monday: { isWorking: true, startTime: '09:00', endTime: '17:00' },
      tuesday: { isWorking: true, startTime: '09:00', endTime: '17:00' },
      wednesday: { isWorking: true, startTime: '09:00', endTime: '17:00' },
      thursday: { isWorking: true, startTime: '09:00', endTime: '17:00' },
      friday: { isWorking: true, startTime: '09:00', endTime: '17:00' },
      saturday: { isWorking: false, startTime: '09:00', endTime: '17:00' },
      sunday: { isWorking: false, startTime: '09:00', endTime: '17:00' }
    }
  }

  /**
   * Transform backend working hours response to frontend format
   */
  transformWorkingHoursResponse(backendData) {
    // Backend returns: { working_hours: [{ day: 'monday', start_time: '09:00', end_time: '17:00', is_active: true }] }
    // Frontend expects: { monday: { isWorking: true, startTime: '09:00', endTime: '17:00' } }
    
    console.log('🔄 Transforming backend working hours to frontend format:', backendData)
    
    const workingHours = this.getDefaultWorkingHours()
    
    if (backendData.working_hours) {
      backendData.working_hours.forEach(dayHours => {
        if (workingHours[dayHours.day]) {
          workingHours[dayHours.day] = {
            isWorking: dayHours.is_active,
            startTime: dayHours.start_time,
            endTime: dayHours.end_time
          }
          console.log(`📅 Transformed ${dayHours.day}:`, {
            isWorking: dayHours.is_active,
            startTime: dayHours.start_time,
            endTime: dayHours.end_time
          })
        }
      })
    } else {
      console.warn('⚠️ No working_hours found in backend data, using defaults')
    }
    
    console.log('✅ Final transformed working hours:', workingHours)
    return workingHours
  }

  /**
   * Get employee permissions
   */
  async getEmployeePermissions(employeeId = 'me') {
    try {
      const endpoint = employeeId === 'me' ? '/employees/me/permissions/' : `/employees/${employeeId}/permissions/`
      const response = await apiClient.get(endpoint)
      return response.data
    } catch (error) {
      console.error('Failed to fetch permissions:', error)
      throw new Error(error.response?.data?.message || 'Failed to fetch permissions')
    }
  }

  /**
   * Get employee calendar configurations
   */
  async getEmployeeCalendarConfigs(employeeId = 'me') {
    try {
      const endpoint = employeeId === 'me' ? '/employees/me/calendar-configs/' : `/employees/${employeeId}/calendar-configs/`
      const response = await apiClient.get(endpoint)
      return response.data
    } catch (error) {
      console.error('Failed to fetch calendar configs:', error)
      throw new Error(error.response?.data?.message || 'Failed to fetch calendar configurations')
    }
  }

  /**
   * Update employee calendar configurations
   */
  async updateEmployeeCalendarConfigs(configs, employeeId = 'me') {
    try {
      const endpoint = employeeId === 'me' ? '/employees/me/calendar-configs/' : `/employees/${employeeId}/calendar-configs/`
      const response = await apiClient.put(endpoint, configs)
      return response.data
    } catch (error) {
      console.error('Failed to update calendar configs:', error)
      throw new Error(error.response?.data?.message || 'Failed to update calendar configurations')
    }
  }

  /**
   * Get employee appointments
   */
  async getEmployeeAppointments(employeeId = 'me', params = {}) {
    try {
      const endpoint = employeeId === 'me' ? '/employees/me/appointments/' : `/employees/${employeeId}/appointments/`
      const response = await apiClient.get(endpoint, { params })
      return response.data
    } catch (error) {
      console.error('Failed to fetch employee appointments:', error)
      throw new Error(error.response?.data?.message || 'Failed to fetch appointments')
    }
  }

  /**
   * Create new employee
   */
  async createEmployee(employeeData) {
    try {
      const response = await apiClient.post('/employees/', employeeData)
      return response.data
    } catch (error) {
      console.error('Failed to create employee:', error)
      throw new Error(error.response?.data?.message || 'Failed to create employee')
    }
  }

  /**
   * Update employee information
   */
  async updateEmployee(employeeId, employeeData) {
    try {
      const response = await apiClient.put(`/employees/${employeeId}/`, employeeData)
      return response.data
    } catch (error) {
      console.error('Failed to update employee:', error)
      throw new Error(error.response?.data?.message || 'Failed to update employee')
    }
  }

  /**
   * Delete employee
   */
  async deleteEmployee(employeeId) {
    try {
      const response = await apiClient.delete(`/employees/${employeeId}/`)
      return response.data
    } catch (error) {
      console.error('Failed to delete employee:', error)
      throw new Error(error.response?.data?.message || 'Failed to delete employee')
    }
  }
}

// Export singleton instance
export const employeeApiService = new EmployeeApiService()
export default employeeApiService 