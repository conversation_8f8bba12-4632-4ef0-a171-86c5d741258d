import apiClient from '../../employees/services/apiClient'

/**
 * Booking Rules API Service
 * Connects to Django backend booking-rules endpoints
 */
export class BookingRulesApiService {
  
  /**
   * Get business booking rules
   */
  async getBookingRules(businessId = null) {
    try {
      const params = businessId ? { business_id: businessId } : {}
      const response = await apiClient.get('/booking-rules/', { params })
      
      // Handle paginated response from Django REST Framework
      const data = response.data
      const results = Array.isArray(data) ? data : (data.results || [])
      
      // Return the first booking rule (assuming one per business)
      return results.length > 0 ? results[0] : null
    } catch (error) {
      console.error('Failed to fetch booking rules:', error)
      throw new Error(error.response?.data?.message || 'Failed to fetch booking rules')
    }
  }

  /**
   * Create new booking rules for business
   */
  async createBookingRules(bookingRulesData) {
    try {
      const response = await apiClient.post('/booking-rules/', bookingRulesData)
      return response.data
    } catch (error) {
      console.error('Failed to create booking rules:', error)
      throw new Error(error.response?.data?.message || 'Failed to create booking rules')
    }
  }

  /**
   * Update existing booking rules
   */
  async updateBookingRules(id, bookingRulesData) {
    try {
      const response = await apiClient.patch(`/booking-rules/${id}/`, bookingRulesData)
      return response.data
    } catch (error) {
      console.error('Failed to update booking rules:', error)
      throw new Error(error.response?.data?.message || 'Failed to update booking rules')
    }
  }

  /**
   * Reset booking rules to defaults
   */
  async resetBookingRules(id) {
    try {
      // Default booking rules configuration
      const defaultRules = {
        timezone: "America/Los_Angeles",
        currency: "USD",
        max_days_in_advance: 30,
        min_hours_before: 2,
        appointment_interval: 15,
        allow_cancellation: true,
        cancellation_hours_before: 24,
        cancellation_policy: null,
        allow_rescheduling: true,
        rescheduling_hours_before: 24,
        require_payment: false,
        deposit_percentage: "0.00"
      }
      
      return await this.updateBookingRules(id, defaultRules)
    } catch (error) {
      console.error('Failed to reset booking rules:', error)
      throw new Error(error.response?.data?.message || 'Failed to reset booking rules')
    }
  }

  /**
   * Transform frontend booking rules to backend format
   */
  transformToBackendFormat(frontendRules) {
    // Store frontend-only settings before sending to backend
    this.storeFrontendOnlySettings(frontendRules)
    
    return {
      timezone: frontendRules.timezone || "America/Los_Angeles",
      currency: frontendRules.currency || "USD",
      max_days_in_advance: parseInt(frontendRules.maxDaysInAdvance) || 30,
      min_hours_before: parseInt(frontendRules.minHoursBefore) || 2,
      appointment_interval: parseInt(frontendRules.appointmentInterval) || 15,
      allow_cancellation: frontendRules.allowCancellation ?? true,
      cancellation_hours_before: parseInt(frontendRules.cancellationHoursBefore) || 24,
      cancellation_policy: frontendRules.cancellationPolicy || null,
      allow_rescheduling: frontendRules.allowRescheduling ?? true,
      rescheduling_hours_before: parseInt(frontendRules.reschedulingHoursBefore) || 24,
      require_payment: frontendRules.requirePayment ?? false,
      deposit_percentage: frontendRules.depositPercentage ? parseFloat(frontendRules.depositPercentage).toFixed(2) : "0.00"
      // Note: Frontend-only settings like allowCustomerWaitlistAddition are not sent to backend
    }
  }

  /**
   * Transform backend booking rules to frontend format
   */
  transformToFrontendFormat(backendRules) {
    if (!backendRules) return this.getDefaultFrontendRules()
    
    return {
      id: backendRules.id,
      timezone: backendRules.timezone,
      currency: backendRules.currency,
      maxDaysInAdvance: backendRules.max_days_in_advance,
      minHoursBefore: backendRules.min_hours_before,
      appointmentInterval: backendRules.appointment_interval,
      allowCancellation: backendRules.allow_cancellation,
      cancellationHoursBefore: backendRules.cancellation_hours_before,
      cancellationPolicy: backendRules.cancellation_policy,
      allowRescheduling: backendRules.allow_rescheduling,
      reschedulingHoursBefore: backendRules.rescheduling_hours_before,
      requirePayment: backendRules.require_payment,
      depositPercentage: parseFloat(backendRules.deposit_percentage) || 0,
      createdAt: backendRules.created_at,
      updatedAt: backendRules.updated_at,
      // Frontend-only settings - preserve from localStorage or use defaults
      ...this.getFrontendOnlySettings()
    }
  }

  /**
   * Get default frontend booking rules
   */
  getDefaultFrontendRules() {
    return {
      id: null,
      timezone: "America/Los_Angeles",
      currency: "USD",
      maxDaysInAdvance: 30,
      minHoursBefore: 2,
      appointmentInterval: 15,
      allowCancellation: true,
      cancellationHoursBefore: 24,
      cancellationPolicy: null,
      allowRescheduling: true,
      reschedulingHoursBefore: 24,
      requirePayment: false,
      depositPercentage: 0,
      // Frontend-only waitlist settings (not stored in backend)
      allowCustomerWaitlistAddition: true,
      waitlistEmailNotifications: false,
      waitlistSMSNotifications: false,
      waitlistPushNotifications: false
    }
  }

  /**
   * Convert hours to different time units for display
   */
  convertHoursToDisplayUnit(hours) {
    if (hours >= 24 * 7) {
      return { value: Math.floor(hours / (24 * 7)), unit: 'weeks' }
    } else if (hours >= 24) {
      return { value: Math.floor(hours / 24), unit: 'days' }
    } else {
      return { value: hours, unit: 'hours' }
    }
  }

  /**
   * Convert display unit back to hours
   */
  convertDisplayUnitToHours(value, unit) {
    switch (unit) {
      case 'weeks':
        return value * 24 * 7
      case 'days':
        return value * 24
      case 'hours':
      default:
        return value
    }
  }

  /**
   * Store frontend-only settings in localStorage
   */
  storeFrontendOnlySettings(frontendRules) {
    const frontendOnlySettings = {
      allowCustomerWaitlistAddition: frontendRules.allowCustomerWaitlistAddition ?? true,
      waitlistEmailNotifications: frontendRules.waitlistEmailNotifications ?? false,
      waitlistSMSNotifications: frontendRules.waitlistSMSNotifications ?? false,
      waitlistPushNotifications: frontendRules.waitlistPushNotifications ?? false
    }
    
    try {
      localStorage.setItem('booking_rules_frontend_settings', JSON.stringify(frontendOnlySettings))
    } catch (error) {
      console.warn('Failed to store frontend-only booking settings:', error)
    }
  }

  /**
   * Get frontend-only settings from localStorage
   */
  getFrontendOnlySettings() {
    try {
      const stored = localStorage.getItem('booking_rules_frontend_settings')
      if (stored) {
        return JSON.parse(stored)
      }
    } catch (error) {
      console.warn('Failed to load frontend-only booking settings:', error)
    }
    
    // Return defaults if not found or error
    return {
      allowCustomerWaitlistAddition: true,
      waitlistEmailNotifications: false,
      waitlistSMSNotifications: false,
      waitlistPushNotifications: false
    }
  }
}

// Export singleton instance
export const bookingRulesApiService = new BookingRulesApiService()
export default bookingRulesApiService 