import React, { useState } from 'react'
import { 
  CalendarDaysIcon,
  UserGroupIcon,
  XCircleIcon,
  BookOpenIcon,
  Cog6ToothIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline'
import {
  AppointmentTab,
  BookingTab,
  WaitlistTab,
  CancellationTab,
  ToggleSwitch
} from './appointment-rules'
import { useBookingRules } from '../hooks/useBookingRules'

/**
 * Main Online Appointment Rules Component with Tabs
 */
const OnlineAppointmentRules = () => {
  const [activeTab, setActiveTab] = useState('appointment')
  const [resetConfirmation, setResetConfirmation] = useState(false)
  
  // Use booking rules hook
  const {
    bookingRules,
    isLoading,
    isSaving,
    error,
    hasChanges,
    updateRule,
    updateRules,
    saveBookingRules,
    resetBookingRules,
    cancelChanges,
    convertHoursToDisplayUnit,
    convertDisplayUnitToHours
  } = useBookingRules()

  const handleReset = async () => {
    try {
      await resetBookingRules()
      setResetConfirmation(false)
      console.log('✅ Booking rules reset successfully')
    } catch (err) {
      console.error('❌ Failed to reset booking rules:', err)
    }
  }

  const tabs = [
    { id: 'appointment', label: 'Appointment', icon: CalendarDaysIcon },
    { id: 'booking', label: 'Booking', icon: BookOpenIcon },
    { id: 'waitlist', label: 'Waitlist', icon: UserGroupIcon },
    { id: 'cancellation', label: 'Cancellation', icon: XCircleIcon }
  ]

  const renderTabContent = () => {
    const commonProps = {
      bookingRules,
      updateRule,
      updateRules,
      convertHoursToDisplayUnit,
      convertDisplayUnitToHours
    }

    switch (activeTab) {
      case 'appointment':
        return <AppointmentTab {...commonProps} />
      case 'booking':
        return <BookingTab {...commonProps} />
      case 'waitlist':
        return <WaitlistTab {...commonProps} />
      case 'cancellation':
        return <CancellationTab {...commonProps} />
      default:
        return null
    }
  }

  return (
    <div className="w-full h-full">
      {/* Tab Navigation */}
      <div className="border-b border-gray-200 mb-6 bg-white">
        <nav className="flex space-x-8 overflow-x-auto px-6">
          {tabs.map(tab => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center gap-3 py-4 px-3 border-b-2 font-medium text-base whitespace-nowrap ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="h-5 w-5" />
                {tab.label}
              </button>
            )
          })}
        </nav>
      </div>

      {/* Scrollable Content Wrapper */}
      <div className="max-h-[calc(100vh-200px)] overflow-y-auto px-6">
        {/* Error Display */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        {/* Loading State */}
        {isLoading ? (
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="animate-pulse space-y-4">
              <div className="h-4 bg-gray-200 rounded w-1/4"></div>
              <div className="h-32 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              <div className="h-24 bg-gray-200 rounded"></div>
            </div>
          </div>
        ) : (
          /* Tab Content */
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            {renderTabContent()}
          </div>
        )}

        {/* Reset Section */}
        <div className="mt-6 bg-gray-50 rounded-lg border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-900">Reset to Defaults</h3>
              <p className="text-sm text-gray-600">Restore all appointment rules to their default settings</p>
            </div>
            <div className="flex items-center gap-3">
              {resetConfirmation ? (
                <>
                  <span className="text-sm text-gray-600">Are you sure?</span>
                  <button
                    onClick={handleReset}
                    className="px-3 py-1 text-sm bg-red-600 text-white rounded-lg hover:bg-red-700"
                  >
                    <CheckCircleIcon className="h-4 w-4 inline mr-1" />
                    Yes, Reset
                  </button>
                  <button
                    onClick={() => setResetConfirmation(false)}
                    className="px-3 py-1 text-sm bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400"
                  >
                    Cancel
                  </button>
                </>
              ) : (
                <button
                  onClick={() => setResetConfirmation(true)}
                  className="px-4 py-2 text-sm bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                >
                  <Cog6ToothIcon className="h-4 w-4 inline mr-1" />
                  Reset All Settings
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Spacer for sticky buttons */}
        <div className="h-20"></div>
      </div>

      {/* Sticky Action Buttons */}
      <div className="fixed bottom-0 left-0 lg:left-72 xl:left-80 right-0 bg-white border-t border-gray-200 p-4 shadow-lg z-10">
        <div className="flex items-center justify-end gap-3 max-w-full">
          <button
            onClick={cancelChanges}
            disabled={!hasChanges}
            className="px-6 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Cancel
          </button>
          <button
            onClick={() => {
              // TODO: Connect to API after all tabs are ready
              console.log('Save clicked - API connection disabled for now')
              console.log('Current booking rules:', bookingRules)
            }}
            disabled={!hasChanges}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Save
          </button>
        </div>
      </div>
    </div>
  )
}

export default OnlineAppointmentRules 