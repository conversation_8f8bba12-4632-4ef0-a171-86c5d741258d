import React, { useState } from 'react'
import ToggleSwitch from './ToggleSwitch'

/**
 * Cancellation Configuration Tab Content
 */
const CancellationTab = ({ bookingRules, updateRule }) => {
  const [settings, setSettings] = useState({
    autoRefund: false
  })

  // Handle cancellation/rescheduling hours change
  const handleCancellationHoursChange = (hours) => {
    updateRule('cancellationHoursBefore', parseInt(hours))
    updateRule('reschedulingHoursBefore', parseInt(hours)) // Keep them in sync
  }

  // Handle rescheduling toggle
  const handleReschedulingToggle = (enabled) => {
    updateRule('allowRescheduling', enabled)
  }

  // Handle cancellation policy change
  const handleCancellationPolicyChange = (policy) => {
    updateRule('cancellationPolicy', policy)
  }

  return (
  <div className="space-y-6">
    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
      <h3 className="text-lg font-medium text-red-900 mb-2">Cancellation Policies</h3>
      <p className="text-red-700">Set up cancellation rules, penalties, and restrictions.</p>
    </div>
    
    <div className="space-y-4">
      {/* Customer Can Cancel & Reschedule Appointments Before */}
      <div className="border border-gray-200 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-2">Customer Can Cancel & Reschedule Appointments Before</h4>
        <p className="text-sm text-gray-600 mb-3">Set the minimum time before an appointment that customers can cancel or reschedule</p>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Hours before appointment</label>
          <div className="flex items-center space-x-2">
            <input 
              type="number" 
              value={bookingRules?.cancellationHoursBefore || 24} 
              min="0" 
              onChange={(e) => handleCancellationHoursChange(e.target.value)}
              className="w-20 border border-gray-300 rounded-lg px-3 py-2 text-center"
            />
            <span className="text-sm text-gray-600">hours</span>
          </div>
        </div>
      </div>

      {/* Automatically Refund for Pre-paid Appointments */}
      <div className="border border-gray-200 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-2">Automatically Refund for Pre-paid Appointments</h4>
        <p className="text-sm text-gray-600 mb-3">Enable automatic refunds for cancelled pre-paid appointments</p>
        <ToggleSwitch
          label="Enable automatic refunds"
          checked={settings.autoRefund}
          onChange={(checked) => setSettings(prev => ({ ...prev, autoRefund: checked }))}
        />
      </div>

      {/* Refund Policy */}
      <div className="border border-gray-200 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-2">Refund Policy</h4>
        <p className="text-sm text-gray-600 mb-3">Write your refund policy for customers</p>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Policy details</label>
          <textarea 
            className="w-full border border-gray-300 rounded-lg px-3 py-2" 
            rows="4"
            placeholder="Write your refund policy here..."
          />
        </div>
      </div>

      {/* Customer Can Reschedule Appointments */}
      <div className="border border-gray-200 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-2">Customer Can Reschedule Appointments</h4>
        <p className="text-sm text-gray-600 mb-3">Allow customers to reschedule their appointments online</p>
        <ToggleSwitch
          label="Enable appointment rescheduling"
          checked={bookingRules?.allowRescheduling ?? true}
          onChange={handleReschedulingToggle}
        />
      </div>

      {/* Appointment Cancellation Policy */}
      <div className="border border-gray-200 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-2">Appointment Cancellation Policy</h4>
        <p className="text-sm text-gray-600 mb-3">Write your appointment cancellation policy for customers</p>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Policy details</label>
          <textarea 
            value={bookingRules?.cancellationPolicy || ''}
            onChange={(e) => handleCancellationPolicyChange(e.target.value)}
            className="w-full border border-gray-300 rounded-lg px-3 py-2" 
            rows="4"
            placeholder="Write your appointment cancellation policy here..."
          />
        </div>
      </div>
    </div>
  </div>
  )
}

export default CancellationTab 