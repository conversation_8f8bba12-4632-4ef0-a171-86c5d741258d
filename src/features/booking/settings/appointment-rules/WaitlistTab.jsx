import React from 'react'
import ToggleSwitch from './ToggleSwitch'

/**
 * Waitlist Configuration Tab Content
 */
const WaitlistTab = ({ bookingRules, updateRule }) => {
  // Handle waitlist toggle
  const handleWaitlistToggle = (enabled) => {
    updateRule('allowCustomerWaitlistAddition', enabled)
  }

  // Handle notification toggles
  const handleEmailNotificationToggle = (enabled) => {
    updateRule('waitlistEmailNotifications', enabled)
  }

  const handleSMSNotificationToggle = (enabled) => {
    updateRule('waitlistSMSNotifications', enabled)
  }

  const handlePushNotificationToggle = (enabled) => {
    updateRule('waitlistPushNotifications', enabled)
  }

  return (
  <div className="space-y-6">
    <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
      <h3 className="text-lg font-medium text-purple-900 mb-2">Waitlist Management</h3>
      <p className="text-purple-700">Configure waitlist functionality and notification settings.</p>
    </div>
    
    <div className="space-y-4">
      {/* Waitlist Management Options */}
      <div className="border border-gray-200 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-2">Waitlist Management</h4>
        <p className="text-sm text-gray-600 mb-3">
          Customers can be added to a waitlist when your schedule is fully booked. If a customer cancels, you can manually select a customer from the waitlist or Vagaro can automatically notify your waitlist customers of the availability.
        </p>
        
        <div className="mb-4">
          <h5 className="text-sm font-medium text-gray-700 mb-3">Select a Waitlist type:</h5>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* You Pick */}
            <div className="border-2 border-blue-200 bg-blue-50 rounded-lg p-4 cursor-pointer hover:border-blue-300 transition-colors">
              <div className="flex flex-col items-center text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-3">
                  <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.121 2.122" />
                  </svg>
                </div>
                <h6 className="font-semibold text-gray-900 mb-2">You Pick</h6>
                <p className="text-xs text-gray-600">
                  Manually select a client from the waitlist and move them to an opening on your calendar. (Default)
                </p>
              </div>
            </div>

            {/* AI Pick - Placeholder */}
            <div className="border-2 border-gray-200 bg-white rounded-lg p-4 cursor-pointer hover:border-gray-300 transition-colors opacity-60">
              <div className="flex flex-col items-center text-center">
                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mb-3">
                  <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                </div>
                <h6 className="font-semibold text-gray-900 mb-2">AI Pick</h6>
                <p className="text-xs text-gray-600">
                  AI automatically selects the best customer from the waitlist based on various factors. (Placeholder)
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Customers can Add to Waitlist */}
      <div className="border border-gray-200 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-2">Customers can Add to Waitlist</h4>
        <p className="text-sm text-gray-600 mb-3">Allow customers to add themselves to the waitlist when no appointments are available</p>
        <ToggleSwitch
          label="Enable customer self-addition to waitlist"
          checked={bookingRules?.allowCustomerWaitlistAddition ?? true}
          onChange={handleWaitlistToggle}
        />
      </div>

      {/* Waitlist Notifications */}
      <div className="border border-gray-200 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-2">Waitlist Notifications</h4>
        <p className="text-sm text-gray-600 mb-3">Configure how customers are notified about waitlist updates and availability</p>
        <div className="space-y-3">
          <ToggleSwitch
            label="Send email notifications"
            checked={bookingRules?.waitlistEmailNotifications ?? false}
            onChange={handleEmailNotificationToggle}
          />
          <ToggleSwitch
            label="Send SMS notifications"
            checked={bookingRules?.waitlistSMSNotifications ?? false}
            onChange={handleSMSNotificationToggle}
          />
          <ToggleSwitch
            label="Send push notifications"
            checked={bookingRules?.waitlistPushNotifications ?? false}
            onChange={handlePushNotificationToggle}
          />
        </div>
      </div>
    </div>
  </div>
  )
}

export default WaitlistTab 