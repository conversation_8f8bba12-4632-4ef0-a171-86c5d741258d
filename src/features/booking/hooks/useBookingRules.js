import { useState, useEffect } from 'react'
import { bookingRulesApiService } from '../services/bookingRulesApiService'

/**
 * Custom hook for managing booking rules state
 */
export const useBookingRules = (businessId = null) => {
  const [bookingRules, setBookingRules] = useState(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState(null)
  const [hasChanges, setHasChanges] = useState(false)
  const [originalRules, setOriginalRules] = useState(null)

  // Load booking rules on mount
  useEffect(() => {
    fetchBookingRules()
  }, [businessId])

  /**
   * Fetch booking rules from API
   */
  const fetchBookingRules = async () => {
    try {
      setIsLoading(true)
      setError(null)
      
      const backendRules = await bookingRulesApiService.getBookingRules(businessId)
      const frontendRules = bookingRulesApiService.transformToFrontendFormat(backendRules)
      
      setBookingRules(frontendRules)
      setOriginalRules(frontendRules)
      setHasChanges(false)
      
      console.log('✅ Booking rules loaded:', frontendRules)
    } catch (err) {
      console.error('❌ Failed to load booking rules:', err)
      setError(err.message)
      
      // Set default rules if API fails
      const defaultRules = bookingRulesApiService.getDefaultFrontendRules()
      setBookingRules(defaultRules)
      setOriginalRules(defaultRules)
    } finally {
      setIsLoading(false)
    }
  }

  /**
   * Update a specific booking rule field
   */
  const updateRule = (field, value) => {
    setBookingRules(prev => {
      const newRules = { ...prev, [field]: value }
      
      // Store frontend-only settings immediately for real-time updates
      bookingRulesApiService.storeFrontendOnlySettings(newRules)
      
      // Check if rules have changed from original
      const hasActualChanges = JSON.stringify(newRules) !== JSON.stringify(originalRules)
      setHasChanges(hasActualChanges)
      
      console.log(`🔄 Updated ${field}:`, value)
      return newRules
    })
  }

  /**
   * Update multiple booking rule fields at once
   */
  const updateRules = (updates) => {
    setBookingRules(prev => {
      const newRules = { ...prev, ...updates }
      
      // Store frontend-only settings immediately for real-time updates
      bookingRulesApiService.storeFrontendOnlySettings(newRules)
      
      // Check if rules have changed from original
      const hasActualChanges = JSON.stringify(newRules) !== JSON.stringify(originalRules)
      setHasChanges(hasActualChanges)
      
      console.log('🔄 Updated multiple rules:', updates)
      return newRules
    })
  }

  /**
   * Save booking rules to API
   */
  const saveBookingRules = async () => {
    if (!bookingRules || !hasChanges) {
      console.log('⚠️ No changes to save')
      return
    }

    try {
      setIsSaving(true)
      setError(null)
      
      console.log('💾 Saving booking rules:', bookingRules)
      
      // Transform frontend rules to backend format
      const backendRules = bookingRulesApiService.transformToBackendFormat(bookingRules)
      console.log('📡 Backend format:', backendRules)
      
      let savedRules
      if (bookingRules.id) {
        // Update existing rules
        savedRules = await bookingRulesApiService.updateBookingRules(bookingRules.id, backendRules)
      } else {
        // Create new rules
        savedRules = await bookingRulesApiService.createBookingRules(backendRules)
      }
      
      // Transform back to frontend format and update state
      const updatedFrontendRules = bookingRulesApiService.transformToFrontendFormat(savedRules)
      setBookingRules(updatedFrontendRules)
      setOriginalRules(updatedFrontendRules)
      setHasChanges(false)
      
      console.log('✅ Booking rules saved successfully:', updatedFrontendRules)
      return updatedFrontendRules
    } catch (err) {
      console.error('❌ Failed to save booking rules:', err)
      setError(err.message)
      throw err
    } finally {
      setIsSaving(false)
    }
  }

  /**
   * Reset booking rules to defaults
   */
  const resetBookingRules = async () => {
    try {
      setIsSaving(true)
      setError(null)
      
      console.log('🔄 Resetting booking rules to defaults')
      
      if (bookingRules?.id) {
        // Reset existing rules via API
        const resetRules = await bookingRulesApiService.resetBookingRules(bookingRules.id)
        const frontendRules = bookingRulesApiService.transformToFrontendFormat(resetRules)
        
        setBookingRules(frontendRules)
        setOriginalRules(frontendRules)
        setHasChanges(false)
        
        console.log('✅ Booking rules reset via API:', frontendRules)
      } else {
        // No existing rules, just reset to defaults locally
        const defaultRules = bookingRulesApiService.getDefaultFrontendRules()
        setBookingRules(defaultRules)
        setOriginalRules(defaultRules)
        setHasChanges(false)
        
        console.log('✅ Booking rules reset to defaults:', defaultRules)
      }
    } catch (err) {
      console.error('❌ Failed to reset booking rules:', err)
      setError(err.message)
      throw err
    } finally {
      setIsSaving(false)
    }
  }

  /**
   * Cancel changes and revert to original
   */
  const cancelChanges = () => {
    setBookingRules(originalRules)
    setHasChanges(false)
    setError(null)
    console.log('↶ Changes cancelled, reverted to original rules')
  }

  /**
   * Refresh booking rules from API
   */
  const refreshBookingRules = () => {
    return fetchBookingRules()
  }

  // Helper functions for working with time units
  const convertHoursToDisplayUnit = (hours) => {
    return bookingRulesApiService.convertHoursToDisplayUnit(hours)
  }

  const convertDisplayUnitToHours = (value, unit) => {
    return bookingRulesApiService.convertDisplayUnitToHours(value, unit)
  }

  return {
    // State
    bookingRules,
    isLoading,
    isSaving,
    error,
    hasChanges,
    
    // Actions
    updateRule,
    updateRules,
    saveBookingRules,
    resetBookingRules,
    cancelChanges,
    refreshBookingRules,
    
    // Utilities
    convertHoursToDisplayUnit,
    convertDisplayUnitToHours
  }
}

export default useBookingRules 