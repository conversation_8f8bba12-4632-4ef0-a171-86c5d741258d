import { useState, useEffect } from 'react'
import Signature from '../../../components/Signature'

function CustomerForms({ customer }) {
  const [selectedFile, setSelectedFile] = useState(null)
  const [showSignatureModal, setShowSignatureModal] = useState(false)
  const [currentForm, setCurrentForm] = useState(null)
  const [signatureData, setSignatureData] = useState({
    customer: null,
    employee: null,
    agreed: false
  })

  // Reset signature data when modal is closed
  useEffect(() => {
    if (!showSignatureModal) {
      // Keep the signature data persisted
    }
  }, [showSignatureModal])

  // Dummy form/document data for this customer
  const forms = [
    {
      id: 1,
      name: 'Service Agreement 2024',
      type: 'agreement',
      uploadDate: '2024-01-15',
      fileSize: '245 KB',
      status: 'signed',
      signedDate: '2024-01-15',
      documentUrl: '#', // In real app, this would be actual file URL
      description: 'Standard service agreement for 2024 services'
    },
    {
      id: 2,
      name: 'Privacy Policy Consent',
      type: 'consent',
      uploadDate: '2024-01-15',
      fileSize: '156 KB',
      status: 'signed',
      signedDate: '2024-01-15',
      documentUrl: '#',
      description: 'Customer privacy policy acknowledgment and consent'
    },
    {
      id: 3,
      name: 'Health & Safety Waiver',
      type: 'waiver',
      uploadDate: '2024-03-10',
      fileSize: '198 KB',
      status: 'signed',
      signedDate: '2024-03-10',
      documentUrl: '#',
      description: 'Health and safety waiver for massage and spa services'
    },
    {
      id: 4,
      name: 'Photo Release Form',
      type: 'release',
      uploadDate: '2024-05-20',
      fileSize: '134 KB',
      status: 'pending',
      signedDate: null,
      documentUrl: '#',
      description: 'Permission to use photos for marketing purposes'
    },
    {
      id: 5,
      name: 'Membership Agreement 2024',
      type: 'membership',
      uploadDate: '2024-06-01',
      fileSize: '312 KB',
      status: 'signed',
      signedDate: '2024-06-01',
      documentUrl: '#',
      description: 'Premium membership terms and conditions'
    }
  ]

  const getStatusBadge = (status) => {
    const styles = {
      signed: 'bg-green-100 text-green-800',
      pending: 'bg-yellow-100 text-yellow-800',
      expired: 'bg-red-100 text-red-800'
    }
    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${styles[status] || styles.pending}`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    )
  }

  const getFileIcon = (type) => {
    const iconMap = {
      agreement: (
        <svg className="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      ),
      consent: (
        <svg className="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      waiver: (
        <svg className="w-5 h-5 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.664-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      ),
      release: (
        <svg className="w-5 h-5 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
        </svg>
      ),
      membership: (
        <svg className="w-5 h-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
        </svg>
      )
    }
    return iconMap[type] || iconMap.agreement
  }

  const openFileModal = (form) => {
    setSelectedFile(form)
  }

  const closeFileModal = () => {
    setSelectedFile(null)
  }

  const openSignatureModal = (form) => {
    setCurrentForm(form)
    setShowSignatureModal(true)
  }

  const closeSignatureModal = () => {
    setCurrentForm(null)
    setShowSignatureModal(false)
  }

  const handleSignatureSave = (data) => {
    console.log('Signature saved:', data)
    setSignatureData(data)
  }

  return (
    <div className="bg-white rounded-xl shadow-sm p-8">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">Customer Documents</h2>
          <p className="text-sm text-gray-600 mt-1">
            Signed agreements and forms ({forms.filter(f => f.status === 'signed').length} of {forms.length} completed)
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-500">Total Files:</span>
          <span className="text-sm font-medium text-gray-900">{forms.length}</span>
        </div>
      </div>

      {/* Improved grid layout with better responsive breakpoints */}
      <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-6">
        {forms.map((form) => (
          <div 
            key={form.id} 
            className="border border-gray-200 rounded-lg p-5 hover:shadow-lg hover:border-gray-300 transition-all duration-200 cursor-pointer group"
            onClick={() => openFileModal(form)}
          >
            {/* Header with icon and status */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-3 flex-1 min-w-0">
                <div className="flex-shrink-0">
                  {getFileIcon(form.type)}
                </div>
                <div className="flex-1 min-w-0">
                  {/* Improved title handling with tooltip */}
                  <div className="group relative">
                    <h3 className="text-sm font-medium text-gray-900 leading-5 line-clamp-2 group-hover:text-blue-600 transition-colors">
                      {form.name}
                    </h3>
                    {/* Tooltip for long names */}
                    {form.name.length > 25 && (
                      <div className="absolute bottom-full left-0 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-10 whitespace-nowrap">
                        {form.name}
                        <div className="absolute top-full left-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
                      </div>
                    )}
                  </div>
                  <p className="text-xs text-gray-500 mt-1">{form.fileSize}</p>
                </div>
              </div>
              <div className="flex-shrink-0 ml-3">
                {getStatusBadge(form.status)}
              </div>
            </div>

            {/* Description with better spacing */}
            <p className="text-xs text-gray-600 mb-4 line-clamp-2 leading-relaxed">{form.description}</p>

            {/* Footer with dates */}
            <div className="flex flex-col space-y-1 text-xs text-gray-500">
              <div className="flex items-center justify-between">
                <span className="font-medium">Uploaded:</span>
                <span>{new Date(form.uploadDate).toLocaleDateString()}</span>
              </div>
              {form.signedDate && (
                <div className="flex items-center justify-between">
                  <span className="font-medium">Signed:</span>
                  <span className="text-green-600">{new Date(form.signedDate).toLocaleDateString()}</span>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {forms.length === 0 && (
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <p className="text-gray-500 text-sm">No documents found</p>
        </div>
      )}

      {/* File Modal */}
      {selectedFile && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-3xl">
            <div className="flex items-center justify-between border-b px-6 py-4">
              <h3 className="text-lg font-medium text-gray-900">{selectedFile.name}</h3>
              <button onClick={closeFileModal} className="text-gray-400 hover:text-gray-500">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <div className="p-6">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 mb-6">
                <div>
                  <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Document Type</label>
                  <p className="text-sm text-gray-900 capitalize mt-1">{selectedFile.type}</p>
                </div>
                <div>
                  <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">File Size</label>
                  <p className="text-sm text-gray-900 mt-1">{selectedFile.fileSize}</p>
                </div>
                <div>
                  <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Upload Date</label>
                  <p className="text-sm text-gray-900 mt-1">{new Date(selectedFile.uploadDate).toLocaleDateString()}</p>
                </div>
                <div>
                  <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Status</label>
                  <div className="mt-1">{getStatusBadge(selectedFile.status)}</div>
                </div>
                {selectedFile.signedDate && (
                  <div className="sm:col-span-2">
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Signed Date</label>
                    <p className="text-sm text-gray-900 mt-1">{new Date(selectedFile.signedDate).toLocaleDateString()}</p>
                  </div>
                )}
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={closeFileModal}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  Close
                </button>
                {selectedFile.status === 'signed' && (
                  <button
                    onClick={() => openSignatureModal(selectedFile)}
                    className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-blue-600 rounded-md hover:bg-blue-700"
                  >
                    View & Save Signature
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* Signature Modal */}
      {showSignatureModal && currentForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl">
            <div className="flex items-center justify-between border-b px-6 py-4">
              <h3 className="text-lg font-medium text-gray-900">
                Signature for {currentForm.name}
              </h3>
              <button onClick={closeSignatureModal} className="text-gray-400 hover:text-gray-500">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <div className="p-6">
              {/* This is where we use the Signature component */}
              <Signature 
                initialData={{
                  // In a real app, you would fetch the actual signature data from your backend
                  customer: signatureData.customer || 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAACWCAYAAABkW7XSAAAABmJLR0QA/wD/AP+gvaeTAAAF+UlEQVR4nO3dS2hcZRzG4d+ZJE3SphaNtYKIWxERXIgbQfGCKLjThVJ05U5QEBeKCxEXult0IYKCoBsXghcQvIALQVwUFRRRRBQvWGm9tE2aZDJzXDRpJ5PMJHPOnO+c8z4PZJfJ+eY/+eZ8c+ZMIgEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADQAcPWA2C2rKg2JR1JOpS03XgcAF1VVJuT9icdTzqWdDjpUNKBpP1Jg6QqaZx0IWlr0pakzUkbk9YnrUtamzSStCZpddLKpBVJy5KWJS1JWpy0KGlh0oKk+UnDpHlJVdKcpKqPJ4DuGSatSFqZdFnS5Uk3Jd2adEfSvUkPJD2c9FjSk0nPJr2Y9ErSm0nvJX2S9GXSN0k/Jv2c9FvSyaSzSX8mXUgaJ42TxkkXk7YmbU3anLQpaWPShqR1SWuT1iStTlqVtDJpRdLypGVJSzP/rBZl/jktyvxzWpj557Qg889pftKcpGpO5p/T3Mw/p3mZf07zM/+cFmT+OS3M/HNalPnntDjzz2lJ5p/T0sw/p2WZf07LM/+cVmT+Oa3M/HNalfnntDrzz2lN5p/T2sw/p3WZf077M/+cDmT+OR3M/HM6lPnndDjzz+lI5p/T0cw/p2OZf07HM/+cTmT+OZ3M/HM6lfnndDrzz+lM5p/T2cw/p3OZf07nM/+cLmT+OV3M/HO6lPnndDnzz+lK5p/T1cw/p2uZf07XM/+cbmT+Od3M/HO6lfnndDvzz+lO5p/T3cw/p3uZf073M/+cHmT+OT3M/HN6lPnn9Djzz+lJ5p/T08w/p2eZf07PM/+cXmT+Ob3M/HN6mfnn9Crzz+l55p/Ti8w/p5eZf06vMv+cXmf+Ob3J/HN6m/nn9C7zz+l95p/Tx8w/p0+Zf06fM/+cvmT+OX3N/HP6lvnn9CPzz+ln5p/Tr8w/p9+Zf05nMv+czmX+OZ3P/HO6kPnndDHzz+lS5p/T5cw/pyuZf05XM/+crmX+OV3P/HO6kfnndDPzz+lW5p/T7cw/pzuZf053M/+c7mX+Od3P/HN6kPnn9DDzz+lR5p/T48w/pyeZf05PM/+cnmX+OT3P/HN6kfnn9DLzz+lV5p/T68w/pzeZf05vM/+c3mX+Ob3P/HP6mPnn9Cnzz+lz5p/Tl8w/p6+Zf07fMv+cfmT+Of3M/HP6lfnn9Dvzz+lM5p/Tucw/p/OZf04XMv+cLmb+OV3K/HO6nPnndCXzz+lq5p/Ttcw/p+uZf043Mv+cbmb+Od3K/HO6nfnndCfzz+lu5p/Tvcw/p/uZf04PMv+cHmb+OT3K/HN6nPnn9CTzz+lp5p/Ts8w/p+eZf04vMv+cXmb+Ob3K/HN6nfnn9Cbzz+l15p/Tm8w/p7eZf07vMv+c3mf+OX3M/HP6lPnn9Dnzz+lL5p/T18w/p2+Zf04/Mv+cfmb+Of3K/HP6nfnn9B8rxXz7uT/GGQAAAABJRU5ErkJggg==',
                  employee: signatureData.employee || null,
                  agreed: signatureData.agreed || true
                }}
                onSave={handleSignatureSave}
                customerName={customer?.name || 'Customer'}
                documentType={currentForm.type}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default CustomerForms 