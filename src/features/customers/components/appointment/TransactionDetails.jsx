import { getTransactionStatusColor, getTransactionStatusText } from '../../utils/appointmentUtils.jsx'

function TransactionDetails({ appointment, isExpanded }) {
  const handleRefund = (appointmentId) => {
    console.log('Process refund for appointment:', appointmentId)
  }

  if (!isExpanded) return null

  return (
    <tr className="bg-gray-50">
      <td colSpan="6" className="px-6 py-4">
        <div className="bg-white rounded-lg p-4 border border-gray-200">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Transaction Details</h4>
          
          {appointment.transaction ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Products */}
              <div>
                <h5 className="text-xs font-medium text-gray-500 uppercase mb-2">Products/Services</h5>
                <div className="space-y-2">
                  {appointment.transaction.products.map((product, index) => (
                    <div key={index} className="flex justify-between text-sm">
                      <span className="text-gray-900">{product.name}</span>
                      <span className="text-gray-600">${product.price.toFixed(2)}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Payment Summary */}
              <div>
                <h5 className="text-xs font-medium text-gray-500 uppercase mb-2">Payment Summary</h5>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span>Subtotal:</span>
                    <span>${appointment.transaction.subtotal.toFixed(2)}</span>
                  </div>
                  {appointment.transaction.discountAmount > 0 && (
                    <div className="flex justify-between text-green-600">
                      <span>Discount ({appointment.transaction.discountPercentage}%):</span>
                      <span>-${appointment.transaction.discountAmount.toFixed(2)}</span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span>Tax:</span>
                    <span>${appointment.transaction.tax.toFixed(2)}</span>
                  </div>
                  {appointment.transaction.tipAmount > 0 && (
                    <div className="flex justify-between">
                      <span>Tip ({appointment.transaction.tipPercentage}%):</span>
                      <span>${appointment.transaction.tipAmount.toFixed(2)}</span>
                    </div>
                  )}
                  <div className="flex justify-between font-medium border-t pt-1">
                    <span>Total:</span>
                    <span>${appointment.transaction.total.toFixed(2)}</span>
                  </div>
                  
                  {/* Payment Method */}
                  {appointment.transaction.paymentMethod && (
                    <div className="flex justify-between">
                      <span>Payment Method:</span>
                      <span className="text-gray-600">{appointment.transaction.paymentMethod}</span>
                    </div>
                  )}
                  
                  {/* Transaction Status */}
                  <div className="flex justify-between">
                    <span>Status:</span>
                    <span className={getTransactionStatusColor(appointment.transaction.status, appointment.transaction.paid)}>
                      {getTransactionStatusText(appointment.transaction.status, appointment.transaction.paid)}
                    </span>
                  </div>
                  
                  {/* Refund Information */}
                  {appointment.transaction.status === 'refunded' && (
                    <div className="mt-3 pt-3 border-t border-gray-200 bg-purple-50 rounded-lg p-3">
                      <h6 className="text-xs font-medium text-purple-700 uppercase mb-2">Refund Details</h6>
                      <div className="space-y-1 text-sm">
                        <div className="flex justify-between">
                          <span>Refund Amount:</span>
                          <span className="font-medium text-purple-600">${appointment.transaction.refundAmount.toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Refund Date:</span>
                          <span className="text-gray-600">{new Date(appointment.transaction.refundDate).toLocaleDateString()}</span>
                        </div>
                        <div className="mt-2">
                          <span className="text-xs font-medium text-gray-700">Reason:</span>
                          <p className="text-xs text-gray-600 mt-1">{appointment.transaction.refundReason}</p>
                        </div>
                      </div>
                    </div>
                  )}
                  
                  {/* Failed Payment Information */}
                  {appointment.transaction.status === 'failed' && (
                    <div className="mt-3 pt-3 border-t border-gray-200 bg-red-50 rounded-lg p-3">
                      <h6 className="text-xs font-medium text-red-700 uppercase mb-2">Payment Failed</h6>
                      <div className="text-sm">
                        <div className="flex justify-between">
                          <span>Failure Reason:</span>
                          <span className="text-red-600 font-medium">{appointment.transaction.failureReason}</span>
                        </div>
                        <p className="text-xs text-red-600 mt-2">Payment attempt was unsuccessful. Customer may need to retry with different payment method.</p>
                      </div>
                    </div>
                  )}
                  
                  {/* Pending Payment Information */}
                  {appointment.transaction.status === 'requested' && (
                    <div className="mt-3 pt-3 border-t border-gray-200 bg-yellow-50 rounded-lg p-3">
                      <h6 className="text-xs font-medium text-yellow-700 uppercase mb-2">Payment Pending</h6>
                      <div className="text-sm">
                        <p className="text-xs text-yellow-600">{appointment.transaction.pendingReason}</p>
                      </div>
                    </div>
                  )}
                </div>
                
                {/* Refund Button */}
                {appointment.transaction.paid && 
                 appointment.status === 'completed' && 
                 appointment.transaction.status === 'completed' && (
                  <div className="mt-4 pt-3 border-t border-gray-200">
                    <button 
                      onClick={() => handleRefund(appointment.id)}
                      className="inline-flex items-center px-3 py-2 border border-red-300 rounded-lg text-sm font-medium text-red-700 bg-white hover:bg-red-50 transition-colors"
                    >
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z" />
                      </svg>
                      Process Refund
                    </button>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
              </div>
              <h5 className="text-sm font-medium text-gray-900 mb-1">No Transaction Available</h5>
              <p className="text-sm text-gray-500">
                {appointment.status === 'scheduled' && 'This appointment is scheduled but not yet completed.'}
                {appointment.status === 'cancelled' && 'This appointment was cancelled before completion.'}
                {appointment.status === 'completed' && 'Transaction details are not available for this appointment.'}
              </p>
            </div>
          )}
        </div>
      </td>
    </tr>
  )
}

export default TransactionDetails 