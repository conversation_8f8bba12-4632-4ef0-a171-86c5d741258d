function AppointmentPagination({ 
  currentPage, 
  totalPages, 
  startIndex, 
  endIndex, 
  totalItems, 
  onPageChange 
}) {
  if (totalPages <= 1) return null

  return (
    <div className="flex items-center justify-between mt-6 px-4 py-3 bg-gray-50 rounded-lg">
      <div className="flex items-center">
        <p className="text-sm text-gray-700">
          Showing {startIndex + 1} to {Math.min(endIndex, totalItems)} of {totalItems} appointments
        </p>
      </div>
      <div className="flex items-center space-x-2">
        <button
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage === 1}
          className="px-3 py-1 border border-gray-300 rounded text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Previous
        </button>
        
        {/* Page Numbers */}
        <div className="flex space-x-1">
          {[...Array(totalPages)].map((_, index) => {
            const page = index + 1
            const isCurrentPage = page === currentPage
            const showPage = page === 1 || page === totalPages || 
                            (page >= currentPage - 1 && page <= currentPage + 1)
            
            if (!showPage && page === currentPage - 2) {
              return <span key={page} className="px-2 text-gray-400">...</span>
            }
            if (!showPage && page === currentPage + 2) {
              return <span key={page} className="px-2 text-gray-400">...</span>
            }
            if (!showPage) return null
            
            return (
              <button
                key={page}
                onClick={() => onPageChange(page)}
                className={`px-3 py-1 border rounded text-sm font-medium ${
                  isCurrentPage
                    ? 'border-blue-500 bg-blue-50 text-blue-600'
                    : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                }`}
              >
                {page}
              </button>
            )
          })}
        </div>
        
        <button
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          className="px-3 py-1 border border-gray-300 rounded text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Next
        </button>
      </div>
    </div>
  )
}

export default AppointmentPagination 