import { useState } from 'react'

function ProfileHeader({ customer }) {
  const [pronouncedAs, setPronuncedAs] = useState('')
  const [isExpanded, setIsExpanded] = useState(false)
  const [isSaved, setIsSaved] = useState(false)

  const handleExpand = () => {
    setIsExpanded(true)
  }

  const handleSave = () => {
    if (pronouncedAs.trim()) {
      setIsSaved(true)
      setTimeout(() => {
        setIsSaved(false)
        setIsExpanded(false)
      }, 2000) // Show saved state for 2 seconds, then collapse
    }
  }

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSave()
    }
  }

  const handleCancel = () => {
    setIsExpanded(false)
  }

  const handleClear = () => {
    setPronuncedAs('')
    setIsExpanded(false)
  }

  return (
    <div className="bg-white rounded-xl shadow-sm p-8 mb-6">
      <div className="flex flex-col sm:flex-row items-center sm:items-start gap-6">
        {/* Avatar */}
        <div className="relative">
          <img
            src={customer.avatar}
            alt={customer.name}
            className="w-24 h-24 rounded-full object-cover border-4 border-gray-100"
          />
          <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-white"></div>
        </div>

        {/* Basic Info */}
        <div className="flex-1 text-center sm:text-left">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">{customer.name}</h1>
          
          {/* Pronounced As Field */}
          <div className="mb-4">
            {!isExpanded && !pronouncedAs ? (
              // Default collapsed state - clickable to expand
              <button
                onClick={handleExpand}
                className="flex items-center text-xs text-gray-500 hover:text-gray-700 transition-colors group"
              >
                <svg className="w-3 h-3 mr-1 group-hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                Add pronunciation
              </button>
            ) : !isExpanded && pronouncedAs ? (
                             // Collapsed state with saved pronunciation
               <div className="flex items-center space-x-2">
                 <div className="flex items-center text-xs text-gray-600">
                   <svg className="w-3 h-3 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                     <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
                   </svg>
                   <span className="font-medium">{pronouncedAs}</span>
                 </div>
                 <button
                   onClick={handleClear}
                   className="text-xs text-gray-400 hover:text-red-500 transition-colors"
                   title="Clear pronunciation"
                 >
                   ×
                 </button>
               </div>
            ) : (
              // Expanded state - input box
              <div className={`transition-all duration-200 ${
                isSaved
                  ? 'ring-2 ring-green-500/20 border-green-500'
                  : 'ring-2 ring-blue-500/20 border-blue-500'
              } border rounded-lg bg-white`}>
                <div className="flex items-center px-3 py-2">
                  <svg className={`w-4 h-4 mr-2 transition-colors ${
                    isSaved ? 'text-green-500' : 'text-blue-500'
                  }`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
                  </svg>
                  <input
                    type="text"
                    value={pronouncedAs}
                    onChange={(e) => setPronuncedAs(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="e.g., JOHN DOH, Jane SMITH..."
                    className="flex-1 bg-transparent border-none outline-none text-sm text-gray-900 placeholder-gray-400"
                    autoFocus
                  />
                  <div className="flex items-center space-x-2 ml-2">
                    {pronouncedAs && (
                      <button
                        onClick={handleSave}
                        className="text-xs text-gray-500 hover:text-gray-700 transition-colors"
                      >
                        save
                      </button>
                    )}
                    <button
                      onClick={handleCancel}
                      className="text-xs text-gray-400 hover:text-gray-600 transition-colors"
                      title="Cancel"
                    >
                      ×
                    </button>
                  </div>
                </div>
                <div className="px-3 pb-2">
                  <span className="text-xs text-gray-500">
                    {isSaved 
                      ? 'Pronunciation saved!'
                      : 'Press Enter or click save • For employee reference'
                    }
                  </span>
                </div>
              </div>
            )}
          </div>

          <div className="space-y-1">
            <p className="text-gray-600 flex items-center justify-center sm:justify-start">
              <svg className="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              {customer.email}
            </p>
            <p className="text-gray-600 flex items-center justify-center sm:justify-start">
              <svg className="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
              </svg>
              {customer.phone}
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ProfileHeader 