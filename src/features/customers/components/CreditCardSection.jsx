function CreditCardSection({ customer }) {
  // Check if customer has payment methods using new API fields
  const hasPaymentMethods = customer.card_on_file || false;
  const cardType = customer.payment_card_type || null;
  const lastFourDigits = customer.payment_last_4_digits || null;

  // Create credit card object from new API fields
  const creditCards = [];
  if (hasPaymentMethods && (cardType || lastFourDigits)) {
    creditCards.push({
      id: 1,
      type: cardType || 'Unknown',
      lastFour: lastFourDigits || 'Hidden',
      expiryDate: 'Hidden', // Not available for security
      isDefault: true, // Assuming the returned card is the default one
      cardHolder: customer.name || 'Customer',
      isFromNewAPI: true // Flag to identify new API data
    });
  }
  // Fallback to legacy credit card info if new API fields aren't available
  else if (customer.creditCard && customer.creditCard.trim() !== '') {
    const parsedCard = parseCreditCardInfo(customer.creditCard);
    if (parsedCard) {
      creditCards.push(parsedCard);
    }
  }

  // Legacy parser for imported credit card strings (fallback)
  function parseCreditCardInfo(creditCardString) {
    if (!creditCardString || creditCardString.trim() === '') {
      return null;
    }
    
    // Handle different formats like "ending in 8616", "Visa ****1234", etc.
    const cleanString = creditCardString.toLowerCase();
    
    let lastFour = '';
    let type = 'Unknown';
    
    // Extract last 4 digits
    const numberMatch = creditCardString.match(/\d{4}/);
    if (numberMatch) {
      lastFour = numberMatch[0];
    }
    
    // Determine card type
    if (cleanString.includes('visa')) {
      type = 'Visa';
    } else if (cleanString.includes('master') || cleanString.includes('mc')) {
      type = 'MasterCard';
    } else if (cleanString.includes('amex') || cleanString.includes('american express')) {
      type = 'American Express';
    } else if (cleanString.includes('discover')) {
      type = 'Discover';
    }
    
    return {
      id: 1,
      type: type,
      lastFour: lastFour,
      expiryDate: 'Hidden', // Not available in imported data
      isDefault: true,
      cardHolder: customer.name,
      originalInfo: creditCardString,
      isFromNewAPI: false // Flag to identify legacy data
    };
  }

  const getCardIcon = (type) => {
    switch (type) {
      case 'Visa':
        return (
          <div className="w-8 h-8 bg-blue-600 rounded flex items-center justify-center">
            <span className="text-white text-xs font-bold">V</span>
          </div>
        )
      case 'MasterCard':
        return (
          <div className="w-8 h-8 bg-red-600 rounded flex items-center justify-center">
            <span className="text-white text-xs font-bold">MC</span>
          </div>
        )
      case 'American Express':
        return (
          <div className="w-8 h-8 bg-green-600 rounded flex items-center justify-center">
            <span className="text-white text-xs font-bold">AE</span>
          </div>
        )
      case 'Discover':
        return (
          <div className="w-8 h-8 bg-orange-600 rounded flex items-center justify-center">
            <span className="text-white text-xs font-bold">D</span>
          </div>
        )
      case 'Unknown':
        return (
          <div className="w-8 h-8 bg-purple-600 rounded flex items-center justify-center">
            <span className="text-white text-xs font-bold">?</span>
          </div>
        )
      default:
        return (
          <div className="w-8 h-8 bg-gray-400 rounded flex items-center justify-center">
            <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
            </svg>
          </div>
        )
    }
  }

  return (
    <div className="bg-white rounded-xl shadow-sm p-8">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg font-semibold text-gray-900">Payment Methods</h2>
        <div className="flex items-center space-x-2">
          {hasPaymentMethods && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
              Active Payment Method
            </span>
          )}
          <span className="text-sm text-gray-500">View Only</span>
        </div>
      </div>

      {creditCards.length === 0 ? (
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
            </svg>
          </div>
          <p className="text-gray-500 text-sm mb-2">No payment methods on file</p>
          <p className="text-gray-400 text-xs">Customer has not added any payment methods yet</p>
        </div>
      ) : (
        <div className="space-y-4">
          {creditCards.map((card) => (
            <div
              key={card.id}
              className={`relative p-4 border rounded-xl ${
                card.isDefault 
                  ? 'border-blue-200 bg-blue-50' 
                  : 'border-gray-200 bg-gray-50'
              }`}
            >
              {card.isDefault && (
                <div className="absolute -top-2 -right-2">
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-600 text-white">
                    Default
                  </span>
                </div>
              )}

              <div className="flex items-center space-x-4">
                {getCardIcon(card.type)}
                
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-gray-900">
                      {card.type}
                    </span>
                    <span className="text-sm text-gray-600">
                      {card.lastFour !== 'Hidden' ? `•••• •••• •••• ${card.lastFour}` : '•••• •••• •••• ••••'}
                    </span>
                    {card.isFromNewAPI && (
                      <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                        Secure
                      </span>
                    )}
                  </div>
                  <div className="flex items-center space-x-4 mt-1">
                    <span className="text-xs text-gray-500">
                      Cardholder: {card.cardHolder}
                    </span>
                    {!card.isFromNewAPI && card.originalInfo && (
                      <span className="text-xs text-gray-500">
                        Original: {card.originalInfo}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <div className="flex items-start space-x-3">
          <svg className="w-5 h-5 text-gray-400 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
          </svg>
          <div>
            <p className="text-sm font-medium text-gray-900">Secure Payment Information</p>
            <p className="text-xs text-gray-600 mt-1">
              {hasPaymentMethods 
                ? 'Payment methods are encrypted and securely stored. Only masked card details are visible for privacy protection.'
                : 'No payment information has been stored for this customer. Payment methods can be added during checkout or through the customer portal.'
              }
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CreditCardSection 