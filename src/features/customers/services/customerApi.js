// customerApi.js - API service for customer management functionality

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api/v1'

class CustomerApiService {
  constructor() {
    this.baseURL = API_BASE_URL
  }

  // Helper method to get auth token
  getAuthToken() {
    return localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token')
  }

  // Helper method to make API requests
  async makeRequest(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`
    
    const authToken = this.getAuthToken()
    const defaultOptions = {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${authToken}`
      },
      ...options
    }

    try {
      const response = await fetch(url, defaultOptions)
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.message || errorData.error || `HTTP error! status: ${response.status}`)
      }
      
      return await response.json()
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error)
      throw error
    }
  }

  // Get all customers with optional pagination and filtering
  async getCustomers(params = {}) {
    const queryParams = new URLSearchParams()
    
    if (params.page) queryParams.append('page', params.page)
    if (params.limit) queryParams.append('limit', params.limit)
    if (params.search) queryParams.append('q', params.search)
    if (params.q) queryParams.append('q', params.q)
    if (params.status) queryParams.append('status', params.status)
    if (params.sortBy) queryParams.append('sort_by', params.sortBy)
    if (params.sortOrder) queryParams.append('sort_order', params.sortOrder)
    if (params.fields) queryParams.append('fields', params.fields)
    
    const queryString = queryParams.toString()
    const endpoint = `/business-customers/me/${queryString ? `?${queryString}` : ''}`
    
    return await this.makeRequest(endpoint)
  }

  // Get minimal customer data for list display (fast loading)
  async getCustomersMinimal(params = {}) {
    return this.getCustomers({
      ...params,
      fields: 'id,first_name,last_name,email,phone,has_upcoming_appointment',
      limit: params.limit || 100 // Load more since data is minimal
    })
  }

  // Get a single customer by ID
  async getCustomer(customerId) {
    // Since the individual customer endpoint has a backend issue,
    // use the list endpoint and filter for the specific customer
    const response = await this.makeRequest('/business-customers/me/')
    
    // Handle different possible response structures
    let customerList = []
    if (response.results) {
      // Paginated response
      customerList = response.results
    } else if (Array.isArray(response)) {
      // Direct array response
      customerList = response
    }
    
    // Find the specific customer by ID
    const customer = customerList.find(c => c.id === parseInt(customerId))
    
    if (!customer) {
      throw new Error('Customer not found')
    }
    
    // Transform to match the expected format
    return {
      id: customer.id,
      first_name: customer.first_name || '',
      last_name: customer.last_name || '',
      name: customer.first_name && customer.last_name 
        ? `${customer.first_name} ${customer.last_name}`.trim()
        : customer.first_name || customer.last_name || 'Unknown Customer',
      email: customer.email || '',
      phone: customer.phone || '',
      avatar: customer.avatar || null,
      // Include all original data
      ...customer
    }
  }

  // Create a new customer
  async createCustomer(customerData) {
    try {
      console.log('🚀 Starting customer creation process...')
      console.log('📋 Customer data:', customerData)
      
      // Use the import API which can handle full customer creation
      const [firstName, ...lastNameParts] = customerData.name.trim().split(' ')
      const lastName = lastNameParts.join(' ')
      
      // Format data for the import API
      const importData = {
        customers: [{
          first_name: firstName,
          last_name: lastName,
          email: customerData.email.toLowerCase().trim(),
          mobile: customerData.phone.trim(),
          notes: customerData.notes || ''
        }],
        fieldMappings: {
          first_name: 'first_name',
          last_name: 'last_name', 
          email: 'email',
          mobile: 'mobile'
        }
      }
      
      console.log('📤 Sending import request:', importData)
      
      // Step 1: Start the import
      const importResponse = await this.makeRequest('/customers/import/', {
        method: 'POST',
        body: JSON.stringify(importData)
      })
      
      console.log('✅ Import response:', importResponse)
      
      if (!importResponse.jobId) {
        throw new Error('Import API did not return a job ID')
      }
      
      console.log('🔄 Fetching import results for job:', importResponse.jobId)
      
      // Step 2: Get the actual results using the job ID
      const resultsResponse = await this.makeRequest(`/customers/import/results/${importResponse.jobId}/`)
      
      console.log('📊 Results response:', resultsResponse)
      
      // Extract the created customer from the import results
      if (resultsResponse.successful && resultsResponse.successful.length > 0) {
        const createdCustomer = resultsResponse.successful[0]
        
        console.log('🎉 Customer created successfully:', createdCustomer)
        
        // Transform the response to match the frontend's expected format
        const transformedCustomer = {
          id: createdCustomer.id,
          name: createdCustomer.name || `${firstName} ${lastName}`.trim(),
          email: customerData.email.toLowerCase().trim(),
          phone: customerData.phone.trim(),
          lastVisit: null,
          totalAppointments: 0,
          avatar: null,
          // Keep original data for API calls
          _originalData: createdCustomer
        }
        
        console.log('✨ Transformed customer:', transformedCustomer)
        return transformedCustomer
      } else {
        // Handle import errors
        console.error('❌ Import failed:', resultsResponse)
        const errorMsg = resultsResponse.errors && resultsResponse.errors.length > 0 
          ? resultsResponse.errors[0].message || resultsResponse.errors[0]
          : 'Failed to create customer - no successful results'
        throw new Error(errorMsg)
      }
    } catch (error) {
      console.error('💥 Customer creation failed:', error)
      console.error('📍 Error details:', {
        message: error.message,
        stack: error.stack,
        name: error.name
      })
      
      // Provide more specific error messages
      if (error.message.includes('HTTP error! status: 400')) {
        throw new Error('Invalid customer data provided')
      } else if (error.message.includes('HTTP error! status: 401')) {
        throw new Error('Authentication failed - please log in again')
      } else if (error.message.includes('HTTP error! status: 404')) {
        throw new Error('Customer creation endpoint not found')
      } else {
        throw new Error(`Customer creation failed: ${error.message}`)
      }
    }
  }

  // Update an existing customer
  async updateCustomer(customerId, customerData) {
    // This endpoint has the same backend issue as getCustomer
    // Commenting out until backend is fixed
    throw new Error('Customer update functionality temporarily unavailable due to backend issue')
    
    // return await this.makeRequest(`/business-customers/me/${customerId}/`, {
    //   method: 'PUT',
    //   body: JSON.stringify(customerData)
    // })
  }

  // Delete a customer
  async deleteCustomer(customerId) {
    // This endpoint has the same backend issue as getCustomer
    // Commenting out until backend is fixed
    throw new Error('Customer delete functionality temporarily unavailable due to backend issue')
    
    // return await this.makeRequest(`/business-customers/me/${customerId}/`, {
    //   method: 'DELETE'
    // })
  }

  // Get customer statistics
  async getCustomerStats() {
    return await this.makeRequest('/business-customers/me/stats/')
  }
}

// Export singleton instance
export const customerApi = new CustomerApiService()

// Export individual methods for easier importing
export const {
  getCustomers,
  getCustomer,
  createCustomer,
  updateCustomer,
  deleteCustomer,
  getCustomerStats
} = customerApi 