/**
 * Abstract base class for customer data services
 * Defines the interface that all customer data implementations must follow
 */
export class CustomerDataService {
  /**
   * Initialize the service
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    if (this.constructor === CustomerDataService) {
      throw new Error('CustomerDataService is an abstract class and cannot be instantiated directly')
    }
    this.options = options
  }

  /**
   * Get React hook for customers minimal data
   * @returns {Object} Hook object with { data, isLoading, error, refetch }
   */
  useCustomersMinimal() {
    throw new Error('useCustomersMinimal must be implemented by subclass')
  }

  /**
   * Get React hook for customer details
   * @param {string|number} customerId - Customer ID
   * @returns {Object} Hook object with { data, isLoading, error, refetch }
   */
  useCustomerDetails(customerId) {
    throw new Error('useCustomerDetails must be implemented by subclass')
  }

  /**
   * Get all customers with minimal data (direct API call)
   * @returns {Promise<Array>} Array of customer objects with minimal fields
   */
  async getCustomersMinimal() {
    throw new Error('getCustomersMinimal must be implemented by subclass')
  }

  /**
   * Get detailed customer data by ID (direct API call)
   * @param {string|number} customerId - Customer ID
   * @returns {Promise<Object>} Detailed customer object
   */
  async getCustomerDetails(customerId) {
    throw new Error('getCustomerDetails must be implemented by subclass')
  }

  /**
   * Add a new customer to the cache/storage
   * @param {Object} customer - Customer object to add
   * @returns {Promise<void>}
   */
  async addCustomerToCache(customer) {
    throw new Error('addCustomerToCache must be implemented by subclass')
  }

  /**
   * Refresh/invalidate the customer cache
   * @returns {Promise<void>}
   */
  async refreshCustomers() {
    throw new Error('refreshCustomers must be implemented by subclass')
  }

  /**
   * Clear all cached customer data
   * @returns {Promise<void>}
   */
  async clearCache() {
    throw new Error('clearCache must be implemented by subclass')
  }

  /**
   * Get service metadata (cache size, last updated, etc.)
   * @returns {Promise<Object>} Service metadata
   */
  async getServiceInfo() {
    throw new Error('getServiceInfo must be implemented by subclass')
  }

  /**
   * Check if service is available/ready
   * @returns {Promise<boolean>} True if service is ready
   */
  async isReady() {
    throw new Error('isReady must be implemented by subclass')
  }
} 