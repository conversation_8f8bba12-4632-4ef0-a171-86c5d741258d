import { ReactQueryCustomerService } from './ReactQueryCustomerService'
import { IndexedDBCustomerService } from './IndexedDBCustomerService'

/**
 * Factory class for creating customer data service instances
 * Uses strategy pattern to switch between different implementations
 */
export class CustomerServiceFactory {
  static instance = null
  static currentService = null

  /**
   * Available service types
   */
  static SERVICE_TYPES = {
    REACT_QUERY: 'react-query',
    INDEXED_DB: 'indexed-db'
  }

  /**
   * Get singleton instance of the factory
   * @returns {CustomerServiceFactory} Factory instance
   */
  static getInstance() {
    if (!CustomerServiceFactory.instance) {
      CustomerServiceFactory.instance = new CustomerServiceFactory()
    }
    return CustomerServiceFactory.instance
  }

  /**
   * Create a customer service instance based on type
   * @param {string} type - Service type (react-query or indexed-db)
   * @param {Object} options - Configuration options
   * @returns {CustomerDataService} Service instance
   */
  static createService(type = null, options = {}) {
    const serviceType = type || CustomerServiceFactory.getDefaultServiceType()
    
    switch (serviceType) {
      case CustomerServiceFactory.SERVICE_TYPES.REACT_QUERY:
        return new ReactQueryCustomerService(options)
      
      case CustomerServiceFactory.SERVICE_TYPES.INDEXED_DB:
        return new IndexedDBCustomerService(options)
      
      default:
        console.warn(`Unknown service type: ${serviceType}. Falling back to React Query.`)
        return new ReactQueryCustomerService(options)
    }
  }

  /**
   * Get the default service type from environment or configuration
   * @returns {string} Default service type
   */
  static getDefaultServiceType() {
    // Check environment variable first
    if (typeof process !== 'undefined' && process.env) {
      const envType = process.env.REACT_APP_CUSTOMER_SERVICE_TYPE
      if (envType && Object.values(CustomerServiceFactory.SERVICE_TYPES).includes(envType)) {
        return envType
      }
    }

    // Check localStorage for user preference
    try {
      const storedType = localStorage.getItem('chatbook-customer-service-type')
      if (storedType && Object.values(CustomerServiceFactory.SERVICE_TYPES).includes(storedType)) {
        return storedType
      }
    } catch (error) {
      console.warn('Could not access localStorage for service type preference')
    }

    // Default to React Query
    return CustomerServiceFactory.SERVICE_TYPES.REACT_QUERY
  }

  /**
   * Get or create the current service instance
   * @param {Object} options - Configuration options
   * @returns {CustomerDataService} Current service instance
   */
  static getCurrentService(options = {}) {
    if (!CustomerServiceFactory.currentService) {
      CustomerServiceFactory.currentService = CustomerServiceFactory.createService(null, options)
    }
    return CustomerServiceFactory.currentService
  }

  /**
   * Switch to a different service type
   * @param {string} type - New service type
   * @param {Object} options - Configuration options
   * @returns {CustomerDataService} New service instance
   */
  static switchToService(type, options = {}) {
    const newService = CustomerServiceFactory.createService(type, options)
    
    // Clean up old service if needed
    if (CustomerServiceFactory.currentService) {
      // Could add cleanup logic here if needed
      console.log(`Switching from ${CustomerServiceFactory.currentService.constructor.name} to ${newService.constructor.name}`)
    }
    
    CustomerServiceFactory.currentService = newService
    
    // Store preference
    try {
      localStorage.setItem('chatbook-customer-service-type', type)
    } catch (error) {
      console.warn('Could not save service type preference to localStorage')
    }
    
    return newService
  }

  /**
   * Get service capabilities comparison
   * @returns {Object} Capabilities comparison
   */
  static getServiceCapabilities() {
    return {
      [CustomerServiceFactory.SERVICE_TYPES.REACT_QUERY]: {
        name: 'React Query',
        cacheType: 'in-memory',
        persistent: false,
        offlineCapable: false,
        performance: 'excellent',
        memoryUsage: 'low',
        features: ['Smart caching', 'Background refresh', 'Optimistic updates'],
        bestFor: ['Real-time apps', 'Frequent updates', 'Low memory usage'],
        ready: true
      },
      [CustomerServiceFactory.SERVICE_TYPES.INDEXED_DB]: {
        name: 'IndexedDB',
        cacheType: 'persistent',
        persistent: true,
        offlineCapable: true,
        performance: 'good',
        memoryUsage: 'minimal',
        features: ['Offline support', 'Large storage', 'Persistent cache'],
        bestFor: ['Offline apps', 'Large datasets', 'Slow networks'],
        ready: false // Placeholder implementation
      }
    }
  }

  /**
   * Get current service information
   * @returns {Object} Current service info
   */
  static async getCurrentServiceInfo() {
    const service = CustomerServiceFactory.getCurrentService()
    const info = await service.getServiceInfo()
    const capabilities = CustomerServiceFactory.getServiceCapabilities()
    
    return {
      ...info,
      capabilities: capabilities[info.type?.toLowerCase() === 'reactquery' ? 'react-query' : 'indexed-db']
    }
  }

  /**
   * Test service availability
   * @param {string} type - Service type to test
   * @returns {Promise<boolean>} True if service is available
   */
  static async testService(type) {
    try {
      const service = CustomerServiceFactory.createService(type)
      return await service.isReady()
    } catch (error) {
      console.error(`Failed to test service ${type}:`, error)
      return false
    }
  }

  /**
   * Get recommended service type based on environment
   * @returns {Promise<string>} Recommended service type
   */
  static async getRecommendedServiceType() {
    const capabilities = CustomerServiceFactory.getServiceCapabilities()
    
    // Test React Query availability
    const reactQueryAvailable = await CustomerServiceFactory.testService(
      CustomerServiceFactory.SERVICE_TYPES.REACT_QUERY
    )
    
    // Test IndexedDB availability
    const indexedDBAvailable = await CustomerServiceFactory.testService(
      CustomerServiceFactory.SERVICE_TYPES.INDEXED_DB
    )
    
    // Analyze environment
    const isOnline = navigator.onLine
    const hasLimitedStorage = 'storage' in navigator && 'estimate' in navigator.storage
    
    let storageQuota = Infinity
    if (hasLimitedStorage) {
      try {
        const estimate = await navigator.storage.estimate()
        storageQuota = estimate.quota || Infinity
      } catch (error) {
        console.warn('Could not get storage estimate')
      }
    }
    
    // Recommendation logic
    if (!isOnline && indexedDBAvailable && capabilities[CustomerServiceFactory.SERVICE_TYPES.INDEXED_DB].ready) {
      return CustomerServiceFactory.SERVICE_TYPES.INDEXED_DB
    }
    
    if (storageQuota < 50 * 1024 * 1024 && reactQueryAvailable) { // Less than 50MB
      return CustomerServiceFactory.SERVICE_TYPES.REACT_QUERY
    }
    
    if (reactQueryAvailable) {
      return CustomerServiceFactory.SERVICE_TYPES.REACT_QUERY
    }
    
    if (indexedDBAvailable) {
      return CustomerServiceFactory.SERVICE_TYPES.INDEXED_DB
    }
    
    // Fallback
    return CustomerServiceFactory.SERVICE_TYPES.REACT_QUERY
  }

  /**
   * Reset factory state (mainly for testing)
   */
  static reset() {
    CustomerServiceFactory.currentService = null
    CustomerServiceFactory.instance = null
  }
} 