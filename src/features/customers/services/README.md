# Customer Service Layer Architecture

This directory contains a modular customer data management system that supports multiple storage and caching strategies.

## Architecture Overview

The system uses the **Strategy Pattern** to switch between different customer data implementations:

- **Abstract Layer**: `CustomerDataService` - Defines the interface
- **Implementations**: 
  - `ReactQueryCustomerService` - In-memory caching with React Query
  - `IndexedDBCustomerService` - Persistent storage (placeholder)
- **Factory**: `CustomerServiceFactory` - Creates and manages service instances
- **Hook**: `useCustomers` - React hook that uses the selected service

## Service Types

### React Query Service (Default)
- **Type**: `react-query`
- **Cache**: In-memory
- **Persistence**: None (lost on refresh)
- **Performance**: Excellent
- **Best for**: Real-time apps, frequent updates
- **Status**: ✅ Ready

### IndexedDB Service (Placeholder)
- **Type**: `indexed-db`
- **Cache**: Persistent browser storage
- **Persistence**: Survives browser refresh
- **Performance**: Good
- **Best for**: Offline apps, large datasets
- **Status**: 🚧 Placeholder implementation

## Configuration

### Environment Variables

Add to your `.env` file:

```bash
# Customer service type: 'react-query' or 'indexed-db'
REACT_APP_CUSTOMER_SERVICE_TYPE=react-query
```

### Programmatic Switching

```javascript
import { CustomerServiceFactory } from './services/CustomerServiceFactory'

// Switch to IndexedDB
CustomerServiceFactory.switchToService('indexed-db')

// Switch back to React Query
CustomerServiceFactory.switchToService('react-query')

// Get current service info
const info = await CustomerServiceFactory.getCurrentServiceInfo()
console.log(info)
```

### Runtime Configuration

The service type is determined in this order:
1. Environment variable `REACT_APP_CUSTOMER_SERVICE_TYPE`
2. localStorage user preference
3. Default to `react-query`

## Usage Examples

### Basic Usage (Current Hook Interface)

```javascript
import { useCustomers } from './hooks/useCustomers'

function CustomerList() {
  const {
    allCustomers,
    filteredCustomers,
    searchTerm,
    setSearchTerm,
    isLoading,
    error,
    addCustomerToCache,
    refreshCustomers
  } = useCustomers()

  // Hook interface remains the same regardless of service type
  return (
    <div>
      <input 
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        placeholder="Search customers..."
      />
      {filteredCustomers.map(customer => (
        <div key={customer.id}>{customer.name}</div>
      ))}
    </div>
  )
}
```

### Service Management

```javascript
import { CustomerServiceFactory } from './services/CustomerServiceFactory'

// Get service capabilities
const capabilities = CustomerServiceFactory.getServiceCapabilities()

// Test service availability
const isAvailable = await CustomerServiceFactory.testService('indexed-db')

// Get recommendation based on environment
const recommended = await CustomerServiceFactory.getRecommendedServiceType()

// Get current service info
const info = await CustomerServiceFactory.getCurrentServiceInfo()
```

## Implementation Status

| Feature | React Query | IndexedDB |
|---------|-------------|-----------|
| Basic caching | ✅ | 🚧 |
| Search/filtering | ✅ | 🚧 |
| Cache invalidation | ✅ | 🚧 |
| Offline support | ❌ | 🚧 |
| Persistent storage | ❌ | 🚧 |
| Background sync | ✅ | 🚧 |

## Adding New Service Types

1. Create a new service class extending `CustomerDataService`
2. Implement all required methods
3. Add to `CustomerServiceFactory.SERVICE_TYPES`
4. Update the factory's `createService` method
5. Add service capabilities to `getServiceCapabilities`

## Migration Guide

The hook interface remains unchanged, so switching services requires no code changes in components:

```javascript
// Before (direct React Query usage)
const { data } = useQuery(['customers'], () => customerApi.getCustomers())

// After (service layer)
const { allCustomers } = useCustomers() // Works with any service type
```

## Environment Setup

### Development
```bash
# Use React Query for development (fast, good for debugging)
REACT_APP_CUSTOMER_SERVICE_TYPE=react-query
```

### Production
```bash
# Use IndexedDB for production (once fully implemented)
REACT_APP_CUSTOMER_SERVICE_TYPE=indexed-db
```

### Testing
```bash
# Use React Query for testing (predictable, no persistence side effects)
REACT_APP_CUSTOMER_SERVICE_TYPE=react-query
```

## Performance Comparison

| Metric | React Query | IndexedDB |
|--------|-------------|-----------|
| Initial load | Fast | Medium |
| Search performance | Excellent | Good |
| Memory usage | Low | Minimal |
| Storage size | Limited | Large |
| Offline capability | None | Full |
| Setup complexity | Low | High |

## Next Steps

To fully implement IndexedDB service:

1. Install IndexedDB wrapper: `npm install dexie`
2. Implement database schema in `IndexedDBCustomerService`
3. Add data synchronization logic
4. Implement offline detection and handling
5. Add conflict resolution for concurrent updates
6. Create migration scripts for existing data

## Troubleshooting

### Service Not Available
```javascript
const isReady = await CustomerServiceFactory.testService('indexed-db')
if (!isReady) {
  console.log('Service not available, falling back to React Query')
}
```

### Clear Cache
```javascript
const service = CustomerServiceFactory.getCurrentService()
await service.clearCache()
```

### Debug Service Info
```javascript
const info = await CustomerServiceFactory.getCurrentServiceInfo()
console.log('Current service:', info)
``` 