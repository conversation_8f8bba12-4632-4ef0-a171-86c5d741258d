import { useState } from 'react'

export const useModals = () => {
  const [expandedTransactions, setExpandedTransactions] = useState(new Set())
  const [showHistoryModal, setShowHistoryModal] = useState(false)
  const [selectedAppointmentHistory, setSelectedAppointmentHistory] = useState(null)

  const toggleTransaction = (appointmentId) => {
    setExpandedTransactions(prev => {
      const newSet = new Set(prev)
      if (newSet.has(appointmentId)) {
        newSet.delete(appointmentId)
      } else {
        newSet.add(appointmentId)
      }
      return newSet
    })
  }

  const openHistoryModal = (appointment) => {
    setSelectedAppointmentHistory(appointment)
    setShowHistoryModal(true)
  }

  const closeHistoryModal = () => {
    setShowHistoryModal(false)
    setSelectedAppointmentHistory(null)
  }

  const closeTransactionOnPageChange = () => {
    setExpandedTransactions(new Set())
  }

  const isTransactionExpanded = (appointmentId) => {
    return expandedTransactions.has(appointmentId)
  }

  const hasExpandedTransactions = () => {
    return expandedTransactions.size > 0
  }

  const collapseAllTransactions = () => {
    setExpandedTransactions(new Set())
  }

  return {
    // State
    expandedTransactions,
    showHistoryModal,
    selectedAppointmentHistory,
    
    // Actions
    toggleTransaction,
    openHistoryModal,
    closeHistoryModal,
    closeTransactionOnPageChange,
    isTransactionExpanded,
    hasExpandedTransactions,
    collapseAllTransactions
  }
} 