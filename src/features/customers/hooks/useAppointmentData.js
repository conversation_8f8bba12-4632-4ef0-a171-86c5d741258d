import { useState, useMemo } from 'react'
import { getAppointmentMockData } from '../data/appointmentMockData'

export const useAppointmentData = (customer) => {
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(5)
  const [sortOrder, setSortOrder] = useState('desc')
  const [dateRangeFilter, setDateRangeFilter] = useState('all')

  // Get appointment data
  const appointments = getAppointmentMockData()

  // Filter appointments based on date range filter
  const filteredAppointments = useMemo(() => {
    return appointments.filter(appointment => {
      const matchesDateRange = dateRangeFilter === 'all' || (() => {
        const appointmentDate = new Date(appointment.appointmentDate)
        const now = new Date()
        
        switch (dateRangeFilter) {
          case 'thisMonth':
            return appointmentDate.getMonth() === now.getMonth() && 
                   appointmentDate.getFullYear() === now.getFullYear()
          case 'lastMonth':
            const lastMonth = new Date(now)
            lastMonth.setMonth(now.getMonth() - 1)
            return appointmentDate.getMonth() === lastMonth.getMonth() && 
                   appointmentDate.getFullYear() === lastMonth.getFullYear()
          case 'last3Months':
            const threeMonthsAgo = new Date(now)
            threeMonthsAgo.setMonth(now.getMonth() - 3)
            return appointmentDate >= threeMonthsAgo
          case 'thisYear':
            return appointmentDate.getFullYear() === now.getFullYear()
          default:
            return true
        }
      })()
      
      return matchesDateRange
    })
  }, [appointments, dateRangeFilter])

  // Sort appointments
  const sortedAppointments = useMemo(() => {
    return [...filteredAppointments].sort((a, b) => {
      const dateA = new Date(a.appointmentDate)
      const dateB = new Date(b.appointmentDate)
      return sortOrder === 'desc' ? dateB - dateA : dateA - dateB
    })
  }, [filteredAppointments, sortOrder])

  // Pagination
  const totalPages = Math.ceil(sortedAppointments.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const currentAppointments = sortedAppointments.slice(startIndex, endIndex)

  // Get last visit date
  const lastVisitDate = appointments
    .filter(apt => apt.status === 'completed' && apt.lastVisit)
    .sort((a, b) => new Date(b.lastVisit) - new Date(a.lastVisit))[0]?.lastVisit

  const toggleSort = () => {
    setSortOrder(sortOrder === 'desc' ? 'asc' : 'desc')
  }

  const handlePageChange = (page) => {
    setCurrentPage(page)
  }

  const handleFilterChange = () => {
    setCurrentPage(1)
  }

  const clearFilters = () => {
    setDateRangeFilter('all')
    setCurrentPage(1)
  }

  return {
    // Data
    currentAppointments,
    sortedAppointments,
    lastVisitDate,
    
    // Pagination
    currentPage,
    totalPages,
    startIndex,
    endIndex,
    itemsPerPage,
    
    // Filters
    sortOrder,
    dateRangeFilter,
    
    // Actions
    toggleSort,
    handlePageChange,
    handleFilterChange,
    clearFilters,
    setDateRangeFilter
  }
} 