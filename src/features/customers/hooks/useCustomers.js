import { useState, useMemo } from 'react'
import { CustomerServiceFactory } from '../services/CustomerServiceFactory'

/**
 * Custom hook for efficient customer management
 * - Uses configurable service layer (React Query or IndexedDB)
 * - Loads minimal customer data once and caches it
 * - Provides fast client-side search/filtering
 * - <PERSON><PERSON> loads detailed customer data only when needed
 * - TRULY SERVICE-AGNOSTIC - no React Query imports
 */
export const useCustomers = () => {
  const [searchTerm, setSearchTerm] = useState('')

  // Get the current service instance
  const customerService = CustomerServiceFactory.getCurrentService()

  // Delegate all caching logic to the service layer
  const {
    data: customersData,
    isLoading,
    error,
    refetch
  } = customerService.useCustomersMinimal()

  // Transform API response to consistent format
  const allCustomers = useMemo(() => {
    if (!customersData) return []
    
    let customerList = []
    if (customersData.results) {
      customerList = customersData.results
    } else if (Array.isArray(customersData)) {
      customerList = customersData
    }

    return customerList.map(customer => ({
      id: customer.id,
      first_name: customer.first_name || '',
      last_name: customer.last_name || '',
      name: customer.first_name && customer.last_name 
        ? `${customer.first_name} ${customer.last_name}`.trim()
        : customer.first_name || customer.last_name || 'Unknown Customer',
      email: customer.email || '',
      phone: customer.phone || customer.phone_number || '',
      has_upcoming_appointment: customer.has_upcoming_appointment || false,
      // Store original data for detailed loading
      _originalData: customer
    }))
  }, [customersData])

  // Fast client-side filtering (no network calls)
  const filteredCustomers = useMemo(() => {
    if (!searchTerm.trim()) return allCustomers
    
    const term = searchTerm.toLowerCase().trim()
    return allCustomers.filter(customer =>
      customer.name.toLowerCase().includes(term) ||
      customer.email.toLowerCase().includes(term) ||
      customer.phone.includes(searchTerm) // Direct match for phone numbers
    )
  }, [allCustomers, searchTerm])

  // Get detailed customer data (lazy loading) - delegated to service
  const useCustomerDetails = (customerId) => {
    return customerService.useCustomerDetails(customerId)
  }

  // Add new customer to cache (after creation)
  const addCustomerToCache = async (newCustomer) => {
    await customerService.addCustomerToCache(newCustomer)
  }

  // Refresh customer list
  const refreshCustomers = async () => {
    await customerService.refreshCustomers()
    return refetch()
  }

  // Clear search
  const clearSearch = () => {
    setSearchTerm('')
  }

  return {
    // Data
    allCustomers,
    filteredCustomers,
    customersCount: allCustomers.length,
    filteredCount: filteredCustomers.length,
    
    // Loading states
    isLoading,
    error,
    
    // Search
    searchTerm,
    setSearchTerm,
    clearSearch,
    
    // Actions
    addCustomerToCache,
    refreshCustomers,
    
    // Detailed data hook
    useCustomerDetails,
  }
}

// Hook for getting detailed customer data - delegated to service
export const useCustomerDetails = (customerId) => {
  const customerService = CustomerServiceFactory.getCurrentService()
  return customerService.useCustomerDetails(customerId)
} 