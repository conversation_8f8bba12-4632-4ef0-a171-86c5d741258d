// Calendar Components
export * from './components'

// Calendar Hooks
export { useAdvancedCalendar, DISPLAY_MODES } from './hooks/useAdvancedCalendar'
export { useCalendarConfig } from './hooks/useCalendarConfig'
export { useActionSheet } from './hooks/useActionSheet'
export { useAppointmentCreation, CREATION_STEPS } from './hooks/useAppointmentCreation'
export { AppointmentCreationModal } from './components'

// Calendar Services
export { appointmentService } from './services/appointmentService'
export { appointmentStatusService } from './services/appointmentStatusService'
export { workingHoursManager, workingHoursService } from './services/workingHoursService'

// Employee Services (re-exported for convenience)
export { employeeApiService } from '../employees/services'

// Calendar Configuration & Types
export { 
  APPOINTMENT_STATUSES,
  BREAK_TYPES,
  CALENDAR_CONFIG 
} from './constants/calendarConfig'

// Week Utilities
export {
  generateWeekDays,
  getWeekDayHeaders,
  calculateWeekShift,
  formatWeekRange,
  isDateInCurrentWeek,
  getWeekStartChangeDescription,
  DAY_NAMES,
  DAY_NAMES_SHORT
} from './utils/weekUtils'

/**
 * Calendar Feature Module
 * 
 * This is the main entry point for the calendar feature.
 * Import everything from here rather than directly from subfolders.
 * 
 * The calendar now uses a modular Tailwind CSS system with style hooks
 * and layout components for better maintainability and performance.
 * 
 * Example usage:
 * ```
 * import { Calendar, useAdvancedCalendar } from '@/features/calendar'
 * ```
 * 
 * Note: Styling is now handled automatically through Tailwind CSS
 * and the useCalendarStyles/useAppointmentStyles hooks.
 */ 