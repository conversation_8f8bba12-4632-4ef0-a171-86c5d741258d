import React, { useState, useEffect } from 'react'
import { 
  ClockIcon, 
  CalendarDaysIcon, 
  CogIcon,
  UserGroupIcon,
  Cog6ToothIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline'
import { useCalendarConfig } from '../hooks/useCalendarConfig'
import { employeeApiService } from '../index'
import { DAYS_OF_WEEK, workingHoursManager, workingHoursService } from '../services/workingHoursService'

/**
 * Working Hours Configuration Tab Content
 */
const WorkingHoursTab = ({ config, updateConfig }) => {
  const [employees, setEmployees] = useState([])
  const [selectedEmployee, setSelectedEmployee] = useState(null)
  const [workingHours, setWorkingHours] = useState({})
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState(null)
  const [currentEmployeeId, setCurrentEmployeeId] = useState(null)

  useEffect(() => {
    const fetchEmployees = async () => {
      try {
        setIsLoading(true)
        setError(null)
        
        // Fetch both all employees and current user info (same as CalendarSettingsPanel)
        const [employees, currentEmployee] = await Promise.all([
          employeeApiService.getAllEmployees(),
          employeeApiService.getCurrentEmployee().catch(() => null)
        ])
        
        // Set current employee ID
        if (currentEmployee) {
          setCurrentEmployeeId(currentEmployee.id)
        }
        
        // Transform API data to match expected format for display
        const transformedEmployees = employees.map((emp, index) => {
          const fullName = emp.full_name || 'Unknown Employee'
          const nameParts = fullName.split(' ')
          const firstName = nameParts[0] || ''
          const lastName = nameParts.slice(1).join(' ') || ''
          
          return {
            id: emp.id,
            name: fullName,
            full_name: fullName,
            first_name: firstName,
            last_name: lastName,
            avatar: (firstName[0] || '') + (lastName[0] || '') || (fullName[0] || '') + (fullName[1] || '') || 'U',
            color: emp.color || ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'][index % 5],
            email: emp.user_details?.email || emp.email,
            is_active: emp.is_active,
            canEdit: currentEmployee ? emp.id === currentEmployee.id : false,
            isCurrentUser: currentEmployee ? emp.id === currentEmployee.id : false
          }
        })
        
        setEmployees(transformedEmployees)
        
        // Select current user by default if available
        if (currentEmployee) {
          const currentUserEmployee = transformedEmployees.find(emp => emp.id === currentEmployee.id)
          if (currentUserEmployee) {
            setSelectedEmployee(currentUserEmployee)
          }
        } else if (transformedEmployees.length > 0) {
          setSelectedEmployee(transformedEmployees[0])
        }
      } catch (err) {
        setError('Failed to load employees')
        setEmployees([])
      } finally {
        setIsLoading(false)
      }
    }

    fetchEmployees()
  }, [])

  useEffect(() => {
    if (selectedEmployee) {
      const fetchWorkingHours = async () => {
        try {
          // Use employeeApiService directly - only current user can get their actual working hours
          const hours = selectedEmployee.isCurrentUser 
            ? await employeeApiService.getEmployeeWorkingHours('me')
            : await employeeApiService.getEmployeeWorkingHours(selectedEmployee.id)
          setWorkingHours(hours || {})
        } catch (err) {
          setError('Failed to load working hours')
          // Set default working hours if API fails
          setWorkingHours({
            monday: { isWorking: true, startTime: '09:00', endTime: '17:00' },
            tuesday: { isWorking: true, startTime: '09:00', endTime: '17:00' },
            wednesday: { isWorking: true, startTime: '09:00', endTime: '17:00' },
            thursday: { isWorking: true, startTime: '09:00', endTime: '17:00' },
            friday: { isWorking: true, startTime: '09:00', endTime: '17:00' },
            saturday: { isWorking: false, startTime: '09:00', endTime: '17:00' },
            sunday: { isWorking: false, startTime: '09:00', endTime: '17:00' }
          })
        }
      }
      fetchWorkingHours()
    }
  }, [selectedEmployee])

  const handleSave = async () => {
    if (!selectedEmployee) return

    try {
      setIsSaving(true)
      // Backend only supports saving current user's working hours
      if (selectedEmployee.isCurrentUser) {
        await employeeApiService.updateEmployeeWorkingHours(workingHours)
        setError(null)
      } else {
        setError('Can only edit working hours for current user')
      }
    } catch (err) {
      setError('Failed to save working hours')
    } finally {
      setIsSaving(false)
    }
  }

  const toggleDayEnabled = (day) => {
    setWorkingHours(prev => ({
      ...prev,
      [day]: {
        ...prev[day],
        isWorking: !prev[day]?.isWorking
      }
    }))
  }

  const updateDayHours = (day, field, value) => {
    setWorkingHours(prev => ({
      ...prev,
      [day]: {
        ...prev[day],
        [field]: value
      }
    }))
  }

  if (isLoading) {
    return (
      <div className="animate-pulse space-y-4">
        <div className="h-4 bg-gray-200 rounded w-1/4"></div>
        <div className="h-32 bg-gray-200 rounded"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <p className="text-sm text-gray-600">
        Manage working hours for each employee. These hours will be highlighted in the calendar.
      </p>
      
      <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="flex items-start gap-2">
          <div className="text-blue-600 text-sm">ℹ️</div>
          <div className="text-sm text-blue-800">
            <strong>Note:</strong> You can only edit your own working hours. Other employees need to update their hours themselves.
          </div>
        </div>
      </div>
      
      {error && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}

      {/* Employee Selection */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Employee</label>
        <select
          value={selectedEmployee?.id || ''}
          onChange={(e) => {
            const employeeId = parseInt(e.target.value)
            const employee = employees.find(emp => emp.id === employeeId)
            setSelectedEmployee(employee)
          }}
          className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          {employees.filter(emp => emp.is_active !== false).map(employee => (
            <option key={employee.id} value={employee.id}>
              {employee.full_name}{employee.isCurrentUser ? ' (You)' : ''}
            </option>
          ))}
        </select>
      </div>

      {/* Working Hours Editor */}
      {selectedEmployee && (
        <div className="space-y-3">
          {Object.values(DAYS_OF_WEEK).map(dayObj => (
            <div key={dayObj.id} className="flex items-center gap-4 p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-2 w-20">
                <input
                  type="checkbox"
                  checked={workingHours[dayObj.id]?.isWorking || false}
                  onChange={() => toggleDayEnabled(dayObj.id)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm font-medium text-gray-700">{dayObj.shortName}</span>
              </div>
              
              {workingHours[dayObj.id]?.isWorking && (
                <div className="flex items-center gap-2 flex-1">
                  <input
                    type="time"
                    value={workingHours[dayObj.id]?.startTime || '09:00'}
                    onChange={(e) => updateDayHours(dayObj.id, 'startTime', e.target.value)}
                    className="border border-gray-300 rounded px-2 py-1 text-sm"
                  />
                  <span className="text-gray-500">to</span>
                  <input
                    type="time"
                    value={workingHours[dayObj.id]?.endTime || '17:00'}
                    onChange={(e) => updateDayHours(dayObj.id, 'endTime', e.target.value)}
                    className="border border-gray-300 rounded px-2 py-1 text-sm"
                  />
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Save Button */}
      {selectedEmployee && (
        <button
          onClick={handleSave}
          disabled={isSaving || !selectedEmployee.isCurrentUser}
          className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isSaving ? 'Saving...' : 'Save Working Hours'}
        </button>
      )}
    </div>
  )
}

/**
 * Toggle Switch Component
 */
const ToggleSwitch = ({ label, description, checked, onChange }) => (
  <div className="flex items-start justify-between p-3 bg-gray-50 rounded-lg">
    <div className="flex-1">
      <div className="font-medium text-gray-900">{label}</div>
      {description && <div className="text-sm text-gray-600">{description}</div>}
    </div>
    <label className="relative inline-flex items-center cursor-pointer ml-3">
      <input
        type="checkbox"
        checked={checked}
        onChange={onChange}
        className="sr-only peer"
      />
      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
    </label>
  </div>
)

/**
 * Option Button Component
 */
const OptionButton = ({ selected, onClick, children }) => (
  <button
    onClick={onClick}
    className={`p-3 rounded-lg text-center transition-colors ${
      selected
        ? 'bg-blue-100 text-blue-700 border border-blue-200'
        : 'bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-150'
    }`}
  >
    {children}
  </button>
)

/**
 * General Settings Tab Content
 */
const GeneralSettingsTab = ({ config, updateConfig, WEEK_START_OPTIONS }) => (
  <div className="space-y-6">
    {/* Default View */}
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">Default View</label>
      <div className="flex gap-2">
        {['day', 'week'].map(view => (
          <OptionButton
            key={view}
            selected={config.defaultView === view}
            onClick={() => updateConfig({ defaultView: view })}
          >
            {view.charAt(0).toUpperCase() + view.slice(1)} View
          </OptionButton>
        ))}
      </div>
    </div>

    {/* Week Start Day */}
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">Week Starts On</label>
      <select
        value={config.weekStartDay}
        onChange={(e) => updateConfig({ weekStartDay: parseInt(e.target.value) })}
        className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
      >
        {WEEK_START_OPTIONS.map(option => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    </div>
  </div>
)

/**
 * Time Display Tab Content
 */
const TimeDisplayTab = ({ config, updateConfig, TIME_RESOLUTIONS, DISPLAY_HOURS_PRESETS }) => (
  <div className="space-y-6">
    {/* Time Resolution */}
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">Time Resolution</label>
      <div className="grid grid-cols-3 gap-2">
        {TIME_RESOLUTIONS.map(resolution => (
          <OptionButton
            key={resolution.value}
            selected={config.timeResolution === resolution.value}
            onClick={() => updateConfig({ 
              timeResolution: resolution.value,
              gridHeight: resolution.gridHeight 
            })}
          >
            <div className="font-medium">{resolution.value}min</div>
            <div className="text-xs">{resolution.label}</div>
          </OptionButton>
        ))}
      </div>
    </div>

    {/* Display Hours */}
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">Display Hours</label>
      <div className="grid grid-cols-2 gap-2 mb-3">
        {DISPLAY_HOURS_PRESETS.map(preset => (
          <OptionButton
            key={preset.name}
            selected={config.displayHourStart === preset.start && config.displayHourEnd === preset.end}
            onClick={() => updateConfig({ 
              displayHourStart: preset.start,
              displayHourEnd: preset.end 
            })}
          >
            <div className="font-medium">{preset.name}</div>
            <div className="text-xs">{preset.label}</div>
          </OptionButton>
        ))}
      </div>
      
      {/* Custom Hours */}
      <div className="flex items-center gap-3">
        <div className="flex-1">
          <label className="block text-xs text-gray-500 mb-1">Start Hour</label>
          <input
            type="number"
            min="0"
            max="23"
            value={config.displayHourStart}
            onChange={(e) => updateConfig({ displayHourStart: parseInt(e.target.value) })}
            className="w-full border border-gray-300 rounded px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        <div className="flex-1">
          <label className="block text-xs text-gray-500 mb-1">End Hour</label>
          <input
            type="number"
            min="1"
            max="24"
            value={config.displayHourEnd}
            onChange={(e) => updateConfig({ displayHourEnd: parseInt(e.target.value) })}
            className="w-full border border-gray-300 rounded px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>
    </div>
  </div>
)



/**
 * Main Calendar Configuration Component with Tabs
 */
const CalendarConfiguration = () => {
  const { config, updateConfig, resetConfig, TIME_RESOLUTIONS, WEEK_START_OPTIONS, DISPLAY_HOURS_PRESETS } = useCalendarConfig()
  const [activeTab, setActiveTab] = useState('general')
  const [resetConfirmation, setResetConfirmation] = useState(false)

  const handleReset = () => {
    resetConfig()
    setResetConfirmation(false)
  }

  const tabs = [
    { id: 'general', label: 'General', icon: CogIcon },
    { id: 'time', label: 'Time Display', icon: ClockIcon },
    { id: 'working-hours', label: 'Working Hours', icon: UserGroupIcon }
  ]

  const renderTabContent = () => {
    switch (activeTab) {
      case 'general':
        return <GeneralSettingsTab config={config} updateConfig={updateConfig} WEEK_START_OPTIONS={WEEK_START_OPTIONS} />
      case 'time':
        return <TimeDisplayTab config={config} updateConfig={updateConfig} TIME_RESOLUTIONS={TIME_RESOLUTIONS} DISPLAY_HOURS_PRESETS={DISPLAY_HOURS_PRESETS} />
      case 'working-hours':
        return <WorkingHoursTab config={config} updateConfig={updateConfig} />
      default:
        return null
    }
  }

  return (
    <div className="w-full h-full">
      {/* Tab Navigation */}
      <div className="border-b border-gray-200 mb-6 bg-white">
        <nav className="flex space-x-8 overflow-x-auto px-6">
          {tabs.map(tab => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center gap-3 py-4 px-3 border-b-2 font-medium text-base whitespace-nowrap ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="h-5 w-5" />
                {tab.label}
              </button>
            )
          })}
        </nav>
      </div>

      {/* Scrollable Content Wrapper */}
      <div className="max-h-[calc(100vh-200px)] overflow-y-auto px-6">
        {/* Tab Content */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          {renderTabContent()}
        </div>

        {/* Reset Section */}
        <div className="mt-6 bg-gray-50 rounded-lg border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-900">Reset Configuration</h3>
              <p className="text-sm text-gray-600">Reset all settings to default values</p>
            </div>
            <div className="flex items-center gap-3">
              {resetConfirmation ? (
                <>
                  <span className="text-sm text-gray-600">Are you sure?</span>
                  <button
                    onClick={handleReset}
                    className="px-3 py-1 text-sm bg-red-600 text-white rounded-lg hover:bg-red-700"
                  >
                    <CheckCircleIcon className="h-4 w-4 inline mr-1" />
                    Yes, Reset
                  </button>
                  <button
                    onClick={() => setResetConfirmation(false)}
                    className="px-3 py-1 text-sm bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400"
                  >
                    Cancel
                  </button>
                </>
              ) : (
                <button
                  onClick={() => setResetConfirmation(true)}
                  className="px-4 py-2 text-sm bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                >
                  <Cog6ToothIcon className="h-4 w-4 inline mr-1" />
                  Reset All Settings
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Spacer for bottom padding */}
        <div className="h-6"></div>
      </div>
    </div>
  )
}

export default CalendarConfiguration 