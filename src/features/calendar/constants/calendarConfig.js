// Appointment status definitions with colors and descriptions - matching Django backend exactly
export const APPOINTMENT_STATUSES = {
  REQUESTED: {
    id: 'requested',
    name: 'Requested',
    color: '#6b7280', // Gray
    bgColor: '#f3f4f6',
    textColor: '#4b5563',
    description: 'Appointment requested, awaiting confirmation'
  },
  CONFIRMED: {
    id: 'confirmed',
    name: 'Confirmed',
    color: '#3b82f6', // Blue
    bgColor: '#dbeafe',
    textColor: '#2563eb',
    description: 'Appointment confirmed by customer'
  },
  ACCEPTED: {
    id: 'accepted',
    name: 'Accepted',
    color: '#059669', // Green
    bgColor: '#d1fae5',
    textColor: '#047857',
    description: 'Appointment accepted by staff'
  },
  CHECKED_IN: {
    id: 'checked_in',
    name: 'Checked In',
    color: '#0891b2', // Cyan
    bgColor: '#cffafe',
    textColor: '#0e7490',
    description: 'Customer has checked in'
  },
  SERVICE_STARTED: {
    id: 'service_started',
    name: 'Service Started',
    color: '#eab308', // Yellow
    bgColor: '#fef3c7',
    textColor: '#d97706',
    description: 'Service currently in progress'
  },
  COMPLETED: {
    id: 'completed',
    name: 'Completed',
    color: '#22c55e', // Green
    bgColor: '#dcfce7',
    textColor: '#15803d',
    description: 'Appointment completed successfully'
  },
  CANCELLED: {
    id: 'cancelled',
    name: 'Cancelled',
    color: '#f97316', // Orange
    bgColor: '#fed7aa',
    textColor: '#ea580c',
    description: 'Appointment cancelled'
  },
  NO_SHOW: {
    id: 'no_show',
    name: 'No Show',
    color: '#ef4444', // Red
    bgColor: '#fee2e2',
    textColor: '#dc2626',
    description: 'Customer did not show up'
  }
}

// Employee break/unavailable slot types
export const BREAK_TYPES = {
  LUNCH: {
    id: 'lunch',
    name: 'Lunch Break',
    color: '#f59e0b',
    icon: '🍽️',
    isBookable: false
  },
  BREAK: {
    id: 'break',
    name: 'Break',
    color: '#8b5cf6',
    icon: '☕',
    isBookable: false
  },
  MEETING: {
    id: 'meeting',
    name: 'Meeting',
    color: '#ef4444',
    icon: '👥',
    isBookable: false
  },
  PERSONAL: {
    id: 'personal',
    name: 'Personal Time',
    color: '#6b7280',
    icon: '🚶',
    isBookable: false
  },
  TRAINING: {
    id: 'training',
    name: 'Training',
    color: '#10b981',
    icon: '📚',
    isBookable: false
  }
}

export const CALENDAR_CONFIG = {
  dayBoundaries: {
    start: '07:00',
    end: '22:00'
  },
  defaultView: 'week',
  firstDayOfWeek: 1, // Monday
  weekOptions: {
    gridHeight: 1440, // 24 hours * 60px per hour (full day)
    eventWidth: 98,
    nDays: 7,
    hourHeight: 60 // 60px per hour for better spacing
  },
  monthGridOptions: {
    nEventsPerDay: 4
  },
  // Drag and drop configuration
  dragAndDrop: {
    enabled: true,
    snapInterval: 1, // 1 minute precision for smooth dragging
    allowOverlap: false, // Prevent overlapping appointments for same employee
    crossEmployeeMove: true // Allow moving appointments between employees
  },
  // Resize configuration
  resize: {
    enabled: true,
    snapInterval: 1, // 1 minute precision for smooth resizing
    minDuration: 15, // Minimum 15 minutes appointment
    maxDuration: 480 // Maximum 8 hours appointment
  },
  // Appointment validation rules
  validation: {
    preventOverlap: true,
    enforceBusinessHours: true, // Allow 24-hour scheduling
    allowBackToBack: true, // Allow appointments to be attached head-to-tail
    bufferTime: 0 // No buffer time between appointments (allows attachment)
  }
}

// Create calendar categories for service types
export const createCalendarCategories = () => {
  const hexToRgba = (hex, alpha) => {
    const r = parseInt(hex.slice(1, 3), 16)
    const g = parseInt(hex.slice(3, 5), 16)
    const b = parseInt(hex.slice(5, 7), 16)
    return `rgba(${r}, ${g}, ${b}, ${alpha})`
  }

  return SERVICE_CATEGORIES.reduce((acc, service) => {
    acc[service.id] = {
      colorName: service.name,
      lightColors: {
        main: service.color,
        container: service.color,
        onContainer: '#ffffff',
      },
      darkColors: {
        main: service.color,
        container: service.color,
        onContainer: '#ffffff',
      }
    }
    return acc
  }, {})
}

// Create status-based calendar categories
export const createStatusCategories = () => {
  return Object.values(APPOINTMENT_STATUSES).reduce((acc, status) => {
    acc[status.id] = {
      colorName: status.name,
      lightColors: {
        main: status.color,
        container: status.bgColor,
        onContainer: status.textColor,
      },
      darkColors: {
        main: status.color,
        container: status.color + '20',
        onContainer: status.color,
      }
    }
    return acc
  }, {})
}

// Create break type categories
export const createBreakCategories = () => {
  return Object.values(BREAK_TYPES).reduce((acc, breakType) => {
    acc[breakType.id] = {
      colorName: breakType.name,
      lightColors: {
        main: breakType.color,
        container: breakType.color + '20',
        onContainer: breakType.color,
      },
      darkColors: {
        main: breakType.color,
        container: breakType.color + '30',
        onContainer: breakType.color,
      }
    }
    return acc
  }, {})
}

// Helper function to get status badge styling
export const getStatusBadge = (status) => {
  const statusConfig = Object.values(APPOINTMENT_STATUSES).find(s => s.id === status)
  if (!statusConfig) return 'bg-gray-100 text-gray-800'
  
  return {
    className: `bg-[${statusConfig.bgColor}] text-[${statusConfig.textColor}]`,
    style: {
      backgroundColor: statusConfig.bgColor,
      color: statusConfig.textColor
    }
  }
} 