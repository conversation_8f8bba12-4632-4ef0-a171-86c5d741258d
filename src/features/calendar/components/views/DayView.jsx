import React, { useRef, useCallback } from 'react'
import TimeGrid from '../core/TimeGrid'
import WorkingHoursOverlay from '../core/WorkingHoursOverlay'
import TimeSlot from '../slots/TimeSlot'
import EmployeeHeader from '../core/EmployeeHeader'

/**
 * DayView - Day calendar view component with multi-employee column support
 */
const DayView = ({
  selectedDate,
  timeSlots,
  config,
  selectedEmployees,
  getAppointmentsForSlot,
  isWorkingHourSlot,
  getWorkingHoursForEmployee,
  onSlotClick,
  onAppointmentClick,
  onAppointmentDragStart, // New prop for drag start
  onAppointmentDragMove, // New prop for drag handling
  onAppointmentDragEnd,  // New prop for drag completion
  onAppointmentResizeStart, // New prop for resize start
  onAppointmentResizeMove, // New prop for resize handling
  onAppointmentResizeEnd,  // New prop for resize completion
  onAppointmentStatusUpdate, // New prop for status updates
  onAppointmentEdit, // New prop for appointment editing
  onAppointmentMove, // New prop for appointment moving

  timeGridRef,
  scrollContainerRef,
  onScroll,
  allAppointments = [], // New prop for appointment border detection
  isDragging = false, // New prop to prevent slot clicks during drag
  isShowingConfirmation = false, // New prop to track if confirmation modal is open
  draggedAppointmentId = null, // New prop to track which appointment is being dragged
  globalDragOffset = { x: 0, y: 0 }, // New prop for shared drag offset
  isResizing = false, // New prop to prevent slot clicks during resize
  isShowingResizeConfirmation = false, // New prop to track if resize confirmation modal is open
  resizedAppointmentId = null, // New prop to track which appointment is being resized
  globalResizeOffset = { x: 0, y: 0 }, // New prop for shared resize offset
  isUpdatingStatus = false // New prop to track status update state
}) => {
  const containerRef = useRef(null)
  
  // Constants for layout calculations (matching iOS)
  const TIME_COLUMN_WIDTH = 64 // 64px for time column
  const BORDER_WIDTH = 1
  
  // Calculate column widths
  const calculateColumnWidth = useCallback(() => {
    if (!containerRef.current) return 0
    const totalWidth = containerRef.current.offsetWidth
    const availableWidth = totalWidth - TIME_COLUMN_WIDTH
    return selectedEmployees.length > 0 ? (availableWidth - (selectedEmployees.length - 1) * BORDER_WIDTH) / selectedEmployees.length : availableWidth
  }, [selectedEmployees.length])

  // Enhanced slot click handler with employee detection
  const handleSlotClick = useCallback((date, hour, minute, event) => {
    if (!config.clickToCreate) return
    
    let targetEmployee = null
    
    // Multi-employee tap detection (similar to iOS logic)
    if (selectedEmployees.length > 1) {
      const rect = containerRef.current?.getBoundingClientRect()
      if (rect && event.clientX !== undefined) {
        const relativeX = event.clientX - rect.left - TIME_COLUMN_WIDTH
        const columnWidth = calculateColumnWidth()
        
        if (relativeX >= 0) {
          const columnIndex = Math.min(
            Math.floor(relativeX / (columnWidth + BORDER_WIDTH)),
            selectedEmployees.length - 1
          )
          targetEmployee = selectedEmployees[columnIndex]
        }
      }
    } else if (selectedEmployees.length === 1) {
      targetEmployee = selectedEmployees[0]
    }
    
    // Create enhanced event object with employee context
    const enhancedEvent = {
      ...event,
      targetEmployee,
      columnInfo: {
        totalColumns: selectedEmployees.length,
        columnWidth: calculateColumnWidth()
      }
    }
    
    onSlotClick(date, hour, minute, enhancedEvent)
  }, [selectedEmployees, calculateColumnWidth, config.clickToCreate, onSlotClick])

  // Single employee layout (simplified)
  if (selectedEmployees.length <= 1) {
    const employee = selectedEmployees[0]
    
  return (
    <>
        {/* Single employee header */}
      <div className="bg-gray-50 border-b border-gray-200 flex">
        <div className="w-16 p-3 border-r border-gray-200 flex-shrink-0"></div>
          <div className="flex-1">
            {employee ? (
              <EmployeeHeader 
                employee={employee}
                date={selectedDate}
                isToday={selectedDate.toDateString() === new Date().toDateString()}
              />
            ) : (
              <div className="p-3 text-center">
          <div className="text-sm font-medium text-gray-600">
            {selectedDate.toLocaleDateString('en-US', { weekday: 'short' })}
          </div>
          <div className={`text-lg font-semibold ${
            selectedDate.toDateString() === new Date().toDateString() ? 'text-blue-600' : 'text-gray-900'
          }`}>
            {selectedDate.getDate()}
          </div>
              </div>
            )}
        </div>
      </div>

        {/* Time grid container */}
        <div className="flex-1 calendar-scroll-container overflow-y-auto overflow-x-hidden" ref={timeGridRef} onScroll={onScroll}>
        <TimeGrid 
          timeSlots={timeSlots} 
          config={config}
          selectedDate={selectedDate}
          displayMode="day"
          selectedEmployees={selectedEmployees}
        >
            <div className="flex-1 border-r border-gray-200 relative" ref={containerRef}>
              {employee && (
              <WorkingHoursOverlay
                date={selectedDate}
                employeeId={employee.id}
                config={config}
                getWorkingHoursForEmployee={getWorkingHoursForEmployee}
              />
              )}
            
            {timeSlots.map((slot, slotIndex) => {
              const slotAppointments = getAppointmentsForSlot(selectedDate, slot.hour, slot.minute)
                const isWorking = employee ? isWorkingHourSlot(selectedDate, slot.hour, slot.minute, employee.id) : true
              
              return (
                <TimeSlot
                  key={slotIndex}
                  date={selectedDate}
                  hour={slot.hour}
                  minute={slot.minute}
                  appointments={slotAppointments}
                  config={config}
                    isWorking={isWorking}
                    onClick={handleSlotClick}
                  onAppointmentClick={onAppointmentClick}
                  onAppointmentDragStart={onAppointmentDragStart}
                  onAppointmentDragMove={onAppointmentDragMove}
                  onAppointmentDragEnd={onAppointmentDragEnd}
                  onAppointmentResizeStart={onAppointmentResizeStart}
                  onAppointmentResizeMove={onAppointmentResizeMove}
                  onAppointmentResizeEnd={onAppointmentResizeEnd}
                  onAppointmentStatusUpdate={onAppointmentStatusUpdate}
                  onAppointmentEdit={onAppointmentEdit}
                  onAppointmentMove={onAppointmentMove}
                  scrollContainerRef={scrollContainerRef}
                  displayMode="day"
                  selectedEmployees={selectedEmployees}
                  allAppointments={allAppointments}
                  currentDate={selectedDate}
                  isDragging={isDragging}
                  isShowingConfirmation={isShowingConfirmation}
                  draggedAppointmentId={draggedAppointmentId}
                  globalDragOffset={globalDragOffset}
                  isResizing={isResizing}
                  isShowingResizeConfirmation={isShowingResizeConfirmation}
                  resizedAppointmentId={resizedAppointmentId}
                  globalResizeOffset={globalResizeOffset}
                  isUpdatingStatus={isUpdatingStatus}
                />
              )
            })}
            </div>
          </TimeGrid>
        </div>
      </>
    )
  }

  // Multi-employee layout with columns
  const columnWidth = calculateColumnWidth()
  
  return (
    <>
      {/* Multi-employee header */}
      <div className="bg-gray-50 border-b border-gray-200 flex">
        <div className="w-16 p-3 border-r border-gray-200 flex-shrink-0"></div>
        <div className="flex-1 flex">
          {selectedEmployees.map((employee, index) => (
            <div 
              key={employee.id}
              className={`flex-1 border-r border-gray-200 ${index === selectedEmployees.length - 1 ? 'border-r-0' : ''}`}
            >
              <EmployeeHeader 
                employee={employee}
                date={selectedDate}
                isToday={selectedDate.toDateString() === new Date().toDateString()}
                compact={true}
              />
            </div>
          ))}
        </div>
      </div>

      {/* Time grid container */}
      <div className="flex-1 calendar-scroll-container overflow-y-auto overflow-x-hidden" ref={timeGridRef} onScroll={onScroll}>
        <TimeGrid 
          timeSlots={timeSlots} 
          config={config}
          selectedDate={selectedDate}
          displayMode="day"
          selectedEmployees={selectedEmployees}
        >
          <div className="flex-1 flex" ref={containerRef}>
            {selectedEmployees.map((employee, employeeIndex) => (
              <div 
                key={employee.id}
                className={`flex-1 border-r border-gray-200 relative ${employeeIndex === selectedEmployees.length - 1 ? 'border-r-0' : ''}`}
                data-employee-id={employee.id}
              >
                {/* Working hours background for this employee */}
                <WorkingHoursOverlay
                  date={selectedDate}
                  employeeId={employee.id}
                  config={config}
                  getWorkingHoursForEmployee={getWorkingHoursForEmployee}
                />
                
                {/* Time slots for this employee column */}
                {timeSlots.map((slot, slotIndex) => {
                  // Filter appointments for this specific employee
                  const allSlotAppointments = getAppointmentsForSlot(selectedDate, slot.hour, slot.minute)
                  // Handle both string and number employee ID comparisons to fix disappearing appointments bug
                  const employeeAppointments = allSlotAppointments.filter(apt => {
                    const matches = apt.employeeId === employee.id || apt.employeeId === employee.id.toString()
                    
                    // Debug logging for appointment filtering
                    if (allSlotAppointments.length > 0) {
                      console.log(`🔍 Employee ${employee.name} (${employee.id}) filtering:`, {
                        slot: `${slot.hour}:${slot.minute.toString().padStart(2, '0')}`,
                        appointmentId: apt.id,
                        appointmentTitle: apt.title,
                        appointmentEmployeeId: apt.employeeId,
                        appointmentEmployeeIdType: typeof apt.employeeId,
                        employeeId: employee.id,
                        employeeIdType: typeof employee.id,
                        matches
                      })
                    }
                    
                    return matches
                  })
                  const isWorking = isWorkingHourSlot(selectedDate, slot.hour, slot.minute, employee.id)
                  
                  return (
                    <TimeSlot
                      key={`${employee.id}-${slotIndex}`}
                      date={selectedDate}
                      hour={slot.hour}
                      minute={slot.minute}
                      appointments={employeeAppointments}
                      config={config}
                      isWorking={isWorking}
                      onClick={handleSlotClick}
                      onAppointmentClick={onAppointmentClick}
                      onAppointmentDragStart={onAppointmentDragStart}
                      onAppointmentDragMove={onAppointmentDragMove}
                      onAppointmentDragEnd={onAppointmentDragEnd}
                      onAppointmentResizeStart={onAppointmentResizeStart}
                      onAppointmentResizeMove={onAppointmentResizeMove}
                      onAppointmentResizeEnd={onAppointmentResizeEnd}
                      onAppointmentStatusUpdate={onAppointmentStatusUpdate}
                      onAppointmentEdit={onAppointmentEdit}
                      onAppointmentMove={onAppointmentMove}
                      scrollContainerRef={scrollContainerRef}
                      employeeId={employee.id}
                      displayMode="day"
                      selectedEmployees={selectedEmployees}
                      allAppointments={allAppointments}
                      currentDate={selectedDate}
                      isDragging={isDragging}
                      isShowingConfirmation={isShowingConfirmation}
                      draggedAppointmentId={draggedAppointmentId}
                      globalDragOffset={globalDragOffset}
                      isResizing={isResizing}
                      isShowingResizeConfirmation={isShowingResizeConfirmation}
                      resizedAppointmentId={resizedAppointmentId}
                      globalResizeOffset={globalResizeOffset}
                      isUpdatingStatus={isUpdatingStatus}
                    />
                  )
                })}
              </div>
            ))}
          </div>
        </TimeGrid>
      </div>
    </>
  )
}

export default DayView 