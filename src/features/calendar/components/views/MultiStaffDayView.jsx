import React, { useMemo, useCallback, useRef, useEffect } from 'react'
import { useAdvancedCalendar } from '../../hooks/useAdvancedCalendar'
import Calendar from '../core/Calendar'
import { useDateHeaderClicks } from '../../hooks/useDateHeaderClicks'
import { useSmartNavigation } from '../../hooks/useSmartNavigation'

const ColumnCalendar = ({ 
  employeeId, 
  appointments, 
  selectedDate, 
  navigationFeedback, 
  onEventUpdate,
  onDateUpdate,
  onViewChange,
  centralizedNavigation,
  calendarRef,
  showCloseButton,
  onRemoveStaff
}) => {
  const filtered = useMemo(() => appointments.filter(a => a.employeeId === employeeId), [appointments, employeeId])
  const employee = useMemo(() => EMPLOYEES.find(emp => emp.id === employeeId), [employeeId])

  const { calendar, calendarControls } = useBasicCalendar({
    selectedDate,
    filteredAppointments: filtered,
    onEventUpdate,
    onSelectedDateUpdate: onDateUpdate,
    setAppointments: () => {},
    navigationFeedback,
    // Multi-staff comparison configuration
    forceView: 'day',
    hideViewSelector: true,
    disableViewSwitching: true
  })

  // Store calendar controls in ref for centralized access
  useEffect(() => {
    if (calendarControls && calendarRef) {
      calendarRef.current = calendarControls
    }
  }, [calendarControls, calendarRef])

  // Set up centralized date header navigation
  useEffect(() => {
    if (calendarControls && centralizedNavigation) {
      // Register this calendar instance with centralized navigation
      centralizedNavigation.registerCalendar(employeeId, calendarControls)
      
      return () => {
        centralizedNavigation.unregisterCalendar(employeeId)
      }
    }
  }, [calendarControls, centralizedNavigation, employeeId])

  const handleRemoveClick = useCallback(() => {
    if (onRemoveStaff && employee) {
      onRemoveStaff(employee)
    }
  }, [onRemoveStaff, employee])

  if (!calendar) return null
  
  return (
    <div className="flex-1 min-w-[300px] border-r border-gray-100 relative" data-employee-id={employeeId}>
      {/* Close Button */}
      {showCloseButton && (
        <button
          onClick={handleRemoveClick}
          className="staff-panel-close-button"
          title={`Remove ${employee?.name || 'staff member'} from view`}
        >
          <svg 
            className="w-4 h-4" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M6 18L18 6M6 6l12 12" 
            />
          </svg>
        </button>
      )}
      
      <BasicCalendar 
        appointments={filtered}
        selectedDate={selectedDate}
        onDateSelect={onDateUpdate}
        onEventClick={onEventUpdate}
        view="day"
      />
    </div>
  )
}

const MultiStaffDayView = ({ 
  employeeIds, 
  appointments, 
  selectedDate, 
  navigationFeedback, 
  onEventUpdate,
  onRemoveStaff,
  onNavigationReady
}) => {
  const calendarRefs = useRef({})
  const calendarControlsMap = useRef({})

  // Centralized navigation handler
  const centralizedNavigation = useMemo(() => ({
    registerCalendar: (employeeId, controls) => {
      calendarControlsMap.current[employeeId] = controls
      console.log(`📅 Registered calendar for ${employeeId}`, controls)
    },
    
    unregisterCalendar: (employeeId) => {
      delete calendarControlsMap.current[employeeId]
      console.log(`📅 Unregistered calendar for ${employeeId}`)
    },
    
    syncAllCalendars: (date, view = null) => {
      console.log('🔄 Syncing all calendars to:', date, view)
      
      // Add visual feedback for synchronization
      Object.entries(calendarControlsMap.current).forEach(([empId, controls]) => {
        try {
          if (controls?.setDate) {
            controls.setDate(date)
          }
          if (view && controls?.setView) {
            controls.setView(view)
          }
          
          // Add visual sync feedback
          const calendarElement = document.querySelector(`[data-employee-id="${empId}"]`)
          if (calendarElement) {
            calendarElement.classList.add('calendar-sync-flash')
            setTimeout(() => {
              calendarElement.classList.remove('calendar-sync-flash')
            }, 600)
          }
          
          console.log(`✅ Synced calendar for ${empId}`)
        } catch (error) {
          console.error(`❌ Failed to sync calendar for ${empId}:`, error)
        }
      })
    }
  }), [])

  // Smart navigation for coordinated header clicks
  const showToast = useCallback((message, type) => {
    if (navigationFeedback?.showToast) {
      navigationFeedback.showToast(message, type)
    }
  }, [navigationFeedback])

  // Get the first available calendar controls for smart navigation
  const getMainCalendarControls = useCallback(() => {
    const controls = Object.values(calendarControlsMap.current)
    return controls.length > 0 ? controls[0] : null
  }, [])

  // Enhanced date header click handler that syncs all calendars
  const handleSynchronizedDateNavigation = useCallback((date, context) => {
    console.log('🎯 Synchronized header click:', date, context)
    
    const mainControls = getMainCalendarControls()
    if (!mainControls) {
      console.warn('❌ No calendar controls available for navigation')
      return
    }

    const currentView = mainControls.getView?.()
    
    // Smart navigation logic - determine if we should change views based on context
    let newView = null
    let navigationMessage = ''
    
    if (context === 'headerClick') {
      if (currentView === 'month-grid') {
        newView = 'week'
        navigationMessage = `Switched to Week view for ${new Date(date).toLocaleDateString()}`
      } else if (currentView === 'week') {
        newView = 'day'
        navigationMessage = `Zoomed to Day view for ${new Date(date).toLocaleDateString()}`
      } else if (currentView === 'day') {
        // For day view, stay in day view but change date
        navigationMessage = `All calendars navigated to ${new Date(date).toLocaleDateString()}`
      } else {
        navigationMessage = `All calendars navigated to ${new Date(date).toLocaleDateString()}`
      }
    } else {
      navigationMessage = `All calendars navigated to ${new Date(date).toLocaleDateString()}`
    }

    // Sync all calendars to the new date and view
    centralizedNavigation.syncAllCalendars(date, newView)
    
    // Show feedback
    showToast(navigationMessage, 'info')
  }, [centralizedNavigation, getMainCalendarControls, showToast])

  // Set up date header click detection for the entire multi-staff view
  // Use a stable reference by getting controls in the effect
  const mainControlsRef = useRef(null)
  
  useEffect(() => {
    mainControlsRef.current = getMainCalendarControls()
  })
  
  useDateHeaderClicks(() => mainControlsRef.current, handleSynchronizedDateNavigation)

  // Unified event update handler
  const handleEventUpdate = useCallback((updatedEvent) => {
    onEventUpdate(updatedEvent)
  }, [onEventUpdate])

  // Unified date update handler
  const handleDateUpdate = useCallback((date) => {
    // When one calendar's date changes, sync all others
    centralizedNavigation.syncAllCalendars(date)
  }, [centralizedNavigation])

  // Handle staff removal
  const handleRemoveStaff = useCallback((employee) => {
    console.log(`🗑️ Removing staff member: ${employee.name}`)
    
    if (onRemoveStaff) {
      onRemoveStaff(employee)
    }
    
    showToast(`Removed ${employee.name} from view`, 'info')
  }, [onRemoveStaff, showToast])

  // Expose navigation method to parent component
  const navigateToDate = useCallback((date) => {
    console.log('🎯 MultiStaffDayView: External date navigation to:', date)
    centralizedNavigation.syncAllCalendars(date)
    showToast(`All calendars navigated to ${new Date(date).toLocaleDateString()}`, 'info')
  }, [centralizedNavigation, showToast])

  // Notify parent when navigation is ready
  useEffect(() => {
    if (onNavigationReady && getMainCalendarControls()) {
      onNavigationReady(navigateToDate)
    }
  }, [onNavigationReady, navigateToDate, getMainCalendarControls])

  return (
    <div className="flex w-full h-full overflow-x-auto" data-multi-staff-view="true">
      {employeeIds.map((id, index) => {
        // Create a ref for each calendar
        if (!calendarRefs.current[id]) {
          calendarRefs.current[id] = React.createRef()
        }
        
        // Show close button for all panels except the first one
        const showCloseButton = index > 0
        
        return (
          <ColumnCalendar
            key={id}
            employeeId={id}
            appointments={appointments}
            selectedDate={selectedDate}
            navigationFeedback={navigationFeedback}
            onEventUpdate={handleEventUpdate}
            onDateUpdate={handleDateUpdate}
            centralizedNavigation={centralizedNavigation}
            calendarRef={calendarRefs.current[id]}
            showCloseButton={showCloseButton}
            onRemoveStaff={handleRemoveStaff}
          />
        )
      })}
    </div>
  )
}

export default MultiStaffDayView 