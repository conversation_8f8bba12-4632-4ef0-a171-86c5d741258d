import React from 'react'
import TimeGrid from '../core/TimeGrid'
import WorkingHoursOverlay from '../core/WorkingHoursOverlay'
import TimeSlot from '../slots/TimeSlot'

/**
 * WeekView - Week calendar view component
 */
const WeekView = ({
  weekDays,
  timeSlots,
  config,
  currentEmployee,
  getAppointmentsForSlot,
  isWorkingHourSlot,
  getWorkingHoursForEmployee,
  onSlotClick,
  onAppointmentClick,
  onAppointmentDragStart, // New prop for drag start
  onAppointmentDragMove, // New prop for drag handling
  onAppointmentDragEnd,  // New prop for drag completion
  onAppointmentResizeStart, // New prop for resize start
  onAppointmentResizeMove, // New prop for resize handling
  onAppointmentResizeEnd,  // New prop for resize completion
  onAppointmentStatusUpdate, // New prop for status updates
  onAppointmentEdit, // New prop for appointment editing
  onAppointmentMove, // New prop for appointment moving
  onDateHeaderClick, // New prop for date header clicks

  timeGridRef,
  scrollContainerRef,
  onScroll,
  allAppointments = [], // New prop for appointment border detection
  isDragging = false, // New prop to prevent slot clicks during drag
  isShowingConfirmation = false, // New prop to track if confirmation modal is open
  draggedAppointmentId = null, // New prop to track which appointment is being dragged
  globalDragOffset = { x: 0, y: 0 }, // New prop for shared drag offset
  isResizing = false, // New prop to prevent slot clicks during resize
  isShowingResizeConfirmation = false, // New prop to track if resize confirmation modal is open
  resizedAppointmentId = null, // New prop to track which appointment is being resized
  globalResizeOffset = { x: 0, y: 0 }, // New prop for shared resize offset
  isUpdatingStatus = false // New prop to track status update state
}) => {
  const handleDateHeaderClick = (date) => {
    if (onDateHeaderClick) {
      onDateHeaderClick(date)
    }
  }

  return (
    <>
      {/* Day headers */}
      <div className="bg-gray-50 border-b border-gray-200 flex">
        <div className="w-16 p-3 border-r border-gray-200 flex-shrink-0"></div>
        <div className="flex-1 flex">
          {weekDays.map((day, index) => (
            <div 
              key={index} 
              className="flex-1 p-3 text-center border-r border-gray-200 last:border-r-0 cursor-pointer hover:bg-gray-100 transition-colors"
              onClick={() => handleDateHeaderClick(day)}
              title={`Click to zoom into ${day.toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric' })}`}
            >
              <div className="text-sm font-medium text-gray-600">
                {day.toLocaleDateString('en-US', { weekday: 'short' })}
              </div>
              <div className={`text-lg font-semibold ${
                day.toDateString() === new Date().toDateString() ? 'text-blue-600' : 'text-gray-900'
              }`}>
                {day.getDate()}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Time grid container */}
      <div className="flex-1 calendar-scroll-container overflow-y-auto overflow-x-hidden" ref={timeGridRef} onScroll={onScroll}>
        <TimeGrid 
          timeSlots={timeSlots} 
          config={config}
          selectedDate={weekDays[0]} // Pass first day of week as selected date
          weekDays={weekDays}
          displayMode="week"
          selectedEmployees={[currentEmployee].filter(Boolean)}
        >
          {/* Day columns */}
          <div className="flex-1 flex">
            {weekDays.map((day, dayIndex) => (
              <div key={dayIndex} className="flex-1 border-r border-gray-200 last:border-r-0 relative">
                {/* Working hours background */}
                {currentEmployee && (
                  <WorkingHoursOverlay
                    date={day}
                    employeeId={currentEmployee.id}
                    config={config}
                    getWorkingHoursForEmployee={getWorkingHoursForEmployee}
                  />
                )}
                
                {timeSlots.map((slot, slotIndex) => {
                  const slotAppointments = getAppointmentsForSlot(day, slot.hour, slot.minute)
                  const isWorking = currentEmployee ? isWorkingHourSlot(day, slot.hour, slot.minute, currentEmployee.id) : true
                  
                  return (
                    <TimeSlot
                      key={slotIndex}
                      date={day}
                      hour={slot.hour}
                      minute={slot.minute}
                      appointments={slotAppointments}
                      config={config}
                      isWorking={isWorking}
                      onClick={onSlotClick}
                      onAppointmentClick={onAppointmentClick}
                      onAppointmentDragStart={onAppointmentDragStart}
                      onAppointmentDragMove={onAppointmentDragMove}
                      onAppointmentDragEnd={onAppointmentDragEnd}
                      onAppointmentResizeStart={onAppointmentResizeStart}
                      onAppointmentResizeMove={onAppointmentResizeMove}
                      onAppointmentResizeEnd={onAppointmentResizeEnd}
                      onAppointmentStatusUpdate={onAppointmentStatusUpdate}
                      onAppointmentEdit={onAppointmentEdit}
                      onAppointmentMove={onAppointmentMove}
                      scrollContainerRef={scrollContainerRef}
          
                      displayMode="week"
                      weekDays={weekDays}
                      allAppointments={allAppointments}
                      currentDate={day}
                      isDragging={isDragging}
                      isShowingConfirmation={isShowingConfirmation}
                      draggedAppointmentId={draggedAppointmentId}
                      globalDragOffset={globalDragOffset}
                      isResizing={isResizing}
                      isShowingResizeConfirmation={isShowingResizeConfirmation}
                      resizedAppointmentId={resizedAppointmentId}
                      globalResizeOffset={globalResizeOffset}
                      isUpdatingStatus={isUpdatingStatus}
                    />
                  )
                })}
              </div>
            ))}
          </div>
        </TimeGrid>
      </div>
    </>
  )
}

export default WeekView 