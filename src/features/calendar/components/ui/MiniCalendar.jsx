import { useState } from 'react'

const MiniCalendar = ({ selectedDate, onDateSelect, onDoubleClick, className = "" }) => {
  const [currentMonth, setCurrentMonth] = useState(() => {
    // Handle both string and Date object inputs
    let date
    if (selectedDate instanceof Date) {
      date = selectedDate
    } else if (typeof selectedDate === 'string') {
      date = new Date(selectedDate)
    } else {
      date = new Date()
    }
    return new Date(date.getFullYear(), date.getMonth(), 1)
  })

  // Convert selectedDate to consistent format for comparison
  const getSelectedDateString = () => {
    if (!selectedDate) return null
    
    let date
    if (selectedDate instanceof Date) {
      date = selectedDate
    } else if (typeof selectedDate === 'string') {
      date = new Date(selectedDate)
    } else {
      return null
    }
    
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
  }

  const today = new Date()
  const year = currentMonth.getFullYear()
  const month = currentMonth.getMonth()
  const selectedDateString = getSelectedDateString()

  // Get first and last day of the month
  const firstDay = new Date(year, month, 1)
  const lastDay = new Date(year, month + 1, 0)
  const daysInMonth = lastDay.getDate()
  const startDay = firstDay.getDay() // 0 = Sunday

  // Get month names
  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ]

  const goToPreviousMonth = () => {
    setCurrentMonth(new Date(year, month - 1, 1))
  }

  const goToNextMonth = () => {
    setCurrentMonth(new Date(year, month + 1, 1))
  }

  const goToToday = () => {
    const today = new Date()
    setCurrentMonth(new Date(today.getFullYear(), today.getMonth(), 1))
    // Pass Date object instead of string for consistency
    onDateSelect(today)
  }

  // Handle double-click on date
  const handleDateDoubleClick = (dateObj) => {
    if (onDoubleClick) {
      onDoubleClick(dateObj)
    }
  }

  const generateCalendarDays = () => {
    const days = []
    
    // Add empty cells for days before month starts
    for (let i = 0; i < startDay; i++) {
      const prevMonth = new Date(year, month - 1, 0)
      const prevDate = prevMonth.getDate() - (startDay - i - 1)
      const prevDateObj = new Date(prevMonth.getFullYear(), prevMonth.getMonth(), prevDate)
      const dateStr = `${prevMonth.getFullYear()}-${String(prevMonth.getMonth() + 1).padStart(2, '0')}-${String(prevDate).padStart(2, '0')}`
      
      days.push(
        <button
          key={`prev-${i}`}
          onClick={() => onDateSelect(prevDateObj)}
          onDoubleClick={() => handleDateDoubleClick(prevDateObj)}
          className="w-8 h-8 text-xs text-gray-400 hover:bg-gray-100 rounded-md transition-colors"
          title={`Double-click to zoom to ${prevDateObj.toLocaleDateString()}`}
        >
          {prevDate}
        </button>
      )
    }
    
    // Add days of the current month
    for (let day = 1; day <= daysInMonth; day++) {
      const dateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`
      const dayDateObj = new Date(year, month, day)
      const isSelected = selectedDateString === dateStr
      const isToday = day === today.getDate() && 
                     month === today.getMonth() && 
                     year === today.getFullYear()
      
      days.push(
        <button
          key={day}
          onClick={() => onDateSelect(dayDateObj)}
          onDoubleClick={() => handleDateDoubleClick(dayDateObj)}
          className={`w-8 h-8 text-xs rounded-md transition-colors font-medium ${
            isSelected 
              ? 'bg-blue-500 text-white' 
              : isToday
              ? 'bg-blue-100 text-blue-600 font-semibold'
              : 'hover:bg-gray-100 text-gray-700'
          }`}
          title={`Double-click to zoom to ${dayDateObj.toLocaleDateString()}`}
        >
          {day}
        </button>
      )
    }

    // Add empty cells for days after month ends
    const totalCells = Math.ceil((startDay + daysInMonth) / 7) * 7
    const remainingCells = totalCells - (startDay + daysInMonth)
    
    for (let i = 1; i <= remainingCells; i++) {
      const nextDateObj = new Date(year, month + 1, i)
      
      days.push(
        <button
          key={`next-${i}`}
          onClick={() => onDateSelect(nextDateObj)}
          onDoubleClick={() => handleDateDoubleClick(nextDateObj)}
          className="w-8 h-8 text-xs text-gray-400 hover:bg-gray-100 rounded-md transition-colors"
          title={`Double-click to zoom to ${nextDateObj.toLocaleDateString()}`}
        >
          {i}
        </button>
      )
    }

    return days
  }

  return (
    <div className={`bg-white ${className}`}>
      {/* Header with month navigation */}
      <div className="flex items-center justify-between mb-3">
        <button
          onClick={goToPreviousMonth}
          className="p-1 hover:bg-gray-100 rounded-md transition-colors"
          aria-label="Previous month"
        >
          <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        
        <div className="flex flex-col items-center">
          <h3 className="text-sm font-semibold text-gray-900">
            {monthNames[month]} {year}
          </h3>
          <button
            onClick={goToToday}
            className="text-xs text-blue-600 hover:text-blue-700 font-medium"
          >
            Today
          </button>
        </div>
        
        <button
          onClick={goToNextMonth}
          className="p-1 hover:bg-gray-100 rounded-md transition-colors"
          aria-label="Next month"
        >
          <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>

      {/* Day headers */}
      <div className="grid grid-cols-7 gap-1 text-center text-xs text-gray-500 mb-2">
        {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
          <div key={day} className="w-8 h-6 flex items-center justify-center font-medium">
            {day}
          </div>
        ))}
      </div>

      {/* Calendar grid */}
      <div className="grid grid-cols-7 gap-1">
        {generateCalendarDays()}
      </div>
    </div>
  )
}

export default MiniCalendar 