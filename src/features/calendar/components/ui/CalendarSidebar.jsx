// Using inline SVG icons for better compatibility
import React, { useState, useMemo } from 'react'
import { ChevronDownIcon, ChevronRightIcon, CalendarIcon, CheckIcon, MagnifyingGlassIcon, XMarkIcon } from '@heroicons/react/24/outline'
import { DISPLAY_MODES } from '../../hooks/useAdvancedCalendar'
import { APPOINTMENT_STATUSES } from '../../constants/calendarConfig'
import MiniCalendar from './MiniCalendar'

const FilterSection = ({ 
  title, 
  items, 
  selectedItems, 
  onToggle, 
  onSelectAll, 
  onDeselectAll,
  isCollapsed,
  onToggleCollapse,
  renderItem
}) => (
  <div className="border-b border-gray-100">
    <button
      onClick={onToggleCollapse}
      className="w-full p-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
    >
      <h3 className="text-sm font-semibold text-gray-900">{title}</h3>
      {isCollapsed ? (
        <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
        </svg>
      ) : (
        <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      )}
    </button>
    
    {!isCollapsed && (
      <div className="px-4 pb-4">
        <div className="flex gap-2 mb-3">
          <button
            onClick={onSelectAll}
            className="text-xs text-blue-600 hover:text-blue-700 font-medium"
          >
            Select All
          </button>
          <span className="text-xs text-gray-300">•</span>
          <button
            onClick={onDeselectAll}
            className="text-xs text-gray-500 hover:text-gray-600 font-medium"
          >
            None
          </button>
        </div>
        
        <div className="space-y-1">
          {items.map(item => renderItem(item, selectedItems.includes(item.id), () => onToggle(item.id)))}
        </div>
      </div>
    )}
  </div>
)

// Employee item component with avatar
const EmployeeItem = ({ employee, isSelected, onToggle, singleSelection }) => (
  <label className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors group">
    <input
      type={singleSelection ? 'radio' : 'checkbox'}
      name={singleSelection ? 'employee-filter' : undefined}
      checked={isSelected}
      onChange={onToggle}
      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 focus:ring-offset-0"
    />
    <div 
      className="w-8 h-8 rounded-full flex items-center justify-center text-white text-xs font-semibold flex-shrink-0"
      style={{ backgroundColor: employee.color }}
    >
      {employee.avatar}
    </div>
    <span className="text-sm text-gray-700 group-hover:text-gray-900 font-medium truncate">
      {employee.name}
    </span>
  </label>
)

const CalendarSidebar = ({
  employees,
  selectedEmployees,
  selectedDate,
  filteredAppointments,
  onToggleEmployee,
  onSelectAllEmployees,
  onDeselectAllEmployees,
  onDateSelect,
  onDateDoubleClick,
  onCreateBreakSlot,
  currentView,
  serviceCategories = []
}) => {
  const isSingleSelection = currentView && currentView !== 'day'
  const [employeesCollapsed, setEmployeesCollapsed] = useState(false)
  const [legendCollapsed, setLegendCollapsed] = useState(false) // Changed to false to expand by default
  const [searchTerm, setSearchTerm] = useState('')

  // Filter employees based on search term
  const filteredEmployees = useMemo(() => {
    if (!searchTerm.trim()) {
      return employees || []
    }
    
    const searchLower = searchTerm.toLowerCase().trim()
    return (employees || []).filter(employee => {
      const name = (employee.name || '').toLowerCase()
      const fullName = (employee.full_name || '').toLowerCase()
      const firstName = (employee.first_name || '').toLowerCase()
      const lastName = (employee.last_name || '').toLowerCase()
      
      return name.includes(searchLower) || 
             fullName.includes(searchLower) || 
             firstName.includes(searchLower) || 
             lastName.includes(searchLower)
    })
  }, [employees, searchTerm])

  // Clear search function
  const clearSearch = () => {
    setSearchTerm('')
  }

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Scrollable Content - hidden scrollbar */}
      <div className="flex-1 calendar-scroll-container overflow-x-hidden min-h-0">
        {/* Mini Calendar */}
        <div className="p-4 lg:p-6 border-b border-gray-200">
          <h3 className="text-sm font-semibold text-gray-900 mb-4">Calendar</h3>
          <MiniCalendar
            selectedDate={selectedDate}
            onDateSelect={onDateSelect}
            onDoubleClick={onDateDoubleClick}
          />
        </div>

        {/* Employee Filter */}
        <div className="border-b border-gray-200">
          <button
            onClick={() => setEmployeesCollapsed(!employeesCollapsed)}
            className="w-full p-4 lg:p-6 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
          >
            <h3 className="text-sm font-semibold text-gray-900">Staff</h3>
            {employeesCollapsed ? (
              <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            ) : (
              <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            )}
          </button>
          
          {!employeesCollapsed && (
            <div className="px-4 lg:px-6 pb-4 lg:pb-6">
              {/* Search Input */}
              <div className="mb-4">
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <MagnifyingGlassIcon className="h-4 w-4 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    placeholder="Search staff..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-lg text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  {searchTerm && (
                    <button
                      onClick={clearSearch}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    >
                      <XMarkIcon className="h-4 w-4 text-gray-400 hover:text-gray-600 transition-colors" />
                    </button>
                  )}
                </div>
                {searchTerm && (
                  <div className="mt-2 text-xs text-gray-500">
                    {filteredEmployees.length === 0 
                      ? `No staff found for "${searchTerm}"`
                      : `${filteredEmployees.length} staff member${filteredEmployees.length !== 1 ? 's' : ''} found`
                    }
                  </div>
                )}
              </div>
              
              {/* Employee List */}
              <div className="space-y-1">
                {employees && employees.length > 0 ? (
                  filteredEmployees.length > 0 ? (
                    filteredEmployees.map(employee => (
                      <EmployeeItem
                        key={employee.id}
                        employee={employee}
                        isSelected={selectedEmployees.includes(employee.id)}
                        onToggle={() => onToggleEmployee(employee.id)}
                        singleSelection={isSingleSelection}
                      />
                    ))
                  ) : searchTerm ? (
                    <div className="text-sm text-gray-500 text-center py-8">
                      <div className="mb-2">
                        <MagnifyingGlassIcon className="h-8 w-8 text-gray-300 mx-auto" />
                      </div>
                      <div className="font-medium">No staff found</div>
                      <div className="text-xs mt-1">Try adjusting your search terms</div>
                      <button
                        onClick={clearSearch}
                        className="mt-3 text-xs text-blue-600 hover:text-blue-700 font-medium"
                      >
                        Clear search
                      </button>
                    </div>
                  ) : (
                    filteredEmployees.map(employee => (
                      <EmployeeItem
                        key={employee.id}
                        employee={employee}
                        isSelected={selectedEmployees.includes(employee.id)}
                        onToggle={() => onToggleEmployee(employee.id)}
                        singleSelection={isSingleSelection}
                      />
                    ))
                  )
                ) : (
                  <div className="text-sm text-gray-500 text-center py-4">
                    Loading employees...
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Legend Section - Moved after Staff */}
        <div className="border-b border-gray-200">
          <button
            onClick={() => setLegendCollapsed(!legendCollapsed)}
            className="w-full p-4 lg:p-6 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
          >
            <h3 className="text-sm font-semibold text-gray-900">Legend</h3>
            {legendCollapsed ? (
              <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            ) : (
              <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            )}
          </button>
          
          {!legendCollapsed && (
            <div className="px-4 lg:px-6 pb-4 lg:pb-6">
              {/* Legend Content */}
              <div className="space-y-4">
                {/* Service Colors Legend */}
                {serviceCategories.length > 0 && (
                  <div className="space-y-2">
                    <h4 className="text-xs font-medium text-gray-500 uppercase tracking-wider">Service Colors</h4>
                    <div className="space-y-1">
                      {serviceCategories.map(category => (
                        <div key={category.id} className="flex items-center gap-2">
                          <div 
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: category.color }}
                          />
                          <span className="text-xs text-gray-600">{category.name}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Status Indicators */}
                <div className="space-y-2">
                  <h4 className="text-xs font-medium text-gray-500 uppercase tracking-wider">Status Indicators</h4>
                  <div className="text-xs text-gray-600 space-y-2">
                    {Object.values(APPOINTMENT_STATUSES).map((status) => (
                      <div key={status.id} className="flex items-center gap-2">
                        <div 
                          className="w-6 h-3 bg-gray-100 border border-gray-200 rounded-sm relative flex-shrink-0"
                        >
                          <div 
                            className="absolute top-0 left-0 right-0 h-0.5 rounded-t-sm"
                            style={{ backgroundColor: status.color }}
                          />
                        </div>
                        <span>{status.name}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Usage Tips */}
                <div className="border-t border-gray-100 pt-4">
                  <h4 className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-2">Quick Tips</h4>
                  <div className="text-xs text-gray-600 space-y-1">
                    <div className="flex items-center gap-2">
                      <svg className="w-3 h-3 text-blue-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                      </svg>
                      <span>Drag appointments to reschedule</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <svg className="w-3 h-3 text-green-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
                      </svg>
                      <span>Resize appointments by dragging edges</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <svg className="w-3 h-3 text-purple-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                      </svg>
                      <span>Completed appointments cannot be moved</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
        
        {/* Bottom padding to ensure last section is always reachable */}
        <div className="h-16 lg:h-20 flex-shrink-0"></div>
      </div>
    </div>
  )
}

export default CalendarSidebar 