import React, { useMemo, useEffect } from 'react'
import { cn } from '../../../../utils'
import { calculateAppointmentBlockTopPosition, alignTimeToResolution } from '../../utils/timeGridCalculations'

/**
 * DragLineIndicator - Shows a horizontal line indicating where the dragged appointment will be dropped
 * Uses newTime (current drag position) as primary source, with appointment start_time as fallback
 * 
 * Expected appointment data structure from GET /api/v1/appointments/{id}/:
 * {
 *   "start_time": "2025-07-15T10:00:00-0700",  // Original position (fallback)
 *   "end_time": "2025-07-15T11:10:00-0700",
 *   "customer_full_name": "<PERSON>",
 *   // ... other appointment data
 * }
 */
const DragLineIndicator = ({
  isDragging,
  draggedAppointment,
  newTime, // PRIMARY: Current drag position with offset applied
  config,
  timeGridRef,
  displayMode,
  isEdgeCaseMode = false, // Edge case mode for offset positioning
  edgeCaseInfo = null // Edge case details for offset calculations
}) => {


  // Calculate the exact position using newTime (current drag position)
  const linePosition = useMemo(() => {
    if (!isDragging || !draggedAppointment || !newTime || !config || !timeGridRef?.current) {
      return null
    }

    try {
      // PRIMARY: Use newTime (current drag position) as the main positioning source
      // This already includes the drag offset and represents where the appointment should be
      let targetTime = newTime
      
      // Apply edge case or normal snapping logic based on original appointment position
      if (isEdgeCaseMode && edgeCaseInfo) {
        // EDGE CASE: Original appointment sits between grid lines (e.g., 10:45 AM in 30-min resolution)
        // Use newTime EXACTLY as-is - NO SNAPPING AT ALL
        targetTime = newTime
      } else {
        // NORMAL CASE: Original appointment exactly on grid intervals
        // Only apply snapping for appointments that started on grid lines
        targetTime = alignTimeToResolution(newTime, config.timeResolution)
      }
      
      const appointmentHour = targetTime.getHours()
      const appointmentMinute = targetTime.getMinutes()
      
      // Use new function that calculates exact appointment block top border position
      const positionInfo = calculateAppointmentBlockTopPosition(appointmentHour, appointmentMinute, config)
      
      // Ensure the position is within valid bounds
      if (positionInfo.appointmentTopPosition < 0) {
        return null
      }
      
      // CRITICAL FIX: Account for scroll position and calendar header offset
      const scrollContainer = timeGridRef.current
      const scrollTop = scrollContainer.scrollTop || 0
      
      // Dynamically calculate total header height for all views and resolutions
      let totalHeaderHeight = 0
      
      // Find all header elements above the time grid
      const mainCalendarHeader = document.querySelector('.bg-white.border-b.border-gray-200.p-4') // Main calendar header
      const employeeProfileHeader = document.querySelector('.bg-gray-50.border-b.border-gray-200.px-4.py-2') // Employee profile (week view)
      const dayViewHeaders = document.querySelectorAll('.bg-gray-50.border-b.border-gray-200.flex') // Day view headers
      
      // Add main calendar header height
      if (mainCalendarHeader) {
        totalHeaderHeight += mainCalendarHeader.offsetHeight
      }
      
      // Add employee profile header (week view single employee)
      if (employeeProfileHeader) {
        totalHeaderHeight += employeeProfileHeader.offsetHeight
      }
      
      // Add day view headers (single employee or multi-employee headers)
      dayViewHeaders.forEach(header => {
        totalHeaderHeight += header.offsetHeight
      })
      
      // Fallback if no headers found
      if (totalHeaderHeight === 0) {
        totalHeaderHeight = displayMode === 'week' ? 100 : 120 // Different defaults for different views
      }
      
      // Calculate final position accounting for scroll and all headers
      const finalPosition = positionInfo.appointmentTopPosition - scrollTop + totalHeaderHeight
        
        // targetTime shows the current drag position (newTime + offset)
        // This moves with the appointment block as it's being dragged
        const displayTime = targetTime
        
        return {
          top: finalPosition, // Use scroll and header adjusted position
          visible: true,
          time: displayTime, // Show current drag position time
          positionInfo: {
            ...positionInfo,
            scrollTop,
            totalHeaderHeight,
            finalPosition,
            draggedFrom: draggedAppointment?.start_time || draggedAppointment?.start,
            currentPosition: newTime.toISOString()
          }
        }
    } catch (error) {
      console.error('Error calculating drag line position:', error)
      return null
    }
  }, [isDragging, draggedAppointment?.id, newTime?.getTime(), config?.timeResolution, config?.gridHeight, config?.displayHourStart, displayMode, timeGridRef, isEdgeCaseMode, edgeCaseInfo?.detectedAt]) // Focus on newTime for positioning

  // Don't render if not dragging or position can't be calculated
  if (!linePosition || !linePosition.visible) {
    return null
  }

  return (
    <div
      className={cn(
        // Use relative positioning within calendar container for better viewport compatibility
        "absolute left-0 right-0 z-[35] pointer-events-none",
        "transition-all duration-75 ease-out" // Faster transition for more responsive feel
      )}
      style={{
        top: `${linePosition.top}px`, // Use calculated position (already includes dynamic header offset)
        // Ensure it's positioned relative to the calendar content, not viewport
        transform: 'translateZ(0)', // Enable hardware acceleration
      }}
      key={`${draggedAppointment?.id}-${newTime?.getTime()}`} // Force re-render on newTime change
    >
      {/* Main drop line with enhanced styling */}
      <div className="relative w-full">
        {/* Glow effect behind the line */}
        <div
          className="absolute inset-0 h-1 bg-blue-400 opacity-30 blur-sm"
        />
        
        {/* Main line - spans full width of calendar */}
        <div
          className="relative h-0.5 bg-gradient-to-r from-blue-500 via-blue-600 to-blue-500 shadow-lg"
          style={{
            boxShadow: '0 0 12px rgba(59, 130, 246, 0.8), 0 0 24px rgba(59, 130, 246, 0.4)',
          }}
        />
      </div>
      
      {/* Drop indicator arrows on the sides */}
      <div className="absolute left-1 top-0 -mt-1">
        <div className="w-0 h-0 border-t-2 border-b-2 border-l-4 border-transparent border-l-blue-500" />
      </div>
      <div className="absolute right-1 top-0 -mt-1">
        <div className="w-0 h-0 border-t-2 border-b-2 border-r-4 border-transparent border-r-blue-500" />
      </div>
      
      {/* Time label with improved styling */}
      <div className="absolute left-1/2 -translate-x-1/2 -top-8 bg-blue-600 text-white text-xs px-3 py-1.5 rounded-full shadow-lg font-medium border border-blue-400 z-[36]">
        <div className="flex items-center gap-1">
          <div className="w-1.5 h-1.5 bg-white rounded-full animate-pulse" />
          {linePosition.time.toLocaleTimeString('en-US', { 
            hour: 'numeric', 
            minute: '2-digit',
            hour12: true 
          })}
        </div>
        {/* Arrow pointing down */}
        <div className="absolute left-1/2 -translate-x-1/2 top-full w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-blue-600" />
      </div>
    </div>
  )
}

export default DragLineIndicator 