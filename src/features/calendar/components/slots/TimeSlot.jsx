import React, { useState, useRef, useEffect } from 'react'
import AppointmentSlot from './AppointmentSlot'
import AppointmentClickOverlay from './appointment-slot/AppointmentClickOverlay'

/**
 * TimeSlot - Reusable time slot component with appointments
 * Enhanced for multi-employee support and smart appointment border snapping
 */
const TimeSlot = ({
  date,
  hour,
  minute,
  appointments = [],
  config,
  isWorking = true,
  onClick,
  onAppointmentClick,
  onAppointmentDragStart, // New prop for drag start
  onAppointmentDragMove, // New prop for drag handling
  onAppointmentDragEnd,  // New prop for drag completion
  onAppointmentResizeStart, // New prop for resize start
  onAppointmentResizeMove, // New prop for resize handling
  onAppointmentResizeEnd,  // New prop for resize completion
  onAppointmentStatusUpdate, // New prop for status updates
  onAppointmentEdit, // New prop for appointment editing
  onAppointmentMove, // New prop for appointment moving
  scrollContainerRef, // New prop for auto-scroll functionality

  employeeId, // New prop for multi-employee support
  displayMode, // New prop for view mode detection
  weekDays, // New prop for week view cross-day support
  selectedEmployees, // New prop for multi-employee day view
  allAppointments = [], // New prop for appointment border detection
  currentDate = new Date(), // New prop for current date context
  isDragging = false, // New prop to prevent slot clicks during drag
  isShowingConfirmation = false, // New prop to track if confirmation modal is open
  draggedAppointmentId = null, // New prop to track which appointment is being dragged
  globalDragOffset = { x: 0, y: 0 }, // New prop for shared drag offset
  isResizing = false, // New prop to prevent slot clicks during resize
  isShowingResizeConfirmation = false, // New prop to track if resize confirmation modal is open
  resizedAppointmentId = null, // New prop to track which appointment is being resized
  globalResizeOffset = { x: 0, y: 0 }, // New prop for shared resize offset
  isUpdatingStatus = false // New prop to track status update state
}) => {
  // State for click overlay
  const [clickedAppointment, setClickedAppointment] = useState(null)
  const [overlayPosition, setOverlayPosition] = useState({ x: 0, y: 0 })
  const [overlayAnchorEl, setOverlayAnchorEl] = useState(null)
  const overlayRef = useRef(null)

  // Listen for global close event to hide overlay
  useEffect(() => {
    const handleGlobalClose = () => {
      setClickedAppointment(null)
      setOverlayAnchorEl(null)
    }
    window.addEventListener('calendar-close-overlays', handleGlobalClose)
    return () => window.removeEventListener('calendar-close-overlays', handleGlobalClose)
  }, [])

  // Handle appointment click to show overlay
  const handleAppointmentClick = (appointment, event) => {
    // Broadcast to close overlays in other slots first
    window.dispatchEvent(new Event('calendar-close-overlays'))
    
    // Prevent the event from bubbling up to slot click handler
    event.stopPropagation()
    
    // If clicking the same appointment, close the overlay
    if (clickedAppointment && clickedAppointment.id === appointment.id) {
      setClickedAppointment(null)
      setOverlayAnchorEl(null)
      return
    }

    // Store anchor element so we can recalc on scroll/resize
    const appointmentElement = event.currentTarget
    setOverlayAnchorEl(appointmentElement)

    // Calculate initial position
    positionOverlay(appointmentElement)
    setClickedAppointment(appointment)
  }

  const positionOverlay = (anchorEl) => {
    if (!anchorEl) return

    const rect = anchorEl.getBoundingClientRect()
    const margin = 8 // spacing between anchor and overlay
    const headerHeight = 64 // approximate calendar header height
    const overlayWidth = overlayRef.current?.offsetWidth || 220 // fallback estimate
    const overlayHeight = overlayRef.current?.offsetHeight || 300 // fallback estimate

    let x = rect.right + margin
    let y = rect.top

    // If overflow right, position to left
    if (x + overlayWidth > window.innerWidth) {
      x = rect.left - overlayWidth - margin
    }

    // Clamp vertical so overlay fully visible
    if (y + overlayHeight > window.innerHeight - margin) {
      y = window.innerHeight - overlayHeight - margin
    }
    if (y < headerHeight + margin) {
      y = headerHeight + margin
    }

    setOverlayPosition({ x, y })
  }

  // Reposition overlay on scroll/resize
  useEffect(() => {
    if (!overlayAnchorEl) return

    const handleScrollOrResize = () => positionOverlay(overlayAnchorEl)

    // Attach to window (covers viewport scroll/resize)
    window.addEventListener('scroll', handleScrollOrResize, true)
    window.addEventListener('resize', handleScrollOrResize)

    // Attach to custom scroll container if provided
    let scrollContainerEl = scrollContainerRef?.current
    if (scrollContainerEl) {
      scrollContainerEl.addEventListener('scroll', handleScrollOrResize)
    }

    return () => {
      window.removeEventListener('scroll', handleScrollOrResize, true)
      window.removeEventListener('resize', handleScrollOrResize)
      if (scrollContainerEl) {
        scrollContainerEl.removeEventListener('scroll', handleScrollOrResize)
      }
    }
  }, [overlayAnchorEl, scrollContainerRef])

  const handleOverlayClose = () => {
    setClickedAppointment(null)
  }

  // Handle global clicks to close overlay
  useEffect(() => {
    if (!clickedAppointment) return

    const handleGlobalClick = (event) => {
      // Close overlay when clicking outside
      setClickedAppointment(null)
    }

    const handleEscapeKey = (event) => {
      if (event.key === 'Escape') {
        setClickedAppointment(null)
      }
    }

    // Add event listeners
    document.addEventListener('click', handleGlobalClick)
    document.addEventListener('keydown', handleEscapeKey)

    // Cleanup
    return () => {
      document.removeEventListener('click', handleGlobalClick)
      document.removeEventListener('keydown', handleEscapeKey)
    }
  }, [clickedAppointment])

  const handleSlotClick = (e) => {
    // Prevent slot clicks during drag or resize operations
    if (isDragging || isResizing) {
      return
    }
    
    // Close overlay if clicking on empty slot
    if (clickedAppointment) {
      setClickedAppointment(null)
      return
    }
    
    if (onClick) {
      onClick(date, hour, minute, e)
    }
  }

  // Enhanced hover state for multi-employee views
  const hoverClass = employeeId 
    ? 'hover:bg-blue-50 hover:border-blue-200' 
    : 'hover:bg-gray-50'

  return (
    <div
      className={`border-b border-gray-300 p-1 cursor-pointer relative transition-colors ${
        config.clickToCreate ? hoverClass : ''
      } ${!isWorking && config.showWorkingHours ? 'bg-gray-100' : ''}`}
      style={{ height: `${config.gridHeight / (60 / config.timeResolution)}px` }}
      onClick={handleSlotClick}
      data-employee-id={employeeId}
      data-time-slot={`${hour}:${minute.toString().padStart(2, '0')}`}
    >
      {appointments.filter(appointment => appointment != null).map((appointment, index) => (
        <AppointmentSlot
          key={`${appointment.id}-${index}`}
          appointment={appointment}
          config={config}
          slotHour={hour}
          slotMinute={minute}
          onClick={handleAppointmentClick}
          onDragStart={onAppointmentDragStart}
          onDragMove={onAppointmentDragMove}
          onDragEnd={onAppointmentDragEnd}
          onResizeStart={onAppointmentResizeStart}
          onResizeMove={onAppointmentResizeMove}
          onResizeEnd={onAppointmentResizeEnd}
          onStatusUpdate={onAppointmentStatusUpdate}
          onEditAppointment={onAppointmentEdit}
          onMoveAppointment={onAppointmentMove}
          scrollContainerRef={scrollContainerRef}
          employeeId={employeeId}
          displayMode={displayMode}
          weekDays={weekDays}
          selectedEmployees={selectedEmployees}
          allAppointments={allAppointments}
          currentDate={currentDate}
          isShowingConfirmation={isShowingConfirmation}
          draggedAppointmentId={draggedAppointmentId}
          globalDragOffset={globalDragOffset}
          isShowingResizeConfirmation={isShowingResizeConfirmation}
          resizedAppointmentId={resizedAppointmentId}
          globalResizeOffset={globalResizeOffset}
          isUpdatingStatus={isUpdatingStatus}
        />
      ))}
      
      {/* Visual indicator for multi-employee mode */}
      {employeeId && appointments.length === 0 && config.clickToCreate && (
        <div className="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-20 transition-opacity">
          <div className="text-xs text-gray-400">+</div>
        </div>
      )}

      {/* Click Overlay */}
      {clickedAppointment && (
        <div 
          ref={overlayRef}
          className="fixed pointer-events-auto z-50"
          style={{
            left: overlayPosition.x,
            top: overlayPosition.y
          }}
        >
          <AppointmentClickOverlay
            appointment={clickedAppointment}
            isVisible={true}
            onStatusUpdate={onAppointmentStatusUpdate}
            onEditAppointment={onAppointmentEdit}
            onMoveAppointment={onAppointmentMove}
            onClose={handleOverlayClose}
            isUpdatingStatus={isUpdatingStatus}
          />
        </div>
      )}
    </div>
  )
}

export default TimeSlot 