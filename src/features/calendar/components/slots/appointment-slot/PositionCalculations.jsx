/**
 * PositionCalculations - Handles position calculations for appointments
 */
export const usePositionCalculations = (appointment, config, slotHour, slotMinute) => {
  // Calculate positioning within the time slot
  const calculatePosition = (overlapInfo = null) => {
    // Parse appointment start time to get hour and minute
    const appointmentStart = new Date(appointment.start)
    const appointmentHour = appointmentStart.getHours()
    const appointmentMinute = appointmentStart.getMinutes()
    
    // Calculate if this appointment starts in this specific time slot
    const isAppointmentStart = appointmentHour === slotHour && 
      Math.floor(appointmentMinute / config.timeResolution) * config.timeResolution === slotMinute
    
    if (isAppointmentStart) {
      // This is where the appointment starts - calculate precise positioning
      const minuteOffset = appointmentMinute - slotMinute
      const slotHeight = config.gridHeight / (60 / config.timeResolution)
      const pixelOffset = (minuteOffset / config.timeResolution) * slotHeight
      
      // Base position - account for TimeSlot padding (p-1 = 4px)
      const basePosition = {
        top: `${pixelOffset}px`, // Top positioning handled by parent TimeSlot padding
        left: '3px' // Adjust for TimeSlot p-1 left padding (4px - 1px for spacing)
      }
      
      // Apply overlap positioning if provided
      if (overlapInfo && overlapInfo.hasOverlap) {
        const { positioning } = overlapInfo
        return {
          ...basePosition,
          left: positioning.left,
          width: positioning.width,
          right: 'auto', // Clear right when using left + width
          zIndex: positioning.zIndex,
          marginRight: positioning.marginRight
        }
      }
      
      // Apply consistent width for non-overlapping appointments
      return {
        ...basePosition,
        width: '90%' // Consistent with overlap calculation
      }
    } else {
      // This appointment continues from a previous slot - don't render it here
      return null
    }
  }

  return {
    calculatePosition
  }
} 