import React, { useState } from 'react'
import { 
  CheckCircleIcon, 
  XCircleIcon, 
  ClockIcon, 
  ExclamationTriangleIcon,
  ArrowRightIcon,
  PencilIcon,
  TrashIcon,
  DocumentDuplicateIcon,
  PrinterIcon,
  DocumentTextIcon,
  ClipboardDocumentListIcon,
  ArrowPathIcon,
  CloudArrowUpIcon,
  ChevronRightIcon,
  ChevronDownIcon,
  CreditCardIcon,
  UserIcon,
  BellIcon
} from '@heroicons/react/24/outline'
import { AppointmentStatus } from '../../../types/index'
import { APPOINTMENT_STATUSES } from '../../../constants/calendarConfig'

/**
 * AppointmentClickOverlay - Shows action buttons when clicking appointment blocks
 * Two-layer overlay system with categorized functionalities
 */
const AppointmentClickOverlay = ({ 
  appointment, 
  isVisible, 
  onStatusUpdate, 
  onEditAppointment, 
  onMoveAppointment, 
  onClose,
  isUpdatingStatus = false
}) => {
  const [isUpdating, setIsUpdating] = useState(false)
  const [expandedSection, setExpandedSection] = useState(null)

  if (!isVisible || !appointment) return null

  // Use appointment status directly
  const currentStatus = appointment.status

  const getStatusIcon = (status) => {
    switch (status) {
      case AppointmentStatus.REQUESTED:
        return <ClockIcon className="h-4 w-4" />
      case AppointmentStatus.CONFIRMED:
        return <CheckCircleIcon className="h-4 w-4" />
      case AppointmentStatus.ACCEPTED:
        return <CheckCircleIcon className="h-4 w-4" />
      case AppointmentStatus.CHECKED_IN:
        return <ClockIcon className="h-4 w-4" />
      case AppointmentStatus.SERVICE_STARTED:
        return <ClockIcon className="h-4 w-4" />
      case AppointmentStatus.COMPLETED:
        return <CheckCircleIcon className="h-4 w-4" />
      case AppointmentStatus.CANCELLED:
        return <ExclamationTriangleIcon className="h-4 w-4" />
      case AppointmentStatus.NO_SHOW:
        return <XCircleIcon className="h-4 w-4" />
      default:
        return <CheckCircleIcon className="h-4 w-4" />
    }
  }

  const handleStatusChange = async (newStatus) => {
    // Check if this is the current status - no action needed
    if (newStatus === currentStatus) {
      console.log('⚠️ Status is already set to:', newStatus)
      return
    }
    
    const currentStatusLower = appointment.status?.toLowerCase()
    const isAlreadyCancelled = currentStatusLower === 'cancelled' || currentStatusLower === 'canceled'
    
    if (isAlreadyCancelled && newStatus === AppointmentStatus.CANCELLED) {
      console.log('⚠️ Appointment is already cancelled, skipping action')
      return
    }
    
    // No validation - allow any status change

    setIsUpdating(true)
    try {
      if (newStatus === AppointmentStatus.CANCELLED) {
        await onStatusUpdate(appointment.id, newStatus, 'Cancelled by staff')
      } else {
        await onStatusUpdate(appointment.id, newStatus, null)
      }
    } catch (error) {
      console.error('Failed to update status:', error)
    } finally {
      setIsUpdating(false)
    }
  }

  const handleActionClick = (action, event) => {
    event.stopPropagation()
    
    switch (action) {
      case 'edit':
        if (onClose) onClose()
        if (onEditAppointment) onEditAppointment(appointment)
        break
      case 'move':
        if (onClose) onClose()
        if (onMoveAppointment) onMoveAppointment(appointment)
        break
      case 'notes':
        // TODO: Implement notes functionality
        console.log('Notes clicked')
        break
      case 'forms':
        // TODO: Implement forms functionality
        console.log('Forms clicked')
        break
      case 'rebook':
        // TODO: Implement rebook functionality
        console.log('Rebook clicked')
        break
      case 'print':
        // TODO: Implement print functionality
        console.log('Print clicked')
        break
      case 'delete':
        // TODO: Implement delete functionality
        console.log('Delete clicked')
        break
      case 'upload':
        // TODO: Implement upload functionality
        console.log('Upload clicked')
        break
      case 'checkout':
        // TODO: Implement checkout functionality
        console.log('Checkout clicked')
        break
      default:
        break
    }
  }

  const toggleSection = (section) => {
    setExpandedSection(expandedSection === section ? null : section)
  }

  const isButtonDisabled = isUpdatingStatus || isUpdating

  // Handle clicking inside the overlay to prevent it from closing
  const handleOverlayClick = (event) => {
    event.stopPropagation()
  }

  // Main action categories
  const actionCategories = [
    {
      id: 'status',
      label: 'Change Status',
      icon: <ExclamationTriangleIcon className="h-4 w-4" />,
      color: 'bg-red-500',
      description: 'Update appointment status.'
    },
    {
      id: 'edit',
      label: 'Edit',
      icon: <PencilIcon className="h-4 w-4" />,
      color: 'bg-blue-500',
      description: 'Edit this appointment.',
      action: 'edit'
    },
    {
      id: 'notes',
      label: 'Notes',
      icon: <DocumentTextIcon className="h-4 w-4" />,
      color: 'bg-yellow-500',
      description: 'View client notes.',
      action: 'notes'
    },
    {
      id: 'forms',
      label: 'Forms',
      icon: <ClipboardDocumentListIcon className="h-4 w-4" />,
      color: 'bg-cyan-500',
      description: 'View client forms.',
      action: 'forms'
    },
    {
      id: 'rebook',
      label: 'Rebook',
      icon: <ArrowPathIcon className="h-4 w-4" />,
      color: 'bg-green-500',
      description: 'Rebook this appointment.',
      action: 'rebook'
    },
    {
      id: 'print',
      label: 'Print Ticket',
      icon: <PrinterIcon className="h-4 w-4" />,
      color: 'bg-gray-800',
      description: 'Print a ticket for this appointment.',
      action: 'print'
    },
    {
      id: 'delete',
      label: 'Delete',
      icon: <TrashIcon className="h-4 w-4" />,
      color: 'bg-red-600',
      description: 'Delete this appointment.',
      action: 'delete'
    },
    {
      id: 'move',
      label: 'Move',
      icon: <ArrowRightIcon className="h-4 w-4" />,
      color: 'bg-orange-500',
      description: 'Move to a different time.',
      action: 'move'
    },
    {
      id: 'upload',
      label: 'Upload File',
      icon: <CloudArrowUpIcon className="h-4 w-4" />,
      color: 'bg-blue-600',
      description: 'Attach a file to this appointment.',
      action: 'upload'
    }
  ]

  return (
    <div className="relative">
      <div 
        className="bg-white rounded-2xl shadow-2xl border border-gray-200 overflow-hidden min-w-[200px] max-w-300px] pointer-events-auto transform transition-all duration-200 ease-out" 
        style={{ zIndex: 40 }}
        onClick={handleOverlayClick}
      >
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-500 to-indigo-500 p-2 text-white">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
              <UserIcon className="h-6 w-6" />
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-lg">
                {appointment.clientName || 'Customer'}
              </h3>
              <p className="text-white/90 text-sm">
                {appointment.serviceShortName || appointment.serviceName || appointment.location || 'Service'}
              </p>
              {/* Add-ons display in overlay */}
              {appointment.addOns && appointment.addOns.length > 0 && (
                <p className="text-white/80 text-xs mt-1">
                  + {appointment.addOns.map(addOn => addOn.add_on_short_name || addOn.add_on_name || addOn.name).join(', ')}
                </p>
              )}
              <p className="text-white/80 text-xs">
                {new Date(appointment.start || appointment.startTime).toLocaleDateString('en-US', {
                  weekday: 'short',
                  month: 'short',
                  day: 'numeric',
                  hour: 'numeric',
                  minute: '2-digit'
                })}
              </p>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-h-[70vh] overflow-y-auto">
          {/* Action Categories */}
          <div className="p-2">
            {actionCategories.map((category) => (
              <div key={category.id} className="mb-1">
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    if (category.action) {
                      handleActionClick(category.action, e)
                    } else {
                      toggleSection(category.id)
                    }
                  }}
                  disabled={isButtonDisabled}
                  className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
                >
                  <div className={`p-2 rounded-lg ${category.color} text-white`}>
                    {category.icon}
                  </div>
                  <div className="flex-1 text-left">
                    <p className="font-medium text-gray-900">{category.label}</p>
                    <p className="text-sm text-gray-600">{category.description}</p>
                  </div>
                  {category.id === 'status' && (
                    <ChevronRightIcon 
                      className={`h-5 w-5 text-gray-400 transition-transform ${
                        expandedSection === category.id ? 'rotate-90' : ''
                      }`} 
                    />
                  )}
                </button>

                {/* Status Change Submenu - Show ALL statuses */}
                {category.id === 'status' && expandedSection === 'status' && (
                  <div className="ml-4 mt-2 space-y-2">
                    <p className="text-xs text-gray-500 mb-2">Current: {Object.values(APPOINTMENT_STATUSES).find(s => s.id === currentStatus)?.name || currentStatus}</p>
                    {/* Status Grid */}
                    <div className="grid grid-cols-2 gap-2 mt-3">
                      {Object.values(APPOINTMENT_STATUSES).map((status) => {
                        const isCurrentStatus = appointment.status === status.id
                        
                        return (
                          <button
                            key={status.id}
                            onClick={() => handleStatusChange(status.id)}
                            className={`
                              text-white px-3 py-2 rounded-md text-sm font-medium
                              transition-all duration-200 flex items-center justify-center gap-2
                              ${isCurrentStatus 
                                ? 'ring-2 ring-white ring-offset-2 ring-offset-gray-100' 
                                : 'hover:brightness-110 hover:scale-105'
                              }
                            `}
                            style={{ backgroundColor: status.color }}
                          >
                            {getStatusIcon(status.id)}
                            <span>{status.name}</span>
                            {isCurrentStatus && <span className="text-xs">•</span>}
                          </button>
                        )
                      })}
                    </div>
                    
                    {/* Helper Text */}
                    <div className="mt-3 text-xs text-gray-500">
                      <p>• Current status has a white ring</p>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Checkout Button */}
          <div className="p-4 border-t border-gray-200">
            <button
              onClick={(event) => handleActionClick('checkout', event)}
              disabled={isButtonDisabled}
              className="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors disabled:opacity-50"
            >
              <div className="flex items-center justify-center gap-2">
                <CreditCardIcon className="h-5 w-5" />
                <span>Checkout</span>
              </div>
            </button>
          </div>
        </div>

        {/* Close Button */}
        <div className="absolute top-4 right-4">
          <button
            onClick={(event) => {
              event.stopPropagation()
              onClose?.()
            }}
            className="p-1 rounded-full bg-white/20 hover:bg-white/30 transition-colors"
          >
            <XCircleIcon className="h-5 w-5 text-white" />
          </button>
        </div>
      </div>
    </div>
  )
}

export default AppointmentClickOverlay 