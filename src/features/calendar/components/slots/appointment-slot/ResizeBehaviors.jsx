import { useState, useRef, useCallback, useEffect } from 'react'

/**
 * ResizeBehaviors - Handles all resize-related functionality for appointments
 */
export const useResizeBehaviors = (
  appointment,
  config,
  onResizeStart,
  onResizeMove,
  onResizeEnd,
  appointmentHeight,
  resizedAppointmentId,
  globalResizeOffset,
  isShowingResizeConfirmation
) => {
  const [resizeState, setResizeState] = useState({
    isResizing: false,
    resizeOffset: { x: 0, y: 0 },
    startPosition: { x: 0, y: 0 },
    originalHeight: 0,
    wasResized: false,
    currentDelta: { x: 0, y: 0 },
    resizeDirection: 'end',
    lastSnappedY: null
  })

  const [isPressed, setIsPressed] = useState(false)
  const resizeTopRef = useRef(null)
  const resizeBottomRef = useRef(null)

  // Check if this appointment is being resized globally
  const isThisAppointmentBeingResized = resizedAppointmentId === appointment.id
  const isGloballyResized = isThisAppointmentBeingResized && isShowingResizeConfirmation

  // Reset visual state when confirmation is resolved
  useEffect(() => {
    if (!isShowingResizeConfirmation && resizedAppointmentId === appointment.id && resizeState.wasResized) {
      console.log('🎯 ResizeBehaviors resetting resize visual state after confirmation resolved')
      setResizeState(prev => ({
        ...prev,
        resizeOffset: { x: 0, y: 0 },
        currentDelta: { x: 0, y: 0 },
        wasResized: false,
        lastSnappedY: null
      }))
    }
  }, [isShowingResizeConfirmation, resizedAppointmentId, appointment.id, resizeState.wasResized])

  // Detect appointment data changes and ensure proper height updates after resize
  useEffect(() => {
    if (resizedAppointmentId === appointment.id && resizeState.wasResized) {
      console.log('🎯 ResizeBehaviors forcing visual state reset after data update')
      setResizeState(prev => ({
        ...prev,
        resizeOffset: { x: 0, y: 0 },
        currentDelta: { x: 0, y: 0 },
        wasResized: false,
        lastSnappedY: null
      }))
    }
  }, [appointment.start, appointment.end, appointmentHeight, resizedAppointmentId, resizeState.wasResized])

  // Calculate new start and end times from resize movement
  const calculateNewEndTimeFromResize = (deltaY, direction) => {
    const originalStart = new Date(appointment.start)
    const originalEnd = new Date(appointment.end || appointment.endTime)
    
    const timeSlotHeight = config.gridHeight / (60 / config.timeResolution)
    const timeSteps = Math.round(deltaY / timeSlotHeight)
    const timeChangeMinutes = timeSteps * config.timeResolution
    
    let newStartTime = originalStart
    let newEndTime = originalEnd
    
    if (direction === 'end') {
      newEndTime = new Date(originalEnd.getTime() + (timeChangeMinutes * 60 * 1000))
      const minEndTime = new Date(originalStart.getTime() + (15 * 60 * 1000))
      if (newEndTime < minEndTime) {
        newEndTime = minEndTime
      }
    } else if (direction === 'start') {
      newStartTime = new Date(originalStart.getTime() + (timeChangeMinutes * 60 * 1000))
      
      const maxStartTime = new Date(originalEnd.getTime() - (15 * 60 * 1000))
      if (newStartTime > maxStartTime) {
        newStartTime = maxStartTime
      }
      
      newEndTime = originalEnd
    }
    
    return {
      newStartTime,
      newEndTime
    }
  }

  // Main resize handler
  const handleResizeMouseDown = useCallback((e, direction) => {
    console.log('🎯 ResizeBehaviors handleResizeMouseDown called:', { direction })
    e.preventDefault()
    e.stopPropagation()
    
    const startPosition = {
      x: e.clientX,
      y: e.clientY
    }
    
    setResizeState({
      isResizing: false,
      resizeOffset: { x: 0, y: 0 },
      startPosition,
      originalHeight: appointmentHeight,
      wasResized: false,
      currentDelta: { x: 0, y: 0 },
      resizeDirection: direction,
      lastSnappedY: null
    })
    
    setIsPressed(true)
    
    const handleResizeMove = (moveEvent) => {
      const deltaX = moveEvent.clientX - startPosition.x
      const deltaY = moveEvent.clientY - startPosition.y
      const resizeDistance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)
      
      if (resizeDistance > 5) {
        const resizeResult = calculateNewEndTimeFromResize(deltaY, direction)
        
        if (!resizeState.isResizing) {
          setResizeState(prev => ({
            ...prev,
            isResizing: true,
            resizeOffset: { x: 0, y: deltaY },
            currentDelta: { x: deltaX, y: deltaY }
          }))
          
          if (onResizeStart) {
            onResizeStart(appointment, direction)
          }
        } else {
          setResizeState(prev => ({
            ...prev,
            resizeOffset: { x: 0, y: deltaY },
            currentDelta: { x: deltaX, y: deltaY }
          }))
        }
        
        if (onResizeMove) {
          onResizeMove(appointment, resizeResult.newEndTime, { 
            isResizing: true, 
            offset: { x: 0, y: deltaY },
            direction: direction,
            newStartTime: resizeResult.newStartTime,
            timeResolution: config.timeResolution,
            displayHourStart: config.displayHourStart,
            displayHourEnd: config.displayHourEnd,
            showWorkingHours: config.showWorkingHours
          })
        }
      }
    }

    const handleResizeUp = () => {
      setIsPressed(false)
      
      setResizeState(currentResizeState => {
        if (currentResizeState.isResizing) {
          const finalResizeResult = calculateNewEndTimeFromResize(currentResizeState.currentDelta.y, currentResizeState.resizeDirection)
          
          const originalEnd = new Date(appointment.end || appointment.endTime)
          const timeDifference = Math.abs(new Date(finalResizeResult.newEndTime) - originalEnd)
          const wasResized = timeDifference > 5000
          
          if (onResizeEnd) {
            onResizeEnd(appointment, finalResizeResult.newEndTime, { 
              isResizing: false, 
              offset: currentResizeState.resizeOffset,
              direction: currentResizeState.resizeDirection,
              newStartTime: finalResizeResult.newStartTime,
              completed: true,
              timeResolution: config.timeResolution,
              wasResized
            })
          }
          
          return {
            ...currentResizeState,
            isResizing: false,
            wasResized: true,
          }
        }
        
        return currentResizeState
      })
      
      document.removeEventListener('mousemove', handleResizeMove)
      document.removeEventListener('mouseup', handleResizeUp)
    }
    
    document.addEventListener('mousemove', handleResizeMove)
    document.addEventListener('mouseup', handleResizeUp)
  }, [appointment, config, onResizeStart, onResizeMove, onResizeEnd, resizeState.isResizing])

  // Calculate dynamic height during resize
  const calculateDynamicHeight = (baseHeight) => {
    const effectiveResizeOffset = isGloballyResized ? globalResizeOffset : resizeState.resizeOffset
    const shouldApplyResizeTransform = resizeState.isResizing || resizeState.wasResized || isGloballyResized

    if (shouldApplyResizeTransform) {
      const timeSlotHeight = config.gridHeight / (60 / config.timeResolution)
      const timeSteps = Math.round(effectiveResizeOffset.y / timeSlotHeight)
      const quantizedOffset = timeSteps * timeSlotHeight
      
      if (resizeState.resizeDirection === 'end') {
        return Math.max(30, baseHeight + quantizedOffset)
      } else if (resizeState.resizeDirection === 'start') {
        return Math.max(30, baseHeight - quantizedOffset)
      } else {
        return Math.max(30, baseHeight + quantizedOffset)
      }
    }

    return baseHeight
  }

  return {
    resizeState,
    resizeTopRef,
    resizeBottomRef,
    isPressed,
    isGloballyResized,
    handleResizeMouseDown,
    calculateDynamicHeight,
    setIsPressed
  }
} 