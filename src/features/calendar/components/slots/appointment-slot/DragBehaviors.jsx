import { useState, useRef, useCallback, useEffect } from 'react'
import { 
  calculateNewTimeFromOffset,
  getAppointmentsForBorderDetection,
  triggerHapticFeedback,
  calculateAppointmentOffset,
  calculateSmartSnappedPosition
} from '../../../utils/timeGridCalculations'
import { useAutoScrollOnDrag } from '../../../hooks/useAutoScrollOnDrag'

/**
 * DragBehaviors - Handles all drag-related functionality for appointments
 */
export const useDragBehaviors = (
  appointment,
  config,
  onDragStart,
  onDragMove,
  onDragEnd,
  allAppointments,
  currentDate,
  displayMode,
  weekDays,
  selectedEmployees,
  draggedAppointmentId,
  globalDragOffset,
  isShowingConfirmation,
  scrollContainerRef, // New prop for auto-scroll functionality
  isEdgeCaseMode = false, // NEW: Edge case mode from parent
  edgeCaseInfo = null // NEW: Edge case details from parent
) => {
  // Debug: Log the initial parameters to ensure everything is passed correctly
  console.log('🎯 useDragBehaviors initialized for appointment:', {
    appointmentId: appointment?.id,
    displayMode,
    selectedEmployeeCount: selectedEmployees?.length,
    employeeNames: selectedEmployees?.map(emp => emp.name || emp.full_name)
  })
  const [dragState, setDragState] = useState({
    isDragging: false,
    dragOffset: { x: 0, y: 0 },
    startPosition: { x: 0, y: 0 },
    originalPosition: { x: 0, y: 0 },
    wasDragged: false,
    currentDelta: { x: 0, y: 0 },
    lastSnappedY: null,
    smartSnapInfo: null,
    showGhostCopy: false, // New state for ghost copy visibility
    originalSlotPosition: null // Track original slot position for ghost copy
  })

  const [isPressed, setIsPressed] = useState(false)
  const dragRef = useRef(null)

  // Initialize auto-scroll functionality
  const {
    startAutoScroll,
    stopAutoScroll,
    handleDragMoveWithAutoScroll,
    handleDragEndWithAutoScroll,
    isAutoScrolling
  } = useAutoScrollOnDrag(scrollContainerRef)

  // Check if this appointment is being dragged globally
  const isThisAppointmentBeingDragged = draggedAppointmentId === appointment.id
  const isGloballyDragged = isThisAppointmentBeingDragged && isShowingConfirmation

  // Reset visual state when confirmation is resolved  
  useEffect(() => {
    console.log('🎯 DragBehaviors confirmation state change:', {
      appointmentId: appointment.id,
      isShowingConfirmation,
      draggedAppointmentId,
      isThisAppointmentBeingDragged,
      dragStateWasDragged: dragState.wasDragged,
      currentShowGhostCopy: dragState.showGhostCopy
    })
    
    // Only hide ghost copy when:
    // 1. Confirmation modal is closed (!isShowingConfirmation)
    // 2. This appointment was the one being dragged (isThisAppointmentBeingDragged)
    // 3. We previously had drag state (dragState.wasDragged)
    // 4. No appointment is currently being dragged (draggedAppointmentId is null)
    const shouldHideGhostCopy = !isShowingConfirmation && 
                               dragState.wasDragged && 
                               isThisAppointmentBeingDragged &&
                               !draggedAppointmentId // Only hide when no appointment is being dragged
    
    if (shouldHideGhostCopy) {
      console.log('🎯 Resetting drag state after confirmation - hiding ghost copy')
      setDragState(prev => ({
        ...prev,
        wasDragged: false,
        isDragging: false,
        dragOffset: { x: 0, y: 0 },
        showGhostCopy: false, // Hide ghost copy when confirmation is resolved
        originalSlotPosition: null
      }))
    }
  }, [isShowingConfirmation, draggedAppointmentId, appointment.id, dragState.wasDragged, isThisAppointmentBeingDragged])

  // Calculate movement for the current view mode
  const calculateMovementForViewMode = useCallback((deltaX, deltaY) => {
    console.log('🎯 Calculating movement:', { deltaX, deltaY, displayMode, config, isEdgeCaseMode, edgeCaseInfo })
    
    // Validate config parameter
    if (!config || !config.timeResolution) {
      console.error('❌ Config is missing or invalid:', config)
      return {
        offset: { x: 0, y: deltaY },
        newTime: calculateNewTimeFromOffset(deltaY, appointment, config || {}),
        targetColumn: null,
        targetEmployee: null,
        type: 'time-only',
        smartSnapInfo: { snappedY: deltaY, snapType: 'none' }
      }
    }
    
    // Edge case info is passed to DragLineIndicator for positioning, but does not affect movement logic
    
    // Get appointments for border detection
    const appointmentsForBorderDetection = getAppointmentsForBorderDetection(
      allAppointments,
      currentDate,
      displayMode,
      weekDays,
      selectedEmployees
    )
    
    // Calculate current appointment position
    const currentY = calculateAppointmentOffset(appointment, config)
    console.log('🎯 Appointment position calculation:', { currentY, appointment: appointment.id })
    
    // Use smart snapping for more precise appointment positioning
    const smartSnapResult = calculateSmartSnappedPosition(
      currentY,
      deltaY,
      dragState.lastSnappedY,
      appointment,
      appointmentsForBorderDetection,
      config
    )
    
    console.log('🎯 Smart snap result:', smartSnapResult)
    
    // Trigger haptic feedback if needed
    if (smartSnapResult.shouldTriggerHaptic) {
      triggerHapticFeedback(smartSnapResult.snapType === 'time-grid' ? 'light' : 'medium')
    }
    
    const verticalSnappedOffset = smartSnapResult.snappedY - currentY
    
    // Week view supports cross-day dragging
    if (displayMode === 'week' && weekDays && weekDays.length > 0) {
      console.log('🎯 Week view cross-day calculation')
      
      const getCurrentAppointmentColumn = () => {
        const appointmentDate = new Date(appointment.start)
        
        for (let index = 0; index < weekDays.length; index++) {
          const day = weekDays[index]
          if (appointmentDate.toDateString() === day.toDateString()) {
            return index
          }
        }
        return 0
      }
      
      const calculateNewDateForDrop = (targetColumn, verticalTranslation, targetDate) => {
        if (displayMode === 'week' && weekDays.length > 0) {
          const clampedColumn = Math.max(0, Math.min(targetColumn, weekDays.length - 1))
          const newDate = weekDays[clampedColumn]
          
          const newTime = calculateNewTimeFromOffset(verticalTranslation, appointment, config)
          
          const combinedDateTime = new Date(newDate)
          const timeFromNewTime = new Date(newTime)
          combinedDateTime.setHours(
            timeFromNewTime.getHours(),
            timeFromNewTime.getMinutes(),
            timeFromNewTime.getSeconds(),
            timeFromNewTime.getMilliseconds()
          )
          
          return combinedDateTime
        }
        
        return calculateNewTimeFromOffset(verticalTranslation, appointment, config)
      }
      
      // Cross-day calculation logic
      const calendarContainer = document.querySelector('.calendar-scroll-container')
      const timeColumn = document.querySelector('.w-16.border-r.border-gray-200.bg-gray-50')
      const dayColumnsContainer = document.querySelector('.calendar-scroll-container .flex-1.flex')
      
      let containerWidth = window.innerWidth - 320
      let timeColumnWidth = 64
      
      if (timeColumn) {
        timeColumnWidth = timeColumn.offsetWidth
      }
      
      if (dayColumnsContainer) {
        containerWidth = dayColumnsContainer.offsetWidth
      } else if (calendarContainer) {
        containerWidth = calendarContainer.offsetWidth - timeColumnWidth
      }
      
      const dayColumnWidth = containerWidth / weekDays.length
      const currentAppointmentColumn = getCurrentAppointmentColumn()
      
      const columnOffset = deltaX / dayColumnWidth
      const roundedColumnOffset = Math.round(columnOffset)
      const targetColumn = currentAppointmentColumn + roundedColumnOffset
      
      const clampedTargetColumn = Math.max(0, Math.min(targetColumn, weekDays.length - 1))
      
      const columnDifference = clampedTargetColumn - currentAppointmentColumn
      const snappedHorizontalOffset = columnDifference * dayColumnWidth
      
      const targetDate = weekDays[clampedTargetColumn]
      
      try {
        const newTime = calculateNewDateForDrop(clampedTargetColumn, verticalSnappedOffset, targetDate)
        
        return {
          offset: { x: snappedHorizontalOffset, y: verticalSnappedOffset },
          newTime,
          targetColumn: clampedTargetColumn,
          targetEmployee: null,
          type: 'cross-day',
          smartSnapInfo: {
            ...smartSnapResult,
            crossDayInfo: {
              currentColumn: currentAppointmentColumn,
              columnOffset: columnOffset.toFixed(2),
              roundedColumnOffset,
              targetColumn: clampedTargetColumn,
              columnDifference,
              targetDate: targetDate.toDateString(),
              horizontalOffset: snappedHorizontalOffset,
              verticalOffset: verticalSnappedOffset,
              dayColumnWidth,
              deltaX,
              actualMovement: clampedTargetColumn !== currentAppointmentColumn ? 'CROSS-DAY' : 'SAME-DAY'
            }
          }
        }
      } catch (error) {
        console.error('❌ Error in week view calculation:', error)
        // Fallback to simple calculation
        const fallbackTime = new Date(appointment.start)
        return {
          offset: { x: 0, y: verticalSnappedOffset },
          newTime: fallbackTime,
          targetColumn: clampedTargetColumn,
          targetEmployee: null,
          type: 'cross-day',
          smartSnapInfo: smartSnapResult
        }
      }
    }
    
    // Day view calculation
    try {
      const newTime = calculateNewTimeFromOffset(verticalSnappedOffset, appointment, config)
      console.log('🎯 Day view - calling getTargetEmployeeForDayView with:', {
        deltaX,
        selectedEmployees: selectedEmployees?.map(emp => ({ id: emp.id, name: emp.name || emp.full_name }))
      })
      const targetEmployee = getTargetEmployeeForDayView(deltaX, selectedEmployees)
      console.log('🎯 Day view - targetEmployee result:', targetEmployee)
      
      // Calculate horizontal offset for cross-employee moves in day view
      let horizontalOffset = 0
      if (targetEmployee && selectedEmployees && selectedEmployees.length > 1) {
        const currentEmployeeIndex = selectedEmployees.findIndex(emp => 
          emp.id === appointment.employeeId || emp.id.toString() === appointment.employeeId.toString()
        )
        const targetEmployeeIndex = selectedEmployees.findIndex(emp => emp.id === targetEmployee.id)
        
        if (currentEmployeeIndex !== -1 && targetEmployeeIndex !== -1) {
          // Calculate the column width and offset for visual feedback
          const calendarContainer = document.querySelector('.calendar-scroll-container')
          const timeColumn = document.querySelector('.w-16.border-r.border-gray-200.bg-gray-50')
          
          let containerWidth = window.innerWidth - 320
          let timeColumnWidth = 64
          
          if (timeColumn) {
            timeColumnWidth = timeColumn.offsetWidth
          }
          if (calendarContainer) {
            containerWidth = calendarContainer.offsetWidth - timeColumnWidth
          }
          
          const columnWidth = containerWidth / selectedEmployees.length
          const columnDifference = targetEmployeeIndex - currentEmployeeIndex
          horizontalOffset = columnDifference * columnWidth
          
          console.log('🎯 Day view horizontal offset calculation:', {
            currentEmployeeIndex,
            targetEmployeeIndex,
            columnDifference,
            columnWidth,
            horizontalOffset
          })
        }
      }
      
      return {
        offset: { x: horizontalOffset, y: verticalSnappedOffset },
        newTime,
        targetColumn: null,
        targetEmployee,
        type: targetEmployee ? 'employee-change' : 'time-only',
        smartSnapInfo: smartSnapResult
      }
    } catch (error) {
      console.error('❌ Error in day view calculation:', error)
      // Fallback calculation
      const fallbackTime = new Date(appointment.start)
      return {
        offset: { x: 0, y: verticalSnappedOffset },
        newTime: fallbackTime,
        targetColumn: null,
        targetEmployee: null,
        type: 'time-only',
        smartSnapInfo: smartSnapResult
      }
    }
  }, [appointment, allAppointments, config, currentDate, displayMode, weekDays, selectedEmployees, isEdgeCaseMode, edgeCaseInfo])

  // Get target employee for day view multi-employee scenarios
  const getTargetEmployeeForDayView = (deltaX, employees) => {
    console.log('🎯 getTargetEmployeeForDayView called:', {
      displayMode,
      employeeCount: employees?.length,
      deltaX,
      appointmentEmployeeId: appointment.employeeId
    })
    
    if (displayMode !== 'day' || !employees || employees.length <= 1) {
      console.log('🎯 Early return - not day view or not enough employees:', {
        displayMode,
        employeeCount: employees?.length
      })
      return null
    }
    
    // Calculate container width more accurately for day view
    const calendarContainer = document.querySelector('.calendar-scroll-container')
    const timeColumn = document.querySelector('.w-16.border-r.border-gray-200.bg-gray-50')
    
    // Try multiple selectors for the employee columns container
    let employeeColumnsContainer = document.querySelector('.calendar-scroll-container .flex-1.flex')
    if (!employeeColumnsContainer) {
      // Fallback selector for day view employee columns
      employeeColumnsContainer = document.querySelector('.calendar-scroll-container .flex-1 .flex')
    }
    
    let containerWidth = window.innerWidth - 320 // fallback
    let timeColumnWidth = 64 // fallback
    
    if (timeColumn) {
      timeColumnWidth = timeColumn.offsetWidth
    }
    
    if (employeeColumnsContainer) {
      containerWidth = employeeColumnsContainer.offsetWidth
    } else if (calendarContainer) {
      containerWidth = calendarContainer.offsetWidth - timeColumnWidth
    }
    
    console.log('🎯 DOM element detection:', {
      calendarContainer: !!calendarContainer,
      timeColumn: !!timeColumn,
      employeeColumnsContainer: !!employeeColumnsContainer,
      containerWidth,
      timeColumnWidth
    })
    
    const columnWidth = containerWidth / employees.length
    const currentEmployeeIndex = employees.findIndex(emp => 
      emp.id === appointment.employeeId || emp.id.toString() === appointment.employeeId.toString()
    )
    
    if (currentEmployeeIndex === -1) {
      console.warn('🚨 Could not find current employee in employees list:', {
        appointmentEmployeeId: appointment.employeeId,
        employeeIds: employees.map(emp => emp.id)
      })
      return null
    }
    
    const columnOffset = Math.round(deltaX / columnWidth)
    const targetIndex = Math.max(0, Math.min(currentEmployeeIndex + columnOffset, employees.length - 1))
    
    console.log('🎯 Cross-employee calculation:', {
      displayMode,
      employeeCount: employees.length,
      containerWidth,
      timeColumnWidth,
      columnWidth,
      deltaX,
      columnOffset,
      currentEmployeeIndex,
      targetIndex,
      currentEmployeeId: appointment.employeeId,
      currentEmployeeName: employees[currentEmployeeIndex]?.name || employees[currentEmployeeIndex]?.full_name,
      targetEmployeeId: employees[targetIndex]?.id,
      targetEmployeeName: employees[targetIndex]?.name || employees[targetIndex]?.full_name,
      willReturnEmployee: targetIndex !== currentEmployeeIndex
    })
    
    if (targetIndex !== currentEmployeeIndex) {
      console.log('✅ Returning target employee:', employees[targetIndex])
      return employees[targetIndex]
    }
    
    return null
  }

  // Main drag handler with auto-scroll integration
  const handleMouseDown = useCallback((e) => {
    console.log('🎯 DragBehaviors handleMouseDown called for appointment:', {
      appointmentId: appointment.id,
      appointmentTitle: appointment.title,
      displayMode,
      selectedEmployees: selectedEmployees?.length,
      employeeIds: selectedEmployees?.map(emp => emp.id)
    })
    e.preventDefault()
    e.stopPropagation()
    
    const startPosition = {
      x: e.clientX,
      y: e.clientY
    }
    
    setDragState({
      isDragging: false,
      dragOffset: { x: 0, y: 0 },
      startPosition,
      originalPosition: { x: 0, y: 0 },
      wasDragged: false,
      currentDelta: { x: 0, y: 0 },
      lastSnappedY: null,
      smartSnapInfo: null,
      showGhostCopy: false,
      originalSlotPosition: null
    })
    
    setIsPressed(true)
    
    const handleMove = (moveEvent) => {
      const deltaX = moveEvent.clientX - startPosition.x
      const deltaY = moveEvent.clientY - startPosition.y
      const dragDistance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)
      
      // Log drag movement for debugging
      if (dragDistance > 2) {
        console.log('🎯 Drag movement detected:', {
          appointmentId: appointment.id,
          deltaX,
          deltaY,
          dragDistance,
          displayMode,
          employeeCount: selectedEmployees?.length
        })
      }
      
      // Auto-scroll functionality - check if cursor is near edges
      if (scrollContainerRef?.current) {
        startAutoScroll(moveEvent.clientX, moveEvent.clientY)
      }
      
      if (dragDistance > 5) {
        console.log('🎯 Drag threshold exceeded, calculating movement...')
        const movement = calculateMovementForViewMode(deltaX, deltaY)
        
        if (!dragState.isDragging) {
          console.log('🎯 Starting drag operation for appointment:', {
            appointmentId: appointment.id,
            movement: movement,
            targetEmployee: movement.targetEmployee
          })
          
          // Capture original position for ghost copy
          const appointmentElement = e.target.closest('.appointment-slot')
          const originalSlotPosition = appointmentElement ? {
            top: appointmentElement.style.top,
            left: appointmentElement.style.left,
            width: appointmentElement.style.width,
            height: appointmentElement.style.height
          } : null
          
          console.log('🎯 DragBehaviors drag start - capturing ghost copy position:', {
            appointmentId: appointment.id,
            foundElement: !!appointmentElement,
            capturedPosition: originalSlotPosition,
            elementStyles: appointmentElement ? {
              top: appointmentElement.style.top,
              left: appointmentElement.style.left,
              width: appointmentElement.style.width,
              height: appointmentElement.style.height
            } : null
          })
          
          setDragState(prev => ({
            ...prev,
            isDragging: true,
            dragOffset: movement.offset,
            currentDelta: { x: deltaX, y: deltaY },
            lastSnappedY: movement.smartSnapInfo?.snappedY || prev.lastSnappedY,
            smartSnapInfo: movement.smartSnapInfo,
            showGhostCopy: true, // Show ghost copy when drag starts
            originalSlotPosition: originalSlotPosition
          }))
          
          if (onDragStart) {
            onDragStart(appointment, 'regular')
          }
        } else {
          setDragState(prev => ({
            ...prev,
            dragOffset: movement.offset,
            currentDelta: { x: deltaX, y: deltaY },
            lastSnappedY: movement.smartSnapInfo?.snappedY || prev.lastSnappedY,
            smartSnapInfo: movement.smartSnapInfo
          }))
        }
        
        if (onDragMove) {
          onDragMove(appointment, movement.offset, { 
            isDragging: true, 
            offset: movement.offset,
            mode: 'regular',
            timeResolution: config.timeResolution,
            targetColumn: movement.targetColumn,
            targetEmployee: movement.targetEmployee,
            movementType: movement.type
          })
        }
      }
    }

    const handleUp = () => {
      setIsPressed(false)
      
      // Stop auto-scroll when drag ends
      stopAutoScroll()
      
      setDragState(currentDragState => {
        if (currentDragState.isDragging) {
          const finalMovement = calculateMovementForViewMode(currentDragState.currentDelta.x, currentDragState.currentDelta.y)
          
          const originalTime = new Date(appointment.start)
          const timeDifference = Math.abs(new Date(finalMovement.newTime) - originalTime)
          const wasDragged = timeDifference > 5000
          
          console.log('🎯 DragBehaviors drag end - calculating ghost copy state:', {
            appointmentId: appointment.id,
            originalTime: originalTime.toISOString(),
            newTime: finalMovement.newTime.toISOString(),
            timeDifference: timeDifference,
            wasDragged: wasDragged,
            willShowGhostCopy: wasDragged,
            hasOriginalPosition: !!currentDragState.originalSlotPosition
          })
          
          if (onDragEnd) {
            onDragEnd(appointment, finalMovement.offset, { 
              isDragging: false, 
              offset: finalMovement.offset,
              mode: 'regular',
              completed: true,
              timeResolution: config.timeResolution,
              wasDragged,
              targetColumn: finalMovement.targetColumn,
              targetEmployee: finalMovement.targetEmployee,
              movementType: finalMovement.type
            })
          }
          
          const newState = {
            ...currentDragState,
            isDragging: false,
            wasDragged: true,
            // Keep ghost copy visible if there was significant movement (confirmation will show)
            // Only hide if no significant movement occurred
            showGhostCopy: wasDragged,
            originalSlotPosition: wasDragged ? currentDragState.originalSlotPosition : null
          }
          
          console.log('🎯 DragBehaviors setting drag end state:', {
            appointmentId: appointment.id,
            newShowGhostCopy: newState.showGhostCopy,
            newWasDragged: newState.wasDragged,
            keepingOriginalPosition: !!newState.originalSlotPosition
          })
          
          return newState
        }
        
        return currentDragState
      })
      
      document.removeEventListener('mousemove', handleMove)
      document.removeEventListener('mouseup', handleUp)
    }
    
    document.addEventListener('mousemove', handleMove)
    document.addEventListener('mouseup', handleUp)
  }, [appointment, config, onDragMove, onDragEnd, dragState.isDragging, startAutoScroll, stopAutoScroll, scrollContainerRef])

  return {
    dragState,
    dragRef,
    isPressed,
    isGloballyDragged,
    handleMouseDown,
    setIsPressed,
    isAutoScrolling, // Expose auto-scroll state
    showGhostCopy: dragState.showGhostCopy, // Expose ghost copy state
    originalSlotPosition: dragState.originalSlotPosition // Expose original position
  }
} 