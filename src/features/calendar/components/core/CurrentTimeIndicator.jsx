import React, { useState, useEffect, useMemo } from 'react'

/**
 * CurrentTimeIndicator - Shows a red line at the current time position
 * Works for all calendar views (Week, Day, Multi-employee)
 */
const CurrentTimeIndicator = ({ 
  config, 
  selectedDate, 
  weekDays = [], 
  displayMode = 'day',
  selectedEmployees = []
}) => {
  const [currentTime, setCurrentTime] = useState(new Date())
  
  // Update current time every minute
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(new Date())
    }, 60000) // Update every minute
    
    return () => clearInterval(interval)
  }, [])
  
  // Calculate if current time should be displayed
  const shouldShowIndicator = useMemo(() => {
    const now = new Date()
    const currentHour = now.getHours()
    const currentMinute = now.getMinutes()
    
    // Check if current time is within display hours
    if (currentHour < config.displayHourStart || currentHour >= config.displayHourEnd) {
      return false
    }
    
    // For week view, check if today is in the week
    if (displayMode === 'week' && weekDays.length > 0) {
      const today = new Date()
      return weekDays.some(day => day.toDateString() === today.toDateString())
    }
    
    // For day view, check if selected date is today
    if (displayMode === 'day') {
      const today = new Date()
      return selectedDate.toDateString() === today.toDateString()
    }
    
    return true
  }, [config.displayHourStart, config.displayHourEnd, displayMode, weekDays, selectedDate])
  
  // Calculate position
  const indicatorStyle = useMemo(() => {
    if (!shouldShowIndicator) return { display: 'none' }
    
    const now = new Date()
    const currentHour = now.getHours()
    const currentMinute = now.getMinutes()
    
    // Calculate position based on time grid configuration
    const minutesFromStart = (currentHour - config.displayHourStart) * 60 + currentMinute
    const totalMinutesInView = (config.displayHourEnd - config.displayHourStart) * 60
    const percentage = minutesFromStart / totalMinutesInView
    
    // Calculate pixel position
    const totalHeight = config.gridHeight * (config.displayHourEnd - config.displayHourStart)
    const topPosition = percentage * totalHeight
    
    return {
      position: 'absolute',
      top: `${topPosition}px`,
      left: '0px',
      right: '0px',
      height: '2px',
      backgroundColor: '#ef4444', // Red color
      zIndex: 40,
      pointerEvents: 'none'
    }
  }, [shouldShowIndicator, currentTime, config])
  
  // Calculate column-specific positioning for different views
  const getColumnSpecificStyle = () => {
    if (displayMode === 'week') {
      // For week view, position only on today's column
      const today = new Date()
      const todayIndex = weekDays.findIndex(day => day.toDateString() === today.toDateString())
      
      if (todayIndex === -1) return { display: 'none' }
      
      const columnWidth = `${100 / weekDays.length}%`
      const leftPosition = `${(todayIndex * 100) / weekDays.length}%`
      
      return {
        ...indicatorStyle,
        left: leftPosition,
        width: columnWidth,
        right: 'auto'
      }
    }
    
    // For day view and multi-employee view, span across all columns
    return indicatorStyle
  }
  
  const finalStyle = getColumnSpecificStyle()
  
  if (!shouldShowIndicator) return null
  
  return (
    <>
      {/* Main indicator line */}
      <div style={finalStyle}>
        {/* Circle indicator at the start of the line */}
        <div
          style={{
            position: 'absolute',
            left: '-4px',
            top: '-3px',
            width: '8px',
            height: '8px',
            backgroundColor: '#ef4444',
            borderRadius: '50%',
            border: '2px solid white',
            boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
          }}
        />
      </div>
    </>
  )
}

export default CurrentTimeIndicator 