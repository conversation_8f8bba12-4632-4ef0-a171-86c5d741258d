import React, { useState, useRef, useEffect, useCallback } from 'react'
import { DISPLAY_MODES } from '../../hooks/useAdvancedCalendar'
import { useActionSheet } from '../../hooks/useActionSheet'
import { useAppointmentDrag } from '../../hooks/useAppointmentDrag'
import { useAppointmentUpdateSlotResize } from '../../hooks/useAppointmentUpdateSlotResize'
import { useAppointmentCreation } from '../../hooks/useAppointmentCreation'
import { useWaitlist } from '../../hooks/useWaitlist'
import { useWaitlistSettings } from '../../../booking/hooks/useWaitlistSettings'

import CalendarHeader from './CalendarHeader'
import WeekView from '../views/WeekView'
import DayView from '../views/DayView'
import CalendarActionSheet from '../ui/CalendarActionSheet'
import CalendarSettingsPanel from '../settings/CalendarSettingsPanel'
import AppointmentMoveConfirmation from '../slots/AppointmentMoveConfirmation'
import AppointmentMoveModal from '../modals/AppointmentMoveModal'
import AppointmentUpdateSlotResizeConfirmation from '../slots/AppointmentUpdateSlotResizeConfirmation'
import AppointmentCreationModal from '../modals/AppointmentCreationModal'
import WaitlistModal from '../modals/WaitlistModal'
import DragLineIndicator from '../ui/DragLineIndicator'
import { generateWeekDays } from '../../utils/weekUtils'
import { appointmentService } from '../../services/appointmentService'

/**
 * Calendar - Modular calendar component with iOS-like functionality
 * Replaces EnhancedBasicCalendar with better maintainability
 */
const Calendar = ({ 
  className = '',
  selectedDate,
  displayMode,
  selectedEmployees,
  selectedEmployeeIDs,
  filteredAppointments,
  config,
  updateConfig,
  selectDate,
  setViewMode,
  onAppointmentCreate,
  onAppointmentEdit,
  onAppointmentCancel,
  onAppointmentStatusChange,
  onAppointmentClick,
  createAppointment,
  updateAppointment,
  deleteAppointment,
  refreshAppointments,
  getWorkingHoursForEmployee,
  getWorkingHoursForEmployeeSync,
  isEmployeeWorking,
  isWorkingHourSlot,
  scrollPosition,
  setScrollPosition,
  scrollToCurrentTime,
  currentEmployee
}) => {
  // Action sheet state
  const {
    actionSheet,
    hideActionSheet,
    showTimeSlotActionSheet,
    showAppointmentActionSheet
  } = useActionSheet()

  // Appointment creation state
  const appointmentCreation = useAppointmentCreation()
  
  // Waitlist management
  const waitlistHook = useWaitlist()
  
  // Waitlist settings
  const { allowCustomerWaitlistAddition } = useWaitlistSettings()

  // Appointment drag functionality
  const {
    handleDragStart,
    handleDragMove,
    handleDragEnd,
    confirmMove,
    cancelMove,
    showConfirmation,
    draggedAppointment,
    originalTime,
    newTime,
    movementType,
    targetEmployee,
    targetColumn,
    isUpdating,
    isDragging,
    finalDragOffset,
    isEdgeCaseMode,
    edgeCaseInfo,
    error: dragError
  } = useAppointmentDrag({
    isWorkingHourSlot,
    getWorkingHoursForEmployeeSync,
    refreshAppointments,
    config // ✅ ADD: Pass the calendar config for time resolution
  })

  // Appointment resize functionality
  const {
    handleResizeStart,
    handleResizeMove,
    handleResizeEnd,
    confirmResize,
    cancelResize,
    showConfirmation: showResizeConfirmation,
    resizedAppointment,
    originalDuration,
    newDuration,
    originalEndTime,
    newEndTime: newResizeEndTime,
    resizeDirection,
    isUpdating: isResizeUpdating,
    isResizing,
    finalResizeOffset,
    error: resizeError
  } = useAppointmentUpdateSlotResize({
    isWorkingHourSlot,
    getWorkingHoursForEmployeeSync
  })

  // Debug logging for drag state
  useEffect(() => {
    console.log('🔍 Calendar drag state:', {
      isDragging,
      showConfirmation,
      draggedAppointment: draggedAppointment?.id,
      originalTime: originalTime?.toISOString(),
      newTime: newTime?.toISOString()
    })
  }, [isDragging, showConfirmation, draggedAppointment, originalTime, newTime])

  // UI state
  const [isSettingsOpen, setIsSettingsOpen] = useState(false)
  const timeGridRef = useRef(null)

  // Comprehensive move modal state
  const [comprehensiveMoveModal, setComprehensiveMoveModal] = useState({
    isOpen: false,
    appointment: null,
    isUpdating: false
  })

  // Optimized scroll handler: avoid frequent React state updates on heavy DOM (5- & 15-min grids)
  const scrollRafRef = useRef(null)
  const scrollDebounceRef = useRef(null)
  const latestScrollPosRef = useRef(scrollPosition)

  const handleScroll = (e) => {
    const pos = e.target.scrollTop
    latestScrollPosRef.current = pos

    // Light animation-frame work for visual side-effects (none currently)
    if (scrollRafRef.current) cancelAnimationFrame(scrollRafRef.current)
    scrollRafRef.current = requestAnimationFrame(() => {
      // no-op: kept for future smooth sync (e.g. sticky headers)
    })

    // Debounce the expensive React state update (every 150 ms after user stops scrolling)
    if (scrollDebounceRef.current) clearTimeout(scrollDebounceRef.current)
    scrollDebounceRef.current = setTimeout(() => {
      setScrollPosition(latestScrollPosRef.current)
    }, 150)
  }

  // Cleanup timers/raf on unmount
  useEffect(() => {
    return () => {
      if (scrollRafRef.current) cancelAnimationFrame(scrollRafRef.current)
      if (scrollDebounceRef.current) clearTimeout(scrollDebounceRef.current)
    }
  }, [])

  // Update CSS custom properties when config changes
  useEffect(() => {
    const updateGridPattern = () => {
      const hourHeight = config.gridHeight
      const slotHeight = hourHeight / (60 / config.timeResolution)
      
      // Update CSS custom properties immediately
      document.documentElement.style.setProperty('--calendar-hour-height', `${hourHeight}px`)
      document.documentElement.style.setProperty('--calendar-slot-height', `${slotHeight}px`)
      
      // Force a reflow to ensure CSS changes are applied
      document.documentElement.offsetHeight
    }

    updateGridPattern()
  }, [config.gridHeight, config.timeResolution, config.displayHourStart, config.displayHourEnd])

  // Scroll position management
  useEffect(() => {
    if (timeGridRef.current) {
      timeGridRef.current.scrollTop = scrollPosition
    }
  }, [scrollPosition])

  // Generate time slots based on current configuration
  const generateTimeSlots = () => {
    const slots = []
    const startHour = config.displayHourStart
    const endHour = config.displayHourEnd
    const resolution = config.timeResolution

    for (let hour = startHour; hour < endHour; hour++) {
      for (let minute = 0; minute < 60; minute += resolution) {
        // Generate readable label based on resolution
        let label = ''
        
        if (minute === 0) {
          // Always show hour labels
          const hour12 = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour
          const period = hour < 12 ? 'AM' : 'PM'
          label = `${hour12} ${period}`
        } else if (resolution === 30 && minute === 30) {
          // Show 30-minute marker for 30-minute resolution
          label = '30'
        } else if (resolution === 15 && minute % 15 === 0) {
          // Show quarter-hour markers for 15-minute resolution
          label = minute.toString()
        } else if (resolution === 5 && minute % 15 === 0) {
          // Show quarter-hour markers for 5-minute resolution (every 15 minutes)
          label = minute === 0 ? '' : minute.toString() // Don't duplicate hour label
        }

        slots.push({
          hour,
          minute,
          time: `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`,
          label
        })
      }
    }

    return slots
  }

  const timeSlots = generateTimeSlots()

  // Generate display days
  const generateDisplayDays = () => {
    if (displayMode === DISPLAY_MODES.DAY) {
      return [new Date(selectedDate)]
    }
    
    // Week view - use utility function for consistent week generation
    return generateWeekDays(selectedDate, config.weekStartDay)
  }

  const weekDays = generateDisplayDays()

  // Get appointments for a specific time slot
  // ✅ FIXED: Use slot-based matching instead of exact minute matching to handle resolution changes
  const getAppointmentsForSlot = (date, hour, minute) => {
    const result = filteredAppointments.filter(apt => {
      const aptDate = new Date(apt.start)
      const aptHour = aptDate.getHours()
      const aptMinute = aptDate.getMinutes()
      
      // Check if appointment is on the same date and hour
      if (aptDate.toDateString() !== date.toDateString() || aptHour !== hour) {
        return false
      }
      
      // Calculate which time slot the appointment belongs to based on current resolution
      const appointmentSlotMinute = Math.floor(aptMinute / config.timeResolution) * config.timeResolution
      
      // Check if this appointment belongs to the current time slot
      const matches = appointmentSlotMinute === minute
      
      // Debug logging for slot-based matching (development only)
      if (process.env.NODE_ENV === 'development' && matches) {
        console.log('📅 Found appointment for slot (slot-based matching):', {
          appointment: apt.title || apt.clientName,
          appointmentTime: `${aptHour}:${aptMinute.toString().padStart(2, '0')}`,
          slotTime: `${hour}:${minute.toString().padStart(2, '0')}`,
          resolution: config.timeResolution,
          calculatedSlotMinute: appointmentSlotMinute,
          employeeId: apt.employeeId
        })
      }
      
      return matches
    })
    
    return result
  }

  // ✅ isWorkingHourSlot is now passed as a prop from parent to fix state sync issue

  // Enhanced time slot occupancy detection
  const isTimeSlotOccupied = (date, hour, minute, employeeId = null) => {
    const slotAppointments = getAppointmentsForSlot(date, hour, minute)
    
    if (employeeId) {
      // Check for specific employee
      return slotAppointments.some(apt => apt.employeeId === employeeId)
    }
    
    // Check for any appointments in multi-employee mode
    return slotAppointments.length > 0
  }

  // Get conflicting appointments for a time slot
  const getConflictingAppointments = (date, hour, minute, employeeId = null) => {
    const slotAppointments = getAppointmentsForSlot(date, hour, minute)
    
    if (employeeId) {
      return slotAppointments.filter(apt => apt.employeeId === employeeId)
    }
    
    return slotAppointments
  }

  // Enhanced event handlers with occupancy detection
  const handleSlotClick = (date, hour, minute, event) => {
    if (!config.clickToCreate) return
    
    // Prevent slot clicks during drag operations
    if (isDragging) {
      return
    }
    
    const clickTime = new Date(date)
    clickTime.setHours(hour, minute, 0, 0)
    
    // Use target employee from enhanced event (multi-employee mode) or fallback to current employee
    const targetEmployee = event.targetEmployee || currentEmployee
    
    // ✅ ADD WORKING HOURS VALIDATION - Prevent modal from opening if outside working hours
    if (targetEmployee && config.showWorkingHours) {
      const isWorkingSlot = isWorkingHourSlot(date, hour, minute, targetEmployee.id)
      if (!isWorkingSlot) {
        console.log('❌ Clicked on non-working hours, preventing modal from opening')
        
        // Show a brief feedback message to the user
        const workingHours = getWorkingHoursForEmployeeSync(targetEmployee.id, date)
        let errorMessage = `${targetEmployee.name} is not available at this time.`
        
        if (workingHours && workingHours.isWorking) {
          errorMessage += ` Working hours: ${workingHours.start} - ${workingHours.end}`
        } else {
          errorMessage += ' Not working on this day.'
        }
        
        // You could show a toast notification here
        console.log('⚠️', errorMessage)
        
        // Early return - don't open the action sheet
        return
      }
    }
    
    // Check if time slot is occupied
    const occupied = isTimeSlotOccupied(date, hour, minute, targetEmployee?.id)
    const conflictingAppointments = getConflictingAppointments(date, hour, minute, targetEmployee?.id)
    
    // Log multi-employee tap detection for debugging (development only)
    if (process.env.NODE_ENV === 'development' && displayMode === DISPLAY_MODES.DAY && selectedEmployees.length > 1) {
      console.log('🎯 Multi-employee tap detected:', {
        targetEmployee: targetEmployee?.name,
        totalEmployees: selectedEmployees.length,
        occupied,
        conflicts: conflictingAppointments.length
      })
    }
    
    // Enhanced action sheet with occupancy information
    showTimeSlotActionSheet(clickTime, targetEmployee, event, {
      timeSlotOccupied: occupied,
      conflictingAppointments
    })
  }

  const handleAppointmentClick = (appointment, event) => {
    event.stopPropagation()
    
    if (onAppointmentClick) {
      onAppointmentClick(appointment, event)
    } else {
      showAppointmentActionSheet(appointment, event)
    }
  }

  // Date header click handler - zoom to day view
  const handleDateHeaderClick = (date) => {
    // Switch to day view and set the selected date
    setViewMode(DISPLAY_MODES.DAY)
    selectDate(date)
    
    // Provide user feedback
    console.log('📅 Date header clicked, switching to day view:', date.toLocaleDateString())
  }

  // Appointment drag handlers
  const handleAppointmentDragMove = (appointment, newTime, dragInfo) => {
    handleDragMove(appointment, newTime, dragInfo)
  }

  const handleAppointmentDragEnd = (appointment, newTime, dragInfo) => {
    console.log('🎯 Calendar handleAppointmentDragEnd called:', {
      appointmentId: appointment.id,
      newTime,
      dragInfo
    })
    handleDragEnd(appointment, newTime, dragInfo)
  }

  const handleConfirmMove = async (notifyCustomer) => {
    try {
      const updatedAppointment = await confirmMove(notifyCustomer)
      if (updatedAppointment) {
        // Refresh appointments data or trigger parent update
        console.log('✅ Appointment moved successfully:', updatedAppointment)
        // Optionally trigger a data refresh here
        refreshAppointments(updatedAppointment)
      }
    } catch (error) {
      console.error('❌ Failed to move appointment:', error)
    }
  }

  // Appointment resize handlers
  const handleAppointmentResizeMove = (appointment, newEndTime, resizeInfo) => {
    // Enhanced resize info with working hours configuration
    const enhancedResizeInfo = {
      ...resizeInfo,
      displayHourStart: config.displayHourStart,
      displayHourEnd: config.displayHourEnd,
      showWorkingHours: config.showWorkingHours,
      timeResolution: config.timeResolution
    }
    
    handleResizeMove(appointment, newEndTime, enhancedResizeInfo)
  }

  const handleAppointmentResizeEnd = (appointment, newEndTime, resizeInfo) => {
    console.log('🎯 Calendar handleAppointmentResizeEnd called:', {
      appointmentId: appointment.id,
      newEndTime,
      resizeInfo
    })
    handleResizeEnd(appointment, newEndTime, resizeInfo)
  }

  const handleConfirmResize = async (notifyCustomer) => {
    try {
      const updatedAppointment = await confirmResize(notifyCustomer)
      if (updatedAppointment) {
        // Refresh appointments data or trigger parent update
        refreshAppointments(updatedAppointment)
      }
    } catch (error) {
      console.error('❌ Failed to resize appointment:', error)
    }
  }

  // Navigation handlers
  const handlePreviousPeriod = () => {
    const newDate = new Date(selectedDate)
    const daysToMove = displayMode === DISPLAY_MODES.DAY ? 1 : 7
    newDate.setDate(newDate.getDate() - daysToMove)
    selectDate(newDate)
  }

  const handleNextPeriod = () => {
    const newDate = new Date(selectedDate)
    const daysToMove = displayMode === DISPLAY_MODES.DAY ? 1 : 7
    newDate.setDate(newDate.getDate() + daysToMove)
    selectDate(newDate)
  }

  const handleToday = () => {
    selectDate(new Date())
  }

  // Enhanced action sheet handlers
  const handleNewAppointment = () => {
    if (actionSheet.date && actionSheet.employee) {
      // Use the appointment creation hook instead of direct callback
      appointmentCreation.startCreation({
        date: actionSheet.date,
        employee: actionSheet.employee
      })
      hideActionSheet()
    } else {
      // Fallback to original callback for compatibility
      onAppointmentCreate?.(actionSheet.date, actionSheet.employee)
    }
  }

  const handleAddToWaitlist = () => {
    // Check if waitlist addition is enabled
    if (!allowCustomerWaitlistAddition) {
      console.log('⚠️ Waitlist addition is disabled in settings')
      hideActionSheet()
      return
    }
    
    console.log('📋 Add to waitlist for:', actionSheet.date, actionSheet.employee)
    waitlistHook.startWaitlistCreation({ 
      date: actionSheet.date, 
      employee: actionSheet.employee 
    })
    hideActionSheet()
  }

  const handlePersonalTask = () => {
    console.log('📅 Create personal task for:', actionSheet.date, actionSheet.employee)
    // TODO: Implement personal task creation
    hideActionSheet()
  }

  // Handle appointment creation through the modal
  const handleCreateAppointmentFromModal = useCallback(async (formData) => {
    try {
      console.log('🆕 Creating appointment from modal with form data:', formData)
      
      // Transform form data to appointment data format first
      const appointmentData = {
        customerId: formData.selectedCustomer?.id,
        employeeId: formData.selectedEmployee?.id,
        selectedService: formData.selectedService,
        selectedAddOns: formData.selectedAddOns || [],
        selectedDate: formData.selectedDate,
        startTime: formData.selectedDate?.toISOString(),
        notes: formData.notes || '',
        status: 'requested', // ✅ Fixed: Use 'requested' instead of 'pending'
        source: 'admin',
        notifyCustomer: formData.notifyCustomer,
        depositPercentage: formData.depositPercentage
      }
      
      console.log('📋 Transformed appointment data:', appointmentData)

      // Now validate the transformed data
      const validation = appointmentService.validateAppointmentData(appointmentData)
      if (!validation.isValid) {
        console.error('❌ Form validation failed:', validation.errors)
        throw new Error(`Validation failed: ${validation.errors.join(', ')}`)
      }

      // Create the appointment using the service
      const newAppointment = await createAppointment(appointmentData)
      
      console.log('✅ Appointment created successfully:', newAppointment)
      return newAppointment
    } catch (error) {
      console.error('❌ Failed to create appointment:', error)
      
      // Show user-friendly error message
      const errorMessage = error.message || 'Failed to create appointment'
      if (errorMessage.includes('Customer selection is required')) {
        throw new Error('Please select a customer before creating the appointment')
      } else if (errorMessage.includes('Employee assignment is required')) {
        throw new Error('Employee assignment is required')
      } else if (errorMessage.includes('Service selection is required')) {
        throw new Error('Please select a service for the appointment')
      } else {
        throw new Error(errorMessage)
      }
    }
  }, [createAppointment, onAppointmentCreate, appointmentService])

  const handleEditAppointment = () => {
    if (actionSheet.appointment) {
      onAppointmentEdit?.(actionSheet.appointment)
    }
  }

    const handleCancelAppointment = async () => {
    if (actionSheet.appointment) {
      try {
        // 🔧 STATUS UPDATE: Update status to cancelled instead of hard delete
        await updateAppointment(actionSheet.appointment.id, { 
          status: 'cancelled',
          cancellationReason: 'Appointment cancelled by staff'
        })
        onAppointmentCancel?.(actionSheet.appointment)
      } catch (error) {
        console.error('Failed to cancel appointment:', error)
      }
    }
  }

  const handleMarkNoShow = async () => {
    if (actionSheet.appointment) {
      try {
        const updatedAppointment = await updateAppointment(actionSheet.appointment.id, { 
          status: 'no-show' 
        })
        onAppointmentStatusChange?.(updatedAppointment)
      } catch (error) {
        console.error('Failed to mark no show:', error)
      }
    }
  }

  const handleMarkCompleted = async () => {
    if (actionSheet.appointment) {
      try {
        const updatedAppointment = await updateAppointment(actionSheet.appointment.id, { 
          status: 'completed' 
        })
        onAppointmentStatusChange?.(updatedAppointment)
      } catch (error) {
        console.error('Failed to mark completed:', error)
      }
    }
  }

  // Hover overlay action handlers
  const handleAppointmentStatusUpdate = async (appointmentId, newStatus, reason = null) => {
    try {
      // Use the passed-in onAppointmentStatusChange function instead of updateAppointment
      await onAppointmentStatusChange(appointmentId, newStatus, reason)
    } catch (error) {
      console.error('Failed to update appointment status:', error)
    }
  }

  const handleAppointmentEdit = async (appointment) => {
    console.log('🔍 handleAppointmentEdit called with appointment:', {
      id: appointment.id,
      employeeName: appointment.employeeName,
      clientName: appointment.clientName,
      needsNameLookup: appointment.needsNameLookup
    })
    
    // If the appointment needs employee name enhancement, do it now
    if (appointment.employeeId && 
        (appointment.employeeName?.startsWith('Employee ') || !appointment.employeeName)) {
      console.log('🔄 Enhancing employee name for edit modal...')
      try {
        const { customerLookupService } = await import('../../services/customerLookupService')
        const actualEmployeeName = await customerLookupService.getEmployeeName(appointment.employeeId)
        
        if (actualEmployeeName && !actualEmployeeName.startsWith('Employee ')) {
          const enhancedAppointment = {
            ...appointment,
            employeeName: actualEmployeeName,
            people: [actualEmployeeName]
          }
          console.log('✅ Enhanced appointment with employee name:', actualEmployeeName)
          onAppointmentEdit?.(enhancedAppointment)
          return
        }
      } catch (error) {
        console.error('❌ Failed to enhance employee name for edit modal:', error)
      }
    }
    
    onAppointmentEdit?.(appointment)
  }

  const handleAppointmentMove = (appointment) => {
    // Open the comprehensive move modal
    setComprehensiveMoveModal({
      isOpen: true,
      appointment: appointment,
      isUpdating: false
    })
  }



  const handleComprehensiveMoveConfirm = async (moveData) => {
    const { newDateTime, selectedEmployees, selectedService, selectedTimeSlot, targetEmployee } = moveData
    
    setComprehensiveMoveModal(prev => ({ ...prev, isUpdating: true }))
    
    try {
      const appointment = comprehensiveMoveModal.appointment
      
      // Calculate new end time maintaining duration
      const originalStart = new Date(appointment.start || appointment.startTime)
      const originalEnd = new Date(appointment.end || appointment.endTime)
      const duration = originalEnd - originalStart
      const newEndTime = new Date(newDateTime.getTime() + duration)
      
      // 🔧 CRITICAL FIX: Use targetEmployee instead of first selected employee
      const targetEmployeeId = targetEmployee?.id || selectedEmployees[0]?.id
      
      console.log('🎯 Moving appointment to target employee:', {
        targetEmployeeId,
        targetEmployeeName: targetEmployee?.full_name || targetEmployee?.name,
        newDateTime: newDateTime.toISOString(),
        newEndTime: newEndTime.toISOString()
      })
      
      // Update appointment - use the move-specific format expected by appointmentService
      const updatedAppointment = await updateAppointment(appointment.id, {
        newDateTime: newDateTime, // 🔧 FIX: Use newDateTime for move logic
        selectedEmployees: targetEmployee ? [targetEmployee] : selectedEmployees, // 🔧 FIX: Use selectedEmployees format
        serviceId: selectedService?.id,
        notifyCustomer: true
      })
      
      console.log('✅ Comprehensive move completed:', updatedAppointment)
      
      // Close modal
      setComprehensiveMoveModal({
        isOpen: false,
        appointment: null,
        isUpdating: false
      })
      
      // Refresh appointments
      if (refreshAppointments) {
        refreshAppointments()
      }
      
    } catch (error) {
      console.error('❌ Failed to move appointment:', error)
      setComprehensiveMoveModal(prev => ({ ...prev, isUpdating: false }))
    }
  }

  const handleComprehensiveMoveCancel = () => {
    setComprehensiveMoveModal({
      isOpen: false,
      appointment: null,
      isUpdating: false
    })
  }



  return (
    <div className={`h-full bg-white flex flex-col relative ${className}`}>
      {/* Header */}
      <CalendarHeader
        selectedDate={selectedDate}
        displayMode={displayMode}
        onPreviousPeriod={handlePreviousPeriod}
        onNextPeriod={handleNextPeriod}
        onToday={handleToday}
        onViewModeChange={setViewMode}
        onSettingsClick={() => setIsSettingsOpen(true)}
        currentEmployee={currentEmployee}
        config={config}
      />

      {/* Drag Shadow Overlay - Dims background when dragging */}
      {isDragging && (
        <div 
          className="absolute inset-0 top-16 z-[30] pointer-events-none transition-opacity duration-200"
          style={{
            background: 'linear-gradient(135deg, rgba(0, 0, 0, 0.08), rgba(0, 0, 0, 0.04))',
            backdropFilter: 'blur(1px) brightness(0.92) saturate(0.9)',
            boxShadow: 'inset 0 0 100px rgba(0, 0, 0, 0.1)',
          }}
        />
      )}

      {/* Drag Line Indicator - Shows where appointment will be dropped */}
      <DragLineIndicator
        isDragging={isDragging}
        draggedAppointment={draggedAppointment}
        newTime={newTime}
        config={config}
        timeGridRef={timeGridRef}
        displayMode={displayMode}
        isEdgeCaseMode={isEdgeCaseMode}
        edgeCaseInfo={edgeCaseInfo}
      />

      {/* Calendar Content */}
      {displayMode === DISPLAY_MODES.WEEK ? (
        <WeekView
          key={`week-${config.timeResolution}-${config.gridHeight}`}
          weekDays={weekDays}
          timeSlots={timeSlots}
          config={config}
          currentEmployee={currentEmployee}
          getAppointmentsForSlot={getAppointmentsForSlot}
          isWorkingHourSlot={isWorkingHourSlot}
          getWorkingHoursForEmployee={getWorkingHoursForEmployeeSync}
          onSlotClick={handleSlotClick}
          onAppointmentClick={handleAppointmentClick}
          onAppointmentDragStart={handleDragStart}
          onAppointmentDragMove={handleAppointmentDragMove}
          onAppointmentDragEnd={handleAppointmentDragEnd}
          onAppointmentResizeStart={handleResizeStart}
          onAppointmentResizeMove={handleAppointmentResizeMove}
          onAppointmentResizeEnd={handleAppointmentResizeEnd}
          onAppointmentStatusUpdate={handleAppointmentStatusUpdate}
          onAppointmentEdit={handleAppointmentEdit}
          onAppointmentMove={handleAppointmentMove}
          onDateHeaderClick={handleDateHeaderClick}
          timeGridRef={timeGridRef}
          scrollContainerRef={timeGridRef}
          onScroll={handleScroll}
          allAppointments={filteredAppointments}
          isDragging={isDragging}
          isShowingConfirmation={showConfirmation}
          draggedAppointmentId={draggedAppointment?.id}
          globalDragOffset={finalDragOffset}
          isResizing={isResizing}
          isShowingResizeConfirmation={showResizeConfirmation}
          resizedAppointmentId={resizedAppointment?.id}
          globalResizeOffset={finalResizeOffset}
          isUpdatingStatus={isUpdating || isResizeUpdating}
        />
      ) : (
        <DayView
          key={`day-${config.timeResolution}-${config.gridHeight}`}
          selectedDate={selectedDate}
          timeSlots={timeSlots}
          config={config}
          selectedEmployees={selectedEmployees}
          getAppointmentsForSlot={getAppointmentsForSlot}
          isWorkingHourSlot={isWorkingHourSlot}
          getWorkingHoursForEmployee={getWorkingHoursForEmployeeSync}
          onSlotClick={handleSlotClick}
          onAppointmentClick={handleAppointmentClick}
          onAppointmentDragStart={handleDragStart}
          onAppointmentDragMove={handleAppointmentDragMove}
          onAppointmentDragEnd={handleAppointmentDragEnd}
          onAppointmentResizeStart={handleResizeStart}
          onAppointmentResizeMove={handleAppointmentResizeMove}
          onAppointmentResizeEnd={handleAppointmentResizeEnd}
          onAppointmentStatusUpdate={handleAppointmentStatusUpdate}
          onAppointmentEdit={handleAppointmentEdit}
          onAppointmentMove={handleAppointmentMove}
          timeGridRef={timeGridRef}
          scrollContainerRef={timeGridRef}
          onScroll={handleScroll}
          allAppointments={filteredAppointments}
          isDragging={isDragging}
          isShowingConfirmation={showConfirmation}
          draggedAppointmentId={draggedAppointment?.id}
          globalDragOffset={finalDragOffset}
          isResizing={isResizing}
          isShowingResizeConfirmation={showResizeConfirmation}
          resizedAppointmentId={resizedAppointment?.id}
          globalResizeOffset={finalResizeOffset}
          isUpdatingStatus={isUpdating || isResizeUpdating}
        />
      )}
      
      {/* Enhanced Action Sheet */}
      <CalendarActionSheet
        isOpen={actionSheet.isOpen}
        onClose={hideActionSheet}
        date={actionSheet.date}
        appointment={actionSheet.appointment}
        employee={actionSheet.employee}
        position={actionSheet.position}
        timeSlotOccupied={actionSheet.timeSlotOccupied}
        conflictingAppointments={actionSheet.conflictingAppointments}
        onNewAppointment={handleNewAppointment}
        onAddToWaitlist={handleAddToWaitlist}
        onPersonalTask={handlePersonalTask}
        allowCustomerWaitlistAddition={allowCustomerWaitlistAddition}
        onEditWorkingHours={() => {
          setIsSettingsOpen(true)
          hideActionSheet()
        }}
        onEditAppointment={handleEditAppointment}
        onCancelAppointment={handleCancelAppointment}
        onMarkNoShow={handleMarkNoShow}
        onMarkCompleted={handleMarkCompleted}
      />
      
      {/* Settings Panel */}
      <CalendarSettingsPanel
        isOpen={isSettingsOpen}
        onClose={() => setIsSettingsOpen(false)}
        config={config}
        updateConfig={updateConfig}
        selectedEmployees={selectedEmployees}
      />

      {/* Appointment Move Confirmation (Drag & Drop) */}
      <AppointmentMoveConfirmation
        isOpen={showConfirmation}
        onClose={cancelMove}
        appointment={draggedAppointment}
        originalTime={originalTime}
        newTime={newTime}
        onConfirm={handleConfirmMove}
        movementType={movementType}
        targetEmployee={targetEmployee}
        targetColumn={targetColumn}
      />



      {/* Appointment Resize Confirmation */}
              <AppointmentUpdateSlotResizeConfirmation
        isOpen={showResizeConfirmation}
        onClose={cancelResize}
        appointment={resizedAppointment}
        originalDuration={originalDuration}
        newDuration={newDuration}
        originalEndTime={originalEndTime}
        newEndTime={newResizeEndTime}
        onConfirm={handleConfirmResize}
        resizeDirection={resizeDirection}
        error={resizeError}
        isUpdating={isResizeUpdating}
      />

      {/* Appointment Creation Modal */}
      <AppointmentCreationModal
        appointmentCreationHook={appointmentCreation}
        onCreateAppointment={handleCreateAppointmentFromModal}
        onCancel={() => {
          console.log('Appointment creation cancelled')
        }}
      />

      {/* Waitlist Modal */}
      <WaitlistModal
        waitlistHook={waitlistHook}
        allowCustomerWaitlistAddition={allowCustomerWaitlistAddition}
        onSuccess={(waitlistEntry) => {
          console.log('✅ Waitlist entry created successfully:', waitlistEntry)
          // You can add additional success handling here
        }}
        onCancel={() => {
          console.log('Waitlist creation cancelled')
        }}
      />

      {/* Comprehensive Appointment Move Modal */}
      <AppointmentMoveModal
        isOpen={comprehensiveMoveModal.isOpen}
        onClose={handleComprehensiveMoveCancel}
        appointment={comprehensiveMoveModal.appointment}
        onConfirm={handleComprehensiveMoveConfirm}
        employees={selectedEmployees || []}
        isUpdating={comprehensiveMoveModal.isUpdating}
      />
    </div>
  )
}

export default Calendar 