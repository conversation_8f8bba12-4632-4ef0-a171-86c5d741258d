import React from 'react'
import { ChevronLeftIcon, ChevronRightIcon, CogIcon } from '@heroicons/react/24/outline'
import { DISPLAY_MODES } from '../../hooks/useAdvancedCalendar'
import { formatWeekRange, generateWeekDays } from '../../utils/weekUtils'

/**
 * CalendarHeader - Extracted header component with navigation and view controls
 */
const CalendarHeader = ({
  selectedDate,
  displayMode,
  onPreviousPeriod,
  onNextPeriod,
  onToday,
  onViewModeChange,
  onSettingsClick,
  currentEmployee = null,
  config = { weekStartDay: 1 } // Default config for week start day
}) => {
  const isEmployeeWorking = (employee, date) => {
    // Simplified working check - can be enhanced with actual working hours logic
    return true
  }

  const formatHeaderTitle = () => {
    if (displayMode === DISPLAY_MODES.DAY) {
      return selectedDate.toLocaleDateString('en-US', { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      })
    }
    
    if (displayMode === DISPLAY_MODES.WEEK) {
      const weekDays = generateWeekDays(selectedDate, config.weekStartDay)
      return formatWeekRange(weekDays)
    }
    
    return selectedDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })
  }

  return (
    <>
      {/* Main Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          {/* Navigation Controls */}
          <div className="flex items-center space-x-4">
            <button
              onClick={onPreviousPeriod}
              className="p-2 hover:bg-gray-100 rounded-lg"
              aria-label="Previous period"
            >
              <ChevronLeftIcon className="h-5 w-5" />
            </button>
            <button
              onClick={onNextPeriod}
              className="p-2 hover:bg-gray-100 rounded-lg"
              aria-label="Next period"
            >
              <ChevronRightIcon className="h-5 w-5" />
            </button>
            <button
              onClick={onToday}
              className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200"
            >
              Today
            </button>
          </div>
          
          {/* Title */}
          <h2 className="text-lg font-semibold">
            {formatHeaderTitle()}
          </h2>
          
          {/* View Controls */}
          <div className="flex items-center space-x-2">
            <button
              onClick={() => onViewModeChange(DISPLAY_MODES.DAY)}
              className={`px-3 py-1 text-sm rounded ${
                displayMode === DISPLAY_MODES.DAY ? 'bg-blue-100 text-blue-700' : 'bg-gray-100'
              }`}
            >
              Day
            </button>
            <button
              onClick={() => onViewModeChange(DISPLAY_MODES.WEEK)}
              className={`px-3 py-1 text-sm rounded ${
                displayMode === DISPLAY_MODES.WEEK ? 'bg-blue-100 text-blue-700' : 'bg-gray-100'
              }`}
            >
              Week
            </button>
            <button
              onClick={onSettingsClick}
              className="p-2 hover:bg-gray-100 rounded-lg"
              title="Calendar Settings"
            >
              <CogIcon className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>

      {/* Employee Profile Display (for single selection in week view) */}
      {currentEmployee && displayMode === DISPLAY_MODES.WEEK && (
        <div className="bg-gray-50 border-b border-gray-200 px-4 py-2">
          <div className="flex items-center justify-center gap-3">
            <div 
              className="w-8 h-8 rounded-full flex items-center justify-center text-white text-xs font-semibold"
              style={{ backgroundColor: currentEmployee.color }}
            >
              {currentEmployee.avatar}
            </div>
            <span className="font-medium text-gray-900">{currentEmployee.name}</span>
            <span className="text-xs text-gray-500">
              {isEmployeeWorking(currentEmployee, selectedDate) ? 'Working Today' : 'Not Working Today'}
            </span>
          </div>
        </div>
      )}
    </>
  )
}

export default CalendarHeader 