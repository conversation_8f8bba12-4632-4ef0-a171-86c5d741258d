import React from 'react'

/**
 * ToggleSettings - Reusable component for boolean toggle settings
 */
const ToggleSettings = ({ settings, config, updateConfig }) => {
  return (
    <div className="space-y-4">
      {settings.map(option => (
        <div key={option.key} className="flex items-start justify-between p-3 bg-gray-50 rounded-lg">
          <div className="flex-1">
            <div className="font-medium text-gray-900">{option.label}</div>
            <div className="text-sm text-gray-600">{option.description}</div>
          </div>
          <label className="relative inline-flex items-center cursor-pointer ml-3">
            <input
              type="checkbox"
              checked={config[option.key]}
              onChange={(e) => updateConfig({ [option.key]: e.target.checked })}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
          </label>
        </div>
      ))}
    </div>
  )
}

export default ToggleSettings 