import React, { useState, useEffect } from 'react'
import { 
  XMarkIcon, 
  CalendarDaysIcon,
  ClockIcon,
  UserIcon,
  CheckIcon,
  MagnifyingGlassIcon,
  ChevronDownIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline'
import { servicesApiService } from '../../services/servicesApiService'
import { employeeApiService } from '../../../employees/services/employeeApiService'
import { appointmentService } from '../../services/appointmentService'

/**
 * AppointmentMoveModal - Comprehensive appointment move modal
 * Allows selection of new date, time, service, and employees
 */
const AppointmentMoveModal = ({
  isOpen,
  onClose,
  appointment,
  onConfirm,
  employees = [],
  isUpdating = false,
  currentUserId = null // Add this prop to identify current user
}) => {
  const [selectedDate, setSelectedDate] = useState('')
  const [selectedService, setSelectedService] = useState(null)
  const [selectedEmployees, setSelectedEmployees] = useState([])
  const [availableTimeSlots, setAvailableTimeSlots] = useState([])
  const [selectedTimeSlot, setSelectedTimeSlot] = useState(null)
  const [services, setServices] = useState([])
  const [categories, setCategories] = useState([])
  const [servicesByCategory, setServicesByCategory] = useState({})
  const [allEmployees, setAllEmployees] = useState([])
  const [employeeSearch, setEmployeeSearch] = useState('')
  const [isLoadingServices, setIsLoadingServices] = useState(false)
  const [isLoadingEmployees, setIsLoadingEmployees] = useState(false)
  const [isLoadingTimeSlots, setIsLoadingTimeSlots] = useState(false)
  const [error, setError] = useState('')
  const [isServicesCollapsed, setIsServicesCollapsed] = useState(false)
  const [isEmployeesCollapsed, setIsEmployeesCollapsed] = useState(false)

  // Initialize form with current appointment data
  useEffect(() => {
    if (isOpen && appointment) {
      const currentDate = new Date(appointment.start || appointment.startTime)
      setSelectedDate(currentDate.toISOString().split('T')[0])
      
      // Pre-select current service if available
      if (appointment.serviceId) {
        const currentService = {
          id: appointment.serviceId,
          name: appointment.serviceName || 'Current Service',
          duration: appointment.serviceDuration || appointment.duration || 60,
          price: appointment.price || 0
        }
        setSelectedService(currentService)
      }
      
      // Reset selected employees - will be set in loadEmployees
      setSelectedEmployees([])
      
      // Load initial data
      loadServices()
      loadEmployees()
    }
  }, [isOpen, appointment])

  // Load available time slots when dependencies change
  useEffect(() => {
    if (selectedDate && selectedService && selectedEmployees.length > 0) {
      loadAvailableTimeSlots()
    } else {
      setAvailableTimeSlots([])
      setSelectedTimeSlot(null)
    }
  }, [selectedDate, selectedService, selectedEmployees])

  const loadServices = async () => {
    setIsLoadingServices(true)
    setError('')
    try {
      // Load both services and categories
      const [servicesData, categoriesData] = await Promise.all([
        servicesApiService.fetchServices(),
        servicesApiService.fetchServiceCategories()
      ])
      
      setServices(servicesData)
      setCategories(categoriesData)
      
      // Group services by category
      const grouped = {}
      
      // Initialize all categories with empty arrays
      categoriesData.forEach(category => {
        grouped[category.id] = {
          category: category,
          services: []
        }
      })
      
      // Add services to their respective categories
      servicesData.forEach(service => {
        const categoryId = service.category || 'uncategorized'
        if (!grouped[categoryId]) {
          grouped[categoryId] = {
            category: { id: categoryId, name: 'Uncategorized' },
            services: []
          }
        }
        grouped[categoryId].services.push(service)
      })
      
      // Remove empty categories
      Object.keys(grouped).forEach(categoryId => {
        if (grouped[categoryId].services.length === 0) {
          delete grouped[categoryId]
        }
      })
      
      setServicesByCategory(grouped)
      
      console.log('✅ Loaded services and categories:', {
        services: servicesData.length,
        categories: categoriesData.length,
        grouped: Object.keys(grouped).length
      })
    } catch (error) {
      console.error('❌ Failed to load services:', error)
      setError('Failed to load services')
    } finally {
      setIsLoadingServices(false)
    }
  }

  const loadEmployees = async () => {
    setIsLoadingEmployees(true)
    setError('')
    try {
      const employeesData = await employeeApiService.getAllEmployees()
      setAllEmployees(employeesData)
      console.log('✅ Loaded employees:', employeesData.length)
      console.log('🔍 Employee ID mapping:', employeesData.map(emp => ({ 
        id: emp.id, 
        name: emp.full_name || emp.name,
        idType: typeof emp.id 
      })))
      
      // Pre-select the current logged-in user (Serena Zhou) by default
      const currentUser = employeesData.find(emp => 
        emp.full_name === 'Serena Zhou' || 
        emp.name === 'Serena Zhou' ||
        (currentUserId && emp.id === currentUserId)
      )
      
      if (currentUser) {
        setSelectedEmployees([currentUser])
        console.log('✅ Pre-selected current user:', currentUser)
      } else {
        console.log('❌ Current user not found in employees list')
      }
    } catch (error) {
      console.error('❌ Failed to load employees:', error)
      setError('Failed to load employees')
    } finally {
      setIsLoadingEmployees(false)
    }
  }

  const loadAvailableTimeSlots = async () => {
    if (!selectedDate || !selectedService || selectedEmployees.length === 0) return
    
    setIsLoadingTimeSlots(true)
    setError('')
    try {
      console.log('🔄 Loading slots for selected employees:', selectedEmployees.map(emp => ({id: emp.id, name: emp.full_name || emp.name})))
      console.log('📝 Selected service:', selectedService.id)
      console.log('📅 Selected date:', selectedDate)
      
      let allSlots = []
      
      if (selectedEmployees.length === 1) {
        // Single employee selected - fetch slots for specific employee
        const employeeId = selectedEmployees[0].id
        console.log('🎯 Fetching slots for single employee:', employeeId)
        
        const slots = await appointmentService.getAvailableSlots(
          employeeId,
          selectedDate,
          selectedService.id,
          1 // Business ID - hardcoded to 1 for now, consistent with other parts of the app
        )
        
        allSlots = slots || []
      } else {
        // Multiple employees selected - fetch slots for all employees then filter
        console.log('👥 Fetching slots for multiple employees - getting all available slots first')
        
        // Call API without employee_id to get all available slots
        const allAvailableSlots = await appointmentService.getAvailableSlots(
          null, // No specific employee - get all
          selectedDate,
          selectedService.id,
          1 // Business ID - hardcoded to 1 for now, consistent with other parts of the app
        )
        
        console.log('✅ Received all available slots:', allAvailableSlots)
        
        // Filter slots to only include selected employees
        const selectedEmployeeNames = selectedEmployees.map(emp => emp.full_name || emp.name)
        console.log('🔍 Filtering for employee names:', selectedEmployeeNames)
        
        if (Array.isArray(allAvailableSlots)) {
          // If slots are returned as an array, filter by employee_name
          allSlots = allAvailableSlots.filter(slot => 
            selectedEmployeeNames.includes(slot.employee_name)
          )
        } else {
          // If API returns object format with employee names as keys, this will be handled by the service transformation
          allSlots = allAvailableSlots || []
        }
      }
      
      console.log('✅ Final filtered slots:', allSlots)
      console.log('📋 Sample slot structure:', allSlots?.[0])
      console.log('👥 Available employees for mapping:', allEmployees.map(emp => ({id: emp.id, name: emp.full_name || emp.name})))
      setAvailableTimeSlots(allSlots)
      
      // Clear error if slots are successfully loaded
      if (allSlots && allSlots.length > 0) {
        setError('')
      } else {
        setError('No available time slots found for the selected employees, date and service. Please try a different date or service.')
      }
    } catch (error) {
      console.error('❌ Failed to load available time slots:', error)
      setError('Failed to load available time slots. Please try again.')
      setAvailableTimeSlots([])
    } finally {
      setIsLoadingTimeSlots(false)
    }
  }

  const handleEmployeeToggle = (employee) => {
    setSelectedEmployees(prev => {
      const isSelected = prev.some(emp => emp.id === employee.id)
      if (isSelected) {
        return prev.filter(emp => emp.id !== employee.id)
      } else {
        return [...prev, employee]
      }
    })
  }

  const handleConfirm = () => {
    if (!selectedTimeSlot) {
      setError('Please select a time slot')
      return
    }

    // Use the raw datetime from the API instead of the formatted start_time
    // selectedTimeSlot.start_time is formatted like "4:15 PM" which is invalid for Date constructor
    // selectedTimeSlot.datetime or raw_time contains the original UTC time from API
    const rawDateTime = selectedTimeSlot.datetime || selectedTimeSlot.raw_time
    
    if (!rawDateTime) {
      setError('Invalid time slot data. Please try selecting a different time.')
      console.error('❌ No valid datetime found in selected time slot:', selectedTimeSlot)
      return
    }

    const newDateTime = new Date(rawDateTime)
    
    // Validate that we created a valid date
    if (isNaN(newDateTime.getTime())) {
      setError('Invalid time selected. Please try a different time slot.')
      console.error('❌ Failed to create valid date from:', rawDateTime)
      return
    }

    // 🔧 CRITICAL FIX: Find the target employee based on the selected time slot
    let targetEmployee = null
    
    // First, try to find employee by employee_id from the time slot
    if (selectedTimeSlot.employee_id) {
      targetEmployee = allEmployees.find(emp => emp.id === selectedTimeSlot.employee_id)
    }
    
    // If not found by ID, try to find by employee_name from the time slot
    if (!targetEmployee && selectedTimeSlot.employee_name) {
      targetEmployee = allEmployees.find(emp => 
        (emp.full_name || emp.name) === selectedTimeSlot.employee_name
      )
    }
    
    // If still not found, use the first selected employee as fallback
    if (!targetEmployee && selectedEmployees.length > 0) {
      targetEmployee = selectedEmployees[0]
      console.warn('⚠️ Could not determine target employee from time slot, using first selected employee:', targetEmployee)
    }
    
    if (!targetEmployee) {
      setError('Could not determine target employee for the selected time slot.')
      console.error('❌ No target employee found for time slot:', selectedTimeSlot)
      return
    }

    const moveData = {
      newDateTime: newDateTime,
      selectedEmployees,
      selectedService,
      selectedTimeSlot,
      targetEmployee // 🔧 ADD: Include the specific target employee
    }

    console.log('🔄 Moving appointment:', {
      originalDateTime: rawDateTime,
      newDateTime: newDateTime,
      formattedTime: newDateTime.toISOString(),
      localTime: newDateTime.toLocaleString(),
      selectedEmployees: selectedEmployees.map(emp => emp.full_name || emp.name),
      targetEmployee: targetEmployee.full_name || targetEmployee.name, // 🔧 ADD: Log target employee
      targetEmployeeId: targetEmployee.id, // 🔧 ADD: Log target employee ID
      selectedService: selectedService.name,
      timeSlotDetails: {
        employee_id: selectedTimeSlot.employee_id,
        employee_name: selectedTimeSlot.employee_name,
        start_time: selectedTimeSlot.start_time
      },
      allAvailableEmployees: allEmployees.map(emp => ({ id: emp.id, name: emp.full_name || emp.name }))
    })

    onConfirm(moveData)
  }

  const handleClose = () => {
    if (isUpdating) return
    
    // Reset form
    setSelectedDate('')
    setSelectedService(null)
    setSelectedEmployees([])
    setAvailableTimeSlots([])
    setSelectedTimeSlot(null)
    setEmployeeSearch('')
    setError('')
    setIsServicesCollapsed(false)
    setIsEmployeesCollapsed(false)
    
    onClose()
  }

  const filteredEmployees = allEmployees.filter(employee =>
    employee.full_name?.toLowerCase().includes(employeeSearch.toLowerCase()) ||
    employee.name?.toLowerCase().includes(employeeSearch.toLowerCase())
  )

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-6xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Move Appointment</h2>
            {appointment && (
              <p className="text-sm text-gray-600 mt-1">
                {appointment.clientName} - {appointment.serviceName || 'Service'}
              </p>
            )}
          </div>
          <button
            onClick={handleClose}
            disabled={isUpdating}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 disabled:opacity-50"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mx-6 mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        {/* Content */}
        <div className="flex-1 flex overflow-hidden">
          {/* Left Panel - Filters */}
          <div className="w-1/2 p-6 border-r border-gray-200 overflow-y-auto">
            <div className="space-y-6">
              {/* Date Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <CalendarDaysIcon className="h-4 w-4 inline mr-2" />
                  Select Date
                </label>
                <input
                  type="date"
                  value={selectedDate}
                  onChange={(e) => setSelectedDate(e.target.value)}
                  min={new Date().toISOString().split('T')[0]}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Service Selection */}
              <div>
                <button
                  onClick={() => setIsServicesCollapsed(!isServicesCollapsed)}
                  className="flex items-center justify-between w-full text-left text-sm font-medium text-gray-700 mb-2 hover:text-gray-900 transition-colors"
                >
                  <span>Service {selectedService && `(${selectedService.name})`}</span>
                  {isServicesCollapsed ? (
                    <ChevronRightIcon className="h-4 w-4" />
                  ) : (
                    <ChevronDownIcon className="h-4 w-4" />
                  )}
                </button>
                
                {!isServicesCollapsed && (
                  <div className="transition-all duration-200 ease-in-out">
                    {isLoadingServices ? (
                      <div className="text-sm text-gray-500 p-3">Loading services...</div>
                    ) : Object.keys(servicesByCategory).length > 0 ? (
                      <div className="space-y-4 max-h-60 overflow-y-auto">
                        {Object.values(servicesByCategory).map(({ category, services }) => (
                          <div key={category.id} className="space-y-2">
                            {/* Category Header */}
                            <div className="text-sm font-semibold text-gray-800 px-3 py-2 bg-gray-100 rounded-lg border-l-4 border-blue-500">
                              {category.name}
                              <span className="ml-2 text-xs text-gray-600">({services.length})</span>
                            </div>
                            
                            {/* Services in Category */}
                            <div className="space-y-2 ml-2">
                              {services.map((service) => (
                                <button
                                  key={service.id}
                                  onClick={() => setSelectedService(service)}
                                  className={`w-full p-3 text-left rounded-lg border transition-colors ${
                                    selectedService?.id === service.id
                                      ? 'bg-blue-50 border-blue-500 text-blue-700'
                                      : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
                                  }`}
                                >
                                  <div className="font-medium">{service.name}</div>
                                  <div className="text-sm text-gray-600">
                                    {service.duration} min • ${service.price}
                                  </div>
                                </button>
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-sm text-gray-500 p-3">No services available</div>
                    )}
                  </div>
                )}
              </div>

              {/* Employee Selection */}
              <div>
                <button
                  onClick={() => setIsEmployeesCollapsed(!isEmployeesCollapsed)}
                  className="flex items-center justify-between w-full text-left text-sm font-medium text-gray-700 mb-2 hover:text-gray-900 transition-colors"
                >
                  <span className="flex items-center">
                    <UserIcon className="h-4 w-4 mr-2" />
                    Select Employee(s) {selectedEmployees.length > 0 && `(${selectedEmployees.length} selected)`}
                  </span>
                  {isEmployeesCollapsed ? (
                    <ChevronRightIcon className="h-4 w-4" />
                  ) : (
                    <ChevronDownIcon className="h-4 w-4" />
                  )}
                </button>
                
                {!isEmployeesCollapsed && (
                  <div className="transition-all duration-200 ease-in-out">
                    {/* Dropdown Container */}
                    <div className="border border-gray-300 rounded-lg bg-white">
                      {/* Select All / Select None */}
                      <div className="flex items-center justify-between p-3 border-b border-gray-200 bg-gray-50">
                        <button
                          onClick={() => setSelectedEmployees(filteredEmployees)}
                          className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                        >
                          Select All
                        </button>
                        <button
                          onClick={() => setSelectedEmployees([])}
                          className="text-sm text-gray-600 hover:text-gray-800 font-medium"
                        >
                          Select None
                        </button>
                      </div>

                      {/* Search */}
                      <div className="p-3 border-b border-gray-200">
                        <input
                          type="text"
                          placeholder="Search employees..."
                          value={employeeSearch}
                          onChange={(e) => setEmployeeSearch(e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>

                      {/* Employee List */}
                      {isLoadingEmployees ? (
                        <div className="text-sm text-gray-500 p-3">Loading employees...</div>
                      ) : (
                        <div className="max-h-40 overflow-y-auto">
                          {filteredEmployees.map((employee) => {
                            const isSelected = selectedEmployees.some(emp => emp.id === employee.id)
                            const isCurrentAppointmentEmployee = appointment?.employeeId === employee.id
                            const isCurrentUser = currentUserId && employee.id === currentUserId
                            
                            // Debug logging
                            if (employee.full_name === 'Serena Zhou' || employee.name === 'Serena Zhou') {
                              console.log('Serena Zhou debug:', {
                                employee: employee,
                                selectedEmployees: selectedEmployees,
                                isSelected: isSelected,
                                employeeId: employee.id,
                                selectedIds: selectedEmployees.map(emp => emp.id)
                              })
                            }
                            
                            return (
                              <button
                                key={employee.id}
                                onClick={() => handleEmployeeToggle(employee)}
                                className={`w-full p-3 text-left hover:bg-gray-50 border-b border-gray-100 last:border-b-0 flex items-center justify-between transition-colors ${
                                  isSelected ? 'bg-blue-50' : ''
                                }`}
                              >
                                <div className="flex items-center space-x-2">
                                  <span className="text-sm text-gray-900">{employee.full_name || employee.name}</span>
                                  {isCurrentUser && (
                                    <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full font-medium">
                                      Current User
                                    </span>
                                  )}
                                  {isCurrentAppointmentEmployee && (
                                    <span className="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded-full font-medium">
                                      Assigned
                                    </span>
                                  )}
                                </div>
                                {isSelected && (
                                  <CheckIcon className="h-4 w-4 text-blue-600" />
                                )}
                              </button>
                            )
                          })}
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Right Panel - Available Time Slots */}
          <div className="w-1/2 p-6 overflow-y-auto">
            <div className="mb-4">
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                <ClockIcon className="h-5 w-5 inline mr-2" />
                Available Time Slots
              </h3>
              {selectedDate && selectedService && selectedEmployees.length > 0 ? (
                <p className="text-sm text-gray-600">
                  {selectedDate} • {selectedService.name} • {selectedEmployees.length} employee(s)
                </p>
              ) : (
                <p className="text-sm text-gray-500">
                  Please select date, service, and employee(s) to see available times
                </p>
              )}
            </div>

            {/* Time Slots */}
            {isLoadingTimeSlots ? (
              <div className="text-center py-8">
                <div className="text-sm text-gray-500">Loading available time slots...</div>
              </div>
            ) : availableTimeSlots.length > 0 ? (
              <div className="space-y-6">
                {(() => {
                  // Group time slots by employee
                  const slotsByEmployee = {}
                  
                  availableTimeSlots.forEach(slot => {
                    // Try to find the actual employee name from our loaded employees
                    let employee = null
                    let employeeName = 'Unknown Employee'
                    
                    // Method 1: Try to find by employee_id
                    if (slot.employee_id) {
                      employee = allEmployees.find(emp => emp.id === slot.employee_id)
                    }
                    
                    // Method 2: If no employee_id, try to match by name
                    if (!employee && slot.employee_name) {
                      employee = allEmployees.find(emp => 
                        emp.full_name === slot.employee_name || 
                        emp.name === slot.employee_name
                      )
                    }
                    
                    // Method 3: Fall back to selected employee (since we're getting slots for selected employee)
                    if (!employee && selectedEmployees.length > 0) {
                      employee = selectedEmployees[0]
                    }
                    
                    // Get the final employee name and info
                    if (employee) {
                      employeeName = employee.full_name || employee.name
                    } else if (slot.employee_name) {
                      employeeName = slot.employee_name
                    }
                    
                    // Group slots by employee name
                    if (!slotsByEmployee[employeeName]) {
                      slotsByEmployee[employeeName] = {
                        employee: employee,
                        slots: []
                      }
                    }
                    
                    slotsByEmployee[employeeName].slots.push(slot)
                  })
                  
                  console.log('📊 Grouped slots by employee:', slotsByEmployee)
                  
                  // Render grouped time slots
                  return Object.entries(slotsByEmployee).map(([employeeName, employeeData], employeeIndex) => {
                    // Generate different colors for different employees
                    const colors = [
                      { bg: 'bg-blue-100', text: 'text-blue-600', border: 'border-blue-200' },
                      { bg: 'bg-green-100', text: 'text-green-600', border: 'border-green-200' },
                      { bg: 'bg-purple-100', text: 'text-purple-600', border: 'border-purple-200' },
                      { bg: 'bg-orange-100', text: 'text-orange-600', border: 'border-orange-200' },
                      { bg: 'bg-pink-100', text: 'text-pink-600', border: 'border-pink-200' },
                      { bg: 'bg-indigo-100', text: 'text-indigo-600', border: 'border-indigo-200' }
                    ]
                    const colorScheme = colors[employeeIndex % colors.length]
                    
                    return (
                      <div key={employeeName} className="space-y-3">
                        {/* Employee Header */}
                        <div className={`flex items-center space-x-3 pb-3 border-b-2 ${colorScheme.border} bg-gradient-to-r from-gray-50 to-white p-3 rounded-t-lg`}>
                          <div className={`flex items-center justify-center w-10 h-10 ${colorScheme.bg} ${colorScheme.text} rounded-full text-sm font-bold shadow-sm`}>
                            {employeeName.split(' ').map(name => name[0]).join('').toUpperCase()}
                          </div>
                          <div className="flex-1">
                            <h4 className="font-semibold text-gray-900 text-base">{employeeName}</h4>
                            <p className="text-sm text-gray-600">
                              {employeeData.slots.length} available slot{employeeData.slots.length !== 1 ? 's' : ''}
                            </p>
                          </div>
                          <UserIcon className={`h-5 w-5 ${colorScheme.text}`} />
                        </div>
                        
                        {/* Time Slots for this Employee */}
                        <div className="space-y-2 ml-11">
                          {employeeData.slots.map((slot, slotIndex) => (
                            <button
                              key={`${employeeName}-${slotIndex}`}
                              onClick={() => setSelectedTimeSlot(slot)}
                              className={`w-full p-3 text-left rounded-lg border transition-colors ${
                                selectedTimeSlot === slot
                                  ? 'bg-green-50 border-green-500 text-green-700'
                                  : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
                              }`}
                            >
                              <div className="font-medium">{slot.start_time} - {slot.end_time}</div>
                              <div className="text-sm text-gray-600">
                                Duration: {slot.duration} min
                              </div>
                            </button>
                          ))}
                        </div>
                      </div>
                    )
                  })
                })()}
              </div>
             ) : selectedDate && selectedService && selectedEmployees.length > 0 ? (
               <div className="text-center py-8">
                 <div className="text-sm text-gray-500 mb-2">No available time slots found</div>
                 <div className="text-xs text-gray-400">
                   Try selecting a different date or check if the employee is available
                 </div>
               </div>
             ) : (
               <div className="text-center py-8">
                 <div className="text-sm text-gray-400">
                   Select date, service, and employee(s) to view available times
                 </div>
               </div>
             )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200">
          <div className="text-sm text-gray-600">
            {selectedTimeSlot ? (
              `Moving to ${selectedDate} at ${selectedTimeSlot.start_time}`
            ) : (
              'Select a time slot to continue'
            )}
          </div>
          <div className="flex space-x-3">
            <button
              onClick={handleClose}
              disabled={isUpdating}
              className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg disabled:opacity-50"
            >
              Cancel
            </button>
            <button
              onClick={handleConfirm}
              disabled={!selectedTimeSlot || isUpdating}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isUpdating ? 'Moving...' : 'Move Appointment'}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AppointmentMoveModal 