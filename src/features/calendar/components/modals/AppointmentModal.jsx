import React, { useState, useEffect } from 'react'
import { 
  XMarkIcon, 
  ClockIcon, 
  UserIcon,
  CurrencyDollarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  CheckIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  PencilIcon
} from '@heroicons/react/24/outline'
import { 
  AppointmentStatus, 
  validateStatusTransition, 
  getStatusLabel,
  formatCurrency,
  formatDuration,
  getCustomerInitials 
} from '../../types'
import { APPOINTMENT_STATUSES } from '../../constants/calendarConfig'
import AppointmentStatusService from '../../services/appointmentStatusService'
import { appointmentService } from '../../services/appointmentService'
import { servicesApiService } from '../../services/servicesApiService'

/**
 * CustomDateTimePicker - Date and time picker with separate dropdowns for hours, minutes (5-minute intervals), and AM/PM
 */
const CustomDateTimePicker = ({ value, onChange, className = '', disabled = false }) => {
  const [selectedDate, setSelectedDate] = useState('')
  const [selectedHour, setSelectedHour] = useState('09')
  const [selectedMinute, setSelectedMinute] = useState('00')
  const [selectedPeriod, setSelectedPeriod] = useState('AM')

  // Initialize with current value whenever value changes
  useEffect(() => {
    if (value) {
      const date = new Date(value)
      
      // Validate the date
      if (isNaN(date.getTime())) {
        console.error('Invalid date provided to CustomDateTimePicker:', value)
        return
      }
      
      // Round to nearest 5-minute interval
      const minutes = date.getMinutes()
      const remainder = minutes % 5
      if (remainder !== 0) {
        const roundedMinutes = remainder < 2.5 ? minutes - remainder : minutes + (5 - remainder)
        date.setMinutes(roundedMinutes)
        date.setSeconds(0)
        date.setMilliseconds(0)
      }
      
      // Set date (use local date, not UTC)
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const day = date.getDate().toString().padStart(2, '0')
      setSelectedDate(`${year}-${month}-${day}`)
      
      // Set time components
      let hour = date.getHours()
      const minute = date.getMinutes()
      const period = hour >= 12 ? 'PM' : 'AM'
      
      // Convert to 12-hour format
      if (hour === 0) hour = 12
      else if (hour > 12) hour -= 12
      
      setSelectedHour(hour.toString().padStart(2, '0'))
      setSelectedMinute(minute.toString().padStart(2, '0'))
      setSelectedPeriod(period)
    }
  }, [value])

  const handleDateTimeChange = (newDate, newHour, newMinute, newPeriod) => {
    if (!newDate || !newHour || !newMinute || !newPeriod) return
    
    // Convert to 24-hour format
    let hour24 = parseInt(newHour)
    if (newPeriod === 'PM' && hour24 !== 12) {
      hour24 += 12
    } else if (newPeriod === 'AM' && hour24 === 12) {
      hour24 = 0
    }
    
    // Create new date using local timezone
    const dateTime = new Date(newDate + 'T00:00:00')
    dateTime.setHours(hour24, parseInt(newMinute), 0, 0)
    
    // Format for datetime-local input (avoid timezone conversion)
    const year = dateTime.getFullYear()
    const month = (dateTime.getMonth() + 1).toString().padStart(2, '0')
    const day = dateTime.getDate().toString().padStart(2, '0')
    const hours = dateTime.getHours().toString().padStart(2, '0')
    const minutes = dateTime.getMinutes().toString().padStart(2, '0')
    
    const isoString = `${year}-${month}-${day}T${hours}:${minutes}`
    onChange(isoString)
  }

  const handleDateChange = (newDate) => {
    setSelectedDate(newDate)
    handleDateTimeChange(newDate, selectedHour, selectedMinute, selectedPeriod)
  }

  const handleHourChange = (newHour) => {
    setSelectedHour(newHour)
    handleDateTimeChange(selectedDate, newHour, selectedMinute, selectedPeriod)
  }

  const handleMinuteChange = (newMinute) => {
    setSelectedMinute(newMinute)
    handleDateTimeChange(selectedDate, selectedHour, newMinute, selectedPeriod)
  }

  const handlePeriodChange = (newPeriod) => {
    setSelectedPeriod(newPeriod)
    handleDateTimeChange(selectedDate, selectedHour, selectedMinute, newPeriod)
  }

  // Generate options
  const hourOptions = Array.from({ length: 12 }, (_, i) => {
    const hour = i + 1
    return hour.toString().padStart(2, '0')
  })

  const minuteOptions = Array.from({ length: 12 }, (_, i) => {
    const minute = i * 5
    return minute.toString().padStart(2, '0')
  })

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Date picker */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Date</label>
        <input
          type="date"
          value={selectedDate}
          onChange={(e) => handleDateChange(e.target.value)}
          disabled={disabled}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
      </div>
      
      {/* Time picker */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Time</label>
        <div className="flex space-x-2">
          {/* Hours */}
          <select
            value={selectedHour}
            onChange={(e) => handleHourChange(e.target.value)}
            disabled={disabled}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            {hourOptions.map(hour => (
              <option key={hour} value={hour}>{hour}</option>
            ))}
          </select>
          
          {/* Minutes */}
          <select
            value={selectedMinute}
            onChange={(e) => handleMinuteChange(e.target.value)}
            disabled={disabled}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            {minuteOptions.map(minute => (
              <option key={minute} value={minute}>{minute}</option>
            ))}
          </select>
          
          {/* AM/PM */}
          <select
            value={selectedPeriod}
            onChange={(e) => handlePeriodChange(e.target.value)}
            disabled={disabled}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="AM">AM</option>
            <option value="PM">PM</option>
          </select>
        </div>
      </div>
    </div>
  )
}

/**
 * AppointmentModal - Combined appointment details viewing and editing in a single modal
 */
const AppointmentModal = ({
  isOpen,
  onClose,
  appointment,
  onUpdateStatus,
  onAppointmentUpdate,
  onCancelAppointment,
  isLoading = false
}) => {
  // Modal mode state
  const [mode, setMode] = useState('view') // 'view' or 'edit'
  
  // View mode state
  const [showCancellationReason, setShowCancellationReason] = useState(false)
  const [cancellationReason, setCancellationReason] = useState('')
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false)
  const [detailedAppointment, setDetailedAppointment] = useState(null)
  const [isLoadingDetails, setIsLoadingDetails] = useState(false)

  // Edit mode state
  const [formData, setFormData] = useState({
    start_time: '',
    notes_from_customer: '',
    selectedService: null,
    status: 'confirmed'
  })
  const [services, setServices] = useState([])
  const [categories, setCategories] = useState([])
  const [servicesByCategory, setServicesByCategory] = useState({})
  const [expandedCategories, setExpandedCategories] = useState(new Set())
  const [isLoadingServices, setIsLoadingServices] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState('')
  const [validationErrors, setValidationErrors] = useState({})

  // Reset mode when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setMode('view')
      setCancellationReason('')
      setShowCancellationReason(false)
      setError('')
      setValidationErrors({})
    } else {
      setDetailedAppointment(null)
    }
  }, [isOpen])

  // Fetch detailed appointment data when modal opens in view mode
  useEffect(() => {
    if (isOpen && appointment?.id && mode === 'view') {
      const fetchDetails = async () => {
        setIsLoadingDetails(true)
        try {
          const details = await appointmentService.fetchAppointmentDetails(appointment.id)
          setDetailedAppointment(details)
          console.log('✅ Fetched detailed appointment for modal:', details)
        } catch (error) {
          console.error('❌ Failed to fetch appointment details for modal:', error)
          setDetailedAppointment(appointment)
        } finally {
          setIsLoadingDetails(false)
        }
      }
      
      fetchDetails()
    }
  }, [isOpen, appointment?.id, appointment?.status, appointment?.lastStatusChange, mode])

  // Initialize edit form data when switching to edit mode
  useEffect(() => {
    if (mode === 'edit' && appointment) {
      const startTime = new Date(appointment.start || appointment.startTime)
      
      // Round to nearest 5-minute interval
      const minutes = startTime.getMinutes()
      const remainder = minutes % 5
      if (remainder !== 0) {
        const roundedMinutes = remainder < 2.5 ? minutes - remainder : minutes + (5 - remainder)
        startTime.setMinutes(roundedMinutes)
        startTime.setSeconds(0)
        startTime.setMilliseconds(0)
      }
      
      setFormData({
        start_time: startTime.toISOString().slice(0, 16),
        notes_from_customer: appointment.notes || appointment.description || '',
        selectedService: null,
        status: appointment.status || 'confirmed'
      })
      
      loadServices()
    }
  }, [mode, appointment])

  // Load services and categories for edit mode
  const loadServices = async () => {
    setIsLoadingServices(true)
    setError('')
    try {
      const [servicesData, categoriesData] = await Promise.all([
        servicesApiService.fetchServices(),
        servicesApiService.fetchServiceCategories()
      ])
      
      setServices(servicesData)
      setCategories(categoriesData)
      
      // Group services by category
      const grouped = {}
      categoriesData.forEach(category => {
        grouped[category.id] = {
          category: category,
          services: []
        }
      })
      
      servicesData.forEach(service => {
        const categoryId = service.category || 'uncategorized'
        if (!grouped[categoryId]) {
          grouped[categoryId] = {
            category: { id: categoryId, name: 'Uncategorized' },
            services: []
          }
        }
        grouped[categoryId].services.push(service)
      })
      
      // Remove empty categories
      Object.keys(grouped).forEach(categoryId => {
        if (grouped[categoryId].services.length === 0) {
          delete grouped[categoryId]
        }
      })
      
      setServicesByCategory(grouped)
      
      // Expand first category by default
      if (Object.keys(grouped).length > 0) {
        const firstCategoryId = Object.keys(grouped)[0]
        setExpandedCategories(new Set([firstCategoryId]))
      }
      
      // Try to find and select the current service
      if (appointment && appointment.serviceId) {
        const currentService = servicesData.find(s => s.id === parseInt(appointment.serviceId))
        if (currentService) {
          setFormData(prev => ({
            ...prev,
            selectedService: currentService
          }))
        }
      }
      
    } catch (error) {
      console.error('Failed to load services:', error)
      setError('Failed to load services. Please try again.')
    } finally {
      setIsLoadingServices(false)
    }
  }

  if (!isOpen || !appointment) return null

  // Use detailed appointment data if available, otherwise fallback to original
  const displayAppointment = detailedAppointment || appointment

  // Shared helper functions
  const handleStatusUpdate = async (newStatus, reason = null) => {
    const currentMappedStatus = AppointmentStatusService.mapLegacyStatus(appointment.status)
    const validation = validateStatusTransition(currentMappedStatus, newStatus)
    if (!validation.isValid) {
      alert(validation.error)
      return
    }

    setIsUpdatingStatus(true)
    try {
      await onUpdateStatus(appointment.id, newStatus, reason)
      if (newStatus === AppointmentStatus.CANCELLED) {
        setShowCancellationReason(false)
      }
    } catch (error) {
      console.error('Failed to update status:', error)
      alert('Failed to update appointment status')
    } finally {
      setIsUpdatingStatus(false)
    }
  }

  const handleCancelWithReason = () => {
    if (!cancellationReason.trim()) {
      alert('Cancellation reason is required')
      return
    }
    handleStatusUpdate(AppointmentStatus.CANCELLED, cancellationReason)
  }

  const getStatusBadgeColor = (status) => {
    const mappedStatus = AppointmentStatusService.mapLegacyStatus(status)
    const config = Object.values(APPOINTMENT_STATUSES).find(s => s.id === mappedStatus)
    return config ? {
      backgroundColor: config.bgColor,
      color: config.textColor,
      borderColor: config.color
    } : { backgroundColor: '#f3f4f6', color: '#4b5563' }
  }

  const customerInitials = getCustomerInitials(
    displayAppointment.clientName?.split(' ')[0] || '',
    displayAppointment.clientName?.split(' ')[1] || ''
  )

  const formatDateTime = (date) => {
    const dateObj = new Date(date)
    return {
      date: dateObj.toLocaleDateString('en-US', {
        weekday: 'long',
        month: 'long', 
        day: 'numeric',
        year: 'numeric'
      }),
      time: dateObj.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit'
      })
    }
  }

  const { date, time } = formatDateTime(displayAppointment.startTime || displayAppointment.start)
  
  // Map the current status and get available transitions
  const currentStatus = AppointmentStatusService.mapLegacyStatus(displayAppointment.status)
  const availableTransitions = AppointmentStatusService.getAvailableTransitions(currentStatus)

  // Edit mode form handlers
  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
    
    if (validationErrors[field]) {
      setValidationErrors(prev => ({
        ...prev,
        [field]: null
      }))
    }
  }

  const handleServiceSelect = (service) => {
    setFormData(prev => ({
      ...prev,
      selectedService: service
    }))
    
    if (validationErrors.selectedService) {
      setValidationErrors(prev => ({
        ...prev,
        selectedService: null
      }))
    }
  }

  const toggleCategory = (categoryId) => {
    setExpandedCategories(prev => {
      const newSet = new Set(prev)
      if (newSet.has(categoryId)) {
        newSet.delete(categoryId)
      } else {
        newSet.add(categoryId)
      }
      return newSet
    })
  }

  const validateForm = () => {
    const errors = {}
    
    if (!formData.start_time) {
      errors.start_time = 'Start time is required'
    }
    
    if (!formData.selectedService) {
      errors.selectedService = 'Service selection is required'
    }
    
    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }
    
    setIsSubmitting(true)
    setError('')
    
    try {
      const updateData = {
        start_time: new Date(formData.start_time).toISOString().slice(0, 19) + '+0000',
        notes_from_customer: formData.notes_from_customer,
        status: formData.status
      }
      
      if (formData.selectedService && formData.selectedService.id !== parseInt(appointment.serviceId)) {
        updateData.appointment_services = [{
          service: formData.selectedService.id,
          quantity: 1,
          base_price: parseFloat(formData.selectedService.price || 0),
          duration: parseInt(formData.selectedService.duration || 60),
          buffer_time: parseInt(formData.selectedService.buffer_time || 0),
          notes: ''
        }]
      }
      
      console.log('🔄 Updating appointment with data:', updateData)
      
      const updatedAppointment = await appointmentService.patchAppointment(appointment.id, updateData)
      
      console.log('✅ Appointment updated successfully:', updatedAppointment)
      
      if (onAppointmentUpdate) {
        onAppointmentUpdate(updatedAppointment)
      }
      
      // Switch back to view mode to show updated details
      setMode('view')
      
    } catch (error) {
      console.error('Failed to update appointment:', error)
      setError(error.message || 'Failed to update appointment. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleClose = () => {
    if (isSubmitting) return
    onClose()
  }

  const handleEditClick = () => {
    setMode('edit')
  }

  const handleCancelEdit = () => {
    setMode('view')
    setError('')
    setValidationErrors({})
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-2 sm:p-4">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black bg-opacity-50"
        onClick={handleClose}
      />
      
      {/* Modal */}
      <div className="relative bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[95vh] sm:max-h-[90vh] min-h-[300px] sm:min-h-[400px] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 flex-shrink-0">
          <h2 className="text-lg font-semibold text-gray-900">
            {mode === 'view' ? 'Appointment Details' : 'Edit Appointment'}
          </h2>
          <div className="flex items-center gap-2">
            {mode === 'view' && (
              <button
                onClick={handleEditClick}
                className="flex items-center gap-1 px-3 py-1.5 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <PencilIcon className="h-4 w-4" />
                Edit
              </button>
            )}
            <button
              onClick={handleClose}
              disabled={isSubmitting}
              className="p-1 text-gray-400 hover:text-gray-600 rounded-lg disabled:opacity-50"
            >
              <XMarkIcon className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          {mode === 'view' ? (
            // VIEW MODE CONTENT
            <>
              {/* Loading indicator for detailed data */}
              {isLoadingDetails && (
                <div className="p-4 text-center">
                  <div className="inline-flex items-center gap-2 text-sm text-gray-600">
                    <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                    Loading appointment details...
                  </div>
                </div>
              )}

              {/* Customer Section */}
              <div className="p-4 border-b border-gray-100">
                <div className="flex items-center gap-3">
                  <div 
                    className="w-12 h-12 rounded-full bg-blue-500 flex items-center justify-center text-white font-semibold"
                  >
                    {customerInitials}
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-900">
                      {displayAppointment.clientName || 'Customer'}
                    </h3>
                    <div className="flex items-center gap-2 text-sm text-gray-600 mt-1">
                      <UserIcon className="h-4 w-4" />
                      <span>Customer ID: {displayAppointment.customerId || displayAppointment.customer || displayAppointment.clientId || 'N/A'}</span>
                    </div>
                  </div>
                  <div 
                    className="px-3 py-1 rounded-full border text-sm font-medium"
                    style={getStatusBadgeColor(displayAppointment.status)}
                  >
                    {getStatusLabel(currentStatus)}
                  </div>
                </div>
              </div>

              {/* Date & Time Section */}
              <div className="p-4 border-b border-gray-100">
                <div className="flex items-center gap-3">
                  <ClockIcon className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="font-medium text-gray-900">{date}</p>
                    <p className="text-gray-600">{time}</p>
                  </div>
                </div>
              </div>

              {/* Employee Section */}
              <div className="p-4 border-b border-gray-100">
                <div className="flex items-center gap-3">
                  <UserIcon className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="font-medium text-gray-900">
                      {displayAppointment.employeeName || `Employee ${displayAppointment.employeeId}`}
                    </p>
                    <p className="text-sm text-gray-600">Service Provider</p>
                  </div>
                </div>
              </div>

              {/* Service Details Section */}
              <div className="p-4 border-b border-gray-100">
                <h4 className="font-medium text-gray-900 mb-3">Service Details</h4>
                
                <div className="mb-2">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <p className="font-medium text-gray-800">
                        {displayAppointment.serviceName || displayAppointment.location || 'Service'}
                      </p>
                      <div className="text-sm text-gray-600 space-y-1">
                        <p>{formatDuration(displayAppointment.services?.[0]?.duration || 60)}</p>
                        {(displayAppointment.bufferTime > 0 || displayAppointment.services?.[0]?.buffer_time > 0) && (
                          <p className="text-orange-600">
                            + {formatDuration(displayAppointment.bufferTime || displayAppointment.services?.[0]?.buffer_time || 0)} buffer time
                          </p>
                        )}
                        {displayAppointment.services?.[0]?.notes && (
                          <p className="text-blue-600 italic">
                            Note: {displayAppointment.services[0].notes}
                          </p>
                        )}
                      </div>
                    </div>
                    <p className="font-medium text-gray-900">
                      {formatCurrency(displayAppointment.price || 0)}
                    </p>
                  </div>
                </div>

                {/* Add-ons */}
                {displayAppointment.addOns && displayAppointment.addOns.length > 0 && (
                  <div className="mt-3 pt-3 border-t border-gray-100">
                    <p className="text-sm font-medium text-gray-700 mb-2">Add-ons:</p>
                    {displayAppointment.addOns.map((addOn, index) => (
                      <div key={index} className="flex justify-between items-center mb-1">
                        <div className="flex-1">
                          <p className="text-sm text-gray-800">+ {addOn.add_on_short_name || addOn.add_on_name || addOn.name}</p>
                          {addOn.duration && (
                            <p className="text-xs text-gray-500">{addOn.duration} min</p>
                          )}
                        </div>
                        <p className="text-sm text-gray-900">
                          {formatCurrency(addOn.add_on_price || addOn.price || 0)}
                        </p>
                      </div>
                    ))}
                  </div>
                )}

                {/* Total */}
                <div className="mt-3 pt-3 border-t border-gray-200">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="font-semibold text-gray-900">Total Appointment Time</p>
                      <div className="text-sm text-gray-600">
                        <p className="font-medium">{formatDuration(displayAppointment.totalDuration || 60)}</p>
                        {(displayAppointment.bufferTime > 0 || displayAppointment.services?.[0]?.buffer_time > 0) && (
                          <p className="text-xs text-gray-500">
                            (Service: {formatDuration((displayAppointment.services?.[0]?.duration || 60))} + 
                            Buffer: {formatDuration(displayAppointment.bufferTime || displayAppointment.services?.[0]?.buffer_time || 0)})
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-lg font-bold text-gray-900">
                        {formatCurrency(displayAppointment.price || 0)}
                      </p>
                      <p className="text-sm text-gray-500">Total Cost</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Payment & Status Information */}
              <div className="p-4 border-b border-gray-100">
                <h4 className="font-medium text-gray-900 mb-3">Payment & Status</h4>
                
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Payment Status:</span>
                    <p className="font-medium text-gray-900 capitalize">
                      {displayAppointment.paymentStatus || 'Unpaid'}
                    </p>
                  </div>
                  
                  <div>
                    <span className="text-gray-600">Source:</span>
                    <p className="font-medium text-gray-900 capitalize">
                      {displayAppointment.source === 'admin' ? 'Staff Booking' : 
                       displayAppointment.source === 'online' ? 'Online Booking' : 
                       displayAppointment.source || 'Staff Booking'}
                    </p>
                  </div>
                </div>
              </div>

              {/* Notes Section */}
              <div className="p-4 border-b border-gray-100">
                <h4 className="font-medium text-gray-900 mb-2">Appointment Notes</h4>
                {displayAppointment.notes ? (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                    <p className="text-gray-700 text-sm">{displayAppointment.notes}</p>
                  </div>
                ) : (
                  <p className="text-gray-500 text-sm italic">No notes from customer</p>
                )}
              </div>

              {/* Cancellation Reason Section */}
              {displayAppointment.status === 'cancelled' && displayAppointment.cancellationReason && (
                <div className="p-4 border-b border-gray-100 bg-red-50">
                  <h4 className="font-medium text-red-900 mb-2">Cancellation Reason</h4>
                  <p className="text-red-700 text-sm">{displayAppointment.cancellationReason}</p>
                </div>
              )}

              {/* Description Section */}
              {displayAppointment.description && (
                <div className="p-4 border-b border-gray-100">
                  <h4 className="font-medium text-gray-900 mb-2">Additional Details</h4>
                  <pre className="text-gray-700 text-sm whitespace-pre-wrap">{displayAppointment.description}</pre>
                </div>
              )}

              {/* Cancellation Reason Input */}
              {showCancellationReason && (
                <div className="p-4 border-b border-gray-100 bg-red-50">
                  <h4 className="font-medium text-gray-900 mb-2">Cancellation Reason</h4>
                  <textarea
                    value={cancellationReason}
                    onChange={(e) => setCancellationReason(e.target.value)}
                    placeholder="Please provide a reason for cancellation..."
                    className="w-full p-3 border border-gray-300 rounded-lg text-sm"
                    rows={3}
                  />
                  <div className="flex gap-2 mt-3">
                    <button
                      onClick={handleCancelWithReason}
                      disabled={Boolean(isUpdatingStatus || !cancellationReason.trim())}
                      className="flex-1 bg-red-600 text-white py-2 px-4 rounded-lg text-sm font-medium hover:bg-red-700 disabled:opacity-50"
                    >
                      {isUpdatingStatus ? 'Cancelling...' : 'Confirm Cancellation'}
                    </button>
                    <button
                      onClick={() => setShowCancellationReason(false)}
                      className="flex-1 bg-gray-200 text-gray-800 py-2 px-4 rounded-lg text-sm font-medium hover:bg-gray-300"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              )}
            </>
          ) : (
            // EDIT MODE CONTENT
            <div className="p-6">
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Error Display */}
                {error && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-start space-x-3">
                    <ExclamationTriangleIcon className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
                    <div className="text-sm text-red-800">{error}</div>
                  </div>
                )}

                {/* Current Appointment Info */}
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <h3 className="font-medium text-gray-900 mb-2">Current Appointment</h3>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Customer:</span>
                      <span className="ml-2 font-medium">{appointment.clientName || 'Customer'}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Employee:</span>
                      <span className="ml-2 font-medium">{appointment.employeeName || 'Employee'}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Current Service:</span>
                      <span className="ml-2 font-medium">{appointment.serviceName || 'Service'}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Status:</span>
                      <span className="ml-2 font-medium capitalize">{appointment.status || 'confirmed'}</span>
                    </div>
                  </div>
                </div>

                {/* Start Time */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <ClockIcon className="h-4 w-4 inline mr-1" />
                    Start Time
                  </label>
                  <CustomDateTimePicker
                    value={formData.start_time}
                    onChange={(value) => handleInputChange('start_time', value)}
                    className={validationErrors.start_time ? 'border-red-300' : ''}
                    disabled={isSubmitting}
                  />
                  {validationErrors.start_time && (
                    <p className="mt-1 text-sm text-red-600">{validationErrors.start_time}</p>
                  )}
                </div>

                {/* Service Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <UserIcon className="h-4 w-4 inline mr-1" />
                    Service
                  </label>
                  {isLoadingServices ? (
                    <div className="flex items-center justify-center py-8">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                      <span className="ml-2 text-sm text-gray-600">Loading services...</span>
                    </div>
                  ) : (
                    <div className="border border-gray-300 rounded-md max-h-64 overflow-y-auto">
                      {Object.entries(servicesByCategory).map(([categoryId, categoryData]) => (
                        <div key={categoryId} className="border-b border-gray-200 last:border-b-0">
                          <button
                            type="button"
                            onClick={() => toggleCategory(categoryId)}
                            className="w-full px-4 py-3 text-left bg-gray-50 hover:bg-gray-100 flex items-center justify-between transition-colors"
                          >
                            <span className="font-medium text-gray-900">
                              {categoryData.category.name}
                            </span>
                            {expandedCategories.has(categoryId) ? (
                              <ChevronDownIcon className="h-5 w-5 text-gray-500" />
                            ) : (
                              <ChevronRightIcon className="h-5 w-5 text-gray-500" />
                            )}
                          </button>
                          {expandedCategories.has(categoryId) && (
                            <div className="divide-y divide-gray-100">
                              {categoryData.services.map(service => (
                                <button
                                  key={service.id}
                                  type="button"
                                  onClick={() => handleServiceSelect(service)}
                                  className={`w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors ${
                                    formData.selectedService?.id === service.id ? 'bg-blue-50 border-l-4 border-blue-500' : ''
                                  }`}
                                >
                                  <div className="flex items-start justify-between">
                                    <div className="flex-1">
                                      <div className="flex items-center space-x-2">
                                        <h4 className="font-medium text-gray-900">{service.name}</h4>
                                        {formData.selectedService?.id === service.id && (
                                          <CheckIcon className="h-4 w-4 text-blue-600" />
                                        )}
                                      </div>
                                      {service.description && (
                                        <p className="text-sm text-gray-600 mt-1">{service.description}</p>
                                      )}
                                      <div className="flex items-center space-x-4 mt-2">
                                        <span className="text-sm font-medium text-gray-900">
                                          <CurrencyDollarIcon className="h-4 w-4 inline mr-1" />
                                          {formatCurrency(service.price || 0)}
                                        </span>
                                        <span className="text-sm text-gray-600">
                                          <ClockIcon className="h-4 w-4 inline mr-1" />
                                          {formatDuration(service.duration || 60)}
                                        </span>
                                      </div>
                                    </div>
                                  </div>
                                </button>
                              ))}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                  {validationErrors.selectedService && (
                    <p className="mt-1 text-sm text-red-600">{validationErrors.selectedService}</p>
                  )}
                </div>

                {/* Notes */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Notes
                  </label>
                  <textarea
                    value={formData.notes_from_customer}
                    onChange={(e) => handleInputChange('notes_from_customer', e.target.value)}
                    placeholder="Any special notes or instructions..."
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                    disabled={isSubmitting}
                  />
                </div>

                {/* Status */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Status
                  </label>
                  <select
                    value={formData.status}
                    onChange={(e) => handleInputChange('status', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    disabled={isSubmitting}
                  >
                    <option value="requested">Requested</option>
                    <option value="confirmed">Confirmed</option>
                    <option value="accepted">Accepted</option>
                    <option value="checked_in">Checked In</option>
                    <option value="service_started">Service Started</option>
                    <option value="completed">Completed</option>
                  </select>
                </div>
              </form>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex-shrink-0 p-4 border-t border-gray-200 bg-gray-50">
          {mode === 'view' ? (
            <div className="flex justify-center">
              <button
                onClick={handleClose}
                className="bg-gray-200 text-gray-800 px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-300"
              >
                Close
              </button>
            </div>
          ) : (
            <div className="flex items-center justify-end space-x-3">
              <button
                type="button"
                onClick={handleCancelEdit}
                disabled={isSubmitting}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                onClick={handleSubmit}
                disabled={isSubmitting}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 flex items-center space-x-2"
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Updating...</span>
                  </>
                ) : (
                  <>
                    <CheckIcon className="h-4 w-4" />
                    <span>Update Appointment</span>
                  </>
                )}
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default AppointmentModal 