import React from 'react'
import { 
  getTimeResolutionInfo,
  getSnappingThreshold,
  validateTimePosition,
  findClosestTimePoints 
} from '../../utils/timeGridCalculations'

/**
 * DragBehaviorManager - Manages drag behavior for different time resolution scenarios
 * Implements case-by-case movement logic based on iOS patterns
 */
const DragBehaviorManager = ({
  timeResolution,
  config,
  onBehaviorChange,
  children
}) => {
  const resolutionInfo = getTimeResolutionInfo(timeResolution)
  const snappingThreshold = getSnappingThreshold(timeResolution)

  // Enhanced drag behavior based on time resolution
  const getDragBehavior = () => {
    switch (timeResolution) {
      case 5:
        return getPrecisionModeBehavior()
      case 15:
        return getBalancedModeBehavior()
      case 30:
        return getBlockModeBehavior()
      default:
        return getBalancedModeBehavior()
    }
  }

  /**
   * 5-Minute Resolution (Precision Mode)
   * - 12 intervals per hour
   * - High precision snapping
   * - Half-interval support for ultra-precise positioning
   */
  const getPrecisionModeBehavior = () => ({
    mode: 'precision',
    snappingDistance: 15,
    allowHalfIntervals: true,
    visualFeedback: {
      snapIndicators: true,
      gridHighlight: true,
      preciseTimeDisplay: true
    },
    dragThreshold: 3, // Lower threshold for precision
    snapSensitivity: 'high',
    description: 'Ultra-precise 5-minute scheduling with half-interval support',
    gridLines: {
      major: [0, 15, 30, 45], // Quarter-hour lines
      minor: [5, 10, 20, 25, 35, 40, 50, 55], // 5-minute lines
      micro: [] // No micro lines needed
    },
    snappingLogic: (offset, gridCellHeight) => {
      // Primary snapping to 5-minute intervals
      const primarySnap = Math.round(offset / gridCellHeight) * gridCellHeight
      
      // Secondary snapping to 2.5-minute half-intervals for precision
      const halfCellHeight = gridCellHeight / 2
      const halfSnap = Math.round(offset / halfCellHeight) * halfCellHeight
      
      // Choose closest snap point
      const distanceToPrimary = Math.abs(offset - primarySnap)
      const distanceToHalf = Math.abs(offset - halfSnap)
      
      return distanceToHalf < distanceToPrimary ? halfSnap : primarySnap
    }
  })

  /**
   * 15-Minute Resolution (Balanced Mode)
   * - 4 intervals per hour
   * - Standard snapping behavior
   * - Optimal for most business use cases
   */
  const getBalancedModeBehavior = () => ({
    mode: 'balanced',
    snappingDistance: 20,
    allowHalfIntervals: false,
    visualFeedback: {
      snapIndicators: true,
      gridHighlight: false,
      preciseTimeDisplay: false
    },
    dragThreshold: 5, // Standard threshold
    snapSensitivity: 'medium',
    description: 'Standard 15-minute scheduling for business appointments',
    gridLines: {
      major: [0, 30], // Half-hour lines
      minor: [15, 45], // Quarter-hour lines
      micro: [] // No micro lines
    },
    snappingLogic: (offset, gridCellHeight) => {
      // Simple snapping to 15-minute intervals
      return Math.round(offset / gridCellHeight) * gridCellHeight
    }
  })

  /**
   * 30-Minute Resolution (Block Mode)
   * - 2 intervals per hour
   * - Loose snapping for block scheduling
   * - Simplified grid for clarity
   */
  const getBlockModeBehavior = () => ({
    mode: 'block',
    snappingDistance: 25,
    allowHalfIntervals: false,
    visualFeedback: {
      snapIndicators: false,
      gridHighlight: false,
      preciseTimeDisplay: false
    },
    dragThreshold: 8, // Higher threshold for block mode
    snapSensitivity: 'low',
    description: 'Block scheduling with 30-minute intervals',
    gridLines: {
      major: [0], // Hour lines only
      minor: [30], // Half-hour lines
      micro: [] // No micro lines
    },
    snappingLogic: (offset, gridCellHeight) => {
      // Block snapping to 30-minute intervals with wider tolerance
      const tolerance = gridCellHeight * 0.3 // 30% tolerance
      const snapPoint = Math.round(offset / gridCellHeight) * gridCellHeight
      
      // Only snap if within tolerance, otherwise allow free movement
      return Math.abs(offset - snapPoint) <= tolerance ? snapPoint : offset
    }
  })

  const currentBehavior = getDragBehavior()

  // Notify parent of behavior changes
  React.useEffect(() => {
    if (onBehaviorChange) {
      onBehaviorChange(currentBehavior)
    }
  }, [timeResolution, onBehaviorChange])

  return (
    <div className="drag-behavior-manager" data-resolution={timeResolution}>
      {/* Debug info for development */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed top-4 right-4 bg-black bg-opacity-75 text-white p-2 rounded text-xs z-50">
          <div className="font-bold">{resolutionInfo.name}</div>
          <div>{resolutionInfo.description}</div>
          <div>Snap Distance: {currentBehavior.snappingDistance}px</div>
          <div>Intervals/Hour: {resolutionInfo.intervalsPerHour}</div>
          <div>Mode: {currentBehavior.mode}</div>
        </div>
      )}
      
      {children}
    </div>
  )
}

/**
 * Hook for accessing drag behavior in components
 */
export const useDragBehavior = (timeResolution, config) => {
  const resolutionInfo = getTimeResolutionInfo(timeResolution)
  const snappingThreshold = getSnappingThreshold(timeResolution)
  
  const getBehaviorForResolution = (resolution) => {
    const manager = new DragBehaviorManager({ timeResolution: resolution, config })
    return manager.getDragBehavior ? manager.getDragBehavior() : null
  }

  const validateDragPosition = (newTime, workingHours = null) => {
    return validateTimePosition(newTime, config, workingHours)
  }

  const getClosestSnapPoints = (yPosition) => {
    return findClosestTimePoints(yPosition, config)
  }

  const shouldShowPrecisionIndicators = () => {
    return timeResolution === 5 // Only show for precision mode
  }

  const getDragSensitivity = () => {
    switch (timeResolution) {
      case 5: return 'high'
      case 15: return 'medium'
      case 30: return 'low'
      default: return 'medium'
    }
  }

  return {
    resolutionInfo,
    snappingThreshold,
    validateDragPosition,
    getClosestSnapPoints,
    shouldShowPrecisionIndicators,
    getDragSensitivity,
    getBehaviorForResolution
  }
}

/**
 * Time Resolution Scenario Configurations
 * Defines specific behavior for each supported resolution
 */
export const TIME_RESOLUTION_SCENARIOS = {
  PRECISION_5MIN: {
    resolution: 5,
    name: 'Precision Mode',
    intervals: [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55],
    gridDensity: 'high',
    snappingBehavior: 'ultra-precise',
    useCase: 'Medical appointments, therapy sessions, precise scheduling',
    features: ['half-interval-snapping', 'micro-positioning', 'precision-indicators']
  },
  
  BALANCED_15MIN: {
    resolution: 15,
    name: 'Balanced Mode',
    intervals: [0, 15, 30, 45],
    gridDensity: 'medium',
    snappingBehavior: 'standard',
    useCase: 'Standard business appointments, consultations, meetings',
    features: ['quarter-hour-snapping', 'standard-grid', 'balanced-precision']
  },
  
  BLOCK_30MIN: {
    resolution: 30,
    name: 'Block Mode',
    intervals: [0, 30],
    gridDensity: 'low',
    snappingBehavior: 'loose',
    useCase: 'Long appointments, workshops, block scheduling',
    features: ['half-hour-blocks', 'simplified-grid', 'loose-snapping']
  }
}

/**
 * Movement Scenario Calculator
 * Calculates specific movement behavior based on current scenario
 */
export const calculateMovementScenario = (timeResolution, dragDistance, config) => {
  const scenario = Object.values(TIME_RESOLUTION_SCENARIOS).find(s => s.resolution === timeResolution)
  if (!scenario) return null

  const gridCellHeight = config.gridHeight / (60 / timeResolution)
  const intervals = scenario.intervals.length
  
  return {
    scenario,
    gridCellHeight,
    intervals,
    snapDistance: gridCellHeight,
    movementType: dragDistance > gridCellHeight * 2 ? 'major' : 'minor',
    precision: scenario.gridDensity,
    recommendedSnapping: scenario.snappingBehavior
  }
}

export default DragBehaviorManager 