import React, { forwardRef } from 'react'
import { cn } from '../../../../utils'
import { useCalendarStyles } from '../../hooks/useCalendarStyles'

/**
 * CalendarContainer - Main calendar wrapper component
 * Provides the main container styling and CSS custom properties
 */
export const CalendarContainer = forwardRef(({ 
  children, 
  className,
  config,
  ...props 
}, ref) => {
  const { baseStyles, getGridStyles } = useCalendarStyles()
  const gridStyles = getGridStyles(config)
  
  return (
    <div 
      ref={ref}
      className={cn(baseStyles.container, className)}
      style={gridStyles.style}
      {...props}
    >
      {children}
    </div>
  )
})

CalendarContainer.displayName = 'CalendarContainer'

/**
 * CalendarScrollContainer - Scrollable content wrapper
 * Handles scrolling behavior and scroll event management
 */
export const CalendarScrollContainer = forwardRef(({ 
  children, 
  className,
  onScroll,
  ...props 
}, ref) => {
  const { baseStyles } = useCalendarStyles()
  
  return (
    <div 
      ref={ref}
      className={cn(baseStyles.scrollContainer, className)}
      onScroll={onScroll}
      {...props}
    >
      {children}
    </div>
  )
})

CalendarScrollContainer.displayName = 'CalendarScrollContainer'

/**
 * CalendarGrid - Time grid wrapper
 * Provides the grid background and structure
 */
export const CalendarGrid = forwardRef(({ 
  children, 
  className,
  ...props 
}, ref) => {
  const { baseStyles } = useCalendarStyles()
  
  return (
    <div 
      ref={ref}
      className={cn(baseStyles.timeGrid, className)}
      {...props}
    >
      {children}
    </div>
  )
})

CalendarGrid.displayName = 'CalendarGrid'

/**
 * CalendarHeader - Header wrapper component
 * Provides header styling and sticky positioning
 */
export const CalendarHeader = forwardRef(({ 
  children, 
  className,
  ...props 
}, ref) => {
  const { baseStyles } = useCalendarStyles()
  
  return (
    <div 
      ref={ref}
      className={cn(baseStyles.header, className)}
      {...props}
    >
      {children}
    </div>
  )
})

CalendarHeader.displayName = 'CalendarHeader'

/**
 * TimeSlot - Individual time slot component
 * Handles time slot styling and interaction states
 */
export const TimeSlot = forwardRef(({ 
  children, 
  className,
  hour,
  minute,
  isWorking = true,
  isOccupied = false,
  onClick,
  onMouseEnter,
  onMouseLeave,
  config,
  ...props 
}, ref) => {
  const { baseStyles } = useCalendarStyles()
  
  // Determine slot styling based on state
  const slotClassName = isOccupied 
    ? baseStyles.timeSlotOccupied
    : isWorking 
      ? baseStyles.timeSlotWorking 
      : baseStyles.timeSlotNonWorking
  
  // Calculate height based on time resolution
  const slotHeight = config?.gridHeight 
    ? config.gridHeight / (60 / (config.timeResolution || 15))
    : 20
  
  return (
    <div
      ref={ref}
      className={cn(slotClassName, className)}
      style={{
        height: `${slotHeight}px`
      }}
      onClick={onClick}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      data-hour={hour}
      data-minute={minute}
      data-working={isWorking}
      data-occupied={isOccupied}
      {...props}
    >
      {children}
    </div>
  )
})

TimeSlot.displayName = 'TimeSlot'

/**
 * TimeAxis - Time axis component
 * Displays time labels and hour markers
 */
export const TimeAxis = forwardRef(({ 
  children, 
  className,
  hour,
  isHour = false,
  ...props 
}, ref) => {
  const { baseStyles } = useCalendarStyles()
  
  const axisClassName = isHour ? baseStyles.timeAxisHour : baseStyles.timeAxis
  
  return (
    <div 
      ref={ref}
      className={cn(axisClassName, className)}
      data-hour={hour}
      {...props}
    >
      {children}
    </div>
  )
})

TimeAxis.displayName = 'TimeAxis'

/**
 * WeekGrid - Week view grid wrapper
 */
export const WeekGrid = forwardRef(({ 
  children, 
  className,
  ...props 
}, ref) => {
  const { baseStyles } = useCalendarStyles()
  
  return (
    <div 
      ref={ref}
      className={cn(baseStyles.weekGrid, className)}
      {...props}
    >
      {children}
    </div>
  )
})

WeekGrid.displayName = 'WeekGrid'

/**
 * WeekGridDayHeader - Week view day header
 */
export const WeekGridDayHeader = forwardRef(({ 
  children, 
  className,
  isToday = false,
  ...props 
}, ref) => {
  const { baseStyles } = useCalendarStyles()
  
  return (
    <div 
      ref={ref}
      className={cn(
        baseStyles.weekGridDayHeader,
        isToday && baseStyles.weekGridDayHeaderToday,
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
})

WeekGridDayHeader.displayName = 'WeekGridDayHeader'

/**
 * WeekGridDay - Week view day cell
 */
export const WeekGridDay = forwardRef(({ 
  children, 
  className,
  ...props 
}, ref) => {
  const { baseStyles } = useCalendarStyles()
  
  return (
    <div 
      ref={ref}
      className={cn(baseStyles.weekGridDay, className)}
      {...props}
    >
      {children}
    </div>
  )
})

WeekGridDay.displayName = 'WeekGridDay'

/**
 * MonthGridDay - Month view day cell
 */
export const MonthGridDay = forwardRef(({ 
  children, 
  className,
  isToday = false,
  isLeading = false,
  ...props 
}, ref) => {
  const { baseStyles } = useCalendarStyles()
  
  return (
    <div 
      ref={ref}
      className={cn(
        baseStyles.monthGridDay,
        isToday && baseStyles.monthGridDayToday,
        isLeading && baseStyles.monthGridDayLeading,
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
})

MonthGridDay.displayName = 'MonthGridDay'

/**
 * MonthGridDayNumber - Month view day number
 */
export const MonthGridDayNumber = forwardRef(({ 
  children, 
  className,
  ...props 
}, ref) => {
  const { baseStyles } = useCalendarStyles()
  
  return (
    <div 
      ref={ref}
      className={cn(baseStyles.monthGridDayNumber, className)}
      {...props}
    >
      {children}
    </div>
  )
})

MonthGridDayNumber.displayName = 'MonthGridDayNumber'

/**
 * CurrentTimeIndicator - Current time line indicator
 */
export const CurrentTimeIndicator = forwardRef(({ 
  className,
  ...props 
}, ref) => {
  const { baseStyles } = useCalendarStyles()
  
  return (
    <div 
      ref={ref}
      className={cn(baseStyles.currentTimeIndicator, className)}
      {...props}
    />
  )
})

CurrentTimeIndicator.displayName = 'CurrentTimeIndicator'

/**
 * MultiEmployeeHeader - Multi-employee view header
 */
export const MultiEmployeeHeader = forwardRef(({ 
  children, 
  className,
  ...props 
}, ref) => {
  const { baseStyles } = useCalendarStyles()
  
  return (
    <div 
      ref={ref}
      className={cn(baseStyles.multiEmployeeHeader, className)}
      {...props}
    >
      {children}
    </div>
  )
})

MultiEmployeeHeader.displayName = 'MultiEmployeeHeader'

/**
 * EmployeeColumn - Employee column in multi-employee view
 */
export const EmployeeColumn = forwardRef(({ 
  children, 
  className,
  employeeId,
  ...props 
}, ref) => {
  const { baseStyles } = useCalendarStyles()
  
  return (
    <div 
      ref={ref}
      className={cn(baseStyles.employeeColumn, className)}
      data-employee-id={employeeId}
      {...props}
    >
      {children}
    </div>
  )
})

EmployeeColumn.displayName = 'EmployeeColumn'

/**
 * EmployeeHeaderCompact - Compact employee header
 */
export const EmployeeHeaderCompact = forwardRef(({ 
  children, 
  className,
  ...props 
}, ref) => {
  const { baseStyles } = useCalendarStyles()
  
  return (
    <div 
      ref={ref}
      className={cn(baseStyles.employeeHeaderCompact, className)}
      {...props}
    >
      {children}
    </div>
  )
})

EmployeeHeaderCompact.displayName = 'EmployeeHeaderCompact'

/**
 * EmployeeAvatarCompact - Compact employee avatar
 */
export const EmployeeAvatarCompact = forwardRef(({ 
  children, 
  className,
  ...props 
}, ref) => {
  const { baseStyles } = useCalendarStyles()
  
  return (
    <div 
      ref={ref}
      className={cn(baseStyles.employeeAvatarCompact, className)}
      {...props}
    >
      {children}
    </div>
  )
})

EmployeeAvatarCompact.displayName = 'EmployeeAvatarCompact'

/**
 * EmployeeNameCompact - Compact employee name
 */
export const EmployeeNameCompact = forwardRef(({ 
  children, 
  className,
  ...props 
}, ref) => {
  const { baseStyles } = useCalendarStyles()
  
  return (
    <div 
      ref={ref}
      className={cn(baseStyles.employeeNameCompact, className)}
      {...props}
    >
      {children}
    </div>
  )
})

EmployeeNameCompact.displayName = 'EmployeeNameCompact'

// Export all layout components
export default {
  CalendarContainer,
  CalendarScrollContainer,
  CalendarGrid,
  CalendarHeader,
  TimeSlot,
  TimeAxis,
  WeekGrid,
  WeekGridDayHeader,
  WeekGridDay,
  MonthGridDay,
  MonthGridDayNumber,
  CurrentTimeIndicator,
  MultiEmployeeHeader,
  EmployeeColumn,
  EmployeeHeaderCompact,
  EmployeeAvatarCompact,
  EmployeeNameCompact
} 