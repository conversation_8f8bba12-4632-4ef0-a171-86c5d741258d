import React from 'react'

/**
 * Calendar Container Component
 * Handles the main calendar layout and loading states
 */
const CalendarContainer = ({ children, isLoading }) => {
  if (isLoading) {
    return <LoadingSpinner />
  }

  return (
    <div className="w-full h-full">
      {children}
    </div>
  )
}

/**
 * Loading Spinner Component
 */
const LoadingSpinner = () => (
  <div className="flex h-full items-center justify-center bg-gray-50">
    <div className="text-center">
      <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
      <p className="text-gray-600">Loading calendar...</p>
    </div>
  </div>
)

export default CalendarContainer 