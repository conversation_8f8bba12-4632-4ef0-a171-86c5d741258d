import React, { useState, useRef } from 'react'
import { 
  MagnifyingGlassIcon, 
  UserPlusIcon, 
  UserIcon,
  PhoneIcon,
  EnvelopeIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline'
import QuickCustomerCreateModal from './QuickCustomerCreateModal'
import { useCustomers } from '../../../customers/hooks/useCustomers'

/**
 * CustomerSelectionStep - Efficient customer search and selection with iOS-like performance
 * Features: Minimal data loading, client-side filtering, instant search results
 */
const CustomerSelectionStep = ({
  selectedCustomer,
  onCustomerSelect,
  validationErrors = {}
}) => {
  // Use the efficient customer hook (loads minimal data, caches it, fast filtering)
  const {
    allCustomers,
    filteredCustomers,
    customersCount,
    filteredCount,
    isLoading,
    error,
    searchTerm,
    setSearchTerm,
    clearSearch,
    addCustomerToCache
  } = useCustomers()

  // Local UI state
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [focusedIndex, setFocusedIndex] = useState(-1)
  const searchInputRef = useRef(null)

  // Focus search input when not loading
  React.useEffect(() => {
    if (!isLoading && searchInputRef.current) {
      searchInputRef.current.focus()
    }
  }, [isLoading])

  // Keyboard navigation
  const handleKeyDown = (e) => {
    if (filteredCustomers.length === 0) return

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        setFocusedIndex(prev => Math.min(prev + 1, filteredCustomers.length - 1))
        break
      case 'ArrowUp':
        e.preventDefault()
        setFocusedIndex(prev => Math.max(prev - 1, -1))
        break
      case 'Enter':
        e.preventDefault()
        if (focusedIndex >= 0 && focusedIndex < filteredCustomers.length) {
          handleCustomerSelect(filteredCustomers[focusedIndex])
        }
        break
      case 'Escape':
        setFocusedIndex(-1)
        clearSearch()
        break
    }
  }

  // Handle customer selection
  const handleCustomerSelect = (customer) => {
    console.log('👤 Selected customer:', customer)
    onCustomerSelect(customer)
    clearSearch()
    setFocusedIndex(-1)
  }

  // Get customer initials for avatar
  const getCustomerInitials = (name) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  // Handle new customer created
  const handleCustomerCreated = (newCustomer) => {
    // Add new customer to cache and select it
    addCustomerToCache(newCustomer)
    handleCustomerSelect(newCustomer)
    setShowCreateForm(false)
  }

  return (
    <div className="p-6 space-y-6">
      {/* Step Header */}
      <div className="text-center">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Select a Customer
        </h3>
        <p className="text-gray-600">
          Choose from existing customers or create a new one
        </p>
      </div>

      {/* Search Section */}
      <div className="space-y-4">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
          </div>
          <input
            ref={searchInputRef}
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Search customers by name, email, or phone..."
            className={`
              block w-full pl-10 pr-3 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors
              ${validationErrors.customer ? 'border-red-300' : 'border-gray-300'}
            `}
            disabled={isLoading}
          />
          {isLoading && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
              <div className="w-5 h-5 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
            </div>
          )}
        </div>

        {/* Create New Customer Button */}
        <div className="flex justify-center">
          <button
            onClick={() => setShowCreateForm(true)}
            className="inline-flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            disabled={isLoading}
          >
            <UserPlusIcon className="h-4 w-4" />
            <span>Create New Customer</span>
          </button>
        </div>

        {/* Validation Error */}
        {validationErrors.customer && (
          <p className="text-sm text-red-600">{validationErrors.customer}</p>
        )}

        {/* API Error */}
        {error && (
          <p className="text-sm text-red-600">Failed to load customers. Please try again.</p>
        )}
      </div>

      {/* Selected Customer Display */}
      {selectedCustomer && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
                <span className="text-white font-medium">
                  {getCustomerInitials(selectedCustomer.name)}
                </span>
              </div>
              <div className="flex-1">
                <h4 className="font-medium text-gray-900">{selectedCustomer.name}</h4>
                <div className="text-sm text-gray-600 space-y-1">
                  {selectedCustomer.email && (
                    <div className="flex items-center space-x-2">
                      <EnvelopeIcon className="h-4 w-4" />
                      <span>{selectedCustomer.email}</span>
                    </div>
                  )}
                  {selectedCustomer.phone && (
                    <div className="flex items-center space-x-2">
                      <PhoneIcon className="h-4 w-4" />
                      <span>{selectedCustomer.phone}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
            <button
              onClick={() => {
                onCustomerSelect(null)
                clearSearch()
                searchInputRef.current?.focus()
              }}
              className="text-sm text-blue-600 hover:text-blue-800 font-medium"
            >
              Change
            </button>
          </div>
        </div>
      )}

      {/* Customer List */}
      {isLoading ? (
        <div className="text-center py-8">
          <div className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading customers...</p>
        </div>
      ) : filteredCustomers.length > 0 ? (
        <div className="border border-gray-200 rounded-lg shadow-sm max-h-96 overflow-y-auto">
          <div className="p-3 bg-gray-50 border-b border-gray-200">
            <h4 className="text-sm font-medium text-gray-700">
              {searchTerm 
                ? `Search Results (${filteredCount} of ${customersCount})` 
                : `All Customers (${customersCount})`
              }
            </h4>
          </div>
          {filteredCustomers.map((customer, index) => (
            <button
              key={customer.id}
              onClick={() => handleCustomerSelect(customer)}
              className={`
                w-full flex items-center space-x-3 p-4 text-left hover:bg-gray-50 transition-colors border-b border-gray-100 last:border-b-0
                ${focusedIndex === index ? 'bg-blue-50 border-blue-200' : ''}
                ${selectedCustomer?.id === customer.id ? 'bg-green-50 border-green-200' : ''}
              `}
            >
              <div className="w-10 h-10 bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0">
                <span className="text-white text-sm font-medium">
                  {getCustomerInitials(customer.name)}
                </span>
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="font-medium text-gray-900 truncate">{customer.name}</h4>
                <div className="text-sm text-gray-600 space-y-1">
                  {customer.email && (
                    <div className="flex items-center space-x-2">
                      <EnvelopeIcon className="h-3 w-3" />
                      <span className="truncate">{customer.email}</span>
                    </div>
                  )}
                  {customer.phone && (
                    <div className="flex items-center space-x-2">
                      <PhoneIcon className="h-3 w-3" />
                      <span>{customer.phone}</span>
                    </div>
                  )}
                </div>
              </div>
              <div className="text-right text-sm text-gray-500">
                {customer.has_upcoming_appointment && (
                  <div className="text-blue-600 font-medium">Upcoming</div>
                )}
              </div>
              <ChevronRightIcon className="h-5 w-5 text-gray-400" />
            </button>
          ))}
        </div>
      ) : (
        <div className="text-center py-8 text-gray-500">
          <UserIcon className="h-12 w-12 mx-auto mb-3 text-gray-300" />
          <p className="text-lg mb-2">
            {searchTerm ? 'No customers found' : 'No customers yet'}
          </p>
          <p className="text-sm mb-4">
            {searchTerm 
              ? 'Try a different search term or create a new customer'
              : 'Create your first customer to get started'
            }
          </p>
          <button
            onClick={() => setShowCreateForm(true)}
            className="inline-flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <UserPlusIcon className="h-4 w-4" />
            <span>Create New Customer</span>
          </button>
        </div>
      )}

      {/* Quick Customer Creation Modal */}
      <QuickCustomerCreateModal
        isOpen={showCreateForm}
        onClose={() => setShowCreateForm(false)}
        onCustomerCreated={handleCustomerCreated}
        prefillName={searchTerm}
      />
    </div>
  )
}

export default CustomerSelectionStep 