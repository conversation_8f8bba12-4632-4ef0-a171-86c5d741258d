import React, { useState, useEffect } from 'react'
import { 
  ChevronDownIcon, 
  ChevronRightIcon,
  ClockIcon,
  CurrencyDollarIcon,
  PlusIcon,
  MinusIcon,
  CheckIcon
} from '@heroicons/react/24/outline'
import apiClient from '../../../employees/services/apiClient'

/**
 * ServiceSelectionStep - Desktop-optimized service and add-ons selection
 * Features: Categorized services, pricing display, add-ons with real API integration
 */
const ServiceSelectionStep = ({
  selectedService,
  selectedAddOns = [],
  onServiceSelect,
  onAddOnsSelect,
  validationErrors = {}
}) => {
  const [expandedCategories, setExpandedCategories] = useState(new Set())
  const [serviceCategories, setServiceCategories] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(null)
  const [isLoadingServiceDetails, setIsLoadingServiceDetails] = useState(false)

  // Fetch services and categories from API
  useEffect(() => {
    const fetchServicesAndCategories = async () => {
      try {
        setIsLoading(true)
        setError(null)
        
        console.log('🔍 Fetching services and categories...')
        
        // Fetch both services and categories
        const [servicesResponse, categoriesResponse] = await Promise.all([
          apiClient.get('/services/'),
          apiClient.get('/service-categories/')
        ])
        
        console.log('✅ Services response:', servicesResponse.data)
        console.log('✅ Categories response:', categoriesResponse.data)
        
        // Process services data
        let services = []
        if (servicesResponse.data.results) {
          services = servicesResponse.data.results
        } else if (Array.isArray(servicesResponse.data)) {
          services = servicesResponse.data
        }
        
        // Process categories data
        let categories = []
        if (categoriesResponse.data.results) {
          categories = categoriesResponse.data.results
        } else if (Array.isArray(categoriesResponse.data)) {
          categories = categoriesResponse.data
        }
        
        // Group services by category
        const categorizedServices = categories.map(category => ({
          id: category.id,
          name: category.name,
          icon: category.name.includes('Hair') ? '✂️' : 
                category.name.includes('Color') ? '🎨' : 
                category.name.includes('Treatment') ? '💆' : '💇',
          services: services.filter(service => service.category === category.id).map(service => ({
            id: service.id,
            name: service.name,
            description: service.description || 'Professional service',
            price: parseFloat(service.price || 0),
            duration: parseInt(service.duration || 60),
            bufferTime: parseInt(service.buffer_time || 0),
            category: service.category,
            categoryName: service.category_name,
            addOns: [], // Will be loaded when service is selected
            _originalData: service
          }))
        })).filter(category => category.services.length > 0)
        
        console.log('📋 Processed categories with services:', categorizedServices)
        
        setServiceCategories(categorizedServices)
        
        // Expand first category by default
        if (categorizedServices.length > 0) {
          setExpandedCategories(new Set([categorizedServices[0].id]))
        }
        
      } catch (error) {
        console.error('❌ Failed to fetch services:', error)
        setError('Failed to load services. Please try again.')
        
        // Fallback to minimal mock data
        setServiceCategories([
          {
            id: 'general',
            name: 'General Services',
            icon: '💇',
            services: [
              {
                id: 'service-1',
                name: 'Standard Service',
                description: 'General service',
                price: 50,
                duration: 60,
                bufferTime: 15,
                addOns: []
              }
            ]
          }
        ])
        setExpandedCategories(new Set(['general']))
      } finally {
        setIsLoading(false)
      }
    }

    fetchServicesAndCategories()
  }, [])

  // Fetch detailed service information including add-ons
  const fetchServiceDetails = async (service) => {
    try {
      setIsLoadingServiceDetails(true)
      console.log('🔍 Fetching service details for:', service.id)
      
      const response = await apiClient.get(`/services/${service.id}/`)
      const serviceDetails = response.data
      
      console.log('✅ Service details response:', serviceDetails)
      
      // Map add-ons from Django API format to frontend format
      const mappedAddOns = serviceDetails.addons?.map(addon => ({
        id: addon.addon.id,
        name: addon.addon.name,
        description: addon.addon.description,
        price: parseFloat(addon.price || 0),
        duration: parseInt(addon.duration || 0),
        color: addon.addon.color,
        isActive: addon.is_active,
        isRequired: addon.is_required,
        displayOrder: addon.display_order,
        _originalData: addon
      })) || []
      
      console.log('📋 Mapped add-ons:', mappedAddOns)
      
      // Return enhanced service with add-ons
      return {
        ...service,
        addOns: mappedAddOns,
        _detailedData: serviceDetails
      }
      
    } catch (error) {
      console.error('❌ Failed to fetch service details:', error)
      // Return service without add-ons on error
      return {
        ...service,
        addOns: []
      }
    } finally {
      setIsLoadingServiceDetails(false)
    }
  }

  // Toggle category expansion
  const toggleCategory = (categoryId) => {
    setExpandedCategories(prev => {
      const newSet = new Set(prev)
      if (newSet.has(categoryId)) {
        newSet.delete(categoryId)
      } else {
        newSet.add(categoryId)
      }
      return newSet
    })
  }

  // Enhanced service selection with add-ons fetching
  const handleServiceSelect = async (service) => {
    console.log('🎯 Selected service:', service)
    
    // Fetch detailed service information with add-ons
    const serviceWithAddOns = await fetchServiceDetails(service)
    
    // Keep compatible add-ons when switching services
    const compatibleAddOns = selectedAddOns.filter(addOn => 
      serviceWithAddOns.addOns?.some(serviceAddOn => serviceAddOn.id === addOn.id)
    )
    
    onServiceSelect(serviceWithAddOns)
    onAddOnsSelect(compatibleAddOns)
    
    // Auto-calculate updated pricing and duration
    const serviceDuration = (serviceWithAddOns.duration || 0) + (serviceWithAddOns.bufferTime || 0)
    const addOnsDuration = compatibleAddOns.reduce((sum, addOn) => sum + (addOn.duration || 0), 0)
    const totalDuration = serviceDuration + addOnsDuration
    
    console.log('📊 Service calculations:', {
      servicePrice: serviceWithAddOns.price,
      serviceDuration,
      addOnsDuration,
      totalDuration,
      availableAddOns: serviceWithAddOns.addOns?.length || 0
    })
  }

  // Handle add-on toggle
  const handleAddOnToggle = (addOn) => {
    const isSelected = selectedAddOns.some(item => item.id === addOn.id)
    if (isSelected) {
      onAddOnsSelect(selectedAddOns.filter(item => item.id !== addOn.id))
    } else {
      onAddOnsSelect([...selectedAddOns, addOn])
    }
  }

  // Calculate totals
  const calculateTotals = () => {
    if (!selectedService) return { totalPrice: 0, totalDuration: 0 }
    
    const servicePrice = selectedService.price || 0
    const serviceDuration = (selectedService.duration || 0) + (selectedService.bufferTime || 0)
    
    const addOnsPrice = selectedAddOns.reduce((sum, addOn) => sum + (addOn.price || 0), 0)
    const addOnsDuration = selectedAddOns.reduce((sum, addOn) => sum + (addOn.duration || 0), 0)
    
    return {
      totalPrice: servicePrice + addOnsPrice,
      totalDuration: serviceDuration + addOnsDuration
    }
  }

  const { totalPrice, totalDuration } = calculateTotals()

  // Format duration for display
  const formatDuration = (minutes) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    if (hours > 0) {
      return `${hours}h ${mins > 0 ? `${mins}m` : ''}`
    }
    return `${mins}m`
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="p-6 space-y-6">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Choose Service</h3>
          <p className="text-gray-600">Loading available services...</p>
        </div>
        <div className="flex justify-center">
          <div className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Step Header */}
      <div className="text-center">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Choose Service
        </h3>
        <p className="text-gray-600">
          Select the main service and any additional treatments
        </p>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-600">{error}</p>
        </div>
      )}

      {/* Service Categories */}
      <div className="space-y-4">
        {serviceCategories.map(category => (
          <div key={category.id} className="border border-gray-200 rounded-lg overflow-hidden">
            <button
              onClick={() => toggleCategory(category.id)}
              className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center space-x-3">
                <span className="text-2xl">{category.icon}</span>
                <h4 className="font-medium text-gray-900">{category.name}</h4>
                <span className="text-sm text-gray-500">({category.services.length} services)</span>
              </div>
              {expandedCategories.has(category.id) ? (
                <ChevronDownIcon className="h-5 w-5 text-gray-400" />
              ) : (
                <ChevronRightIcon className="h-5 w-5 text-gray-400" />
              )}
            </button>

            {expandedCategories.has(category.id) && (
              <div className="border-t border-gray-200">
                {category.services.map(service => (
                  <div
                    key={service.id}
                    className={`
                      border-b border-gray-100 last:border-b-0 transition-colors
                      ${selectedService?.id === service.id ? 'bg-blue-50' : 'hover:bg-gray-50'}
                    `}
                  >
                    <button
                      onClick={() => handleServiceSelect(service)}
                      disabled={isLoadingServiceDetails && selectedService?.id === service.id}
                      className="w-full p-4 text-left space-y-2 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <h5 className="font-medium text-gray-900">{service.name}</h5>
                            {selectedService?.id === service.id && !isLoadingServiceDetails && (
                              <CheckIcon className="h-5 w-5 text-blue-600" />
                            )}
                            {isLoadingServiceDetails && selectedService?.id === service.id && (
                              <div className="w-5 h-5 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
                            )}
                          </div>
                          <p className="text-sm text-gray-600 mt-1">{service.description}</p>
                        </div>
                        <div className="text-right ml-4">
                          <div className="flex items-center space-x-1 text-green-600 font-medium">
                            <CurrencyDollarIcon className="h-4 w-4" />
                            <span>${service.price}</span>
                          </div>
                          <div className="flex items-center space-x-1 text-gray-500 text-sm">
                            <ClockIcon className="h-3 w-3" />
                            <span>{formatDuration(service.duration)}</span>
                            {service.bufferTime > 0 && (
                              <span className="text-xs text-gray-400">
                                (+{formatDuration(service.bufferTime)} buffer)
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* No Services Message */}
      {!isLoading && serviceCategories.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <p className="text-lg mb-2">No services available</p>
          <p className="text-sm">Please contact your administrator to add services.</p>
        </div>
      )}

      {/* Validation Error */}
      {validationErrors.service && (
        <p className="text-sm text-red-600">{validationErrors.service}</p>
      )}

      {/* Add-Ons Section */}
      {selectedService && (
        <div className="border border-gray-200 rounded-lg">
          <div className="p-4 border-b border-gray-200 bg-gray-50">
            <h4 className="font-medium text-gray-900">Available Add-Ons</h4>
            <p className="text-sm text-gray-600">Enhance your service with these additional treatments</p>
          </div>
          <div className="p-4">
            {isLoadingServiceDetails ? (
              <div className="flex items-center justify-center py-8">
                <div className="w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mr-2" />
                <span className="text-gray-600">Loading add-ons...</span>
              </div>
            ) : selectedService.addOns && selectedService.addOns.length > 0 ? (
              <div className="space-y-3">
                {selectedService.addOns.map(addOn => {
                  const isSelected = selectedAddOns.some(item => item.id === addOn.id)
                  return (
                    <button
                      key={addOn.id}
                      onClick={() => handleAddOnToggle(addOn)}
                      className={`
                        w-full flex items-center justify-between p-3 rounded-lg border transition-colors
                        ${isSelected 
                          ? 'border-blue-300 bg-blue-50' 
                          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                        }
                      `}
                    >
                      <div className="flex items-center space-x-3">
                        <div className={`
                          w-5 h-5 rounded border-2 flex items-center justify-center
                          ${isSelected ? 'border-blue-600 bg-blue-600' : 'border-gray-300'}
                        `}>
                          {isSelected && <CheckIcon className="h-3 w-3 text-white" />}
                        </div>
                        <div className="text-left">
                          <h5 className="font-medium text-gray-900">{addOn.name}</h5>
                          {addOn.description && (
                            <p className="text-xs text-gray-500 mt-0.5">{addOn.description}</p>
                          )}
                          <div className="flex items-center space-x-3 text-sm text-gray-600 mt-1">
                            <span className="flex items-center space-x-1">
                              <CurrencyDollarIcon className="h-3 w-3" />
                              <span>${addOn.price}</span>
                            </span>
                            <span className="flex items-center space-x-1">
                              <ClockIcon className="h-3 w-3" />
                              <span>{formatDuration(addOn.duration)}</span>
                            </span>
                          </div>
                        </div>
                      </div>
                      {isSelected ? (
                        <MinusIcon className="h-5 w-5 text-blue-600" />
                      ) : (
                        <PlusIcon className="h-5 w-5 text-gray-400" />
                      )}
                    </button>
                  )
                })}
              </div>
            ) : (
              <div className="text-center py-6 text-gray-500">
                <p className="text-sm">No add-ons available for this service</p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Service Summary */}
      {selectedService && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h4 className="font-medium text-gray-900 mb-3">Service Summary</h4>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">{selectedService.name}</span>
              <div className="flex items-center space-x-4 text-sm">
                <span className="flex items-center space-x-1">
                  <CurrencyDollarIcon className="h-4 w-4 text-green-600" />
                  <span className="text-green-600 font-medium">${selectedService.price}</span>
                </span>
                <span className="flex items-center space-x-1">
                  <ClockIcon className="h-4 w-4 text-gray-500" />
                  <span className="text-gray-500">
                    {formatDuration(selectedService.duration)}
                    {selectedService.bufferTime > 0 && (
                      <span className="text-xs text-gray-400 ml-1">
                        (+{formatDuration(selectedService.bufferTime)} buffer)
                      </span>
                    )}
                  </span>
                </span>
              </div>
            </div>

            {selectedAddOns.map(addOn => (
              <div key={addOn.id} className="flex justify-between items-center text-sm">
                <span className="text-gray-600">+ {addOn.name}</span>
                <div className="flex items-center space-x-4">
                  <span className="flex items-center space-x-1">
                    <CurrencyDollarIcon className="h-3 w-3 text-green-600" />
                    <span className="text-green-600">${addOn.price}</span>
                  </span>
                  <span className="flex items-center space-x-1">
                    <ClockIcon className="h-3 w-3 text-gray-500" />
                    <span className="text-gray-500">{formatDuration(addOn.duration)}</span>
                  </span>
                </div>
              </div>
            ))}

            <div className="border-t border-green-300 pt-2 mt-3">
              <div className="flex justify-between items-center font-medium">
                <span className="text-gray-900">Total</span>
                <div className="flex items-center space-x-4">
                  <span className="flex items-center space-x-1">
                    <CurrencyDollarIcon className="h-4 w-4 text-green-600" />
                    <span className="text-green-600 font-semibold">${totalPrice}</span>
                  </span>
                  <span className="flex items-center space-x-1">
                    <ClockIcon className="h-4 w-4 text-gray-700" />
                    <span className="text-gray-700">{formatDuration(totalDuration)}</span>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default ServiceSelectionStep 