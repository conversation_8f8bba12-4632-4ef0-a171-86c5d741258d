import { customerApi } from '../../customers/services/customerApi'
import apiClient from '../../employees/services/apiClient'

/**
 * Customer Lookup Service
 * Provides efficient customer name lookups for appointment blocks
 */
class CustomerLookupService {
  constructor() {
    this.customerCache = new Map()
    this.employeeCache = new Map()
    this.customerLoadingPromise = null
    this.employeeLoadingPromise = null
  }

  /**
   * Get customer name by ID
   * @param {number|string} customerId - Customer ID
   * @returns {Promise<string>} Customer name
   */
  async getCustomerName(customerId) {
    if (!customerId) return 'Customer'
    
    // Normalize customer ID to string for consistent lookup
    const normalizedId = customerId.toString()
    
    console.log('🔍 CustomerLookupService: Attempting to get customer name for ID:', normalizedId)
    
    // Check cache first
    if (this.customerCache.has(normalizedId)) {
      const cachedName = this.customerCache.get(normalizedId)
      console.log('✅ CustomerLookupService: Found cached name:', cachedName)
      return cachedName
    }
    
    // Also check if we have it stored as a number
    if (this.customerCache.has(customerId)) {
      const cachedName = this.customerCache.get(customerId)
      console.log('✅ CustomerLookupService: Found cached name (original type):', cachedName)
      return cachedName
    }
    
    try {
      // Load all customers and wait for completion
      await this.loadAllCustomers()
      
      // Try both string and original ID
      const customerName = this.customerCache.get(normalizedId) || 
                          this.customerCache.get(customerId) || 
                          `Customer ${customerId}`
      console.log('✅ CustomerLookupService: Resolved customer name:', customerName)
      return customerName
    } catch (error) {
      console.warn('⚠️ CustomerLookupService: Failed to load customers, using fallback name:', error)
      // Return a fallback name if lookup fails
      return `Customer ${customerId}`
    }
  }

  /**
   * Load all customers into cache
   */
  async loadAllCustomers() {
    // If already loading, return the existing promise
    if (this.customerLoadingPromise) {
      return this.customerLoadingPromise
    }
    
    // Create and store the loading promise
    this.customerLoadingPromise = this._doLoadCustomers()
    
    try {
      await this.customerLoadingPromise
    } finally {
      // Clear the promise when done (success or failure)
      this.customerLoadingPromise = null
    }
  }

  /**
   * Internal method to actually load customers
   */
  async _doLoadCustomers() {
    try {
      console.log('📋 CustomerLookupService: Loading customers for appointment display...')
      const customers = await customerApi.getCustomers()
      console.log('📋 CustomerLookupService: Raw customer API response:', customers)
      
      // Handle different response formats
      let customerList = []
      if (customers.results) {
        customerList = customers.results
        console.log('📋 CustomerLookupService: Using paginated results format')
      } else if (Array.isArray(customers)) {
        customerList = customers
        console.log('📋 CustomerLookupService: Using direct array format')
      } else {
        console.warn('⚠️ CustomerLookupService: Unexpected customer API response format:', typeof customers)
        return // Exit early if format is unexpected
      }
      
      console.log(`📋 CustomerLookupService: Processing ${customerList.length} customers`)
      
      // Cache customer names by ID
      customerList.forEach(customer => {
        let name = customer.name
        if (!name) {
          const firstName = customer.first_name || customer.customer?.user?.first_name || ''
          const lastName = customer.last_name || customer.customer?.user?.last_name || ''
          name = `${firstName} ${lastName}`.trim()
        }
        
        // Ensure we have a reasonable fallback
        const finalName = name || `Customer ${customer.id}`
        
        // Cache both the original ID and string version for consistent lookup
        this.customerCache.set(customer.id, finalName)
        this.customerCache.set(customer.id.toString(), finalName)
        
        console.log(`📋 CustomerLookupService: Cached customer ${customer.id} (both types): "${finalName}"`)
      })
      
      console.log(`✅ CustomerLookupService: Successfully cached ${customerList.length} customer names`)
      console.log('🗂️ CustomerLookupService: Cache contents:', Array.from(this.customerCache.entries()))
      
    } catch (error) {
      console.error('❌ CustomerLookupService: Failed to load customers for appointment display:', error)
      console.error('📍 CustomerLookupService: Error details:', {
        message: error.message,
        name: error.name,
        stack: error.stack
      })
      throw error // Re-throw to let caller handle
    }
  }

  /**
   * Get employee name by ID
   * @param {number} employeeId - Employee ID
   * @returns {Promise<string>} Employee name
   */
  async getEmployeeName(employeeId) {
    if (!employeeId) return 'Employee'
    
    // Check cache first
    if (this.employeeCache.has(employeeId)) {
      return this.employeeCache.get(employeeId)
    }
    
    // Load all employees and wait for completion
    await this.loadAllEmployees()
    
    // Return actual employee name or fallback
    return this.employeeCache.get(employeeId) || `Employee ${employeeId}`
  }

  /**
   * Load all employees into cache
   */
  async loadAllEmployees() {
    // If already loading, return the existing promise
    if (this.employeeLoadingPromise) {
      return this.employeeLoadingPromise
    }
    
    // Create and store the loading promise
    this.employeeLoadingPromise = this._doLoadEmployees()
    
    try {
      await this.employeeLoadingPromise
    } finally {
      // Clear the promise when done (success or failure)
      this.employeeLoadingPromise = null
    }
  }

  /**
   * Internal method to actually load employees
   */
  async _doLoadEmployees() {
    try {
      console.log('👥 Loading employees for appointment display...')
      
      // Import employeeApiService dynamically to avoid circular imports
      const { employeeApiService } = await import('../../employees/services')
      const employees = await employeeApiService.getAllEmployees()
      
      console.log('👥 CustomerLookupService: Raw employee API response:', employees)
      
      // Cache employee names by ID
      employees.forEach(employee => {
        const name = employee.full_name || employee.name || `Employee ${employee.id}`
        
        // Cache both the original ID and string version for consistent lookup
        this.employeeCache.set(employee.id, name)
        this.employeeCache.set(employee.id.toString(), name)
        
        console.log(`👥 CustomerLookupService: Cached employee ${employee.id} (both types): "${name}"`)
      })
      
      console.log(`✅ CustomerLookupService: Successfully cached ${employees.length} employee names`)
      
    } catch (error) {
      console.error('❌ Failed to load employees for appointment display:', error)
      throw error // Re-throw to let caller handle
    }
  }

  /**
   * Update customer name in cache
   * @param {number} customerId - Customer ID
   * @param {string} name - Customer name
   */
  updateCustomerName(customerId, name) {
    this.customerCache.set(customerId, name)
  }

  /**
   * Update employee name in cache
   * @param {number} employeeId - Employee ID
   * @param {string} name - Employee name
   */
  updateEmployeeName(employeeId, name) {
    this.employeeCache.set(employeeId, name)
  }

  /**
   * Clear all caches
   */
  clearCache() {
    this.customerCache.clear()
    this.employeeCache.clear()
  }
}

/**
 * ✅ iOS PATTERN: Customer cache with localStorage persistence like CoreData
 */
class CustomerCache {
  constructor() {
    this.customers = new Map()
    this.employees = new Map()
    this.lastCustomerFetch = null
    this.lastEmployeeFetch = null
    this.CACHE_TTL = 30 * 60 * 1000 // 30 minutes like iOS
    
    // ✅ iOS PATTERN: Local persistence keys
    this.CUSTOMER_STORAGE_KEY = 'chatbook_customers_cache'
    this.EMPLOYEE_STORAGE_KEY = 'chatbook_employees_cache'
    this.CUSTOMER_TIMESTAMP_KEY = 'chatbook_customers_timestamp'
    this.EMPLOYEE_TIMESTAMP_KEY = 'chatbook_employees_timestamp'
    
    // ✅ iOS PATTERN: Load from localStorage on startup
    this.loadFromLocalStorage()
  }

  /**
   * ✅ iOS PATTERN: Load cached data from localStorage
   */
  loadFromLocalStorage() {
    try {
      // Load customers
      const customerData = localStorage.getItem(this.CUSTOMER_STORAGE_KEY)
      const customerTimestamp = localStorage.getItem(this.CUSTOMER_TIMESTAMP_KEY)
      
      if (customerData && customerTimestamp) {
        const parsedCustomers = JSON.parse(customerData)
        const parsedTimestamp = parseInt(customerTimestamp)
        
        if ((Date.now() - parsedTimestamp) < this.CACHE_TTL) {
          console.log('💾 [iOS Pattern] Loading customers from localStorage')
          this.customers.clear()
          parsedCustomers.forEach(customer => {
            this.customers.set(customer.id?.toString(), customer)
          })
          this.lastCustomerFetch = parsedTimestamp
          console.log(`✅ [iOS Pattern] Loaded ${this.customers.size} customers from localStorage`)
        }
      }

      // Load employees
      const employeeData = localStorage.getItem(this.EMPLOYEE_STORAGE_KEY)
      const employeeTimestamp = localStorage.getItem(this.EMPLOYEE_TIMESTAMP_KEY)
      
      if (employeeData && employeeTimestamp) {
        const parsedEmployees = JSON.parse(employeeData)
        const parsedTimestamp = parseInt(employeeTimestamp)
        
        if ((Date.now() - parsedTimestamp) < this.CACHE_TTL) {
          console.log('💾 [iOS Pattern] Loading employees from localStorage')
          this.employees.clear()
          parsedEmployees.forEach(employee => {
            this.employees.set(employee.id?.toString(), employee)
          })
          this.lastEmployeeFetch = parsedTimestamp
          console.log(`✅ [iOS Pattern] Loaded ${this.employees.size} employees from localStorage`)
        }
      }
    } catch (error) {
      console.warn('❌ [iOS Pattern] Failed to load from localStorage:', error)
    }
  }

  /**
   * ✅ iOS PATTERN: Save to localStorage
   */
  saveCustomersToLocalStorage() {
    try {
      const customersArray = Array.from(this.customers.values())
      localStorage.setItem(this.CUSTOMER_STORAGE_KEY, JSON.stringify(customersArray))
      localStorage.setItem(this.CUSTOMER_TIMESTAMP_KEY, this.lastCustomerFetch.toString())
      console.log(`💾 [iOS Pattern] Saved ${customersArray.length} customers to localStorage`)
    } catch (error) {
      console.warn('❌ [iOS Pattern] Failed to save customers to localStorage:', error)
    }
  }

  saveEmployeesToLocalStorage() {
    try {
      const employeesArray = Array.from(this.employees.values())
      localStorage.setItem(this.EMPLOYEE_STORAGE_KEY, JSON.stringify(employeesArray))
      localStorage.setItem(this.EMPLOYEE_TIMESTAMP_KEY, this.lastEmployeeFetch.toString())
      console.log(`💾 [iOS Pattern] Saved ${employeesArray.length} employees to localStorage`)
    } catch (error) {
      console.warn('❌ [iOS Pattern] Failed to save employees to localStorage:', error)
    }
  }

  /**
   * ✅ iOS PATTERN: Check cache validity
   */
  isCustomerCacheValid() {
    return this.lastCustomerFetch && (Date.now() - this.lastCustomerFetch) < this.CACHE_TTL
  }

  isEmployeeCacheValid() {
    return this.lastEmployeeFetch && (Date.now() - this.lastEmployeeFetch) < this.CACHE_TTL
  }

  /**
   * ✅ iOS PATTERN: Batch preload customers
   */
  async preloadCustomers() {
    if (this.isCustomerCacheValid() && this.customers.size > 0) {
      console.log('💾 [iOS Pattern] Customer cache valid, skipping preload')
      return
    }

    try {
      console.log('🔍 [iOS Pattern] Preloading customers...')
      const response = await apiClient.get('/business-customers/me/')
      const allCustomers = response.data.results || response.data
      
      if (!Array.isArray(allCustomers)) {
        console.error('❌ [iOS Pattern] Customer API did not return an array:', typeof allCustomers)
        return
      }
      
      this.customers.clear()
      allCustomers.forEach(customer => {
        this.customers.set(customer.id?.toString(), customer)
      })
      
      this.lastCustomerFetch = Date.now()
      this.saveCustomersToLocalStorage()
      
      console.log(`✅ [iOS Pattern] Preloaded ${allCustomers.length} customers`)
    } catch (error) {
      console.warn('❌ [iOS Pattern] Customer preload failed:', error)
    }
  }

  /**
   * ✅ iOS PATTERN: Batch preload employees
   */
  async preloadEmployees() {
    if (this.isEmployeeCacheValid() && this.employees.size > 0) {
      console.log('💾 [iOS Pattern] Employee cache valid, skipping preload')
      return
    }

    try {
      console.log('🔍 [iOS Pattern] Preloading employees...')
      const response = await apiClient.get('/employees/')
      const allEmployees = response.data.results || response.data
      
      if (!Array.isArray(allEmployees)) {
        console.error('❌ [iOS Pattern] Employee API did not return an array:', typeof allEmployees)
        return
      }
      
      this.employees.clear()
      allEmployees.forEach(employee => {
        this.employees.set(employee.id?.toString(), employee)
      })
      
      this.lastEmployeeFetch = Date.now()
      this.saveEmployeesToLocalStorage()
      
      console.log(`✅ [iOS Pattern] Preloaded ${allEmployees.length} employees`)
    } catch (error) {
      console.warn('❌ [iOS Pattern] Employee preload failed:', error)
    }
  }

  /**
   * ✅ iOS PATTERN: Fast customer lookup from cache
   */
  async getCustomerName(customerId) {
    if (!customerId) return 'Unknown Customer'
    
    const customerIdStr = customerId.toString()
    
    // Check cache first
    if (this.customers.has(customerIdStr)) {
      const customer = this.customers.get(customerIdStr)
      const name = customer.full_name || customer.name || `${customer.first_name || ''} ${customer.last_name || ''}`.trim()
      console.log(`🎯 [iOS Pattern] Customer ${customerId} served from cache: ${name}`)
      return name || `Customer ${customerId}`
    }

    // If not in cache and cache is stale, preload
    if (!this.isCustomerCacheValid()) {
      await this.preloadCustomers()
      
      // Try again after preload
      if (this.customers.has(customerIdStr)) {
        const customer = this.customers.get(customerIdStr)
        const name = customer.full_name || customer.name || `${customer.first_name || ''} ${customer.last_name || ''}`.trim()
        return name || `Customer ${customerId}`
      }
    }

    // Fallback to individual lookup
    console.log(`⚠️ [iOS Pattern] Customer ${customerId} not in cache, using fallback`)
    return `Customer ${customerId}`
  }

  /**
   * ✅ iOS PATTERN: Fast employee lookup from cache
   */
  async getEmployeeName(employeeId) {
    if (!employeeId) return 'Unknown Employee'
    
    const employeeIdStr = employeeId.toString()
    
    // Check cache first
    if (this.employees.has(employeeIdStr)) {
      const employee = this.employees.get(employeeIdStr)
      const name = employee.full_name || employee.name || `${employee.first_name || ''} ${employee.last_name || ''}`.trim()
      console.log(`🎯 [iOS Pattern] Employee ${employeeId} served from cache: ${name}`)
      return name || `Employee ${employeeId}`
    }

    // If not in cache and cache is stale, preload
    if (!this.isEmployeeCacheValid()) {
      await this.preloadEmployees()
      
      // Try again after preload
      if (this.employees.has(employeeIdStr)) {
        const employee = this.employees.get(employeeIdStr)
        const name = employee.full_name || employee.name || `${employee.first_name || ''} ${employee.last_name || ''}`.trim()
        return name || `Employee ${employeeId}`
      }
    }

    // Fallback
    console.log(`⚠️ [iOS Pattern] Employee ${employeeId} not in cache, using fallback`)
    return `Employee ${employeeId}`
  }

  /**
   * ✅ iOS PATTERN: Cache invalidation
   */
  clearCache() {
    console.log('🗑️ [iOS Pattern] Clearing customer/employee cache')
    this.customers.clear()
    this.employees.clear()
    this.lastCustomerFetch = null
    this.lastEmployeeFetch = null
    
    // Clear localStorage
    try {
      localStorage.removeItem(this.CUSTOMER_STORAGE_KEY)
      localStorage.removeItem(this.EMPLOYEE_STORAGE_KEY)
      localStorage.removeItem(this.CUSTOMER_TIMESTAMP_KEY)
      localStorage.removeItem(this.EMPLOYEE_TIMESTAMP_KEY)
    } catch (error) {
      console.warn('❌ [iOS Pattern] Failed to clear localStorage:', error)
    }
  }

  /**
   * ✅ iOS PATTERN: Get cache statistics
   */
  getCacheStats() {
    return {
      customers: {
        size: this.customers.size,
        lastFetch: this.lastCustomerFetch,
        isValid: this.isCustomerCacheValid()
      },
      employees: {
        size: this.employees.size,
        lastFetch: this.lastEmployeeFetch,
        isValid: this.isEmployeeCacheValid()
      }
    }
  }
}

// ✅ iOS PATTERN: Global cache instance
const customerCache = new CustomerCache()

export const customerLookupService = {
  // ✅ iOS PATTERN: Use cached lookups instead of individual API calls
  async getCustomerName(customerId) {
    return await customerCache.getCustomerName(customerId)
  },

  async getEmployeeName(employeeId) {
    return await customerCache.getEmployeeName(employeeId)
  },

  // ✅ iOS PATTERN: Preload data for better performance
  async preloadData() {
    console.log('🚀 [iOS Pattern] Preloading customer and employee data...')
    await Promise.all([
      customerCache.preloadCustomers(),
      customerCache.preloadEmployees()
    ])
  },

  clearCache() {
    customerCache.clearCache()
  },

  getCacheStats() {
    return customerCache.getCacheStats()
  }
}

// ✅ iOS PATTERN: Export preload function for app startup
export const preloadCustomerData = () => {
  // Only preload if user is authenticated
  const token = localStorage.getItem('auth_token')
  if (!token) {
    console.log('🔒 [iOS Pattern] User not authenticated, skipping customer data preload')
    return
  }
  
  customerLookupService.preloadData().catch(error => {
    console.warn('Failed to preload customer data:', error)
  })
} 