import apiClient from '../../employees/services/apiClient'

/**
 * Services API Service
 * Connects to Django backend service endpoints to fetch services and categories
 */
export class ServicesApiService {

  /**
   * Fetch all service categories from Django backend
   */
  async fetchServiceCategories() {
    try {
      let allCategories = []
      let nextUrl = '/service-categories/'
      
      while (nextUrl) {
        const response = await apiClient.get(nextUrl)
        const data = response.data
        
        // Add current page results
        if (data && data.results && Array.isArray(data.results)) {
          allCategories = [...allCategories, ...data.results]
        }
        
        // Update next URL for pagination
        nextUrl = data.next ? data.next.replace(apiClient.defaults.baseURL, '') : null
      }
      
      console.log(`✅ Fetched ${allCategories.length} service categories from Django API`)
      return allCategories
    } catch (error) {
      console.error('Failed to fetch service categories:', error)
      throw new Error(error.response?.data?.message || 'Failed to fetch service categories')
    }
  }

  /**
   * Fetch all services from Django backend
   */
  async fetchServices(params = {}) {
    try {
      let allServices = []
      let nextUrl = '/services/'
      
      // Add query parameters if provided
      if (Object.keys(params).length > 0) {
        const queryString = new URLSearchParams(params).toString()
        nextUrl += `?${queryString}`
      }
      
      while (nextUrl) {
        const response = await apiClient.get(nextUrl)
        const data = response.data
        
        // Add current page results
        if (data && data.results && Array.isArray(data.results)) {
          allServices = [...allServices, ...data.results]
        }
        
        // Update next URL for pagination
        nextUrl = data.next ? data.next.replace(apiClient.defaults.baseURL, '') : null
      }
      
      console.log(`✅ Fetched ${allServices.length} services from Django API`)
      return allServices
    } catch (error) {
      console.error('Failed to fetch services:', error)
      throw new Error(error.response?.data?.message || 'Failed to fetch services')
    }
  }

  /**
   * Fetch services for a specific employee
   */
  async fetchEmployeeServices(employeeId, params = {}) {
    try {
      const response = await apiClient.get(`/employees/${employeeId}/services/`, { params })
      console.log(`✅ Fetched services for employee ${employeeId} from Django API`)
      return response.data
    } catch (error) {
      console.error(`Failed to fetch services for employee ${employeeId}:`, error)
      throw new Error(error.response?.data?.message || 'Failed to fetch employee services')
    }
  }

  /**
   * Transform Django service to frontend calendar format
   */
  transformServiceForCalendar(djangoService) {
    return {
      id: djangoService.id,
      name: djangoService.name,
      description: djangoService.description,
      color: djangoService.color,
      price: parseFloat(djangoService.price || 0),
      duration: djangoService.duration || 60,
      buffer_time: djangoService.buffer_time || 0,
      category: djangoService.category,
      category_name: djangoService.category_name,
      is_active: djangoService.is_active
    }
  }

  /**
   * Transform Django service category to frontend format
   */
  transformCategoryForCalendar(djangoCategory) {
    return {
      id: djangoCategory.id,
      name: djangoCategory.name,
      description: djangoCategory.description,
      color: djangoCategory.color,
      order: djangoCategory.order,
      is_active: djangoCategory.is_active
    }
  }
}

// Export singleton instance
export const servicesApiService = new ServicesApiService() 