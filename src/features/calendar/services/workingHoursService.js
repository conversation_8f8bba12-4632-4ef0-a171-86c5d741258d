import { employeeApiService } from '../../employees/services'

// Working hours data structure matching iOS implementation
export const DAYS_OF_WEEK = {
  MONDAY: { id: 'monday', name: 'Monday', shortName: 'Mon', index: 1 },
  TUESDAY: { id: 'tuesday', name: 'Tuesday', shortName: 'Tue', index: 2 },
  WEDNESDAY: { id: 'wednesday', name: 'Wednesday', shortName: 'Wed', index: 3 },
  THURSDAY: { id: 'thursday', name: 'Thursday', shortName: 'Thu', index: 4 },
  FRIDAY: { id: 'friday', name: 'Friday', shortName: 'Fri', index: 5 },
  SATURDAY: { id: 'saturday', name: 'Saturday', shortName: 'Sat', index: 6 },
  SUNDAY: { id: 'sunday', name: 'Sunday', shortName: 'Sun', index: 0 }
}

export class WorkingHoursManager {
  constructor() {
    this.workingHours = {}
    this.currentEmployeeId = null
    this.currentUserWorkingHours = null
    this.listeners = new Set()
  }

  // Subscribe to working hours changes
  subscribe(listener) {
    this.listeners.add(listener)
    return () => this.listeners.delete(listener)
  }

  // Notify listeners of changes
  notify() {
    console.log('🔔 Broadcasting working hours changes to', this.listeners.size, 'subscribers')
    this.listeners.forEach(listener => listener(this.workingHours))
  }

  // Set current employee info
  setCurrentEmployee(employeeId, workingHours) {
    console.log('👤 Setting current employee:', employeeId, workingHours)
    this.currentEmployeeId = employeeId
    this.currentUserWorkingHours = workingHours
    this.workingHours[employeeId] = workingHours
  }

  // Get working hours for specific employee
  getWorkingHours(employeeId) {
    // If it's the current user, return their actual working hours
    if (employeeId === this.currentEmployeeId && this.currentUserWorkingHours) {
      console.log('🔍 Returning actual working hours for current user:', employeeId)
      return this.currentUserWorkingHours
    }
    
    // For other employees, return default working hours
    const defaultHours = this.generateDefaultWorkingHours()
    console.log('🔍 Returning default working hours for employee:', employeeId)
    return defaultHours
  }

  // Get working hours for specific employee and day
  getWorkingHoursForDay(employeeId, dayName) {
    const hours = this.getWorkingHours(employeeId)
    const standardizedDay = this.standardizeDayName(dayName)
    return hours[standardizedDay] || { isWorking: false, startTime: '09:00', endTime: '17:00' }
  }

  // Check if employee is working on a specific date
  isEmployeeWorking(employeeId, date) {
    const dayName = this.getDayNameFromDate(date)
    const workingHours = this.getWorkingHoursForDay(employeeId, dayName)
    return workingHours.isWorking
  }

  // Get working hour start for employee on date
  getWorkingHourStart(employeeId, date) {
    const dayName = this.getDayNameFromDate(date)
    const workingHours = this.getWorkingHoursForDay(employeeId, dayName)
    return workingHours.isWorking ? parseInt(workingHours.startTime.split(':')[0]) : 9
  }

  // Get working hour end for employee on date
  getWorkingHourEnd(employeeId, date) {
    const dayName = this.getDayNameFromDate(date)
    const workingHours = this.getWorkingHoursForDay(employeeId, dayName)
    return workingHours.isWorking ? parseInt(workingHours.endTime.split(':')[0]) : 17
  }

  // Update working hours for employee (only works for current user)
  updateWorkingHours(employeeId, dayName, workingHours) {
    if (employeeId !== this.currentEmployeeId) {
      console.warn('⚠️ Cannot update working hours for non-current employee:', employeeId)
      return
    }
    
    const standardizedDay = this.standardizeDayName(dayName)
    this.currentUserWorkingHours = { 
      ...this.currentUserWorkingHours, 
      [standardizedDay]: workingHours 
    }
    this.workingHours[employeeId] = this.currentUserWorkingHours
    
    this.notify()
  }

  // Set all working hours for an employee (only works for current user)
  setEmployeeWorkingHours(employeeId, workingHours) {
    console.log('📝 Setting working hours for employee:', employeeId)
    
    if (employeeId === this.currentEmployeeId) {
      console.log('✅ Setting actual working hours for current user')
      this.currentUserWorkingHours = workingHours
    } else {
      console.log('📋 Note: Employee', employeeId, 'is not current user, hours will be defaults')
    }
    
    this.workingHours[employeeId] = workingHours
    
    // Immediately notify all subscribers
    this.notify()
    
    // Force an additional async notification for any delayed subscribers
    setTimeout(() => {
      console.log('⏰ Delayed notification for working hours update')
      this.notify()
    }, 10)
  }

  // Get all working hours
  getAllWorkingHours() {
    return this.workingHours
  }

  // Helper methods
  standardizeDayName(dayName) {
    return dayName.toLowerCase()
  }

  getDayNameFromDate(date) {
    const dateObj = typeof date === 'string' ? new Date(date) : date
    const days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday']
    return days[dateObj.getDay()]
  }

  // Get visual working hours for calendar display
  getVisualWorkingHours(employeeId, date) {
    const dayName = this.getDayNameFromDate(date)
    const workingHours = this.getWorkingHoursForDay(employeeId, dayName)
    
    if (!workingHours.isWorking) {
      return null
    }

    return {
      start: workingHours.startTime,
      end: workingHours.endTime,
      isWorking: true
    }
  }

  // Generate default working hours for new employees
  generateDefaultWorkingHours() {
    return {
      monday: { isWorking: true, startTime: '09:00', endTime: '17:00' },
      tuesday: { isWorking: true, startTime: '09:00', endTime: '17:00' },
      wednesday: { isWorking: true, startTime: '09:00', endTime: '17:00' },
      thursday: { isWorking: true, startTime: '09:00', endTime: '17:00' },
      friday: { isWorking: true, startTime: '09:00', endTime: '17:00' },
      saturday: { isWorking: false, startTime: '09:00', endTime: '17:00' },
      sunday: { isWorking: false, startTime: '09:00', endTime: '17:00' }
    }
  }
}

// Singleton instance
export const workingHoursManager = new WorkingHoursManager()

// Working hours service API - properly handles backend limitations
export const workingHoursService = {
  // Fetch working hours for a specific employee
  async fetchWorkingHours(employeeId) {
    try {
      console.log('🕒 Fetching working hours for employee:', employeeId)
      
      // First, ensure we know who the current user is
      if (!workingHoursManager.currentEmployeeId) {
        console.log('🔍 Current employee not set, determining current user...')
        await this.initializeCurrentEmployee()
      }

      // For current user, fetch from API
      if (employeeId === workingHoursManager.currentEmployeeId) {
        console.log('👤 Fetching actual working hours for current user:', employeeId)
        const hours = await employeeApiService.getCurrentEmployeeWorkingHours()
        
        // Cache the current user's working hours
        workingHoursManager.setCurrentEmployee(employeeId, hours)
        
        return hours
      }

      // For other employees, return default working hours
      console.log('📋 Returning default working hours for employee:', employeeId)
      const defaultHours = workingHoursManager.generateDefaultWorkingHours()
      
      return defaultHours
    } catch (error) {
      console.error(`Failed to fetch working hours for ${employeeId}:`, error)
      
      // Return default working hours if API fails
      const defaultHours = workingHoursManager.generateDefaultWorkingHours()
      return defaultHours
    }
  },

  // Initialize current employee ID
  async initializeCurrentEmployee() {
    try {
      console.log('🔍 Initializing current employee...')
      const currentEmployee = await employeeApiService.getCurrentEmployee()
      
      if (currentEmployee && currentEmployee.id) {
        console.log('✅ Found current employee:', currentEmployee.full_name || currentEmployee.name, 'ID:', currentEmployee.id)
        workingHoursManager.currentEmployeeId = currentEmployee.id
        return currentEmployee.id
      } else {
        console.warn('⚠️ No current employee found')
        return null
      }
    } catch (error) {
      console.error('❌ Failed to get current employee:', error)
      return null
    }
  },

  // Update working hours (only works for current user)
  async updateWorkingHours(employeeId, dayName, workingHours) {
    try {
      // Only allow updating current user's working hours
      if (employeeId !== workingHoursManager.currentEmployeeId) {
        throw new Error('Can only update working hours for current user')
      }

      // Get current working hours
      const currentHours = workingHoursManager.getWorkingHours(employeeId)
      
      // Update the specific day
      const updatedHours = {
        ...currentHours,
        [workingHoursManager.standardizeDayName(dayName)]: workingHours
      }
      
      // Use the actual employee API service to update working hours
      await employeeApiService.updateEmployeeWorkingHours(updatedHours, 'me')
      
      // Update local cache
      workingHoursManager.updateWorkingHours(employeeId, dayName, workingHours)
      
      return { success: true }
    } catch (error) {
      console.error(`Failed to update working hours for ${employeeId}:`, error)
      throw error
    }
  },

  // Update all working hours (only works for current user)
  async updateAllWorkingHours(employeeId, allWorkingHours) {
    try {
      // Only allow updating current user's working hours
      if (employeeId !== workingHoursManager.currentEmployeeId) {
        throw new Error('Can only update working hours for current user')
      }

      // Use the actual employee API service to update all working hours
      await employeeApiService.updateEmployeeWorkingHours(allWorkingHours, 'me')
      
      // Update local cache
      workingHoursManager.setEmployeeWorkingHours(employeeId, allWorkingHours)
      
      return { success: true }
    } catch (error) {
      console.error(`Failed to update all working hours for ${employeeId}:`, error)
      throw error
    }
  },

  // Initialize working hours for all employees
  async initializeWorkingHours() {
    try {
      console.log('🚀 Initializing working hours for all employees...')
      
      // First, determine who the current user is
      const currentEmployeeId = await this.initializeCurrentEmployee()
      
      // Get all employees
      const employees = await employeeApiService.getAllEmployees()
      console.log('👥 Found employees:', employees.length)
      
      // Fetch current user's working hours from API if we have a current employee
      let currentUserWorkingHours = null
      if (currentEmployeeId) {
        try {
          console.log('📡 Fetching working hours for current user...')
          currentUserWorkingHours = await employeeApiService.getCurrentEmployeeWorkingHours()
          console.log('✅ Loaded current user working hours:', currentUserWorkingHours)
        } catch (error) {
          console.warn('⚠️ Could not fetch current user working hours:', error)
        }
      }
      
      // Set up working hours for all employees
      const allWorkingHours = {}
      
      for (const employee of employees) {
        if (employee.id === currentEmployeeId) {
          // Use actual working hours for current user
          const hours = currentUserWorkingHours || workingHoursManager.generateDefaultWorkingHours()
          allWorkingHours[employee.id] = hours
          workingHoursManager.setCurrentEmployee(employee.id, hours)
          console.log(`✅ Set actual working hours for current user: ${employee.full_name}`)
        } else {
          // Use default working hours for other employees
          const defaultHours = workingHoursManager.generateDefaultWorkingHours()
          allWorkingHours[employee.id] = defaultHours
          workingHoursManager.setEmployeeWorkingHours(employee.id, defaultHours)
          console.log(`📋 Set default working hours for: ${employee.full_name}`)
        }
      }
      
      console.log('✅ Successfully initialized working hours for all employees')
      console.log('👤 Current employee ID:', currentEmployeeId)
      console.log('📊 Working hours summary:')
      Object.entries(allWorkingHours).forEach(([employeeId, hours]) => {
        const isCurrentUser = employeeId == currentEmployeeId
        const sampleDay = hours.monday || hours.sunday || {}
        console.log(`  ${employeeId}: ${isCurrentUser ? '👤 CURRENT USER' : '👥 OTHER'} - Mon: ${sampleDay.startTime || 'N/A'}-${sampleDay.endTime || 'N/A'} (Working: ${sampleDay.isWorking || false})`)
      })
      
      return allWorkingHours
      
    } catch (error) {
      console.error('❌ Failed to initialize working hours:', error)
      throw error
    }
  },

  // Legacy method - replaced with initializeWorkingHours
  async fetchAllWorkingHours() {
    console.log('⚠️ fetchAllWorkingHours is deprecated, use initializeWorkingHours instead')
    return this.initializeWorkingHours()
  },

  // Helper method to check if an employee is available during a specific time
  async isEmployeeAvailable(employeeId, date, startTime, endTime) {
    try {
      const workingHours = await this.fetchWorkingHours(employeeId)
      const dayName = workingHoursManager.getDayNameFromDate(date)
      const dayHours = workingHours[dayName]
      
      if (!dayHours || !dayHours.isWorking) {
        return false
      }
      
      // Convert times to minutes for comparison
      const startMinutes = this.timeToMinutes(startTime)
      const endMinutes = this.timeToMinutes(endTime)
      const workStartMinutes = this.timeToMinutes(dayHours.startTime)
      const workEndMinutes = this.timeToMinutes(dayHours.endTime)
      
      return startMinutes >= workStartMinutes && endMinutes <= workEndMinutes
    } catch (error) {
      console.error(`Failed to check availability for employee ${employeeId}:`, error)
      return false
    }
  },

  // Helper method to convert time string to minutes
  timeToMinutes(timeString) {
    const [hours, minutes] = timeString.split(':').map(Number)
    return hours * 60 + minutes
  },

  // Helper method to convert minutes to time string
  minutesToTime(minutes) {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    return `${String(hours).padStart(2, '0')}:${String(mins).padStart(2, '0')}`
  }
} 