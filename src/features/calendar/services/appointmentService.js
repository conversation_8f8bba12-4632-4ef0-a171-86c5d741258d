import { 
  APPOINTMENT_STATUSES,
  BREAK_TYPES,
  CALENDAR_CONFIG 
} from '../constants/calendarConfig'
import { workingHoursManager } from './workingHoursService'
import { employeeApiService } from '../../employees/services'
import { customerLookupService } from './customerLookupService'
import { servicesApiService } from './servicesApiService'
import apiClient from '../../employees/services/apiClient'
import AppointmentStatusService from './appointmentStatusService'
import { serviceCache } from './serviceCache'

// Service lookup mechanism - no caching, fetch on demand
const serviceLookup = {
  services: new Map(),
  
  // ✅ iOS PATTERN: Use global service cache instead of direct API calls
  async getServiceName(serviceId) {
    return await serviceCache.getServiceName(serviceId)
  },
  
  async getServiceShortName(serviceId) {
    return await serviceCache.getServiceShortName(serviceId)
  },
  
    async getServiceDetails(serviceId) {
    return await serviceCache.getService(serviceId)
  }
}

// Generate employee break slots (real functionality, not mocking)
export const generateEmployeeBreaks = async (employees = null, daysAhead = 7) => {
  // Fetch employees if not provided
  if (!employees) {
    try {
      employees = await employeeApiService.getAllEmployees()
      
      if (!employees || employees.length === 0) {
        console.warn('No employees found, skipping break generation')
        return []
      }
    } catch (error) {
      console.warn('Failed to fetch employees for break generation:', error)
      return []
    }
  }
  const breaks = []
  const today = new Date()
  
  employees.forEach(employee => {
    for (let day = 0; day < daysAhead; day++) {
      const date = new Date(today)
      date.setDate(today.getDate() + day)
      
      // Skip weekends for regular breaks
      if (date.getDay() === 0 || date.getDay() === 6) continue
      
      // Generate lunch break (12:00-13:00)
      const lunchStart = new Date(date)
      lunchStart.setHours(12, 0, 0, 0)
      const lunchEnd = new Date(date)
      lunchEnd.setHours(13, 0, 0, 0)
      
      breaks.push({
        id: `break-lunch-${employee.id}-${date.toISOString().slice(0, 10)}`,
        title: `${BREAK_TYPES.LUNCH.icon} Lunch Break`,
        description: `Lunch break for ${employee.name}`,
        start: lunchStart.toISOString().slice(0, 16).replace('T', ' '),
        end: lunchEnd.toISOString().slice(0, 16).replace('T', ' '),
        calendarId: 'lunch',
        employeeId: employee.id,
        people: [employee.name],
        isBookable: false,
        type: 'break',
        breakType: 'lunch',
        _options: {
          disableDND: false, // Allow moving break times
          disableResize: true, // Fixed duration breaks
          additionalClasses: ['break-slot', 'lunch-break', `employee-${employee.id}`]
        }
      })
      
      // Random additional breaks
      if (Math.random() > 0.6) {
        const breakTypes = Object.values(BREAK_TYPES).filter(t => t.id !== 'lunch')
        const breakType = breakTypes[Math.floor(Math.random() * breakTypes.length)]
        
        const breakHour = 10 + Math.floor(Math.random() * 5) // 10 AM to 2 PM (avoid late afternoon breaks)
        const breakStart = new Date(date)
        breakStart.setHours(breakHour, 0, 0, 0)
        const breakEnd = new Date(breakStart)
        breakEnd.setMinutes(breakEnd.getMinutes() + 30) // 30 min breaks
        
        breaks.push({
          id: `break-${breakType.id}-${employee.id}-${date.toISOString().slice(0, 10)}-${breakHour}`,
          title: `${breakType.icon} ${breakType.name}`,
          description: `${breakType.name} for ${employee.name}`,
          start: breakStart.toISOString().slice(0, 16).replace('T', ' '),
          end: breakEnd.toISOString().slice(0, 16).replace('T', ' '),
          calendarId: breakType.id,
          employeeId: employee.id,
          people: [employee.name],
          isBookable: false,
          type: 'break',
          breakType: breakType.id,
          _options: {
            disableDND: false,
            disableResize: false,
            additionalClasses: ['break-slot', `${breakType.id}-break`, `employee-${employee.id}`]
          }
        })
      }
    }
  })
  
  return breaks
}

// Validation functions for drag and drop
export const validateAppointmentMove = (event, newStart, newEnd, employeeId) => {
  // Check business hours
  const startHour = new Date(newStart).getHours()
  const endHour = new Date(newEnd).getHours()
  const config = CALENDAR_CONFIG
  
  const businessStart = parseInt(config.dayBoundaries.start.split(':')[0])
  const businessEnd = parseInt(config.dayBoundaries.end.split(':')[0])
  
  if (startHour < businessStart || endHour > businessEnd) {
    return {
      isValid: false,
      message: `Appointments must be within business hours (${config.dayBoundaries.start} - ${config.dayBoundaries.end})`
    }
  }
  
  // Check minimum duration
  const duration = (new Date(newEnd) - new Date(newStart)) / (1000 * 60) // minutes
  if (duration < config.resize.minDuration) {
    return {
      isValid: false,
      message: `Minimum appointment duration is ${config.resize.minDuration} minutes`
    }
  }
  
  if (duration > config.resize.maxDuration) {
    return {
      isValid: false,
      message: `Maximum appointment duration is ${config.resize.maxDuration} minutes`
    }
  }
  
  return { isValid: true }
}

export const checkAppointmentConflicts = async (event, newStart, newEnd, employeeId) => {
  // Skip API conflict checking since the endpoint doesn't exist in this backend
  // Instead, return no conflicts to allow appointment moves to proceed
  console.log('⚠️ Skipping conflict check - /appointments/check-conflicts/ endpoint not available')
  console.log('📍 Would check conflicts for:', {
    appointmentId: event.id,
    employeeId: employeeId,
    start: newStart,
    end: newEnd
  })
  
  // TODO: Implement client-side conflict checking using existing appointments data
  // For now, return no conflicts to allow moves to proceed
  return { 
    hasConflict: false,
    message: 'Conflict checking disabled - endpoint not available'
  }
}

// ✅ iOS PATTERN: Debouncing for API calls (1 second like iOS)
let fetchDebounceTimer = null
let lastFetchParams = null
let cachedAppointmentsResult = null
let lastSuccessfulFetch = null
const DEBOUNCE_DELAY = 1000 // 1 second like iOS
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes like iOS

// Main appointment service with consolidated methods
export const appointmentService = {
  
  // === CORE API METHODS ===
  
  async fetchAppointments(dateRange = null, selectedEmployeeIDs = null) {
    console.log('📅 appointmentService.fetchAppointments called with iOS-style parameters:', {
      dateRange,
      selectedEmployeeIDs: selectedEmployeeIDs ? [...selectedEmployeeIDs] : null
    })
    
    // ✅ iOS PATTERN: Build filtering parameters like iOS app does
    const params = {}
    
    // ✅ iOS PATTERN: Add date range filtering (now backend supports it!)
    if (dateRange && dateRange.start && dateRange.end) {
      params.from = dateRange.start
      params.to = dateRange.end
      console.log('📅 [iOS Pattern] Adding date range filters:', params.from, 'to', params.to)
    }
    
    // ✅ iOS PATTERN: Add employee filtering (now backend supports it!) 
    if (selectedEmployeeIDs && selectedEmployeeIDs.size > 0) {
      params.employee__in = [...selectedEmployeeIDs].join(',')
      console.log('👥 [iOS Pattern] Adding employee filters:', params.employee__in)
    }
    
    // ✅ iOS PATTERN: Cache check - return cached data if still valid
    const cacheKey = JSON.stringify({dateRange, employees: selectedEmployeeIDs ? [...selectedEmployeeIDs].sort() : null})
    if (cachedAppointmentsResult && lastSuccessfulFetch && 
        (Date.now() - lastSuccessfulFetch) < CACHE_DURATION &&
        JSON.stringify(lastFetchParams) === cacheKey) {
      console.log('💾 [iOS Pattern] Returning cached appointments (5min cache)')
      return cachedAppointmentsResult
    }
    
    // ✅ iOS PATTERN: Debouncing - prevent rapid successive calls
    return new Promise((resolve, reject) => {
      if (fetchDebounceTimer) {
        clearTimeout(fetchDebounceTimer)
      }
      
      fetchDebounceTimer = setTimeout(async () => {
        try {
          console.log('🔄 [iOS Pattern] Debounced fetch executing after 1 second delay')
          const result = await this._performFetch(params, dateRange, selectedEmployeeIDs)
          
          // ✅ iOS PATTERN: Cache successful results
          cachedAppointmentsResult = result
          lastSuccessfulFetch = Date.now()
          lastFetchParams = cacheKey
          
          resolve(result)
        } catch (error) {
          reject(error)
        }
      }, DEBOUNCE_DELAY)
    })
  },

  async _performFetch(params, dateRange, selectedEmployeeIDs) {
    try {
      console.log('🔍 [iOS Pattern] Fetching appointments with params:', params)
      const appointments = await this.fetchAppointmentsFromApi(params)
      console.log('📋 [iOS Pattern] Raw appointments from Django API:', appointments.length, 'appointments')
      
      // Transform Django API response to match frontend format
      console.log('🔄 Transforming appointments to frontend format...')
      const transformedAppointments = this.transformApiAppointments(appointments)
      console.log('✅ Transformed appointments:', transformedAppointments.length)
      
      // ✅ iOS PATTERN: Remove cache clearing! iOS keeps cache warm
      // customerLookupService.clearCache() // REMOVED - iOS doesn't clear cache on every load
      
      // Enhance appointments with actual customer names
      console.log('🔍 Enhancing appointments with customer names...')
      const enhancedAppointments = await this.enhanceAppointmentsWithNames(transformedAppointments)
      console.log('✅ Enhanced appointments with customer names:', enhancedAppointments.length)
      
      // ✅ iOS PATTERN: Client-side filtering (since backend doesn't support it)
      const filteredAppointments = this.filterAppointmentsClientSide(enhancedAppointments, dateRange, selectedEmployeeIDs)
      console.log('🎯 [iOS Pattern] Filtered appointments client-side:', filteredAppointments.length, 'appointments')
      
      const breaks = await generateEmployeeBreaks() // Keep break generation
      console.log('🔧 Generated employee breaks:', breaks.length)
      
      const finalResult = [...filteredAppointments, ...breaks]
      console.log('🎯 [iOS Pattern] Final appointments result:', finalResult.length, 'total (', filteredAppointments.length, 'appointments +', breaks.length, 'breaks)')
      
      return finalResult
    } catch (error) {
      console.error('❌ Failed to fetch appointments from API:', error)
      throw error
    }
  },

  async fetchAppointmentsFromApi(params = {}) {
    try {
      console.log('🔍 Fetching appointments from Django API with params:', params)
      
      let allAppointments = []
      let nextUrl = '/appointments/'
      let pageCount = 0
      const MAX_PAGES = 10 // ✅ BACKEND REALITY: Need higher limits since we're fetching all appointments for client-side filtering
      const MAX_RECORDS = 1000 // ✅ BACKEND REALITY: Need higher limits since we can't filter by date/employee on backend
      
      // Handle Django pagination with safety limits
      while (nextUrl && pageCount < MAX_PAGES && allAppointments.length < MAX_RECORDS) {
        pageCount++
        console.log(`📄 [iOS Pattern] Fetching page ${pageCount}/${MAX_PAGES}...`)
        
        const response = await apiClient.get(nextUrl, { params })
        const data = response.data
        
        console.log('📋 Django API response:', data)
        
        // Add current page results
        if (data && data.results && Array.isArray(data.results)) {
          const newAppointments = data.results.slice(0, MAX_RECORDS - allAppointments.length)
          allAppointments = [...allAppointments, ...newAppointments]
          console.log(`📄 Added ${newAppointments.length} appointments, total: ${allAppointments.length}`)
        } else if (Array.isArray(data)) {
          // Handle case where API returns array directly (non-paginated)
          allAppointments = data.slice(0, MAX_RECORDS)
          break
        }
        
        // Update next URL for pagination
        nextUrl = data.next ? data.next.replace(apiClient.defaults.baseURL, '') : null
        
        // Clear params for subsequent requests (URL already contains them)
        params = {}
      }
      
      // ✅ iOS PATTERN: Log safety limits being hit
      if (pageCount >= MAX_PAGES) {
        console.warn(`⚠️ [iOS Pattern] Hit page limit (${MAX_PAGES} pages), consider better filtering`)
      }
      if (allAppointments.length >= MAX_RECORDS) {
        console.warn(`⚠️ [iOS Pattern] Hit record limit (${MAX_RECORDS} records), consider better filtering`)
      }
      
      console.log(`✅ Fetched total of ${allAppointments.length} appointments from Django API`)
      return allAppointments
    } catch (error) {
      console.error('❌ Failed to fetch appointments:', error)
      throw new Error(error.response?.data?.message || 'Failed to fetch appointments')
    }
  },

  async fetchEmployeeAppointments(employeeId = 'me', params = {}) {
    try {
      return await employeeApiService.getEmployeeAppointments(employeeId, params)
    } catch (error) {
      console.error('Failed to fetch employee appointments:', error)
      throw new Error(error.response?.data?.message || 'Failed to fetch employee appointments')
    }
  },

  async fetchAppointmentById(appointmentId) {
    try {
      console.log('🔍 Fetching detailed appointment by ID:', appointmentId)
      const response = await apiClient.get(`/appointments/${appointmentId}/`)
      console.log('✅ Detailed appointment data:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Failed to fetch detailed appointment:', error)
      throw new Error(error.response?.data?.message || 'Failed to fetch appointment details')
    }
  },

  async createAppointment(appointmentData) {
    try {
      // Transform frontend format to Django API format
      const apiData = this.transformToApiFormat(appointmentData)
      console.log('🚀 Sending appointment data to API:', apiData)
      const response = await apiClient.post('/appointments/', apiData)
      console.log('✅ API response:', response.data)
      
      // Transform back to frontend format
      return this.transformFromApiFormat(response.data)
    } catch (error) {
      console.error('❌ Failed to create appointment:', error)
      console.error('❌ Error response:', error.response?.data)
      console.error('❌ Error status:', error.response?.status)
      
      // Extract detailed error information from Django REST Framework
      let errorMessage = 'Failed to create appointment'
      
      if (error.response?.data) {
        const errorData = error.response.data
        
        // Handle different error formats from Django REST Framework
        if (typeof errorData === 'string') {
          errorMessage = errorData
        } else if (errorData.detail) {
          errorMessage = errorData.detail
        } else if (errorData.message) {
          errorMessage = errorData.message
        } else {
          // Handle field-specific validation errors
          const fieldErrors = []
          for (const [field, errors] of Object.entries(errorData)) {
            if (Array.isArray(errors)) {
              fieldErrors.push(`${field}: ${errors.join(', ')}`)
            } else if (typeof errors === 'string') {
              fieldErrors.push(`${field}: ${errors}`)
            }
          }
          if (fieldErrors.length > 0) {
            errorMessage = `Validation errors: ${fieldErrors.join('; ')}`
          }
        }
      }
      
      throw new Error(errorMessage)
    }
  },

  async updateAppointment(appointmentId, updates) {
    // Validate the update if it's a move operation
    if (updates.start && updates.end && updates.employeeId) {
      const validation = validateAppointmentMove(
        { id: appointmentId }, 
        updates.start, 
        updates.end, 
        updates.employeeId
      )
      
      if (!validation.isValid) {
        throw new Error(validation.message)
      }
      
      // Check for conflicts via API
      const conflictCheck = await checkAppointmentConflicts(
        { id: appointmentId },
        updates.start,
        updates.end,
        updates.employeeId
      )
      
      if (conflictCheck.hasConflict) {
        throw new Error('Appointment conflicts with existing booking')
      }
    }
    
    try {
      // Always use PATCH for appointment updates to avoid customer ID requirements
      console.log('🔄 Updating appointment with PATCH:', updates)
      
      const patchData = {}
      
      // Handle appointment moves (date/time/employee changes)
      if (updates.newDateTime || updates.selectedEmployees) {
        console.log('🔄 Handling appointment move with PATCH:', updates)
        
        // Handle new date/time
        if (updates.newDateTime) {
          const startTime = updates.newDateTime
          if (startTime instanceof Date) {
            patchData.start_time = startTime.toISOString().slice(0, 19) + '+0000'
          } else if (typeof startTime === 'string') {
            const parsedDate = new Date(startTime)
            if (!isNaN(parsedDate.getTime())) {
              patchData.start_time = parsedDate.toISOString().slice(0, 19) + '+0000'
            }
          }
        }
        
        // Handle employee change
        if (updates.selectedEmployees && updates.selectedEmployees.length > 0) {
          patchData.employee = parseInt(updates.selectedEmployees[0].id)
          console.log('🔧 Employee change detected:', {
            from: 'selectedEmployees',
            targetEmployee: updates.selectedEmployees[0],
            targetEmployeeId: updates.selectedEmployees[0].id,
            patchEmployeeId: patchData.employee
          })
        }
        
        console.log('🚀 Sending PATCH data for appointment move:', patchData)
      }
      
      // Handle direct field updates (status changes, etc.)
      else {
        // Handle start/end time updates
        if (updates.start) {
          const startTime = updates.start
          if (startTime instanceof Date) {
            patchData.start_time = startTime.toISOString().slice(0, 19) + '+0000'
          } else if (typeof startTime === 'string') {
            const parsedDate = new Date(startTime)
            if (!isNaN(parsedDate.getTime())) {
              patchData.start_time = parsedDate.toISOString().slice(0, 19) + '+0000'
            }
          }
        }
        
        if (updates.end) {
          const endTime = updates.end
          if (endTime instanceof Date) {
            patchData.end_time = endTime.toISOString().slice(0, 19) + '+0000'
          } else if (typeof endTime === 'string') {
            const parsedDate = new Date(endTime)
            if (!isNaN(parsedDate.getTime())) {
              patchData.end_time = parsedDate.toISOString().slice(0, 19) + '+0000'
            }
          }
        }
        
        // Handle employee ID changes
        if (updates.employeeId) {
          patchData.employee = parseInt(updates.employeeId)
        }
        
        // Handle status changes
        if (updates.status) {
          patchData.status = updates.status
        }
        
        // Handle notes
        if (updates.notes !== undefined) {
          patchData.notes_from_customer = updates.notes
        }
        
        // Handle cancellation reason
        if (updates.cancellationReason) {
          patchData.cancellation_reason = updates.cancellationReason
        }
        
        // Handle notification preference
        if (updates.notifyCustomer !== undefined) {
          patchData.notify_customer = updates.notifyCustomer
        }
        
        console.log('🚀 Sending PATCH data for appointment update:', patchData)
      }
      
      // Use PATCH for all updates
      const response = await apiClient.patch(`/appointments/${appointmentId}/`, patchData)
      console.log('✅ PATCH response received:', {
        appointmentId,
        responseStatus: response.status,
        responseData: response.data,
        originalEmployeeId: response.data.employee,
        originalEmployeeIdType: typeof response.data.employee
      })
      
      const transformed = this.transformFromApiFormat(response.data)
      console.log('🔄 Transformed appointment data:', {
        appointmentId: transformed.id,
        employeeId: transformed.employeeId,
        employeeIdType: typeof transformed.employeeId,
        employeeName: transformed.employeeName
      })
      
      return transformed
      
    } catch (error) {
      console.error('Failed to update appointment:', error)
      console.error('Error details:', error.response?.data)
      throw new Error(error.response?.data?.message || error.response?.data?.detail || 'Failed to update appointment')
    }
  },

  async patchAppointment(appointmentId, updates) {
    try {
      const response = await apiClient.patch(`/appointments/${appointmentId}/`, updates)
      return this.transformFromApiFormat(response.data)
    } catch (error) {
      console.error('Failed to patch appointment:', error)
      throw new Error(error.response?.data?.message || 'Failed to update appointment')
    }
  },

  async deleteAppointment(appointmentId) {
    try {
      console.log('🗑️ Hard deleting appointment:', appointmentId)
      const response = await apiClient.delete(`/appointments/${appointmentId}/`)
      console.log('✅ Appointment hard deleted successfully:', response.status)
      return { id: appointmentId, deleted: true }
    } catch (error) {
      console.error('❌ Failed to hard delete appointment:', error)
      
      // Log details about the foreign key constraint error for backend debugging
      if (error.response?.status === 500 && 
          (error.response?.data?.message?.includes('FOREIGN KEY constraint') || 
           error.response?.data?.message?.includes('IntegrityError'))) {
        
        console.error('🚨 BACKEND ISSUE: Foreign key constraint preventing deletion.')
        console.error('💡 BACKEND FIX NEEDED: The appointment has related records that need to be handled.')
        console.error('📋 Related tables likely include: appointment_services, appointment_add_ons, payments, notifications, etc.')
        console.error('🔧 SOLUTION: Implement CASCADE DELETE or delete related records first in the backend.')
      }
      
      throw new Error(error.response?.data?.message || 'Failed to delete appointment')
    }
  },

  async updateAppointmentStatus(appointmentId, status, reason = null) {
    try {
      // Map frontend status to Django backend status
      const backendStatus = AppointmentStatusService.mapToBackendStatus(status)
      
      const data = { status: backendStatus }
      if (reason) {
        data.cancellation_reason = reason
      }
      
      console.log(`🔄 Updating appointment ${appointmentId}: ${status} -> ${backendStatus}`, data)
      
      const response = await apiClient.patch(`/appointments/${appointmentId}/`, data)
      
      const statusConfig = Object.values(APPOINTMENT_STATUSES).find(s => s.id === status)
      
      const result = {
        ...response.data,
        statusLabel: statusConfig?.name || status,
        // Ensure the status is properly set for immediate UI feedback
        status: status,
        cancellation_reason: reason || response.data.cancellation_reason
      }
      
      console.log('✅ Appointment status updated successfully:', result)
      
      return result
    } catch (error) {
      console.error('Failed to update appointment status:', error)
      throw new Error(error.response?.data?.message || 'Failed to update appointment status')
    }
  },

  async getAvailableSlots(employeeId, date, serviceId, businessId = 1) {
    try {
      const params = {
        date: date,
        service_id: serviceId
      }
      
      // Add employee_id only if provided (for specific employee slots)
      if (employeeId && employeeId !== null) {
        params.employee_id = employeeId
      }
      
      console.log('🔄 Calling available-times API with params:', params)
      console.log('📍 Using business ID:', businessId)
      
      // Use the correct endpoint with business_id
      const response = await apiClient.get(`/businesses/${businessId}/appointments/available-times/`, { params })
      
      console.log('✅ API Response:', response.data)
      
      // Transform the API response to match frontend expectations
      const data = response.data
      
      if (data.availability) {
        const availableSlots = []
        
        // Get service duration from service details
        let serviceDuration = 60 // Default fallback
        try {
          const serviceDetails = await serviceLookup.getServiceDetails(serviceId)
          if (serviceDetails && serviceDetails.duration) {
            serviceDuration = serviceDetails.duration
          }
        } catch (error) {
          console.warn('Could not fetch service duration, using default:', error)
        }
        
        console.log('📝 Using service duration:', serviceDuration, 'minutes')
        
        // Transform the availability object to frontend format
        Object.entries(data.availability).forEach(([employeeName, timeSlots]) => {
          timeSlots.forEach(timeSlot => {
            const slotDate = new Date(timeSlot)
            
            // Calculate end time based on actual service duration
            const endTime = new Date(slotDate)
            endTime.setMinutes(endTime.getMinutes() + serviceDuration)
            
            // 🔧 FIX: Properly find the actual employee ID for this employee name
            let actualEmployeeId = employeeId
            if (!employeeId) {
              // When fetching for all employees, we need to find the actual employee ID
              // The employee name should match the full_name or name field
              // For now, we'll store the employee name and handle ID lookup in the modal
              actualEmployeeId = null // Set to null to indicate we need to lookup by name
            }
            
            availableSlots.push({
              start_time: slotDate.toLocaleTimeString('en-US', { 
                hour: 'numeric', 
                minute: '2-digit',
                hour12: true 
              }),
              end_time: endTime.toLocaleTimeString('en-US', { 
                hour: 'numeric', 
                minute: '2-digit',
                hour12: true 
              }),
              employee_name: employeeName,
              employee_id: actualEmployeeId, // 🔧 FIX: Use proper employee ID or null
              duration: serviceDuration,
              available: true,
              datetime: timeSlot,
              raw_time: timeSlot
            })
          })
        })
        
        console.log('🎯 Transformed slots:', availableSlots)
        return availableSlots
      }
      
      return response.data
    } catch (error) {
      console.warn('Backend available-times endpoint failed, using fallback implementation:', error)
      
      // Fallback: Generate available slots based on existing appointments and working hours
      return await this.generateAvailableSlotsFallback(employeeId, date, serviceId)
    }
  },

  async generateAvailableSlotsFallback(employeeId, date, serviceId) {
    try {
      console.log('🔄 Generating available slots fallback for:', { employeeId, date, serviceId })
      
      // Get service details to know duration
      const services = await servicesApiService.fetchServices()
      const service = services.find(s => s.id == serviceId)
      const serviceDuration = service ? service.duration : 60 // Default to 60 minutes
      
      console.log('📋 Service found:', service)
      
      // Get existing appointments for this employee on this date
      const existingAppointments = await this.fetchAppointmentsFromApi({
        employee_id: employeeId,
        start_date: date,
        end_date: date
      })
      
      console.log('📋 Existing appointments:', existingAppointments)
      console.log('📋 Raw appointment times:', existingAppointments.map(apt => ({
        id: apt.id,
        start_time: apt.start_time,
        end_time: apt.end_time,
        employee: apt.employee
      })))
      
      // Parse the input date properly
      const appointmentDate = new Date(date + 'T00:00:00')
      console.log('📅 Parsed appointment date:', appointmentDate)
      console.log('📅 Current time:', new Date())
      
      // Generate time slots (assuming 9 AM to 5 PM working hours)
      const workingHours = {
        start: 9, // 9 AM
        end: 17,  // 5 PM
        slotDuration: 30 // 30-minute slots
      }
      
      const availableSlots = []
      let totalSlotsGenerated = 0
      let futureSlots = 0
      let conflictingSlots = 0
      
      for (let hour = workingHours.start; hour < workingHours.end; hour++) {
        for (let minute = 0; minute < 60; minute += workingHours.slotDuration) {
          totalSlotsGenerated++
          
          const slotStart = new Date(appointmentDate)
          slotStart.setHours(hour, minute, 0, 0)
          
          const slotEnd = new Date(slotStart)
          slotEnd.setMinutes(slotEnd.getMinutes() + serviceDuration)
          
          console.log(`🕐 Checking slot: ${slotStart.toISOString()} - ${slotEnd.toISOString()}`)
          
          // Check if slot is in the future (only check if it's today)
          const now = new Date()
          const isToday = appointmentDate.toDateString() === now.toDateString()
          const isFutureSlot = !isToday || slotStart > now
          
          if (!isFutureSlot) {
            console.log(`⏰ Slot is in the past, skipping`)
            continue
          }
          futureSlots++
          
          // Check if this slot conflicts with existing appointments
          const conflictingAppointments = existingAppointments.filter(apt => {
            // Only check appointments for the same employee
            if (apt.employee != employeeId) {
              return false
            }
            
            const aptStart = new Date(apt.start_time)
            const aptEnd = new Date(apt.end_time)
            
            console.log(`  🔍 Checking against appointment: ${aptStart.toISOString()} - ${aptEnd.toISOString()}`)
            
            // Check for overlap: slot overlaps if it starts before apt ends AND ends after apt starts
            const hasOverlap = (slotStart < aptEnd && slotEnd > aptStart)
            
            if (hasOverlap) {
              console.log(`  ❌ Conflict detected with appointment ${apt.id}`)
            }
            
            return hasOverlap
          })
          
          const hasConflict = conflictingAppointments.length > 0
          
          if (hasConflict) {
            conflictingSlots++
            console.log(`❌ Slot conflicts with ${conflictingAppointments.length} appointment(s)`)
            continue
          }
          
          console.log(`✅ Slot is available!`)
          
          availableSlots.push({
            start_time: slotStart.toLocaleTimeString('en-US', { 
              hour: 'numeric', 
              minute: '2-digit',
              hour12: true 
            }),
            end_time: slotEnd.toLocaleTimeString('en-US', { 
              hour: 'numeric', 
              minute: '2-digit',
              hour12: true 
            }),
            employee_name: `Employee ${employeeId}`,
            duration: serviceDuration,
            available: true,
            datetime: slotStart.toISOString()
          })
        }
      }
      
      console.log('📊 Slot generation summary:', {
        totalSlotsGenerated,
        futureSlots,
        conflictingSlots,
        availableSlots: availableSlots.length
      })
      
      // If no slots were generated, create a few test slots as fallback
      if (availableSlots.length === 0) {
        console.log('⚠️ No slots generated, creating fallback test slots')
        
        // Generate a few simple test slots
        for (let hour = 10; hour <= 15; hour++) {
          const slotStart = new Date(appointmentDate)
          slotStart.setHours(hour, 0, 0, 0)
          
          const slotEnd = new Date(slotStart)
          slotEnd.setMinutes(slotEnd.getMinutes() + serviceDuration)
          
          availableSlots.push({
            start_time: slotStart.toLocaleTimeString('en-US', { 
              hour: 'numeric', 
              minute: '2-digit',
              hour12: true 
            }),
            end_time: slotEnd.toLocaleTimeString('en-US', { 
              hour: 'numeric', 
              minute: '2-digit',
              hour12: true 
            }),
            employee_name: `Employee ${employeeId}`,
            duration: serviceDuration,
            available: true,
            datetime: slotStart.toISOString()
          })
        }
        
        console.log('✅ Generated fallback test slots:', availableSlots)
      }
      
      console.log('✅ Final available slots:', availableSlots)
      return availableSlots
      
    } catch (error) {
      console.error('❌ Failed to generate available slots fallback:', error)
      return []
    }
  },

  async bulkUpdateAppointments(appointmentIds, updates) {
    try {
      const response = await apiClient.put('/appointments/bulk-update/', {
        appointment_ids: appointmentIds,
        updates: updates
      })
      return response.data
    } catch (error) {
      console.error('Failed to bulk update appointments:', error)
      throw new Error(error.response?.data?.message || 'Failed to bulk update appointments')
    }
  },

  // === BUSINESS LOGIC METHODS ===

  async fetchAppointmentDetails(appointmentId) {
    try {
      console.log('📋 Fetching detailed appointment data for ID:', appointmentId)
      const detailedAppointment = await this.fetchAppointmentById(appointmentId)
      
      // Transform the detailed appointment data
      const transformed = this.transformFromApiFormat(detailedAppointment)
      console.log('✅ Transformed detailed appointment:', transformed)
      
      return transformed
    } catch (error) {
      console.error('❌ Failed to fetch appointment details:', error)
      throw error
    }
  },

  async enhanceAppointmentsWithNames(appointments) {
    console.log('🔄 Enhancing appointments with names:', appointments.length, 'appointments')
    
    const enhancedAppointments = await Promise.all(
      appointments.map(async (appointment) => {
        let updatedAppointment = { ...appointment }
        
        console.log('🔍 Processing appointment for enhancement:', {
          id: appointment.id,
          clientName: appointment.clientName,
          needsNameLookup: appointment.needsNameLookup,
          customer_id: appointment.customer_id
        })
        
        // Enhance customer name if needed
        if (appointment.needsNameLookup && appointment.customer_id) {
          console.log('🔍 Looking up customer name for ID:', appointment.customer_id)
          try {
            const actualCustomerName = await customerLookupService.getCustomerName(appointment.customer_id)
            
            console.log('✅ Customer lookup result:', actualCustomerName)
            
            // Update if we got a real name (not a fallback pattern)
            if (actualCustomerName && !actualCustomerName.startsWith('Customer ')) {
              // Remove status icon from enhanced title as well
              updatedAppointment = {
                ...updatedAppointment,
                clientName: actualCustomerName,
                title: actualCustomerName, // Remove status icon from title
                needsNameLookup: false
              }
              console.log('✅ Updated appointment with customer name:', actualCustomerName)
            } else {
              console.log('⚠️ Customer lookup returned fallback name, keeping original')
            }
          } catch (error) {
            console.error('❌ Customer name lookup failed for ID:', appointment.customer_id, error)
          }
        }
        
        // Enhance service name if needed
        if (appointment.needsServiceLookup) {
          try {
            const serviceName = await serviceLookup.getServiceName(appointment.serviceId)
            const serviceShortName = await serviceLookup.getServiceShortName(appointment.serviceId)
            
            if (serviceName && serviceName !== 'Unknown Service') {
              updatedAppointment = {
                ...updatedAppointment,
                serviceName: serviceName,
                serviceShortName: serviceShortName,
                location: serviceName,
                needsServiceLookup: false
              }
              console.log('✅ Updated appointment with service name:', serviceName, 'and short name:', serviceShortName)
            }
          } catch (error) {
            console.error('❌ Service name lookup failed for ID:', appointment.serviceId, error)
          }
        }

        // Enhance employee name if needed
        if (appointment.needsNameLookup && appointment.employeeId && 
            (appointment.employeeName?.startsWith('Employee ') || !appointment.employeeName)) {
          console.log('🔍 Looking up employee name for ID:', appointment.employeeId)
          try {
            const actualEmployeeName = await customerLookupService.getEmployeeName(appointment.employeeId)
            
            console.log('✅ Employee lookup result:', actualEmployeeName)
            
            // Update if we got a real name (not a fallback pattern)
            if (actualEmployeeName && !actualEmployeeName.startsWith('Employee ')) {
              updatedAppointment = {
                ...updatedAppointment,
                employeeName: actualEmployeeName,
                people: [actualEmployeeName], // Update the people array as well
                needsNameLookup: appointment.needsNameLookup && updatedAppointment.clientName?.startsWith('Customer ') // Only false if both customer and employee names are resolved
              }
              console.log('✅ Updated appointment with employee name:', actualEmployeeName)
            } else {
              console.log('⚠️ Employee lookup returned fallback name, keeping original')
            }
          } catch (error) {
            console.error('❌ Employee name lookup failed for ID:', appointment.employeeId, error)
          }
        }

        console.log('✅ Final enhanced appointment:', {
          id: updatedAppointment.id,
          title: updatedAppointment.title,
          clientName: updatedAppointment.clientName
        })

        return updatedAppointment
      })
    )

    console.log('✅ Enhancement complete for', enhancedAppointments.length, 'appointments')
    return enhancedAppointments
  },

  async createBreakSlot(breakData) {
    // For now, keep break slots as local only
    // In the future, these could be managed via API as well
    return {
      id: `break-${Date.now()}`,
      ...breakData,
      type: 'break',
      isBookable: false
    }
  },

  async fetchAvailableSlots(employeeId, date, serviceId = null) {
    return await this.getAvailableSlots(employeeId, date, serviceId)
  },

  async getEmployeeSchedule(employeeId, startDate, endDate) {
    try {
      const params = {
        start_date: startDate,
        end_date: endDate
      }
      const schedule = await this.fetchEmployeeAppointments(employeeId, params)
      
      // Transform API response
      const transformedSchedule = this.transformApiAppointments(schedule)
      
      // Fetch specific employee for break generation
      try {
        const employees = await employeeApiService.getAllEmployees()
        const employee = employees.find(e => e.id === employeeId)
        const breaks = employee ? await generateEmployeeBreaks([employee]) : []
        return [...transformedSchedule, ...breaks]
      } catch (error) {
        console.warn('Failed to generate breaks for employee schedule:', error)
        return transformedSchedule
      }
    } catch (error) {
      console.error(`Failed to fetch employee schedule for ${employeeId}:`, error)
      throw error
    }
  },

  // === DATA TRANSFORMATION METHODS ===

  transformApiAppointments(apiAppointments) {
    console.log('🔄 transformApiAppointments called with:', apiAppointments)
    
    if (!Array.isArray(apiAppointments)) {
      console.warn('⚠️ apiAppointments is not an array:', typeof apiAppointments, apiAppointments)
      return []
    }
    
    console.log(`🔄 Transforming ${apiAppointments.length} appointments...`)
    const transformed = apiAppointments.map(apt => this.transformFromApiFormat(apt))
    console.log('✅ Transformation complete, result:', transformed)
    
    return transformed
  },

  transformFromApiFormat(apiAppointment) {
    console.log('🔄 Transforming Django appointment:', apiAppointment)
    
    // Extract service info from appointment_services array
    const primaryService = apiAppointment.appointment_services?.[0] || {}
    
    // Debug customer name resolution
    console.log('🔍 Customer name debugging:', {
      customer_full_name: apiAppointment.customer_full_name,
      customer_id: apiAppointment.customer,
      available_fields: Object.keys(apiAppointment)
    })
    
    // Try multiple ways to get customer name
    let customerName = apiAppointment.customer_full_name
    if (!customerName && apiAppointment.customer_name) {
      customerName = apiAppointment.customer_name
    }
    if (!customerName && apiAppointment.customer?.name) {
      customerName = apiAppointment.customer.name
    }
    if (!customerName && apiAppointment.customer?.first_name && apiAppointment.customer?.last_name) {
      customerName = `${apiAppointment.customer.first_name} ${apiAppointment.customer.last_name}`.trim()
    }
    if (!customerName && apiAppointment.customer?.first_name) {
      customerName = apiAppointment.customer.first_name
    }
    // Only fall back to generic name if we really can't find anything
    if (!customerName) {
      customerName = `Customer ${apiAppointment.customer}`
    }
    
    console.log('✅ Resolved customer name:', customerName)
    
    // Transform Django API format to frontend calendar format
    const transformed = {
      id: apiAppointment.id,
      title: customerName, // Remove status icon from title
      description: apiAppointment.notes_from_customer || '',
      location: primaryService.service_name || '',
      start: apiAppointment.start_time,
      end: apiAppointment.end_time,
      calendarId: primaryService.service?.toString() || 'default',
      people: [apiAppointment.employee_full_name || `Employee ${apiAppointment.employee}`],
      
      // Custom appointment data
      serviceId: primaryService.service?.toString() || primaryService.service,
      serviceName: primaryService.service_name,
      serviceShortName: primaryService.service_short_name,
      serviceColor: primaryService.color || '#3b82f6',
      employeeId: apiAppointment.employee?.toString() || apiAppointment.employee,
      employeeName: apiAppointment.employee_full_name || `Employee ${apiAppointment.employee}`,
      clientName: customerName,
      // Store the original API data for potential detailed fetching
      customer_id: apiAppointment.customer?.toString() || apiAppointment.customer,
      employee_id: apiAppointment.employee?.toString() || apiAppointment.employee,
      // Only set needsNameLookup if we truly need to look up names
      needsNameLookup: !customerName || customerName.startsWith('Customer ') || !apiAppointment.employee_full_name,
      needsServiceLookup: (!primaryService.service_name || !primaryService.service_short_name) && primaryService.service,
      clientPhone: '', // Not provided in this API structure
      clientEmail: '', // Not provided in this API structure
      status: apiAppointment.status,
      statusLabel: this.getStatusDisplayName(apiAppointment.status),
      statusColor: this.getStatusColor(apiAppointment.status),
      price: parseFloat(apiAppointment.total_price || 0),
      notes: apiAppointment.notes_from_customer || '',
      isBookable: true,
      type: 'appointment',
      
      // Additional Django-specific data
      paymentStatus: apiAppointment.payment_status,
      source: apiAppointment.source,
      totalDuration: apiAppointment.total_duration,
      bufferTime: apiAppointment.buffer_time,
      // Calculate service duration (total duration minus buffer time)
      serviceDuration: apiAppointment.total_duration - (apiAppointment.buffer_time || 0),
      duration: apiAppointment.total_duration - (apiAppointment.buffer_time || 0), // Legacy field for compatibility
      services: apiAppointment.appointment_services || [],
      addOns: apiAppointment.appointment_add_ons || [],
      cancellationReason: apiAppointment.cancellation_reason,
      
      _options: {
        disableDND: apiAppointment.status === 'completed',
        disableResize: apiAppointment.status === 'completed',
        additionalClasses: [
          `service-${primaryService.service?.toString() || 'default'}`,
          `employee-${apiAppointment.employee?.toString() || apiAppointment.employee}`,
          `status-${apiAppointment.status}`
        ]
      }
    }
    
    console.log('✅ Transformed appointment:', {
      id: transformed.id,
      title: transformed.title,
      clientName: transformed.clientName,
      employeeId: transformed.employeeId,
      employeeIdType: typeof transformed.employeeId,
      serviceId: transformed.serviceId,
      serviceIdType: typeof transformed.serviceId,
      start: transformed.start,
      needsNameLookup: transformed.needsNameLookup
    })
    return transformed
  },

  transformToApiFormat(frontendData) {
    // Transform frontend format to Django API format
    console.log('🔄 Transforming frontend data to API format:', frontendData)
    
    // Ensure we have required fields
    if (!frontendData.customerId) {
      throw new Error('Customer ID is required for appointment creation')
    }
    if (!frontendData.employeeId) {
      throw new Error('Employee ID is required for appointment creation')
    }
    if (!frontendData.selectedService && !frontendData.serviceId) {
      throw new Error('Service selection is required for appointment creation')
    }

    // Handle different data formats (from modal vs direct creation)
    const serviceId = frontendData.selectedService?.id || frontendData.serviceId
    const serviceName = frontendData.selectedService?.name || frontendData.serviceName
    const servicePrice = frontendData.selectedService?.price || frontendData.price || 0
    const serviceDuration = frontendData.selectedService?.duration || frontendData.duration || 60
    const serviceBufferTime = frontendData.selectedService?.bufferTime || frontendData.selectedService?.buffer_time || frontendData.bufferTime || 0

    // Build appointment_services array (required by Django)
    const appointmentServices = []
    
    if (serviceId) {
      appointmentServices.push({
        service: serviceId,
        quantity: 1,
        base_price: parseFloat(servicePrice),
        duration: parseInt(serviceDuration),
        buffer_time: parseInt(serviceBufferTime),
        notes: ''
      })
    }

    // Format start time
    let startTime = frontendData.start || frontendData.startTime || frontendData.selectedDate
    
    // Handle array case (unwrap if needed)
    if (Array.isArray(startTime) && startTime.length > 0) {
      console.log('🚨 Start time is an array, unwrapping first element:', startTime[0])
      startTime = startTime[0]
    }
    
    if (startTime instanceof Date) {
      // Format for Django: YYYY-MM-DDThh:mm:ss+HHMM (no milliseconds, proper timezone)
      startTime = startTime.toISOString().slice(0, 19) + '+0000'
    } else if (startTime && typeof startTime === 'string') {
      // Try to parse string dates
      const parsedDate = new Date(startTime)
      if (!isNaN(parsedDate.getTime())) {
        // Format for Django: YYYY-MM-DDThh:mm:ss+HHMM (no milliseconds, proper timezone)
        startTime = parsedDate.toISOString().slice(0, 19) + '+0000'
      }
    } else if (!startTime) {
      throw new Error('Start time is required but was not provided')
    }
    
    // Final validation - ensure it's a proper string
    if (typeof startTime !== 'string') {
      console.error('🚨 Start time is not a string after processing:', startTime, typeof startTime)
      throw new Error(`Invalid start_time format: expected string, got ${typeof startTime}`)
    }
    
    // Validate Django datetime format: YYYY-MM-DDThh:mm:ss+HHMM
    if (!startTime.includes('T') || !startTime.includes(':') || (!startTime.includes('+') && !startTime.includes('-'))) {
      console.error('🚨 Start time does not appear to be a valid Django datetime string:', startTime)
      throw new Error(`Invalid start_time format: "${startTime}" should be YYYY-MM-DDThh:mm:ss+HHMM`)
    }
    
    console.log('📅 Final validated start_time:', startTime, 'Type:', typeof startTime)

    const apiData = {
      customer: parseInt(frontendData.customerId),
      employee: parseInt(frontendData.employeeId),
      appointment_services: appointmentServices,
      start_time: startTime,
      status: frontendData.status || 'requested',
      notes_from_customer: frontendData.notes || '',
      source: frontendData.source || 'admin'
    }

    // Add optional fields if present
    if (frontendData.selectedAddOns && frontendData.selectedAddOns.length > 0) {
      apiData.appointment_add_ons = frontendData.selectedAddOns.map(addOn => ({
        add_on: addOn.id,
        add_on_price: parseFloat(addOn.price || 0),
        duration: parseInt(addOn.duration || 0)
      }))
    }

    console.log('✅ Transformed API data:', apiData)
    return apiData
  },

  // === VALIDATION METHODS ===

  validateAppointmentData(frontendData) {
    const errors = []
    
    // Check if frontendData exists
    if (!frontendData) {
      errors.push('Appointment data is required')
      return { isValid: false, errors }
    }
    
    // Validate customer selection
    if (!frontendData.customerId) {
      errors.push('Customer selection is required')
    }
    
    // Validate employee assignment
    if (!frontendData.employeeId) {
      errors.push('Employee assignment is required') 
    }
    
    // Validate service selection (check both possible field names)
    if (!frontendData.selectedService && !frontendData.serviceId) {
      errors.push('Service selection is required')
    }
    
    // Validate date and time (check all possible field names)
    if (!frontendData.selectedDate && !frontendData.startTime && !frontendData.start) {
      errors.push('Appointment date and time is required')
    }

    // Additional validation for data integrity
    if (frontendData.selectedDate && !(frontendData.selectedDate instanceof Date) && 
        typeof frontendData.selectedDate !== 'string') {
      errors.push('Invalid date format provided')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  },

  // === UTILITY METHODS ===

  getStatusColor(status) {
    const statusConfig = Object.values(APPOINTMENT_STATUSES).find(s => s.id === status)
    return statusConfig?.color || '#6b7280'
  },

  getStatusDisplayName(status) {
    const statusConfig = Object.values(APPOINTMENT_STATUSES).find(s => s.id === status)
    return statusConfig?.name || status.charAt(0).toUpperCase() + status.slice(1)
  },

  transformAppointmentForCalendar(appointment) {
    if (!appointment) return null;

    const startDateTime = new Date(`${appointment.date}T${appointment.start_time}`);
    const endDateTime = new Date(`${appointment.date}T${appointment.end_time}`);

    return {
      id: appointment.id,
      title: appointment.title || 'Untitled Appointment',
      start: startDateTime,
      end: endDateTime,
      location: appointment.location || '',
      description: appointment.description || '',
      calendarId: appointment.service_id || 'default',
      // Custom calendar standard fields
      color: appointment.color || '#3b82f6',
      borderColor: appointment.border_color || '#2563eb',
      textColor: appointment.text_color || '#ffffff',
      category: appointment.category || 'appointment',
      status: appointment.status || 'confirmed',
      // Additional metadata
      employeeId: appointment.employee_id,
      customerId: appointment.customer_id,
      serviceId: appointment.service_id,
      duration: appointment.duration || 60,
      price: appointment.price || 0,
      notes: appointment.notes || '',
      recurring: appointment.recurring || false,
      reminders: appointment.reminders || []
    };
  },

  // ✅ iOS PATTERN: Client-side filtering like iOS does
  filterAppointmentsClientSide(appointments, dateRange, selectedEmployeeIDs) {
    console.log('🎯 [iOS Pattern] Applying client-side filters:', {
      totalAppointments: appointments.length,
      dateRange,
      selectedEmployees: selectedEmployeeIDs ? [...selectedEmployeeIDs] : 'all'
    })
    
    let filtered = [...appointments]
    
    // ✅ iOS PATTERN: Filter by date range if provided
    if (dateRange && dateRange.start && dateRange.end) {
      const startDate = new Date(dateRange.start)
      const endDate = new Date(dateRange.end)
      
      filtered = filtered.filter(appointment => {
        const appointmentDate = new Date(appointment.start)
        return appointmentDate >= startDate && appointmentDate < endDate
      })
      
      console.log(`📅 [iOS Pattern] Date range filter: ${appointments.length} -> ${filtered.length} appointments`)
    }
    
    // ✅ iOS PATTERN: Filter by selected employees if provided
    if (selectedEmployeeIDs && selectedEmployeeIDs.size > 0) {
      const employeeArray = [...selectedEmployeeIDs]
      
      filtered = filtered.filter(appointment => {
        return employeeArray.includes(appointment.employeeId?.toString()) || 
               employeeArray.includes(appointment.employeeId)
      })
      
      console.log(`👥 [iOS Pattern] Employee filter: ${filtered.length} appointments for employees [${employeeArray.join(', ')}]`)
    }
    
    console.log(`✅ [iOS Pattern] Client-side filtering complete: ${appointments.length} -> ${filtered.length} appointments`)
    return filtered
  },

  // ✅ iOS PATTERN: Cache invalidation when data changes
  invalidateCache() {
    console.log('🗑️ [iOS Pattern] Invalidating appointment cache')
    cachedAppointmentsResult = null
    lastSuccessfulFetch = null
    lastFetchParams = null
    if (fetchDebounceTimer) {
      clearTimeout(fetchDebounceTimer)
      fetchDebounceTimer = null
    }
  }
}

// Export for backward compatibility
export default appointmentService 