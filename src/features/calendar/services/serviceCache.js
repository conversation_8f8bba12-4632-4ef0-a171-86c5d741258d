import { servicesApiService } from './servicesApiService'

/**
 * Global service cache for non-React code (like appointmentService.js)
 * iOS-inspired caching with TTL, automatic invalidation, and local persistence
 */
class ServiceCache {
  constructor() {
    this.services = new Map()
    this.lastFetch = null
    this.CACHE_TTL = 30 * 60 * 1000 // 30 minutes like iOS
    this.isLoading = false
    this.loadPromise = null
    
    // ✅ iOS PATTERN: Local persistence like CoreData
    this.STORAGE_KEY = 'chatbook_services_cache'
    this.TIMESTAMP_KEY = 'chatbook_services_timestamp'
    
    // ✅ iOS PATTERN: Load from localStorage on startup
    this.loadFromLocalStorage()
  }

  /**
   * ✅ iOS PATTERN: Load cached services from localStorage (like CoreData persistence)
   */
  loadFromLocalStorage() {
    try {
      const cachedData = localStorage.getItem(this.STORAGE_KEY)
      const timestamp = localStorage.getItem(this.TIMESTAMP_KEY)
      
      if (cachedData && timestamp) {
        const parsedData = JSON.parse(cachedData)
        const parsedTimestamp = parseInt(timestamp)
        
        // Check if cached data is still valid
        if ((Date.now() - parsedTimestamp) < this.CACHE_TTL) {
          console.log('💾 [iOS Pattern] Loading services from localStorage cache')
          
          // Restore to Map
          this.services.clear()
          parsedData.forEach(service => {
            this.services.set(service.id, service)
          })
          
          this.lastFetch = parsedTimestamp
          console.log(`✅ [iOS Pattern] Loaded ${this.services.size} services from localStorage`)
          return
        } else {
          console.log('⏰ [iOS Pattern] localStorage cache expired, will fetch fresh data')
        }
      }
    } catch (error) {
      console.warn('❌ [iOS Pattern] Failed to load from localStorage:', error)
    }
  }

  /**
   * ✅ iOS PATTERN: Save services to localStorage (like CoreData persistence)
   */
  saveToLocalStorage() {
    try {
      const servicesArray = Array.from(this.services.values())
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(servicesArray))
      localStorage.setItem(this.TIMESTAMP_KEY, this.lastFetch.toString())
      console.log(`💾 [iOS Pattern] Saved ${servicesArray.length} services to localStorage`)
    } catch (error) {
      console.warn('❌ [iOS Pattern] Failed to save to localStorage:', error)
    }
  }

  /**
   * Check if cache is still valid
   */
  isCacheValid() {
    if (!this.lastFetch) return false
    return (Date.now() - this.lastFetch) < this.CACHE_TTL
  }

  /**
   * Get service from cache or fetch if needed
   */
  async getService(serviceId) {
    if (!serviceId) return null

    // ✅ iOS PATTERN: Return from cache if valid
    if (this.isCacheValid() && this.services.has(serviceId)) {
      console.log(`🎯 [iOS Pattern] Service ${serviceId} served from cache`)
      return this.services.get(serviceId)
    }

    // ✅ iOS PATTERN: Load all services if cache is stale
    await this.loadServices()
    return this.services.get(serviceId) || null
  }

  /**
   * Get service name with caching
   */
  async getServiceName(serviceId) {
    if (!serviceId) return 'Unknown Service'
    
    const service = await this.getService(serviceId)
    return service ? service.name : `Service ${serviceId}`
  }

  /**
   * Get service short name with caching
   */
  async getServiceShortName(serviceId) {
    if (!serviceId) return 'Unknown Service'
    
    const service = await this.getService(serviceId)
    return service ? (service.short_name || service.name) : `Service ${serviceId}`
  }

  /**
   * Get service duration with caching
   */
  async getServiceDuration(serviceId) {
    if (!serviceId) return 30 // Default 30 minutes
    
    const service = await this.getService(serviceId)
    return service ? (service.duration || 30) : 30
  }

  /**
   * Load all services with deduplication
   */
  async loadServices() {
    // ✅ iOS PATTERN: Prevent multiple simultaneous loads
    if (this.isLoading && this.loadPromise) {
      console.log('⏭️ [iOS Pattern] Service load already in progress, waiting...')
      return this.loadPromise
    }

    // ✅ iOS PATTERN: Skip if cache is still valid
    if (this.isCacheValid()) {
      console.log('💾 [iOS Pattern] Service cache still valid, skipping fetch')
      return
    }

    this.isLoading = true
    this.loadPromise = this._fetchServices()

    try {
      await this.loadPromise
    } finally {
      this.isLoading = false
      this.loadPromise = null
    }
  }

  /**
   * Private method to fetch services from API
   */
  async _fetchServices() {
    try {
      console.log('🔍 [iOS Pattern] Loading services into cache...')
      const allServices = await servicesApiService.fetchServices()
      
      // ✅ iOS PATTERN: Update cache with fresh data
      this.services.clear()
      allServices.forEach(service => {
        this.services.set(service.id, service)
      })
      
      this.lastFetch = Date.now()
      
      // ✅ iOS PATTERN: Persist to localStorage like CoreData
      this.saveToLocalStorage()
      
      console.log(`✅ [iOS Pattern] Cached ${allServices.length} services with localStorage persistence`)
    } catch (error) {
      console.warn('❌ [iOS Pattern] Failed to load services:', error)
      // Don't clear cache on error - keep stale data like iOS does
    }
  }

  /**
   * ✅ iOS PATTERN: Preload services for faster access (like iOS preloading)
   */
  async preloadServices() {
    console.log('🚀 [iOS Pattern] Preloading services for faster access...')
    
    // If we have valid cache, no need to preload
    if (this.isCacheValid() && this.services.size > 0) {
      console.log('💾 [iOS Pattern] Services already cached, skipping preload')
      return
    }
    
    // Preload in background without blocking
    this.loadServices().catch(error => {
      console.warn('❌ [iOS Pattern] Service preload failed:', error)
    })
  }

  /**
   * Manually invalidate cache (for updates)
   */
  invalidate() {
    console.log('🗑️ [iOS Pattern] Service cache invalidated')
    this.lastFetch = null
    
    // ✅ iOS PATTERN: Clear localStorage as well
    try {
      localStorage.removeItem(this.STORAGE_KEY)
      localStorage.removeItem(this.TIMESTAMP_KEY)
      console.log('🗑️ [iOS Pattern] Cleared localStorage cache')
    } catch (error) {
      console.warn('❌ [iOS Pattern] Failed to clear localStorage:', error)
    }
  }

  /**
   * Get all cached services
   */
  getAllServices() {
    return Array.from(this.services.values())
  }

  /**
   * Check if service exists in cache
   */
  hasService(serviceId) {
    return this.services.has(serviceId)
  }

  /**
   * ✅ iOS PATTERN: Get cache statistics for debugging
   */
  getCacheStats() {
    return {
      size: this.services.size,
      lastFetch: this.lastFetch,
      isValid: this.isCacheValid(),
      timeToExpiry: this.lastFetch ? Math.max(0, this.CACHE_TTL - (Date.now() - this.lastFetch)) : 0,
      isLoading: this.isLoading
    }
  }
}

// Global singleton instance
export const serviceCache = new ServiceCache()

// ✅ iOS PATTERN: Auto-preload services on app startup
export const preloadServices = () => {
  serviceCache.preloadServices()
} 