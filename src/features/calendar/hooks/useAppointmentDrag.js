import { useState, useCallback, useRef } from 'react'
import { appointmentService, validateAppointmentMove, checkAppointmentConflicts } from '../services/appointmentService'
import { validateTimePosition, alignTimeToResolution, calculateNewTimeFromOffset } from '../utils/timeGridCalculations'

/**
 * Custom hook for managing appointment drag operations
 * Based on iOS CalendarAppointmentSlotViewModel drag logic
 */
export const useAppointmentDrag = (hookConfig = {}) => {
  const { isWorkingHourSlot, getWorkingHoursForEmployeeSync, refreshAppointments, config: calendarConfig } = hookConfig
  // Drag state management
  const [dragState, setDragState] = useState({
    isDragging: false,
    draggedAppointment: null,
    originalTime: null,
    newTime: null,
    showConfirmation: false,
    dragMode: 'regular', // 'regular' or 'move' (for future implementation)
    lastSnappedPosition: null,
    movementType: 'time-only',
    targetEmployee: null,
    targetColumn: null,
    finalDragOffset: { x: 0, y: 0 }, // Store the final drag offset for confirmation state
    isEdgeCaseMode: false, // NEW: Track appointments between grid lines
    edgeCaseInfo: null // NEW: Store edge case details
  })

  const [isUpdating, setIsUpdating] = useState(false)
  const [error, setError] = useState(null)
  
  // Refs for tracking drag state and avoiding stale closures
  const dragTimeoutRef = useRef(null)
  const lastValidationRef = useRef(null)
  const dragStateRef = useRef(dragState)
  
  // Keep dragStateRef in sync with dragState
  dragStateRef.current = dragState

  // Handle drag start
  const handleDragStart = useCallback((appointment, mode = 'regular') => {
    
    const originalStartTime = new Date(appointment.start_time || appointment.start)
    
    // ✅ FIXED: Use actual calendar config timeResolution instead of hardcoded value
    const timeResolution = calendarConfig?.timeResolution || 30
    const appointmentMinute = originalStartTime.getMinutes()
    const isExactlyOnGridLine = appointmentMinute % timeResolution === 0
    const startsBetweenGridLines = !isExactlyOnGridLine
    
    let edgeCaseInfo = null
    if (startsBetweenGridLines) {
      const slotMinute = Math.floor(appointmentMinute / timeResolution) * timeResolution
      edgeCaseInfo = {
        appointmentTime: `${originalStartTime.getHours()}:${appointmentMinute.toString().padStart(2, '0')}`,
        slotTime: `${originalStartTime.getHours()}:${slotMinute.toString().padStart(2, '0')}`,
        minuteOffset: appointmentMinute - slotMinute,
        timeResolution: timeResolution,
        detectedAt: 'dragStart',
        reason: 'Appointment starts between time grid lines - will bypass snapping'
      }
    }
    
    setDragState(prev => ({
        ...prev,
        isDragging: true,
        draggedAppointment: appointment,
        originalTime: originalStartTime,
        newTime: originalStartTime, // Set newTime immediately to show drag line
        dragMode: mode,
        showConfirmation: false, // Reset confirmation state
        isEdgeCaseMode: startsBetweenGridLines, // TRUE only when appointment falls between grid lines
        edgeCaseInfo: edgeCaseInfo
      }))
    
    setError(null)
  }, [calendarConfig?.timeResolution])

  // Handle drag movement (real-time updates during drag)
  const handleDragMove = useCallback((appointment, dragOffset, dragInfo) => {
    if (!dragStateRef.current.isDragging || appointment.id !== dragStateRef.current.draggedAppointment?.id) {
      return
    }

    // ✅ FIXED: Calculate newTime based on original API start_time + drag offset
    const actualTimeResolution = dragInfo.timeResolution || calendarConfig?.timeResolution || 30
    
    // Use ORIGINAL appointment position for edge case detection (not current drag position)
    const originalAppointment = dragStateRef.current.draggedAppointment
    const originalStartTime = new Date(originalAppointment.start_time || originalAppointment.start)
    const originalMinute = originalStartTime.getMinutes()
    const isOriginallyOnGridLine = originalMinute % actualTimeResolution === 0
    const isEdgeCaseMode = !isOriginallyOnGridLine
    
    // ✅ CRITICAL FIX: Calculate newTime from original API start_time + drag offset
    // This ensures the drag line moves WITH the appointment block
    const newTimeFromOffset = calculateNewTimeFromOffset(
      dragOffset.y || 0, // Vertical drag offset in pixels
      { start: originalStartTime }, // Use original API start_time
      { 
        timeResolution: actualTimeResolution,
        gridHeight: calendarConfig?.gridHeight || 1200,
        displayHourStart: calendarConfig?.displayHourStart || 8
      }
    )

    // SNAPPING LOGIC: Use edge case mode from ORIGINAL position, not current position
    let finalTime
    let updatedEdgeCaseInfo = null
    
    if (isEdgeCaseMode) {
      // ✅ EDGE CASE: Original appointment starts between grid lines - bypass snapping
      finalTime = newTimeFromOffset // Use calculated time without additional snapping
      
      const originalSlotMinute = Math.floor(originalMinute / actualTimeResolution) * actualTimeResolution
      updatedEdgeCaseInfo = {
        appointmentTime: `${originalStartTime.getHours()}:${originalMinute.toString().padStart(2, '0')}`,
        slotTime: `${originalStartTime.getHours()}:${originalSlotMinute.toString().padStart(2, '0')}`,
        minuteOffset: originalMinute - originalSlotMinute,
        timeResolution: actualTimeResolution,
        detectedAt: 'dragMove',
        bypassedSnapping: true,
        reason: 'Original appointment starts between time grid lines - preserving exact drag position'
      }
      
    } else {
      // ✅ NORMAL CASE: Original appointment exactly on grid line - apply standard snapping
      finalTime = alignTimeToResolution(newTimeFromOffset, actualTimeResolution)
      updatedEdgeCaseInfo = null
    }

    // Update drag state with final time and movement info - immediate update for visual feedback
    setDragState(prev => {
      const newState = {
        ...prev,
        newTime: finalTime, // Use final time (calculated from original start_time + offset)
        lastSnappedPosition: dragInfo.offset,
        movementType: dragInfo.movementType || 'time-only',
        targetEmployee: dragInfo.targetEmployee,
        targetColumn: dragInfo.targetColumn,
        isEdgeCaseMode: isEdgeCaseMode, // TRUE only when ORIGINAL appointment falls between grid lines
        edgeCaseInfo: updatedEdgeCaseInfo // Edge case details or null
      }
      
      return newState
    })

    // Debounced validation to avoid excessive API calls
    if (dragTimeoutRef.current) {
      clearTimeout(dragTimeoutRef.current)
    }

    dragTimeoutRef.current = setTimeout(() => {
      validateDragPosition(appointment, finalTime, dragInfo)
    }, 300) // 300ms debounce

  }, [calendarConfig?.timeResolution, calendarConfig?.gridHeight, calendarConfig?.displayHourStart])

  // Handle drag completion
  const handleDragEnd = useCallback((appointment, dragOffset, dragInfo) => {

    if (!dragInfo.completed) {
      // Drag was cancelled - reset state
      setDragState({
        isDragging: false,
        draggedAppointment: null,
        originalTime: null,
        newTime: null,
        showConfirmation: false,
        dragMode: 'regular',
        lastSnappedPosition: null,
        movementType: 'time-only',
        targetEmployee: null,
        targetColumn: null,
        isEdgeCaseMode: false,
        edgeCaseInfo: null
      })
      setError(null)
      return
    }

    // ✅ Calculate final newTime the same way as in handleDragMove
    const actualTimeResolution = dragInfo.timeResolution || calendarConfig?.timeResolution || 30
    const originalStartTime = new Date(appointment.start_time || appointment.start)
    
    const newTimeFromOffset = calculateNewTimeFromOffset(
      dragOffset.y || 0,
      { start: originalStartTime },
      { 
        timeResolution: actualTimeResolution,
        gridHeight: calendarConfig?.gridHeight || 1200,
        displayHourStart: calendarConfig?.displayHourStart || 8
      }
    )
    
    // Apply same snapping logic as in handleDragMove
    const originalMinute = originalStartTime.getMinutes()
    const isOriginallyOnGridLine = originalMinute % actualTimeResolution === 0
    const isEdgeCaseMode = !isOriginallyOnGridLine
    
    const finalNewTime = isEdgeCaseMode 
      ? newTimeFromOffset 
      : alignTimeToResolution(newTimeFromOffset, actualTimeResolution)

    // Use appointment.start as the source of truth for original time
    const originalTime = originalStartTime
    const newTimeDate = new Date(finalNewTime)
    const timeDifference = Math.abs(newTimeDate - originalTime)
    const minChangeThreshold = 5 * 60 * 1000 // 5 minutes in milliseconds

    // Check for employee change - this is significant for cross-employee moves
    const hasEmployeeChange = dragInfo.targetEmployee && 
                              dragInfo.targetEmployee.id !== appointment.employeeId

    console.log('🎯 Change detection:', {
      originalTime: originalTime.toISOString(),
      newTime: newTimeDate.toISOString(),
      timeDifference: timeDifference,
      minChangeThreshold: minChangeThreshold,
      hasTimeChange: timeDifference >= minChangeThreshold,
      hasEmployeeChange: hasEmployeeChange,
      originalEmployee: appointment.employeeId,
      targetEmployee: dragInfo.targetEmployee?.id,
      targetEmployeeName: dragInfo.targetEmployee?.name || dragInfo.targetEmployee?.full_name,
      shouldShowConfirmation: timeDifference >= minChangeThreshold || hasEmployeeChange
    })

    // Show confirmation if there's significant time change OR employee change
    if (timeDifference < minChangeThreshold && !hasEmployeeChange) {
      console.log('🎯 No significant changes, cancelling move')
      setDragState({
        isDragging: false,
        draggedAppointment: null,
        originalTime: null,
        newTime: null,
        showConfirmation: false,
        dragMode: 'regular',
        lastSnappedPosition: null,
        movementType: 'time-only',
        targetEmployee: null,
        targetColumn: null,
        isEdgeCaseMode: false,
        edgeCaseInfo: null
      })
      setError(null)
      return
    }

    // Show confirmation dialog for significant changes
    console.log('🎯 Showing confirmation dialog - setting state')
    setDragState(prev => {
      const newState = {
        ...prev,
        draggedAppointment: appointment, // Make sure appointment is set
        originalTime: originalTime, // Store the original time
        newTime: newTimeDate,
        showConfirmation: true,
        movementType: dragInfo.movementType || 'time-only',
        targetEmployee: dragInfo.targetEmployee,
        targetColumn: dragInfo.targetColumn,
        finalDragOffset: dragInfo.offset || { x: 0, y: 0 } // Store the final drag offset
      }
      console.log('🎯 New drag state:', newState)
      return newState
    })

  }, [calendarConfig?.timeResolution, calendarConfig?.gridHeight, calendarConfig?.displayHourStart])

  // Validate drag position (business hours, conflicts, etc.)
  const validateDragPosition = useCallback(async (appointment, newTime, dragInfo = {}) => {
    try {
      // Calculate end time based on appointment duration
      const startTime = new Date(newTime)
      const originalStart = new Date(appointment.start)
      const originalEnd = new Date(appointment.end || appointment.endTime)
      const duration = originalEnd - originalStart
      const endTime = new Date(startTime.getTime() + duration)

      // Determine target employee for validation - use target employee if cross-employee move
      const targetEmployeeId = dragInfo.targetEmployee?.id || appointment.employeeId

      // First validate time position against resolution and business rules
      const timeValidation = validateTimePosition(startTime, {
        timeResolution: dragInfo.timeResolution || 15,
        displayHourStart: 8, // Default business hours
        displayHourEnd: 18,
        showWorkingHours: true
      })

      if (!timeValidation.isValid) {
        setError(timeValidation.message)
        return false
      }

      // Validate business rules using existing service - use target employee
      const validation = validateAppointmentMove(
        appointment,
        startTime.toISOString(),
        endTime.toISOString(),
        targetEmployeeId
      )

      if (!validation.isValid) {
        setError(validation.message)
        return false
      }

      // Check for conflicts - use target employee for conflict checking
      const conflictCheck = await checkAppointmentConflicts(
        appointment,
        startTime.toISOString(),
        endTime.toISOString(),
        targetEmployeeId
      )

      if (conflictCheck.hasConflict) {
        const employeeInfo = dragInfo.targetEmployee ? 
          ` for ${dragInfo.targetEmployee.name || dragInfo.targetEmployee.full_name}` : ''
        setError(`Appointment conflicts with existing booking${employeeInfo}`)
        return false
      }

      // Clear any previous errors
      setError(null)
      return true

    } catch (err) {
      console.error('❌ Validation error:', err)
      setError('Failed to validate appointment move')
      return false
    }
  }, [])

  // Confirm appointment move
  const confirmMove = useCallback(async (notifyCustomer = true) => {
    if (!dragStateRef.current.draggedAppointment || !dragStateRef.current.newTime) {
      console.error('❌ No appointment or new time to confirm')
      return
    }

    setIsUpdating(true)
    setError(null)

    try {
      const appointment = dragStateRef.current.draggedAppointment
      const newStartTime = new Date(dragStateRef.current.newTime)
      
      // Calculate new end time maintaining duration
      const originalStart = new Date(appointment.start)
      const originalEnd = new Date(appointment.end || appointment.endTime)
      const duration = originalEnd - originalStart
      const newEndTime = new Date(newStartTime.getTime() + duration)

      console.log('🔄 Updating appointment:', {
        appointmentId: appointment.id,
        originalStart: originalStart.toISOString(),
        newStart: newStartTime.toISOString(),
        newEnd: newEndTime.toISOString(),
        originalEmployee: appointment.employeeId,
        targetEmployee: dragStateRef.current.targetEmployee?.id,
        targetEmployeeName: dragStateRef.current.targetEmployee?.name || dragStateRef.current.targetEmployee?.full_name,
        notifyCustomer
      })

      // Update appointment via API - send only the fields that need updating
      const appointmentUpdateData = {
        // Time-related fields (the main update) - use Django format YYYY-MM-DDThh:mm:ss+HHMM
        start_time: newStartTime.toISOString().slice(0, 19) + '+0000',
        end_time: newEndTime.toISOString().slice(0, 19) + '+0000',
        // Keep existing status and notification preferences
        status: appointment.status || 'confirmed',
        // Keep existing notes
        notes_from_customer: appointment.notes || appointment.description || ''
      }
      
      // Include employee change if target employee is different
      if (dragStateRef.current.targetEmployee && 
          dragStateRef.current.targetEmployee.id !== appointment.employeeId) {
        appointmentUpdateData.employee = parseInt(dragStateRef.current.targetEmployee.id)
        console.log('🔧 Including employee change in update:', {
          from: appointment.employeeId,
          to: dragStateRef.current.targetEmployee.id,
          targetEmployeeName: dragStateRef.current.targetEmployee.name || dragStateRef.current.targetEmployee.full_name
        })
      }
      
      // If notifyCustomer is provided, include it (Django might expect this field)
      if (notifyCustomer !== undefined) {
        appointmentUpdateData.notify_customer = notifyCustomer
      }

      console.log('🔄 Full appointment update data:', appointmentUpdateData)

      // Use patch method for simple updates instead of full update
      const updatedAppointment = await appointmentService.patchAppointment(
        appointment.id,
        appointmentUpdateData
      )

      console.log('✅ Appointment updated successfully:', updatedAppointment)

      // Reset drag state
      resetDragState()

      // Return updated appointment for parent component to handle
      return updatedAppointment

    } catch (err) {
      console.error('❌ Failed to update appointment:', err)
      setError(err.message || 'Failed to update appointment')
      throw err
    } finally {
      setIsUpdating(false)
    }
  }, [dragState.draggedAppointment, dragState.newTime])

  // Cancel appointment move
  const cancelMove = useCallback(() => {
    console.log('❌ Appointment move cancelled - resetting visual position')
    
    const appointmentToReset = dragStateRef.current.draggedAppointment
    
    // Force immediate visual reset by clearing any pending transformations
    if (appointmentToReset) {
      // Find the appointment element and reset its transform immediately
      const appointmentElement = document.querySelector(`[data-appointment-id="${appointmentToReset.id}"]`)
      if (appointmentElement) {
        appointmentElement.style.transform = 'translate3d(0px, 0px, 0px)'
        appointmentElement.style.transition = 'transform 0.2s ease-out'
        console.log('🔄 Immediately reset visual position for appointment:', appointmentToReset.id)
      }
    }
    
    // Reset drag state with immediate visual feedback
    resetDragState()
    
    // Force immediate state update to trigger re-render with original position
    setDragState(prev => ({
      ...prev,
      isDragging: false,
      draggedAppointment: null,
      finalDragOffset: { x: 0, y: 0 },
      showConfirmation: false
    }))
    
    // Force refresh the appointments to restore visual position
    if (appointmentToReset && refreshAppointments) {
      console.log('🔄 Refreshing appointments after move cancellation for appointment:', appointmentToReset.id)
      
      // Use the provided refresh callback to reload appointment data
      // This will restore the appointment to its original position in the UI
      setTimeout(() => {
        refreshAppointments()
      }, 50)
    }
  }, [dragState.draggedAppointment, refreshAppointments])

  // Reset drag state
  const resetDragState = useCallback(() => {
    if (dragTimeoutRef.current) {
      clearTimeout(dragTimeoutRef.current)
    }

    setDragState({
      isDragging: false,
      draggedAppointment: null,
      originalTime: null,
      newTime: null,
      showConfirmation: false,
      dragMode: 'regular',
      lastSnappedPosition: null,
      movementType: 'time-only',
      targetEmployee: null,
      targetColumn: null,
      finalDragOffset: { x: 0, y: 0 },
      isEdgeCaseMode: false,
      edgeCaseInfo: null
    })
    
    setError(null)
  }, [])

  // Calculate time difference for display
  const getTimeDifference = useCallback(() => {
    if (!dragStateRef.current.originalTime || !dragStateRef.current.newTime) return null

    const diff = new Date(dragStateRef.current.newTime) - new Date(dragStateRef.current.originalTime)
    const minutes = Math.round(diff / (1000 * 60))
    const hours = Math.floor(Math.abs(minutes) / 60)
    const remainingMinutes = Math.abs(minutes) % 60

    return {
      minutes,
      hours,
      remainingMinutes,
      isEarlier: minutes < 0,
      formatted: hours > 0 
        ? `${hours}h ${remainingMinutes}m` 
        : `${Math.abs(minutes)}m`
    }
  }, [dragState.originalTime, dragState.newTime])

  // Check if appointment has actually moved
  const hasAppointmentMoved = useCallback(() => {
    if (!dragStateRef.current.originalTime || !dragStateRef.current.newTime) return false
    
    const timeDifference = Math.abs(new Date(dragStateRef.current.newTime) - new Date(dragStateRef.current.originalTime))
    const minChangeThreshold = 5 * 60 * 1000 // 5 minutes
    
    return timeDifference >= minChangeThreshold
  }, [dragState.originalTime, dragState.newTime])

  // Get current drag validation status
  const getDragValidation = useCallback(() => {
    return {
      isValid: !error,
      error: error,
      hasConflict: error?.includes('conflict'),
      isOutsideHours: error?.includes('business hours')
    }
  }, [error])

  return {
    // State
    dragState,
    isUpdating,
    error,
    
    // Actions
    handleDragStart,
    handleDragMove,
    handleDragEnd,
    confirmMove,
    cancelMove,
    resetDragState,
    
    // Computed values
    getTimeDifference,
    hasAppointmentMoved,
    getDragValidation,
    
    // Helper flags - use direct state values for real-time updates
    isDragging: dragState.isDragging,
    showConfirmation: dragState.showConfirmation,
    draggedAppointment: dragState.draggedAppointment,
    originalTime: dragState.originalTime,
    newTime: dragState.newTime,
    movementType: dragState.movementType,
    targetEmployee: dragState.targetEmployee,
    targetColumn: dragState.targetColumn,
    finalDragOffset: dragState.finalDragOffset,
    isEdgeCaseMode: dragState.isEdgeCaseMode,
    edgeCaseInfo: dragState.edgeCaseInfo
  }
} 