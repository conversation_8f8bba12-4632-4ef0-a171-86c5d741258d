import { useRef, useCallback } from 'react'

/**
 * iOS-inspired API debouncing hook
 * Prevents rapid successive API calls like iOS AppointmentManager
 */
export const useAPIDebounce = (delay = 1000) => {
  const timeoutRef = useRef()
  const lastCallRef = useRef()
  const inProgressRef = useRef(false)
  
  const debouncedCall = useCallback((fn, forceRefresh = false) => {
    const now = Date.now()
    
    // ✅ iOS PATTERN: Skip if call in progress (unless forced)
    if (inProgressRef.current && !forceRefresh) {
      console.log('⏭️ [iOS Pattern] API call in progress, returning cached result')
      return Promise.resolve(null)
    }
    
    // ✅ iOS PATTERN: Skip if too recent (unless forced)
    if (lastCallRef.current && (now - lastCallRef.current) < delay && !forceRefresh) {
      const timeSinceLastCall = now - lastCallRef.current
      console.log(`⏭️ [iOS Pattern] Skipping API call due to debounce (${timeSinceLastCall}ms < ${delay}ms)`)
      return Promise.resolve(null)
    }
    
    // Clear any pending timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    
    // Mark as in progress and update timestamp
    inProgressRef.current = true
    lastCallRef.current = now
    
    console.log('🔄 [iOS Pattern] Executing debounced API call')
    
    // Execute the function
    return fn().finally(() => {
      inProgressRef.current = false
    })
  }, [delay])
  
  const isInProgress = useCallback(() => {
    return inProgressRef.current
  }, [])
  
  const getLastCallTime = useCallback(() => {
    return lastCallRef.current
  }, [])
  
  const reset = useCallback(() => {
    inProgressRef.current = false
    lastCallRef.current = null
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
  }, [])
  
  return {
    debouncedCall,
    isInProgress,
    getLastCallTime,
    reset
  }
} 