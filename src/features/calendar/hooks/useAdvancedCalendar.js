import { useState, useEffect, useCallback, useMemo, useRef } from 'react'
import { appointmentService } from '../services/appointmentService'
import { workingHoursManager } from '../services/workingHoursService'
import { workingHoursService } from '../services/workingHoursService'
import { useCalendarConfig } from './useCalendarConfig'
import { servicesApiService } from '../services/servicesApiService'
import { useEmployees } from '../../employees/hooks'
import { generateWeekDays } from '../utils/weekUtils'
// ✅ iOS PATTERNS: Add React Query and debouncing
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { useServicesQuery, useServiceCategoriesQuery } from './useServicesQuery'
import { useAPIDebounce } from './useAPIDebounce'
import { preloadServices } from '../services/serviceCache'

// Display modes matching iOS implementation
export const DISPLAY_MODES = {
  DAY: 'day',
  WEEK: 'week'
}

// Navigation directions
export const NAVIGATION_DIRECTION = {
  FORWARD: 'forward',
  BACKWARD: 'backward'
}

export const useAdvancedCalendar = () => {
  // Calendar configuration
  const { 
    config, 
    updateConfig,
    setTimeResolution, 
    setDisplayHours, 
    setWeekStartDay,
    getTimeSlots,
    getDisplayHours 
  } = useCalendarConfig()

  // Employee data from Django API
  const { 
    employees: apiEmployees, 
    currentEmployee, 
    loading: employeesLoading, 
    error: employeesError 
  } = useEmployees()

  // Core state
  const [selectedDate, setSelectedDate] = useState(new Date())
  const [displayMode, setDisplayMode] = useState(config.defaultView || DISPLAY_MODES.WEEK)
  
  // Update displayMode when config.defaultView changes (only from settings, not manual switching)
  useEffect(() => {
    if (config.defaultView && config.defaultView !== displayMode) {
      setDisplayMode(config.defaultView)
    }
  }, [config.defaultView])
  // ✅ Removed redundant appointments state - using React Query data directly
  const [workingHours, setWorkingHours] = useState({})
  const [workingHoursVersion, setWorkingHoursVersion] = useState(0) // Track working hours updates
  const [isLoading, setIsLoading] = useState(false)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [error, setError] = useState(null)
  
  // Employee and service selection (needed for React Query)
  const [selectedEmployeeIDs, setSelectedEmployeeIDs] = useState(new Set())
  const [selectedServices, setSelectedServices] = useState(new Set())
  
  // ✅ iOS PATTERN: Smart date range calculation with error handling
  const dateRange = useMemo(() => {
    try {
      // Validate selectedDate first
      if (!selectedDate || isNaN(selectedDate.getTime())) {
        console.error('❌ Invalid selectedDate, using current date:', selectedDate)
        const fallbackDate = new Date()
        setSelectedDate(fallbackDate)
        return {
          start: fallbackDate.toISOString().slice(0, 10),
          end: fallbackDate.toISOString().slice(0, 10)
        }
      }

      console.log('🔍 [DEBUG] Date range calculation inputs:', {
        selectedDate: selectedDate,
        selectedDateString: selectedDate.toISOString(),
        displayMode: displayMode,
        weekStartDay: config.weekStartDay
      })
      
      if (displayMode === DISPLAY_MODES.DAY) {
        // Day view: just the selected day
        const dateStr = selectedDate.toISOString().slice(0, 10)
        console.log('🔍 [DEBUG] Day mode date range:', { start: dateStr, end: dateStr })
        return { start: dateStr, end: dateStr }
      } else {
        // Week view: calculate based on week start day setting
        console.log('🔍 [DEBUG] BEFORE generateWeekDays - selectedDate:', selectedDate.toISOString())
        const weekDays = generateWeekDays(selectedDate, config.weekStartDay)
        
        // Validate week days
        if (!weekDays || weekDays.length !== 7) {
          console.error('❌ Invalid weekDays generated:', weekDays)
          const fallbackDate = selectedDate.toISOString().slice(0, 10)
          return { start: fallbackDate, end: fallbackDate }
        }
        
        console.log('🔍 [DEBUG] AFTER generateWeekDays - weekDays:', weekDays.map(d => d.toISOString()))
        
        const startDate = weekDays[0] // First day of week
        const endDate = weekDays[6]   // Last day of week
        
        console.log('🔍 [DEBUG] Week mode calculation:', {
          selectedDate: selectedDate.toISOString(),
          weekStartDay: config.weekStartDay,
          startDate: startDate.toISOString().slice(0, 10),
          endDate: endDate.toISOString().slice(0, 10)
        })
        
        const result = {
          start: startDate.toISOString().slice(0, 10),
          end: endDate.toISOString().slice(0, 10)
        }
        
        console.log('🔍 [DEBUG] Final date range result:', result)
        return result
      }
    } catch (error) {
      console.error('❌ Error in date range calculation:', error)
      // Fallback to current date
      const fallbackDate = new Date().toISOString().slice(0, 10)
      return { start: fallbackDate, end: fallbackDate }
    }
  }, [selectedDate, displayMode, config.weekStartDay])
  
  // ✅ Smart appointments query that supports both Day and Week views efficiently
  // For Day view, expand to week range but filter afterward to reuse cache
  const fetchDateRange = useMemo(() => {
    try {
      if (displayMode === DISPLAY_MODES.DAY) {
        // For Day view, fetch the whole week but we'll filter the results
        // This allows cache reuse and ensures Day view gets data
        if (!selectedDate || isNaN(selectedDate.getTime())) {
          console.error('❌ Invalid selectedDate in fetchDateRange:', selectedDate)
          return dateRange // Fallback to dateRange
        }
        
        const weekDays = generateWeekDays(selectedDate, config.weekStartDay)
        if (!weekDays || weekDays.length !== 7) {
          console.error('❌ Invalid weekDays in fetchDateRange:', weekDays)
          return dateRange // Fallback to dateRange
        }
        
        const expandedRange = {
          start: weekDays[0].toISOString().slice(0, 10),
          end: weekDays[6].toISOString().slice(0, 10)
        }
        console.log('📅 [FIX] Day view using expanded week range for better caching:', expandedRange)
        return expandedRange
      } else {
        // Week view uses the calculated date range as-is
        return dateRange
      }
    } catch (error) {
      console.error('❌ Error in fetchDateRange calculation:', error)
      return dateRange // Fallback to basic dateRange
    }
  }, [selectedDate, displayMode, config.weekStartDay, dateRange])

  const { 
    data: rawQueryAppointments = [], 
    isLoading: appointmentsLoading, 
    error: appointmentsError, 
    refetch: refetchAppointments 
  } = useQuery({
    queryKey: ['appointments', fetchDateRange, [...selectedEmployeeIDs].sort()],
    queryFn: async () => {
      try {
        const result = await appointmentService.fetchAppointments(fetchDateRange, selectedEmployeeIDs)
        // Ensure we return an array and filter out any invalid appointments
        const validAppointments = Array.isArray(result) ? result : []
        console.log(`📅 [FIX] Fetched ${validAppointments.length} appointments, validating...`)
        
        return validAppointments.filter((apt, index) => {
          if (!apt || typeof apt !== 'object') {
            console.warn(`⚠️ Invalid appointment at index ${index}:`, apt)
            return false
          }
          
          const hasStartTime = apt.start_time || apt.start
          if (!hasStartTime) {
            console.warn(`⚠️ Appointment ${apt.id || index} missing start time:`, apt)
            return false
          }
          
          return true
        })
      } catch (error) {
        console.error('❌ Error fetching appointments:', error)
        return [] // Return empty array on error
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 30 * 60 * 1000, // 30 minutes
    refetchOnWindowFocus: false
  })

  // ✅ Filter appointments based on actual display mode with error handling
  const queryAppointments = useMemo(() => {
    if (displayMode === DISPLAY_MODES.DAY) {
      // For Day view, filter the week data to only show selected day
      const dayString = selectedDate.toISOString().slice(0, 10)
      const filteredAppointments = rawQueryAppointments.filter(apt => {
        try {
          // Handle both start_time and start field names, with validation
          const startTime = apt.start_time || apt.start
          if (!startTime) {
            console.warn('⚠️ Appointment missing start time:', apt)
            return false
          }
          
          const aptDate = new Date(startTime)
          if (isNaN(aptDate.getTime())) {
            console.warn('⚠️ Invalid appointment date:', startTime, apt)
            return false
          }
          
          const aptDateString = aptDate.toISOString().slice(0, 10)
          return aptDateString === dayString
        } catch (error) {
          console.error('❌ Error filtering appointment:', apt, error)
          return false
        }
      })
      console.log(`📅 [FIX] Day view filtered ${rawQueryAppointments.length} → ${filteredAppointments.length} appointments for ${dayString}`)
      return filteredAppointments
    } else {
      // Week view uses all fetched appointments (with basic validation)
      return rawQueryAppointments.filter(apt => {
        try {
          const startTime = apt.start_time || apt.start
          if (!startTime) return false
          
          const aptDate = new Date(startTime)
          return !isNaN(aptDate.getTime())
        } catch (error) {
          console.error('❌ Error validating appointment:', apt, error)
          return false
        }
      })
    }
  }, [rawQueryAppointments, displayMode, selectedDate])
  
  // ✅ DEBUG: Log current selection and query parameters
  useEffect(() => {
    console.log('🔄 [DEBUG] Calendar state updated:', {
      displayMode,
      dateRange,
      fetchDateRange,
      selectedEmployeeIDs: [...selectedEmployeeIDs],
      rawQueryAppointments: rawQueryAppointments.length,
      filteredQueryAppointments: queryAppointments.length,
      note: displayMode === DISPLAY_MODES.DAY ? 'Day view: fetching week but filtering to day' : 'Week view: using all fetched data'
    })
  }, [dateRange, fetchDateRange, selectedEmployeeIDs, rawQueryAppointments.length, queryAppointments.length, displayMode])
  
  const { 
    data: availableServices = [], 
    isLoading: servicesLoading, 
    error: servicesError 
  } = useServicesQuery()
  
  const { 
    data: serviceCategories = [], 
    isLoading: categoriesLoading, 
    error: categoriesError 
  } = useServiceCategoriesQuery()
  
  // ✅ iOS PATTERN: Preload service cache for appointmentService.js lookup methods
  useEffect(() => {
    console.log('🔥 [iOS Pattern] Preloading service cache...')
    preloadServices()
  }, [])
  
  // ✅ iOS PATTERN: Use React Query data directly instead of duplicating state
  // No need for separate appointments state since queryAppointments provides the data
  const appointments = queryAppointments || []
  
  // Transform API employees to match calendar format
  const employees = useMemo(() => {
    if (!apiEmployees || apiEmployees.length === 0) {
      // Fallback to mock employees if API fails
      return [
        { id: 'emp1', name: 'Sarah Johnson', color: '#3b82f6', avatar: 'SJ' },
        { id: 'emp2', name: 'Mike Chen', color: '#10b981', avatar: 'MC' },
        { id: 'emp3', name: 'Lisa Rodriguez', color: '#f59e0b', avatar: 'LR' },
        { id: 'emp4', name: 'David Kim', color: '#ef4444', avatar: 'DK' },
      ]
    }

    // Debug: Log the raw API data to see the actual structure
    console.log('Raw Django API employees data:', apiEmployees)

    // Transform Django API employees to calendar format
    const colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#ec4899']
    return apiEmployees.map((emp, index) => {
      // Use the correct field name from your Django API
      const displayName = emp.full_name || emp.name || `Employee ${index + 1}`
      
      // Generate avatar from the display name (first letter of each word)
      const avatar = displayName.split(' ').map(word => word.charAt(0)).join('').toUpperCase().substring(0, 2)

      const transformedEmployee = {
        id: emp.id?.toString() || `emp-${index}`,
        name: displayName,
        color: colors[index % colors.length],
        avatar: avatar || 'U',
        stylist_level: emp.stylist_level,
        stylist_level_display: emp.stylist_level_display,
        is_active: emp.is_active,
        // Keep original data for debugging
        _original: emp
      }

      console.log(`✅ Transformed employee ${index}:`, transformedEmployee)
      return transformedEmployee
    })
  }, [apiEmployees])
  

  
  // ✅ iOS PATTERN: Debouncing hook for API calls
  const { debouncedCall } = useAPIDebounce(1000) // 1 second like iOS

  // Initialize employee selection when employees are loaded
  useEffect(() => {
    console.log('🔍 Employee selection useEffect running:', {
      employeesLength: employees.length,
      selectedEmployeeIDsSize: selectedEmployeeIDs.size,
      currentEmployee: currentEmployee,
      userDataInStorage: localStorage.getItem('user_data')
    })
    
    // Run auto-selection when employees are loaded (regardless of existing selection)
    if (employees.length > 0) {
      let selectedEmployeeId = null
      let shouldUpdateSelection = false
      
      console.log('🔍 Starting auto-selection process with employees:', employees.map(emp => ({
        id: emp.id,
        name: emp.name,
        full_name: emp.full_name,
        original: emp._original
      })))
      
      // Priority 1: Use the current employee from the useEmployees hook
      if (currentEmployee && currentEmployee.id) {
        selectedEmployeeId = currentEmployee.id.toString()
        shouldUpdateSelection = true
        console.log('🎯 Auto-selecting current employee from hook:', currentEmployee.full_name || currentEmployee.name, 'ID:', selectedEmployeeId)
      } else {
        console.log('❌ No currentEmployee from hook:', currentEmployee)
      }
      
      // Priority 2: Try to match the logged-in user with employee records
      if (!selectedEmployeeId) {
        const userDataString = localStorage.getItem('user_data')
        console.log('🔍 Checking localStorage user_data:', userDataString)
        
        if (userDataString) {
          try {
            const userData = JSON.parse(userDataString)
            const userFullName = `${userData.first_name} ${userData.last_name}`.trim()
            
            console.log('🔍 Looking for employee matching user:', {
              userId: userData.id,
              userEmail: userData.email,
              userFullName: userFullName
            })
            
            // Find employee that matches the logged-in user
            const matchingEmployee = employees.find(emp => {
              const matches = {
                userIdMatch: emp._original?.user?.id === userData.id,
                emailMatch: emp._original?.user_details?.email === userData.email,
                nameMatch: emp.name === userFullName,
                originalEmailMatch: emp._original?.email === userData.email
              }
              
              console.log(`🔍 Checking employee ${emp.name}:`, matches)
              
              return (
                matches.userIdMatch ||
                matches.emailMatch ||
                matches.nameMatch ||
                matches.originalEmailMatch
              )
            })
            
            if (matchingEmployee) {
              selectedEmployeeId = matchingEmployee.id?.toString()
              shouldUpdateSelection = true
              console.log('🎯 Auto-selecting employee matching logged-in user:', matchingEmployee.name || matchingEmployee.full_name, 'ID:', selectedEmployeeId)
            } else {
              console.log('❌ No employee found matching user data')
            }
          } catch (error) {
            console.warn('Failed to parse user data for employee matching:', error)
          }
        } else {
          console.log('❌ No user_data in localStorage')
        }
      }
      
      // Priority 3: Only use fallback if no selection exists and no user match found
      if (!selectedEmployeeId && selectedEmployeeIDs.size === 0) {
        selectedEmployeeId = employees[0]?.id?.toString()
        shouldUpdateSelection = true
        console.log('⚠️ Auto-selecting first employee as fallback:', employees[0]?.name || employees[0]?.full_name, 'ID:', selectedEmployeeId)
      }
      
      // Update selection if we found a user-specific employee or need fallback
      if (selectedEmployeeId && shouldUpdateSelection) {
        const currentSelection = Array.from(selectedEmployeeIDs)[0]
        if (currentSelection !== selectedEmployeeId) {
          console.log('✅ Updating selectedEmployeeIDs from', currentSelection, 'to:', selectedEmployeeId)
          setSelectedEmployeeIDs(new Set([selectedEmployeeId]))
        } else {
          console.log('✅ Already selected correct employee:', selectedEmployeeId)
        }
      } else if (!selectedEmployeeId) {
        console.log('❌ No employee ID to select')
      }
    } else {
      console.log('❌ No employees loaded yet')
    }
  }, [employees, currentEmployee]) // Removed selectedEmployeeIDs.size from deps so it runs when currentEmployee changes
  
  // UI state
  const [scrollPosition, setScrollPosition] = useState(() => {
    return config.rememberScrollPosition 
      ? parseInt(localStorage.getItem('calendar-scroll-position') || '0')
      : 0
  })
  const [viewDidAppear, setViewDidAppear] = useState(false)
  const [isViewReady, setIsViewReady] = useState(false)
  
  // Real-time updates
  const refreshIntervalRef = useRef(null)
  const [lastRefreshTime, setLastRefreshTime] = useState(new Date())
  
  // Performance tracking
  const [performanceMetrics, setPerformanceMetrics] = useState({
    appointmentLoadTime: 0,
    workingHoursLoadTime: 0,
    renderTime: 0
  })

  // Generate display days based on current mode and selected date
  const displayDays = useMemo(() => {
    if (displayMode === DISPLAY_MODES.DAY) {
      return [new Date(selectedDate)]
    }
    
    // Week view - use utility function for consistent week generation
    return generateWeekDays(selectedDate, config.weekStartDay)
  }, [selectedDate, displayMode, config.weekStartDay])

  // Selected employees as array
  const selectedEmployees = useMemo(() => {
    return employees.filter(emp => selectedEmployeeIDs.has(emp.id))
  }, [employees, selectedEmployeeIDs])

  // Filtered appointments based on selected employees and date range
  const filteredAppointments = useMemo(() => {
    const employeeIds = Array.from(selectedEmployeeIDs)
    
    // Set proper date range - start of first day to end of last day
    const startDate = new Date(displayDays[0])
    startDate.setHours(0, 0, 0, 0) // Start of day
    
    const endDate = new Date(displayDays[displayDays.length - 1])
    endDate.setHours(23, 59, 59, 999) // End of day
    
    console.log('🔍 Filtering appointments:', {
      totalAppointments: appointments.length,
      selectedEmployeeIds: employeeIds,
      selectedServicesArray: Array.from(selectedServices),
      dateRange: `${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`
    })
    
    // Filter appointments based on current selection criteria
    const filtered = appointments.filter(apt => {
      const aptDate = new Date(apt.start)
      
      // 🔧 IMMEDIATE CANCELLATION FILTER: Hide cancelled appointments immediately
      const normalizedStatus = apt.status?.toLowerCase()
      const isNotCancelled = normalizedStatus !== 'cancelled' && 
                            normalizedStatus !== 'canceled'
      
      // 🔧 DEBUG: Log cancelled appointments to understand the filtering
      if (!isNotCancelled) {
        console.log('🚫 Filtering out cancelled appointment:', {
          id: apt.id,
          title: apt.title,
          originalStatus: apt.status,
          normalizedStatus: normalizedStatus,
          isNotCancelled: isNotCancelled
        })
      }
      
      // 🔧 FIX: Make service filtering optional to prevent appointments from disappearing
      // If selectedServices is empty (service loading failed), show all appointments
      // If selectedServices has items, only show appointments with matching services
      const serviceMatch = selectedServices.size === 0 || selectedServices.has(apt.serviceId)
      
      const employeeMatch = employeeIds.includes(apt.employeeId) || employeeIds.includes(apt.employeeId?.toString())
      const dateMatch = aptDate >= startDate && aptDate <= endDate
      
      // 🔧 DEBUG: Extra logging for appointment moves
      if (apt.id === 3) { // Log specifically for appointment ID 3 (the one being moved)
        console.log(`🎯 APPOINTMENT MOVE DEBUG - Appointment ${apt.id}:`, {
          title: apt.title,
          employeeId: apt.employeeId,
          employeeIdType: typeof apt.employeeId,
          selectedEmployeeIds: employeeIds,
          employeeMatch,
          dateMatch,
          status: apt.status,
          passes: employeeMatch && serviceMatch && dateMatch && isNotCancelled
        })
      }
      
      console.log(`🔍 Appointment ${apt.id} filter check:`, {
        appointmentData: {
          id: apt.id,
          title: apt.title,
          start: apt.start,
          employeeId: apt.employeeId,
          serviceId: apt.serviceId,
          status: apt.status
        },
        checks: {
          isNotCancelled: `Status ${apt.status} is not cancelled = ${isNotCancelled}`,
          serviceMatch: selectedServices.size === 0 ? 
            `No service filter (showing all services) = ${serviceMatch}` :
            `${apt.serviceId} in [${Array.from(selectedServices).join(', ')}] = ${serviceMatch}`,
          employeeMatch: `${apt.employeeId} in [${employeeIds.join(', ')}] = ${employeeMatch}`,
          dateMatch: `${aptDate.toLocaleDateString()} in range = ${dateMatch}`
        },
        passes: employeeMatch && serviceMatch && dateMatch && isNotCancelled
      })
      
      return employeeMatch && serviceMatch && dateMatch && isNotCancelled
    })
    
    // Log summary only when there are changes or in development
    console.log('📅 Calendar filter summary:', {
      total: appointments.length,
      displayed: filtered.length,
      dateRange: `${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`,
      employees: employeeIds.length,
      services: selectedServices.size,
      filteredAppointments: filtered.map(apt => ({
        id: apt.id,
        title: apt.title,
        employeeId: apt.employeeId,
        serviceId: apt.serviceId,
        start: apt.start
      }))
    })
    
    return filtered
  }, [appointments, selectedEmployeeIDs, displayDays, selectedServices, displayMode])

  // ✅ iOS PATTERN: Initialize calendar with React Query data
  useEffect(() => {
    // Wait for basic data to be available
    if (employeesLoading || !employees.length) return
    
    console.log('📅 [iOS Pattern] Initializing calendar with React Query data')
    
    // Load working hours (non-React Query for now)
    const loadWorkingHours = async () => {
      try {
        const workingHoursData = await workingHoursManager.getAllWorkingHours()
        setWorkingHours(workingHoursData)
      } catch (error) {
        console.warn('Failed to load working hours:', error)
      }
    }
    
    loadWorkingHours()
    setIsViewReady(true)
    
    // Scroll to current time if configured
    if (config.scrollToCurrentTime) {
      scrollToCurrentTime()
    }
  }, [employeesLoading, employees.length, config.scrollToCurrentTime])
  
  // ✅ iOS PATTERN: Initialize selectedServices when data becomes available
  useEffect(() => {
    if (availableServices.length > 0 || queryAppointments.length > 0) {
      // Initialize selectedServices with all available service IDs
      const serviceIds = availableServices.map(service => service.id) || []
      
      // Also include service IDs from appointment data to prevent missing services
      const appointmentServiceIds = queryAppointments
        .map(apt => apt.serviceId)
        .filter(serviceId => serviceId != null && serviceId !== undefined)
      
      // Combine service IDs from Django API and appointment data
      const allServiceIds = [...new Set([...serviceIds, ...appointmentServiceIds])]
      
      if (allServiceIds.length > 0) {
        setSelectedServices(new Set(allServiceIds))
        console.log('✅ [iOS Pattern] Initialized selectedServices with React Query data:', {
          fromServices: serviceIds.length,
          fromAppointments: appointmentServiceIds.length,
          total: allServiceIds.length
        })
      }
    }
  }, [availableServices, queryAppointments])

  // Real-time updates setup
  useEffect(() => {
    if (config.enableRealTimeUpdates) {
      refreshIntervalRef.current = setInterval(() => {
        refreshAppointments()
      }, 30000) // Refresh every 30 seconds
      
      return () => {
        if (refreshIntervalRef.current) {
          clearInterval(refreshIntervalRef.current)
        }
      }
    }
  }, [config.enableRealTimeUpdates])

  // Working hours subscription
  useEffect(() => {
    console.log('🔔 Setting up working hours subscription...')
    const unsubscribe = workingHoursManager.subscribe((updatedWorkingHours) => {
      console.log('🔄 Working hours updated via subscription:', Object.keys(updatedWorkingHours))
      setWorkingHours(updatedWorkingHours)
      setWorkingHoursVersion(prev => prev + 1) // Force re-render
      console.log('✅ React state updated with new working hours, version:', workingHoursVersion + 1)
    })
    
    return unsubscribe
  }, [])

  // Save scroll position
  useEffect(() => {
    if (config.rememberScrollPosition) {
      localStorage.setItem('calendar-scroll-position', scrollPosition.toString())
    }
  }, [scrollPosition, config.rememberScrollPosition])

  // Navigation methods
  const navigateDate = useCallback((direction) => {
    const newDate = new Date(selectedDate)
    const daysToMove = displayMode === DISPLAY_MODES.DAY ? 1 : 7
    
    if (direction === NAVIGATION_DIRECTION.FORWARD) {
      newDate.setDate(newDate.getDate() + daysToMove)
    } else {
      newDate.setDate(newDate.getDate() - daysToMove)
    }
    
    setSelectedDate(newDate)
    
    if (config.autoScrollOnDateChange) {
      scrollToCurrentTime()
    }
  }, [selectedDate, displayMode, config.autoScrollOnDateChange])

  const goToToday = useCallback(() => {
    setSelectedDate(new Date())
    if (config.scrollToCurrentTime) {
      scrollToCurrentTime()
    }
  }, [config.scrollToCurrentTime])

  const selectDate = useCallback((date) => {
    setSelectedDate(new Date(date))
    if (config.autoScrollOnDateChange) {
      scrollToCurrentTime()
    }
  }, [config.autoScrollOnDateChange])

  // View mode management
  const setViewMode = useCallback((mode) => {
    // Enforce employee selection rules like iOS
    if (mode === DISPLAY_MODES.WEEK && selectedEmployeeIDs.size > 1) {
      // Force single employee selection for week view
      const firstEmployee = Array.from(selectedEmployeeIDs)[0]
      setSelectedEmployeeIDs(new Set([firstEmployee]))
    }
    
    setDisplayMode(mode)
    resetScrollPosition()
  }, [selectedEmployeeIDs])

  // Employee management
  const updateSelectedEmployeeIDs = useCallback((employeeIds) => {
    const newSelection = new Set(Array.isArray(employeeIds) ? employeeIds : [employeeIds])
    
    console.log('👥 [DEBUG] Employee selection changed:', {
      input: employeeIds,
      newSelection: [...newSelection],
      displayMode
    })
    
    // Enforce view-specific rules
    if (displayMode === DISPLAY_MODES.WEEK && newSelection.size > 1) {
      // Week view only allows single employee
      const firstEmployee = Array.from(newSelection)[0]
      console.log('📍 [DEBUG] Week view: limiting to single employee:', firstEmployee)
      setSelectedEmployeeIDs(new Set([firstEmployee]))
    } else {
      console.log('📍 [DEBUG] Setting selected employees:', [...newSelection])
      setSelectedEmployeeIDs(newSelection)
    }
  }, [displayMode])

  // ✅ iOS PATTERN: Debounced appointment refresh using React Query
  const refreshAppointments = useCallback(async (forceRefresh = false) => {
    return debouncedCall(async () => {
      console.log('🔄 [iOS Pattern] Refreshing appointments with React Query...')
      setIsRefreshing(true)
      setError(null)
      
      try {
        const startTime = performance.now()
        
        // ✅ iOS PATTERN: Use React Query's refetch with current date range and employee filter
        const result = await refetchAppointments()
        const appointmentsData = result.data || []
        
        const endTime = performance.now()
        console.log(`✅ [iOS Pattern] Appointments refreshed: ${appointmentsData.length} appointments loaded for date range:`, dateRange)
        
        // Update selectedServices to include any new service IDs from appointments
        const appointmentServiceIds = appointmentsData
          .map(apt => apt.serviceId)
          .filter(serviceId => serviceId != null && serviceId !== undefined)
        
        setSelectedServices(prev => {
          const newServiceIds = appointmentServiceIds.filter(id => !prev.has(id))
          if (newServiceIds.length > 0) {
            console.log('✅ Added new service IDs to selectedServices:', newServiceIds)
            return new Set([...prev, ...newServiceIds])
          }
          return prev
        })
        
        // Update performance metrics
        setPerformanceMetrics(prev => ({
          ...prev,
          appointmentLoadTime: endTime - startTime
        }))
        
        setLastRefreshTime(new Date())
        console.log('🎯 [iOS Pattern] Appointment refresh completed with React Query caching')
        
        return appointmentsData
      } catch (error) {
        console.error('❌ Failed to refresh appointments:', error)
        setError(error.message)
        throw error
      } finally {
        setIsRefreshing(false)
      }
    }, forceRefresh)
  }, [debouncedCall, refetchAppointments, dateRange])

  const createAppointment = useCallback(async (appointmentData) => {
    setIsLoading(true)
    try {
      const newAppointment = await appointmentService.createAppointment(appointmentData)
      
      // ✅ iOS PATTERN: Invalidate cache after data changes
      appointmentService.invalidateCache()
      
      console.log('✅ Appointment created successfully:', newAppointment)
      await refetchAppointments()
      
      return newAppointment
    } catch (error) {
      console.error('❌ Failed to create appointment:', error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [refetchAppointments])

  const updateAppointment = useCallback(async (appointmentId, updates) => {
    setIsLoading(true)
    try {
      const updatedAppointment = await appointmentService.updateAppointment(appointmentId, updates)
      
      // ✅ iOS PATTERN: Invalidate cache after data changes  
      appointmentService.invalidateCache()
      
      console.log('✅ Appointment updated successfully:', updatedAppointment)
      await refetchAppointments()
      
      return updatedAppointment
    } catch (error) {
      console.error('❌ Failed to update appointment:', error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [refetchAppointments])

  const deleteAppointment = useCallback(async (appointmentId) => {
    setIsLoading(true)
    try {
      await appointmentService.deleteAppointment(appointmentId)
      
      // ✅ iOS PATTERN: Invalidate cache after data changes
      appointmentService.invalidateCache()
      
      console.log('✅ Appointment deleted successfully')
      await refetchAppointments()
    } catch (error) {
      console.error('❌ Failed to delete appointment:', error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [refetchAppointments])

  // Working hours management - FIXED to actually load working hours
  const getWorkingHoursForEmployee = useCallback(async (employeeId, date) => {
    try {
      // First check if we have cached working hours
      let workingHours = workingHoursManager.getVisualWorkingHours(employeeId, date)
      
      if (!workingHours) {
        // Load working hours if not cached
        console.log(`🔄 Loading working hours for employee ${employeeId}...`)
        await workingHoursManager.fetchWorkingHours(employeeId)
        workingHours = workingHoursManager.getVisualWorkingHours(employeeId, date)
      }
      
      // If still no working hours, use defaults
      if (!workingHours) {
        console.log(`⚠️ Using default working hours for employee ${employeeId}`)
        const defaultHours = workingHoursManager.generateDefaultWorkingHours()
        workingHoursManager.setEmployeeWorkingHours(employeeId, defaultHours)
        workingHours = workingHoursManager.getVisualWorkingHours(employeeId, date)
      }
      
      return workingHours
    } catch (error) {
      console.error(`Failed to get working hours for employee ${employeeId}:`, error)
      // Return fallback working hours
      return {
        start: '09:00',
        end: '17:00',
        isWorking: true
      }
    }
  }, [])

  // Synchronous version for immediate use (uses cached data)
  const getWorkingHoursForEmployeeSync = useCallback((employeeId, date) => {
    // CRITICAL: Use the React state instead of manager cache for re-renders
    const employeeWorkingHours = workingHours[employeeId]
    
    if (employeeWorkingHours) {
      const dayName = workingHoursManager.getDayNameFromDate(date)
      const dayHours = employeeWorkingHours[dayName]
      
      if (dayHours) {
        if (dayHours.isWorking) {
          return {
            start: dayHours.startTime,
            end: dayHours.endTime,
            isWorking: true
          }
        } else {
          return null // CRITICAL: Return null for non-working days
        }
      }
    }
    
    // Fallback to manager cache if not in React state
    const workingHoursFromManager = workingHoursManager.getVisualWorkingHours(employeeId, date)
    
    if (workingHoursFromManager) {
      return workingHoursFromManager
    }
    
    // Final fallback to defaults ONLY if we have no data at all
    return {
      start: '09:00',
      end: '17:00',
      isWorking: true
    }
  }, [workingHours]) // FIXED: Remove workingHoursVersion to prevent infinite loop

  const isEmployeeWorking = useCallback((employeeId, date) => {
    return workingHoursManager.isEmployeeWorking(employeeId, date)
  }, [])

  // Scroll management
  const scrollToCurrentTime = useCallback(() => {
    const now = new Date()
    const currentHour = now.getHours()
    const currentMinute = now.getMinutes()
    
    // Calculate scroll position based on current time
    const hoursSinceStart = currentHour - config.displayHourStart
    const minuteOffset = currentMinute / 60
    const totalHours = hoursSinceStart + minuteOffset
    const scrollPos = Math.max(0, totalHours * config.gridHeight - 200) // Offset for visibility
    
    setScrollPosition(scrollPos)
  }, [config.displayHourStart, config.gridHeight])

  const resetScrollPosition = useCallback(() => {
    setScrollPosition(0)
  }, [])

  // Utility functions
  const getAppointmentsForDay = useCallback((date, employeeIds = null) => {
    const targetEmployeeIds = employeeIds || Array.from(selectedEmployeeIDs)
    const dayStart = new Date(date)
    dayStart.setHours(0, 0, 0, 0)
    const dayEnd = new Date(date)
    dayEnd.setHours(23, 59, 59, 999)
    
    return appointments.filter(apt => {
      const aptDate = new Date(apt.start)
      return targetEmployeeIds.includes(apt.employeeId) &&
             aptDate >= dayStart && 
             aptDate <= dayEnd
    })
  }, [appointments, selectedEmployeeIDs])

  const getAppointmentsForTimeSlot = useCallback((date, hour, minute, employeeId = null) => {
    const targetEmployeeIds = employeeId ? [employeeId] : Array.from(selectedEmployeeIDs)
    
    return appointments.filter(apt => {
      const aptDate = new Date(apt.start)
      const aptHour = aptDate.getHours()
      const aptMinute = aptDate.getMinutes()
      
      // Check employee and date match
      const employeeMatch = targetEmployeeIds.includes(apt.employeeId)
      const dateMatch = aptDate.toDateString() === date.toDateString()
      
      if (!employeeMatch || !dateMatch || aptHour !== hour) {
        return false
      }
      
      // ✅ FIXED: Use slot-based matching instead of exact minute matching
      const appointmentSlotMinute = Math.floor(aptMinute / config.timeResolution) * config.timeResolution
      return appointmentSlotMinute === minute
    })
  }, [appointments, selectedEmployeeIDs, config.timeResolution])

  // Utility function to parse time string to minutes
  const parseTime = useCallback((timeString) => {
    const [hours, minutes] = timeString.split(':').map(Number)
    return hours * 60 + minutes
  }, [])

  // Helper function to check if a time slot is within working hours
  const isWorkingHourSlot = useCallback((date, hour, minute, employeeId) => {
    const workingHours = getWorkingHoursForEmployeeSync(employeeId, date)
    
    // If no working hours data or day is not working, return false
    if (!workingHours || !workingHours.isWorking) {
      return false
    }
    
    const slotTime = hour * 60 + minute
    const startTime = parseTime(workingHours.start)
    const endTime = parseTime(workingHours.end)
    
    return slotTime >= startTime && slotTime < endTime
  }, [getWorkingHoursForEmployeeSync, parseTime])

  // Load working hours for all employees when they change
  useEffect(() => {
    const loadAllWorkingHours = async () => {
      if (employees.length > 0) {
        try {
          console.log('🕒 Preloading working hours for all employees...')
          await workingHoursService.fetchAllWorkingHours()
          console.log('✅ Successfully preloaded working hours for all employees')
        } catch (error) {
          console.warn('⚠️ Failed to preload working hours, will load on demand:', error)
        }
      }
    }

    loadAllWorkingHours()
  }, [employees])

  // ✅ iOS PATTERN: Combine loading states and errors from React Query
  const combinedLoading = isLoading || employeesLoading || appointmentsLoading || servicesLoading || categoriesLoading
  const combinedError = error || employeesError || appointmentsError

  // Return the complete advanced calendar interface
  return {
    // Core state
    selectedDate,
    displayMode,
    displayDays,
    appointments,
    filteredAppointments,
    workingHours,
    workingHoursVersion,
    employees,
    selectedEmployeeIDs,
    selectedEmployees,
    selectedServices,
    availableServices,
    serviceCategories,
    currentEmployee,
    isLoading: combinedLoading,
    isRefreshing,
    error: combinedError,
    isViewReady,
    viewDidAppear,
    scrollPosition,
    lastRefreshTime,
    performanceMetrics,
    
    // Configuration
    config,
    updateConfig,
    setTimeResolution,
    setDisplayHours,
    setWeekStartDay,
    getTimeSlots,
    getDisplayHours,
    
    // Navigation
    navigateDate,
    goToToday,
    selectDate,
    
    // View management
    setViewMode,
    setViewDidAppear,
    
    // Employee management
    updateSelectedEmployeeIDs,
    setSelectedServices,
    
    // Appointment management
    refreshAppointments,
    createAppointment,
    updateAppointment,
    deleteAppointment,
    getAppointmentsForDay,
    getAppointmentsForTimeSlot,
    
    // Working hours
    getWorkingHoursForEmployee,
    getWorkingHoursForEmployeeSync,
    isEmployeeWorking,
    isWorkingHourSlot,
    
    // Scroll management
    scrollToCurrentTime,
    resetScrollPosition,
    setScrollPosition,
    
    // Utility
    setError,
    setIsLoading
  }
} 