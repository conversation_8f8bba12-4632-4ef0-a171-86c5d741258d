import { useRef, useCallback, useEffect } from 'react'

/**
 * useAutoScrollOnDrag - Auto-scroll hook for calendar drag operations
 * 
 * This hook implements smooth auto-scrolling when dragging appointments
 * and the cursor approaches the edges of the calendar container.
 * 
 * Features:
 * - Auto-scroll when cursor is near top/bottom edges
 * - Variable scroll speed based on distance from edge
 * - Smooth scrolling with requestAnimationFrame
 * - Automatic cleanup on drag end
 */
export const useAutoScrollOnDrag = (scrollContainerRef) => {
  const autoScrollRef = useRef(null)
  const isAutoScrollingRef = useRef(false)
  const lastScrollTimeRef = useRef(0)

  // Auto-scroll configuration
  const SCROLL_ZONES = {
    TOP_EDGE: 80,     // Distance from top edge to trigger scroll up
    BOTTOM_EDGE: 80,  // Distance from bottom edge to trigger scroll down
    MAX_SPEED: 8,     // Maximum scroll speed (pixels per frame)
    MIN_SPEED: 1,     // Minimum scroll speed (pixels per frame)
    ACCELERATION: 0.1 // Speed acceleration factor
  }

  /**
   * Calculate scroll direction and speed based on cursor position
   */
  const calculateScrollParams = useCallback((clientY, containerRect) => {
    const distanceFromTop = clientY - containerRect.top
    const distanceFromBottom = containerRect.bottom - clientY
    
    let scrollDirection = 0
    let scrollSpeed = 0
    
    // Check if cursor is in top scroll zone
    if (distanceFromTop <= SCROLL_ZONES.TOP_EDGE && distanceFromTop >= 0) {
      scrollDirection = -1 // Scroll up
      const intensity = (SCROLL_ZONES.TOP_EDGE - distanceFromTop) / SCROLL_ZONES.TOP_EDGE
      scrollSpeed = SCROLL_ZONES.MIN_SPEED + (intensity * (SCROLL_ZONES.MAX_SPEED - SCROLL_ZONES.MIN_SPEED))
    }
    // Check if cursor is in bottom scroll zone
    else if (distanceFromBottom <= SCROLL_ZONES.BOTTOM_EDGE && distanceFromBottom >= 0) {
      scrollDirection = 1 // Scroll down
      const intensity = (SCROLL_ZONES.BOTTOM_EDGE - distanceFromBottom) / SCROLL_ZONES.BOTTOM_EDGE
      scrollSpeed = SCROLL_ZONES.MIN_SPEED + (intensity * (SCROLL_ZONES.MAX_SPEED - SCROLL_ZONES.MIN_SPEED))
    }
    
    return { scrollDirection, scrollSpeed }
  }, [])

  /**
   * Perform smooth auto-scroll animation
   */
  const performAutoScroll = useCallback((scrollDirection, scrollSpeed) => {
    if (!scrollContainerRef.current) return

    const container = scrollContainerRef.current
    const now = Date.now()
    
    // Throttle scroll updates for smooth performance
    if (now - lastScrollTimeRef.current < 16) { // ~60fps
      autoScrollRef.current = requestAnimationFrame(() => 
        performAutoScroll(scrollDirection, scrollSpeed)
      )
      return
    }
    
    lastScrollTimeRef.current = now
    
    // Calculate scroll distance
    const scrollDistance = scrollDirection * scrollSpeed
    const newScrollTop = Math.max(0, 
      Math.min(
        container.scrollTop + scrollDistance,
        container.scrollHeight - container.clientHeight
      )
    )
    
    // Apply scroll
    container.scrollTop = newScrollTop
    
    // Continue scrolling if still in auto-scroll mode
    if (isAutoScrollingRef.current) {
      autoScrollRef.current = requestAnimationFrame(() => 
        performAutoScroll(scrollDirection, scrollSpeed)
      )
    }
  }, [scrollContainerRef])

  /**
   * Start auto-scroll based on cursor position
   */
  const startAutoScroll = useCallback((clientX, clientY) => {
    if (!scrollContainerRef.current) return

    const container = scrollContainerRef.current
    const containerRect = container.getBoundingClientRect()
    
    // Check if cursor is within the container horizontally
    if (clientX < containerRect.left || clientX > containerRect.right) {
      stopAutoScroll()
      return
    }
    
    const { scrollDirection, scrollSpeed } = calculateScrollParams(clientY, containerRect)
    
    if (scrollDirection !== 0) {
      // Start auto-scrolling if not already active
      if (!isAutoScrollingRef.current) {
        isAutoScrollingRef.current = true
        performAutoScroll(scrollDirection, scrollSpeed)
      }
    } else {
      // Stop auto-scrolling if cursor is not in scroll zones
      stopAutoScroll()
    }
  }, [scrollContainerRef, calculateScrollParams, performAutoScroll])

  /**
   * Stop auto-scroll animation
   */
  const stopAutoScroll = useCallback(() => {
    isAutoScrollingRef.current = false
    if (autoScrollRef.current) {
      cancelAnimationFrame(autoScrollRef.current)
      autoScrollRef.current = null
    }
  }, [])

  /**
   * Handle drag move events with auto-scroll
   */
  const handleDragMoveWithAutoScroll = useCallback((event) => {
    const clientX = event.clientX || event.touches?.[0]?.clientX
    const clientY = event.clientY || event.touches?.[0]?.clientY
    
    if (clientX !== undefined && clientY !== undefined) {
      startAutoScroll(clientX, clientY)
    }
  }, [startAutoScroll])

  /**
   * Handle drag end to stop auto-scroll
   */
  const handleDragEndWithAutoScroll = useCallback(() => {
    stopAutoScroll()
  }, [stopAutoScroll])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopAutoScroll()
    }
  }, [stopAutoScroll])

  // Add global mouse/touch event listeners for drag operations
  useEffect(() => {
    const handleGlobalMouseMove = (event) => {
      if (isAutoScrollingRef.current || event.buttons === 1) { // Check if dragging
        handleDragMoveWithAutoScroll(event)
      }
    }

    const handleGlobalMouseUp = () => {
      handleDragEndWithAutoScroll()
    }

    const handleGlobalTouchMove = (event) => {
      if (isAutoScrollingRef.current) {
        handleDragMoveWithAutoScroll(event)
      }
    }

    const handleGlobalTouchEnd = () => {
      handleDragEndWithAutoScroll()
    }

    // Add passive listeners for better performance
    document.addEventListener('mousemove', handleGlobalMouseMove, { passive: true })
    document.addEventListener('mouseup', handleGlobalMouseUp, { passive: true })
    document.addEventListener('touchmove', handleGlobalTouchMove, { passive: true })
    document.addEventListener('touchend', handleGlobalTouchEnd, { passive: true })

    return () => {
      document.removeEventListener('mousemove', handleGlobalMouseMove)
      document.removeEventListener('mouseup', handleGlobalMouseUp)
      document.removeEventListener('touchmove', handleGlobalTouchMove)
      document.removeEventListener('touchend', handleGlobalTouchEnd)
    }
  }, [handleDragMoveWithAutoScroll, handleDragEndWithAutoScroll])

  return {
    startAutoScroll,
    stopAutoScroll,
    handleDragMoveWithAutoScroll,
    handleDragEndWithAutoScroll,
    isAutoScrolling: isAutoScrollingRef.current
  }
} 