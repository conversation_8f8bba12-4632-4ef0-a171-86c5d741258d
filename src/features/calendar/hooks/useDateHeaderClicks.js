import { useEffect, useRef, useState } from 'react'

/**
 * Custom hook for handling date header clicks in the calendar
 * @param {Object} calendarControls - Custom calendar controls instance
 * @param {Function} onDateClick - Callback function for date click events
 * @param {Function} onDateNavigation - Callback function for date navigation events
 * @param {Object} options - Configuration options for the hook
 * @returns {Object} Hook utilities and event handlers
 */
export function useDateHeaderClicks(
  calendarControls,
  onDateClick,
  onDateNavigation,
  options = {}
) {
  const [lastClickedDate, setLastClickedDate] = useState(null);
  const [isNavigating, setIsNavigating] = useState(false);
  const clickTimeoutRef = useRef(null);
  const longPressTimeoutRef = useRef(null);

  const {
    debounceDelay = 300,
    longPressDelay = 800,
    enableSmartNavigation = true,
    enableLongPress = true,
    enableDoubleClick = true,
    trackingDelay = 1000
  } = options;

  /**
   * Handles date header clicks and navigation
   * @param {Event} event - The click event
   * @param {string} dateString - The clicked date in ISO format
   * @param {string} clickType - Type of click (single, double, long)
   */
  const handleDateClick = (event, dateString, clickType = 'single') => {
    if (!dateString) return;

    const clickData = {
      date: dateString,
      type: clickType,
      target: event.target,
      coordinates: {
        x: event.clientX,
        y: event.clientY
      },
      timestamp: Date.now()
    };

    // Clear any existing timeout
    if (clickTimeoutRef.current) {
      clearTimeout(clickTimeoutRef.current);
      clickTimeoutRef.current = null;
    }

    // Handle different click types
    switch (clickType) {
      case 'double':
        if (enableDoubleClick) {
          handleDoubleClick(clickData);
        }
        break;
      case 'long':
        if (enableLongPress) {
          handleLongPress(clickData);
        }
        break;
      case 'single':
      default:
        // Debounce single clicks to avoid conflicts with double clicks
        if (enableDoubleClick) {
          clickTimeoutRef.current = setTimeout(() => {
            handleSingleClick(clickData);
          }, debounceDelay);
        } else {
          handleSingleClick(clickData);
        }
        break;
    }
  };

  /**
   * Handle single click on date header
   * @param {Object} clickData - Click event data
   */
  const handleSingleClick = (clickData) => {
    setLastClickedDate(clickData.date);
    if (onDateClick) {
      onDateClick(clickData);
    }
  };

  /**
   * Handle double click on date header
   * @param {Object} clickData - Click event data
   */
  const handleDoubleClick = (clickData) => {
    if (enableSmartNavigation) {
      navigateToDate(clickData.date);
    }
  };

  /**
   * Handle long press on date header
   * @param {Object} clickData - Click event data
   */
  const handleLongPress = (clickData) => {
    // Long press can be used for context menu or special actions
    if (onDateNavigation) {
      onDateNavigation({
        ...clickData,
        action: 'longPress'
      });
    }
  };

  /**
   * Navigate to a specific date
   * @param {string} dateString - The target date in ISO format
   */
  const navigateToDate = (dateString) => {
    if (!calendarControls || !dateString) return;

    setIsNavigating(true);
    
    try {
      // Use calendar controls to navigate to the date
      if (calendarControls.setDate) {
        calendarControls.setDate(dateString);
      }
      
      if (onDateNavigation) {
        onDateNavigation({
          date: dateString,
          action: 'navigate',
          timestamp: Date.now()
        });
          }
        } catch (error) {
      console.error('Error navigating to date:', error);
    } finally {
      // Reset navigation state after a delay
      setTimeout(() => {
        setIsNavigating(false);
      }, trackingDelay);
    }
  };

  /**
   * Get the date from a clicked element
   * @param {HTMLElement} target - The clicked element
   * @returns {string|null} The date string or null if not found
   */
  const getDateFromTarget = (target) => {
    if (!target) return null;

    // Common date header patterns for custom calendar
    const dateElement = 
      target.closest('.week-grid-date') ||
      target.closest('.month-grid-date') ||
      target.closest('.date-grid-date') ||
      target.closest('.date-grid-date-wrapper') ||
      target.closest('.date-grid-date__header') ||
      target.closest('.week-grid-day-header') ||
      target.closest('.month-grid-date') ||
      target.closest('.calendar-week-day-name') ||
      target.closest('.calendar-header-date') ||
      target.closest('[data-date]') ||
      target.closest('[data-grid-date]') ||
      target.closest('[data-calendar-date]');

    if (!dateElement) return null;

    // Try to get date from various data attributes
    const dateString = 
      dateElement.getAttribute('data-date') ||
      dateElement.getAttribute('data-grid-date') ||
      dateElement.getAttribute('data-calendar-date') ||
      dateElement.getAttribute('data-day') ||
      dateElement.getAttribute('data-iso-date');

    return dateString;
  };

  /**
   * Set up event listeners for date header clicks
   */
  useEffect(() => {
    if (!calendarControls) return;

    const handleGlobalClick = (event) => {
      const target = event.target;
      const dateString = getDateFromTarget(target);
      
      if (dateString) {
        handleDateClick(event, dateString, 'single');
      }
    };

    const handleGlobalDoubleClick = (event) => {
      const target = event.target;
      const dateString = getDateFromTarget(target);
      
      if (dateString) {
        handleDateClick(event, dateString, 'double');
      }
    };

    const handleMouseDown = (event) => {
      if (!enableLongPress) return;

      const target = event.target;
      const dateString = getDateFromTarget(target);
      
      if (dateString) {
        longPressTimeoutRef.current = setTimeout(() => {
          handleDateClick(event, dateString, 'long');
        }, longPressDelay);
        }
    };

    const handleMouseUp = () => {
      if (longPressTimeoutRef.current) {
        clearTimeout(longPressTimeoutRef.current);
        longPressTimeoutRef.current = null;
      }
    };

    // Find calendar container
    const calendarContainer = document.querySelector('.calendar-container') ||
      document.querySelector('.calendar-scroll-container') ||
      document.querySelector('[data-calendar-container]');

    if (calendarContainer) {
      calendarContainer.addEventListener('click', handleGlobalClick);
      if (enableDoubleClick) {
        calendarContainer.addEventListener('dblclick', handleGlobalDoubleClick);
      }
      if (enableLongPress) {
        calendarContainer.addEventListener('mousedown', handleMouseDown);
        calendarContainer.addEventListener('mouseup', handleMouseUp);
        calendarContainer.addEventListener('mouseleave', handleMouseUp);
      }
    }

    return () => {
      if (calendarContainer) {
        calendarContainer.removeEventListener('click', handleGlobalClick);
        if (enableDoubleClick) {
          calendarContainer.removeEventListener('dblclick', handleGlobalDoubleClick);
        }
        if (enableLongPress) {
          calendarContainer.removeEventListener('mousedown', handleMouseDown);
          calendarContainer.removeEventListener('mouseup', handleMouseUp);
          calendarContainer.removeEventListener('mouseleave', handleMouseUp);
      }
    }

      // Clean up timeouts
      if (clickTimeoutRef.current) {
        clearTimeout(clickTimeoutRef.current);
      }
      if (longPressTimeoutRef.current) {
        clearTimeout(longPressTimeoutRef.current);
      }
    };
  }, [calendarControls, enableDoubleClick, enableLongPress, debounceDelay, longPressDelay]);

  return {
    lastClickedDate,
    isNavigating,
    handleDateClick,
    navigateToDate,
    getDateFromTarget
  };
} 