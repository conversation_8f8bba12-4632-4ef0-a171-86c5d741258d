import { useQuery } from '@tanstack/react-query'
import { servicesApiService } from '../services/servicesApiService'

/**
 * React Query hook for services with iOS-inspired aggressive caching
 * Services change rarely, so we can cache them for a long time
 */
export const useServicesQuery = () => {
  return useQuery({
    queryKey: ['services'],
    queryFn: async () => {
      console.log('🔍 [iOS Pattern] Fetching services with caching...')
      const services = await servicesApiService.fetchServices()
      console.log('✅ [iOS Pattern] Services fetched:', services.length)
      return services
    },
    staleTime: 10 * 60 * 1000, // 10 minutes - services change rarely
    cacheTime: 30 * 60 * 1000, // 30 minutes cache
    refetchOnWindowFocus: false, // iOS pattern - don't refetch on focus
    refetchOnMount: false, // Only fetch if cache is stale
    retry: 2,
    onError: (error) => {
      console.warn('❌ [iOS Pattern] Services fetch failed:', error)
    }
  })
}

/**
 * React Query hook for service categories with iOS-inspired aggressive caching
 */
export const useServiceCategoriesQuery = () => {
  return useQuery({
    queryKey: ['service-categories'],
    queryFn: async () => {
      console.log('🔍 [iOS Pattern] Fetching service categories with caching...')
      const categories = await servicesApiService.fetchServiceCategories()
      console.log('✅ [iOS Pattern] Service categories fetched:', categories.length)
      return categories
    },
    staleTime: 10 * 60 * 1000, // 10 minutes - categories change rarely
    cacheTime: 30 * 60 * 1000, // 30 minutes cache
    refetchOnWindowFocus: false, // iOS pattern - don't refetch on focus
    refetchOnMount: false, // Only fetch if cache is stale
    retry: 2,
    onError: (error) => {
      console.warn('❌ [iOS Pattern] Service categories fetch failed:', error)
    }
  })
} 