import { useMemo } from 'react'
import { cn } from '../../../utils'

/**
 * useCalendarStyles - Hook providing calendar component styling
 * Converts existing CSS to Tailwind classes while maintaining design consistency
 */
export const useCalendarStyles = () => {
  const baseStyles = useMemo(() => ({
    // Main Calendar Container
    container: cn(
      "w-full h-full rounded-xl overflow-visible relative",
      "shadow-sm border border-calendar-outline",
      "bg-calendar-surface font-sans",
      "z-calendar-base will-change-auto"
    ),

    // Calendar Scroll Container
    scrollContainer: cn(
      "w-full h-full relative overflow-auto",
      "z-calendar-base isolate",
      // Hide scrollbars while maintaining functionality
      "scrollbar-none",
      // Prevent rubber-band bounce effect
      "overscroll-contain",
      // Disable iOS momentum scrolling
      "[-webkit-overflow-scrolling:auto]",
      // Prevent scroll chaining
      "overflow-x-hidden overflow-y-auto"
    ),

    // Time Grid System
    timeGrid: cn(
      "relative z-calendar-content",
      "bg-gradient-to-b from-calendar-surface-variant to-calendar-surface",
      "bg-grid-pattern bg-repeat-y",
      "backface-hidden" // Performance optimization
    ),

    // Time Slots
    timeSlot: cn(
      "relative border-b border-calendar-outline-variant",
      "hover:bg-blue-50/50 transition-colors duration-150",
      "cursor-pointer z-calendar-content"
    ),

    // Time Slot - Working Hours
    timeSlotWorking: cn(
      "relative border-b border-calendar-outline-variant",
      "hover:bg-blue-50/50 transition-colors duration-150",
      "cursor-pointer z-calendar-content"
    ),

    // Time Slot - Non-Working Hours
    timeSlotNonWorking: cn(
      "relative border-b border-calendar-outline-variant",
      "bg-gray-100/50 hover:bg-gray-100/70 transition-colors duration-150",
      "cursor-pointer z-calendar-content"
    ),

    // Time Slot - Occupied
    timeSlotOccupied: cn(
      "relative border-b border-calendar-outline-variant",
      "bg-blue-50/30 hover:bg-blue-50/50 transition-colors duration-150",
      "cursor-pointer z-calendar-content"
    ),

    // Calendar Headers
    header: cn(
      "bg-calendar-surface-variant border-b border-calendar-outline",
      "z-calendar-headers sticky top-0 isolate"
    ),

    // Calendar Main Header
    calendarHeader: cn(
      "bg-calendar-surface-variant border-b border-calendar-outline",
      "z-calendar-headers"
    ),

    // Time Axis
    timeAxis: cn(
      "text-xs text-gray-500 relative min-h-[100px] h-[60px]",
      "border-b border-calendar-outline font-light",
      "z-calendar-headers"
    ),

    // Time Axis Hour
    timeAxisHour: cn(
      "relative min-h-[100px] h-[60px] text-xs text-gray-500",
      "border-b border-calendar-outline",
      "after:content-[':30'] after:absolute after:bottom-[10px] after:right-1",
      "after:text-[9px] after:text-gray-400 after:font-light"
    ),

    // Week Grid
    weekGrid: cn(
      "bg-calendar-surface"
    ),

    // Week Grid Day Header
    weekGridDayHeader: cn(
      "bg-calendar-surface-variant border-b border-calendar-outline",
      "p-4 text-center font-semibold text-gray-700"
    ),

    // Week Grid Day Header - Today
    weekGridDayHeaderToday: cn(
      "text-calendar-primary bg-blue-50"
    ),

    // Week Grid Day
    weekGridDay: cn(
      "border-calendar-outline",
      "hover:bg-calendar-surface-variant transition-colors duration-200"
    ),

    // Month Grid Day
    monthGridDay: cn(
      "min-h-[120px] border border-calendar-outline-variant",
      "bg-calendar-surface p-2 transition-all duration-200",
      "hover:bg-calendar-surface-variant"
    ),

    // Month Grid Day - Today
    monthGridDayToday: cn(
      "bg-blue-50 border-calendar-primary"
    ),

    // Month Grid Day - Leading/Trailing
    monthGridDayLeading: cn(
      "opacity-40"
    ),

    // Month Grid Day Number
    monthGridDayNumber: cn(
      "font-semibold text-gray-900 mb-2"
    ),

    // Current Time Indicator
    currentTimeIndicator: cn(
      "bg-red-500 h-0.5 relative z-[50]"
    ),

    // Grid Lines
    gridLine: cn(
      "border-t border-calendar-outline"
    ),

    // Grid Hour Lines
    gridHourLine: cn(
      "border-t border-calendar-outline"
    ),

    // Grid Quarter Lines
    gridQuarterLine: cn(
      "border-t border-dashed border-calendar-outline opacity-70"
    ),

    // Multi-employee specific styles
    multiEmployeeHeader: cn(
      "bg-calendar-surface-variant border-b border-calendar-outline",
      "flex z-calendar-headers"
    ),

    // Employee Column
    employeeColumn: cn(
      "flex-1 border-r border-calendar-outline last:border-r-0",
      "hover:bg-calendar-surface-variant/50 transition-colors duration-200"
    ),

    // Employee Header Compact
    employeeHeaderCompact: cn(
      "bg-calendar-surface-variant border-b border-calendar-outline",
      "p-2 text-center font-medium text-gray-700 text-sm",
      "hover:bg-gray-100 transition-colors duration-200"
    ),

    // Employee Avatar Compact
    employeeAvatarCompact: cn(
      "w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center",
      "text-xs font-medium text-gray-600 mb-1 mx-auto"
    ),

    // Employee Name Compact
    employeeNameCompact: cn(
      "text-xs font-medium text-gray-700 truncate text-center"
    ),

    // Calendar Feedback
    calendarFeedback: cn(
      "z-calendar-overlays"
    ),

    // Navigation Feedback
    navigationFeedback: cn(
      "z-calendar-overlays"
    ),

    // Calendar Tooltip
    calendarTooltip: cn(
      "z-calendar-overlays"
    ),

    // Calendar Dropdown
    calendarDropdown: cn(
      "z-calendar-ui"
    ),

    // Calendar Modal
    calendarModal: cn(
      "z-[1000]"
    ),

    // Ghost Copy
    ghostCopy: cn(
      "z-calendar-ghost"
    ),

    // Debug styles (for development)
    debugEmployeeColumn: cn(
      "border-2 border-red-500 bg-red-100/20"
    ),

    debugTapArea: cn(
      "border border-blue-500 bg-blue-100/20"
    ),
  }), [])

  // Dynamic grid sizing based on configuration
  const getGridStyles = (config) => {
    const hourHeight = config?.gridHeight || 80
    const slotHeight = hourHeight / (60 / (config?.timeResolution || 15))
    
    return {
      style: {
        '--calendar-hour-height': `${hourHeight}px`,
        '--calendar-slot-height': `${slotHeight}px`,
      }
    }
  }

  return { 
    baseStyles, 
    getGridStyles 
  }
}

export default useCalendarStyles 