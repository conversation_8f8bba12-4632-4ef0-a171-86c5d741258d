import { useMemo } from 'react'
import { cn } from '../../../utils'
import { APPOINTMENT_STATUSES } from '../constants/calendarConfig'

/**
 * Utility function to determine if a color is light or dark
 * @param {string} color - Hex color string (e.g., '#8b5cf6')
 * @returns {boolean} - True if the color is light, false if dark
 */
const isLightColor = (color) => {
  if (!color) return false
  
  // Remove # if present
  const hex = color.replace('#', '')
  
  // Convert to RGB
  const r = parseInt(hex.substr(0, 2), 16)
  const g = parseInt(hex.substr(2, 2), 16)
  const b = parseInt(hex.substr(4, 2), 16)
  
  // Calculate luminance using W3C formula
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255
  
  // Return true if luminance is greater than 0.5 (light color)
  return luminance > 0.5
}

/**
 * Get the appropriate text color based on actual background color
 * @param {string} backgroundColor - The actual hex color from the appointment
 * @returns {string} - Tailwind text color class
 */
const getTextColorForBackground = (backgroundColor) => {
  if (!backgroundColor) return 'text-white' // Default to white for unknown colors
  
  return isLightColor(backgroundColor) ? 'text-black' : 'text-white'
}

/**
 * useAppointmentStyles - Hook providing appointment component styling
 * Handles complex appointment styling including service categories, status indicators, and drag states
 */
export const useAppointmentStyles = () => {
  
  // Get status-based top border styling
  const getStatusBorderClasses = (status) => {
    const statusConfig = Object.values(APPOINTMENT_STATUSES).find(s => s.id === status)
    if (!statusConfig) return { className: 'border-t-4 border-t-gray-400', style: {} }
    
    return { 
      className: 'border-t-8', 
      style: { borderTopColor: statusConfig.color } 
    }
  }
  
  // Service category styling
  const getServiceCategoryClasses = (category) => {
    const categoryMap = {
      haircut: "bg-appointment-haircut border-appointment-haircut-border border-l",
      coloring: "bg-appointment-coloring border-appointment-coloring-border border-l",
      styling: "bg-appointment-styling border-appointment-styling-border border-l",
      treatment: "bg-appointment-treatment border-appointment-treatment-border border-l",
      consultation: "bg-appointment-consultation border-appointment-consultation-border border-l",
      // Break types
      lunch: "bg-yellow-500 border-yellow-600 border-2 border-dashed opacity-80",
      break: "bg-purple-500 border-purple-600 border-2 border-dashed opacity-80",
      meeting: "bg-red-500 border-red-600 border-2 border-dashed opacity-80",
      personal: "bg-gray-500 border-gray-600 border-2 border-dashed opacity-80",
      training: "bg-green-500 border-green-600 border-2 border-dashed opacity-80",
    }
    return categoryMap[category] || "bg-blue-500 border-blue-600 border-l"
  }

  // Status indicator styling using CSS-in-JS approach for complex pseudo-elements
  const getStatusClasses = (status) => {
    const statusMap = {
      completed: "",
      'no-show': "",
      'in-progress': "",
      pending: "",
    }
    return statusMap[status] || ""
  }

  // Main appointment styling function
  const getAppointmentClasses = (appointment, state = {}) => {
    const { 
      isDragging = false, 
      isDimmed = false, 
      isHovered = false,
      isSelected = false,
      isConflicted = false,
      height = 24
    } = state

    // Get dynamic text color based on actual appointment color from backend
    const textColor = getTextColorForBackground(appointment.serviceColor)
    
    // Get status border styling
    const statusBorder = getStatusBorderClasses(appointment.status)

    const baseClasses = cn(
      // Base appointment styling - reduced font weight  
      "absolute rounded-md py-1.5 px-2 overflow-hidden",
      "font-semibold select-none cursor-grab",
      "transition-all duration-150 ease-out",
      "min-h-6 will-change-transform",
      "shadow-sm",

      
      // Dynamic text color based on actual background color
      textColor,
      
      // Z-index management - critical for proper layering
      isDragging && "z-calendar-interactions cursor-grabbing",
      isHovered && !isDragging && "z-calendar-appointment-hover",
      isSelected && "z-calendar-appointment-hover",
      isDimmed && "z-[5]",
      !isDragging && !isDimmed && "z-calendar-appointment",
      
      // Visual states
      isDimmed && cn(
        "opacity-70 pointer-events-none",
        "appointment-dimmed" // Keep class for CSS targeting
      ),
      
      isHovered && !isDragging && cn(
        "transform-gpu -translate-y-0.5 shadow-md",
        "brightness-105 filter",
        "max-w-full", // Prevent expansion beyond boundaries
        "contain-layout" // Performance optimization
      ),
      
      isDragging && cn(
        "cursor-grabbing shadow-xl opacity-95",
        "transition-none" // Disable transitions during drag
      ),
      
      isConflicted && "animate-pulse ring-2 ring-red-400",
      
      // Service category colors
      getServiceCategoryClasses(appointment.serviceCategory || appointment.appointmentType),
      
      // Status-specific classes
      getStatusClasses(appointment.status),
      
      // Add status-based top border class
      statusBorder.className,
      
      // Height-based font sizing - adjusted thresholds for larger fonts
      height <= 35 ? "text-xs" : height <= 70 ? "text-sm" : "text-base"
    )

    return {
      className: baseClasses,
      style: statusBorder.style
    }
  }

  // Appointment content styling
  const getAppointmentContentClasses = (height, serviceColor) => {
    // Get dynamic text color for content based on actual appointment color
    const textColor = getTextColorForBackground(serviceColor)
    
    return {
      title: cn(
        "font-bold break-words leading-tight",
        textColor,
        height <= 35 && "text-xs",
        height > 35 && "text-sm"
      ),
      client: cn(
        "font-semibold break-words opacity-90 leading-tight",
        textColor,
        height <= 35 && "text-[11px]",
        height > 35 && "text-xs"
      ),
      duration: cn(
        "font-medium break-words opacity-75 mt-1",
        textColor,
        "text-[11px]"
      ),
      description: cn(
        "font-medium whitespace-pre-line text-[11px] opacity-75 mt-1",
        textColor
      )
    }
  }



  // Drag state indicator
  const getDragIndicator = (isDragging) => {
    if (!isDragging) return null

    return {
      className: cn(
        "absolute top-0 right-0 bottom-0 left-0 border-2 border-white border-dashed rounded-lg",
        "opacity-75 pointer-events-none"
      )
    }
  }

  // Resize handle styling
  const getResizeHandleClasses = () => {
    return cn(
      "absolute bottom-0 left-0 right-0 h-1",
      "bg-white/20 opacity-0 hover:opacity-100",
      "cursor-ns-resize transition-opacity duration-150"
    )
  }

  return { 
    getAppointmentClasses,
    getAppointmentContentClasses,
    getDragIndicator,
    getResizeHandleClasses,
    getServiceCategoryClasses,
    getStatusClasses,
    getTextColorForBackground,
    getStatusBorderClasses
  }
}

export default useAppointmentStyles 