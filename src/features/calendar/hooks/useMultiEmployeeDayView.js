import { useState, useCallback, useMemo, useRef, useEffect } from 'react'

/**
 * useMultiEmployeeDayView - Hook for managing multi-employee day view state and logic
 * Based on iOS DayCalendarViewModel patterns
 */
export const useMultiEmployeeDayView = ({
  selectedEmployeeIDs,
  selectedDate,
  appointments,
  employees = [], // Add employees as a prop parameter
  onEmployeeSelectionChange,
  onTimeSlotClick,
  onAppointmentClick
}) => {
  // Local state
  const [selectedTimeSlot, setSelectedTimeSlot] = useState(null)
  const [visibleTimeRange, setVisibleTimeRange] = useState(null)
  const [isLoading, setIsLoading] = useState(false)
  
  // Refs for layout calculations
  const containerRef = useRef(null)
  const columnRefs = useRef({})
  
  // Constants (matching iOS implementation)
  const TIME_COLUMN_WIDTH = 64
  const BORDER_WIDTH = 1
  
  // Selected employees as objects - Fixed to use employees prop
  const selectedEmployees = useMemo(() => {
    if (!employees || employees.length === 0) {
      console.warn('⚠️ useMultiEmployeeDayView: No employees provided, returning empty array')
      return []
    }
    
    return employees.filter(emp => selectedEmployeeIDs.has(emp.id))
  }, [employees, selectedEmployeeIDs])
  
  // Calculate column width based on container and employee count
  const calculateColumnWidth = useCallback(() => {
    if (!containerRef.current) return 300 // Default width
    
    const totalWidth = containerRef.current.offsetWidth
    const availableWidth = totalWidth - TIME_COLUMN_WIDTH
    const employeeCount = selectedEmployees.length
    
    if (employeeCount === 0) return availableWidth
    
    // Account for borders between columns
    const totalBorderWidth = (employeeCount - 1) * BORDER_WIDTH
    return (availableWidth - totalBorderWidth) / employeeCount
  }, [selectedEmployees.length])
  
  // Filter appointments for specific employee and date
  const getAppointmentsForEmployee = useCallback((employeeId, date = selectedDate) => {
    return appointments.filter(apt => {
      const aptDate = new Date(apt.start || apt.startTime)
      // Handle both string and number employee ID comparisons
      return (apt.employeeId === employeeId || apt.employeeId === employeeId.toString()) && 
             aptDate.toDateString() === date.toDateString()
    })
  }, [appointments, selectedDate])
  
  // Get appointments for specific time slot and employee
  const getAppointmentsForTimeSlot = useCallback((date, hour, minute, employeeId = null) => {
    const targetEmployeeIds = employeeId ? [employeeId] : Array.from(selectedEmployeeIDs)
    
    return appointments.filter(apt => {
      const aptDate = new Date(apt.start || apt.startTime)
      const aptHour = aptDate.getHours()
      const aptMinute = aptDate.getMinutes()
      
      // Handle both string and number employee ID comparisons
      const employeeMatch = targetEmployeeIds.includes(apt.employeeId) || 
                           targetEmployeeIds.includes(apt.employeeId?.toString())
      const dateMatch = aptDate.toDateString() === date.toDateString()
      
      if (!employeeMatch || !dateMatch || aptHour !== hour) {
        return false
      }
      
      // ✅ FIXED: Use slot-based matching instead of exact minute matching
      // This ensures appointments show up in correct slots regardless of resolution changes
      // Try to determine the best slot based on common resolutions (5, 15, 30)
      let appointmentSlotMinute
      
      // Check if the slot minute suggests a specific resolution
      if (minute % 30 === 0) {
        // 30-minute resolution: [0, 30]
        appointmentSlotMinute = Math.floor(aptMinute / 30) * 30
      } else if (minute % 15 === 0) {
        // 15-minute resolution: [0, 15, 30, 45]
        appointmentSlotMinute = Math.floor(aptMinute / 15) * 15
      } else if (minute % 5 === 0) {
        // 5-minute resolution: [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55]
        appointmentSlotMinute = Math.floor(aptMinute / 5) * 5
      } else {
        // Fallback to 15-minute resolution
        appointmentSlotMinute = Math.floor(aptMinute / 15) * 15
      }
      
      return appointmentSlotMinute === minute
    })
  }, [appointments, selectedEmployeeIDs])
  
  // Enhanced time selection handler with employee detection (matching iOS logic)
  const handleTimeSelection = useCallback((date, hour, minute, tapLocation) => {
    const timeSlot = new Date(date)
    timeSlot.setHours(hour, minute, 0, 0)
    setSelectedTimeSlot(timeSlot)
    
    let tappedEmployee = null
    
    // Multi-employee tap detection (iOS-style logic)
    if (selectedEmployees.length > 1 && tapLocation) {
      console.log('👆 Multi-employee tap at:', tapLocation)
      
      const rect = containerRef.current?.getBoundingClientRect()
      if (rect) {
        const relativeX = tapLocation.clientX - rect.left - TIME_COLUMN_WIDTH
        const columnWidth = calculateColumnWidth()
        
        if (relativeX >= 0) {
          // Sort employee IDs for consistent column ordering
          const sortedEmployeeIds = Array.from(selectedEmployeeIDs).sort()
          const columnIndex = Math.min(
            Math.floor(relativeX / (columnWidth + BORDER_WIDTH)),
            sortedEmployeeIds.length - 1
          )
          
          const employeeId = sortedEmployeeIds[columnIndex]
          tappedEmployee = selectedEmployees.find(emp => emp.id === employeeId)
          
          console.log('🎯 Column calculation:', {
            relativeX,
            columnWidth,
            columnIndex,
            employeeId,
            employeeName: tappedEmployee?.name
          })
        }
      }
    } else if (selectedEmployees.length === 1) {
      // Single employee mode
      tappedEmployee = selectedEmployees[0]
    }
    
    // Fallback to first employee if detection failed
    if (!tappedEmployee && selectedEmployees.length > 0) {
      tappedEmployee = selectedEmployees[0]
      console.log('⚠️ Using fallback employee:', tappedEmployee.name)
    }
    
    // Call parent handler with employee context
    if (onTimeSlotClick) {
      onTimeSlotClick(timeSlot, tappedEmployee, tapLocation)
    }
  }, [selectedEmployees, selectedEmployeeIDs, calculateColumnWidth, onTimeSlotClick])
  
  // Employee management
  const addEmployee = useCallback((employeeId) => {
    const newSelection = new Set(selectedEmployeeIDs)
    newSelection.add(employeeId)
    onEmployeeSelectionChange?.(newSelection)
  }, [selectedEmployeeIDs, onEmployeeSelectionChange])
  
  const removeEmployee = useCallback((employeeId) => {
    if (selectedEmployeeIDs.size <= 1) {
      console.warn('Cannot remove last employee')
      return
    }
    
    const newSelection = new Set(selectedEmployeeIDs)
    newSelection.delete(employeeId)
    onEmployeeSelectionChange?.(newSelection)
  }, [selectedEmployeeIDs, onEmployeeSelectionChange])
  
  const toggleEmployee = useCallback((employeeId) => {
    if (selectedEmployeeIDs.has(employeeId)) {
      removeEmployee(employeeId)
    } else {
      addEmployee(employeeId)
    }
  }, [selectedEmployeeIDs, addEmployee, removeEmployee])
  
  // Layout helpers
  const getEmployeeColumnStyle = useCallback((index) => {
    const columnWidth = calculateColumnWidth()
    return {
      width: `${columnWidth}px`,
      borderRight: index < selectedEmployees.length - 1 ? `${BORDER_WIDTH}px solid #e5e7eb` : 'none'
    }
  }, [selectedEmployees.length, calculateColumnWidth])
  
  // Scroll management
  const updateVisibleTimeRange = useCallback((start, end) => {
    setVisibleTimeRange({ start, end })
  }, [])
  
  // Time slot highlighting
  const shouldHighlightTimeSlot = useCallback((date, hour, minute) => {
    if (!selectedTimeSlot) return false
    
    const slotTime = new Date(date)
    slotTime.setHours(hour, minute, 0, 0)
    
    return slotTime.getTime() === selectedTimeSlot.getTime()
  }, [selectedTimeSlot])
  
  // Navigation helpers
  const navigateToDate = useCallback((newDate) => {
    setSelectedTimeSlot(null) // Clear selection when navigating
    // Parent component should handle date changes
  }, [])
  
  // Layout effect for responsive column widths
  useEffect(() => {
    const handleResize = () => {
      // Force recalculation of column widths
      if (containerRef.current) {
        // Trigger a re-render by updating a dummy state
        setIsLoading(prev => prev) // No-op state update to trigger recalc
      }
    }
    
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])
  
  return {
    // State
    selectedEmployees,
    selectedTimeSlot,
    visibleTimeRange,
    isLoading,
    
    // Layout
    containerRef,
    columnRefs,
    TIME_COLUMN_WIDTH,
    BORDER_WIDTH,
    calculateColumnWidth,
    getEmployeeColumnStyle,
    
    // Data helpers
    getAppointmentsForEmployee,
    getAppointmentsForTimeSlot,
    
    // Event handlers
    handleTimeSelection,
    
    // Employee management
    addEmployee,
    removeEmployee,
    toggleEmployee,
    
    // UI helpers
    updateVisibleTimeRange,
    shouldHighlightTimeSlot,
    navigateToDate,
    
    // Computed values
    employeeCount: selectedEmployees.length,
    isMultiEmployee: selectedEmployees.length > 1,
    isSingleEmployee: selectedEmployees.length === 1
  }
} 