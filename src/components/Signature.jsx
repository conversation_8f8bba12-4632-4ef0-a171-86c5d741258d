import React, { useRef, useEffect, useState } from "react";
import { saveSignatureToPdf } from "../utils";

/**
 * A customer‑side signature component that supports drawing, clearing, editing
 * and exporting the signature to PDF.
 *
 * Flow:
 * 1. User draws on the canvas ➜ clicks **Done Signing** ➜ signature is frozen.
 * 2. User can now **Edit Signature** (re‑enter drawing mode) or **Clear Signature**.
 *    – **Clear Signature** instantly wipes the canvas and resets state, even
 *      while drawing (no need to click Done first).
 * 3. Once a signature is present, **Save as PDF** becomes available.
 */
const Signature = ({
  onSave,
  initialData = {},
  customerName = "",
  documentType = "",
}) => {
  const customerCanvasRef = useRef(null);

  /** Drawing state */
  const [isDrawing, setIsDrawing] = useState(false);
  const [lastPoint, setLastPoint] = useState({ x: 0, y: 0 });

  /** Business state */
  const [agreed, setAgreed] = useState(initialData.agreed || false);
  const [customerSignature, setCustomerSignature] = useState(
    initialData.customer || null
  );
  const [isDrawingMode, setIsDrawingMode] = useState(!initialData.customer);
  const [isSaving, setIsSaving] = useState(false);
  const [saveStatus, setSaveStatus] = useState('');

  /* ------------------------------------------------------------------ */
  /* Helpers                                                            */
  /* ------------------------------------------------------------------ */

  /** Initialise canvas size and drawing settings */
  const initialiseCanvas = (canvas) => {
    if (!canvas) return;

    const ctx = canvas.getContext("2d");

    // Match canvas pixels with its rendered size (for crisp HiDPI rendering).
    const rect = canvas.getBoundingClientRect();
    canvas.width = rect.width;
    canvas.height = rect.height;

    ctx.strokeStyle = "#444";
    ctx.lineWidth = 2;
    ctx.lineCap = "round";
    ctx.lineJoin = "round";
  };

  /** Convert mouse / touch coords to canvas coordinates (accounting for scale) */
  const toCanvasCoords = (event, canvas) => {
    const rect = canvas.getBoundingClientRect();
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;
    return {
      x: (event.clientX - rect.left) * scaleX,
      y: (event.clientY - rect.top) * scaleY,
    };
  };

  /* ------------------------------------------------------------------ */
  /* Effects                                                             */
  /* ------------------------------------------------------------------ */

  // 1️⃣ Mount – prepare canvas and handle window resize
  useEffect(() => {
    const canvas = customerCanvasRef.current;
    initialiseCanvas(canvas);

    const handleResize = () => {
      if (!canvas) return;
      const snapshot = canvas.toDataURL("image/png");
      initialiseCanvas(canvas);
      if (snapshot) {
        const img = new Image();
        img.onload = () => {
          const ctx = canvas.getContext("2d");
          ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
        };
        img.src = snapshot;
      }
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // 2️⃣ If the parent passes in an existing customer signature, draw it.
  useEffect(() => {
    if (initialData.customer && customerCanvasRef.current) {
      const img = new Image();
      img.onload = () => {
        const canvas = customerCanvasRef.current;
        const ctx = canvas.getContext("2d");
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
      };
      img.src = initialData.customer;
      setCustomerSignature(initialData.customer);
      setIsDrawingMode(false);
    }
    // We include only the static ref to avoid re‑running unnecessarily.
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 3️⃣ Bubble up value changes to parent (auto‑save)
  useEffect(() => {
    onSave?.({ customer: customerSignature, agreed });
  }, [customerSignature, agreed, onSave]);

  /* ------------------------------------------------------------------ */
  /* Canvas Drawing Handlers                                             */
  /* ------------------------------------------------------------------ */

  const startDrawing = (event) => {
    if (!isDrawingMode) return;
    const canvas = customerCanvasRef.current;
    if (!canvas) return;

    const { x, y } = toCanvasCoords(event, canvas);
    setIsDrawing(true);
    setLastPoint({ x, y });

    const ctx = canvas.getContext("2d");
    ctx.beginPath();
    ctx.moveTo(x, y);
  };

  const draw = (event) => {
    if (!isDrawing || !isDrawingMode) return;
    const canvas = customerCanvasRef.current;
    if (!canvas) return;

    const { x, y } = toCanvasCoords(event, canvas);
    const ctx = canvas.getContext("2d");
    ctx.lineTo(x, y);
    ctx.stroke();
    setLastPoint({ x, y });
  };

  const stopDrawing = () => {
    setIsDrawing(false);
  };

  const finalizeSignature = () => {
    const canvas = customerCanvasRef.current;
    if (!canvas) return;
    const dataUrl = canvas.toDataURL("image/png");
    setCustomerSignature(dataUrl);
    setIsDrawingMode(false);
  };

  /* ------------------------------------------------------------------ */
  /* Clear / Edit / Save                                                 */
  /* ------------------------------------------------------------------ */

  const clearSignature = () => {
    const canvas = customerCanvasRef.current;
    if (!canvas) return;
    const ctx = canvas.getContext("2d");
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Remove existing signature but *stay* in whatever mode makes sense.
    setCustomerSignature(null);
    setIsDrawingMode(true); // Allow immediate re‑drawing.
  };

  const editSignature = () => {
    setIsDrawingMode(true);
  };

  const saveAsPdf = async () => {
    if (!customerSignature) {
      alert("Please add a customer signature before saving as PDF");
      return;
    }

    // Set saving state
    setIsSaving(true);
    setSaveStatus('Generating PDF...');

    try {
      // --------------------------------------------------------------
      // Determine what name to use in the resulting PDF filename.
      // 1. Prefer the explicitly provided `customerName` prop.
      // 2. If not provided, fetch the current user's profile so we can
      //    fall back to their first/last name.
      // --------------------------------------------------------------
      let resolvedCustomerName = customerName;

      if (!resolvedCustomerName) {
        try {
          const apiBase = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';
          const profileResp = await fetch(`${apiBase}/users/profile/`, {
            headers: {
              'Content-Type': 'application/json',
              // Pass JWT for authenticated request if it exists
              Authorization: `Bearer ${localStorage.getItem('auth_token') || ''}`,
            },
          });

          if (profileResp.ok) {
            const profile = await profileResp.json();
            const first = profile.first_name || '';
            const last = profile.last_name || '';
            const combined = `${first} ${last}`.trim();
            if (combined) {
              resolvedCustomerName = combined;
            }
          } else {
            console.warn('Could not fetch user profile – using generic filename');
          }
        } catch (err) {
          console.error('Error fetching user profile:', err);
        }
      }

      const filename = resolvedCustomerName
        ? `${resolvedCustomerName.replace(/\s+/g, "_")}_customer_signature.pdf`
        : `customer_signature.pdf`;

      setSaveStatus('Uploading to cloud storage...');
      
      // Add skipUpload option for debugging
      const debugMode = false; // Set to false to enable S3 upload
      
      // The saveSignatureToPdf function now handles both local saving and uploading
      const result = await saveSignatureToPdf(customerSignature, filename, {
        title: "Customer Signature Document",
        customerName: resolvedCustomerName,
        documentType,
        skipUpload: debugMode // Skip upload in debug mode
      });
      
      // Check if we have a file ID from the upload
      if (result && result.id) {
        setSaveStatus(`PDF saved and uploaded successfully! File ID: ${result.id}`);
      } else if (result && result.debug) {
        setSaveStatus('PDF saved locally (upload skipped in debug mode)');
      } else {
        setSaveStatus('PDF saved locally and upload initiated. Check console for details.');
      }
      
      // Clear success status after a delay
      setTimeout(() => {
        if (!saveStatus.includes('Error')) {
          setSaveStatus('');
        }
      }, 5000);
    } catch (error) {
      console.error('Error in PDF save process:', error);
      
      // Provide more helpful error message based on the error
      if (error.message.includes('auth')) {
        setSaveStatus(`Error: Authentication failed. Please log in again.`);
      } else if (error.message.includes('network')) {
        setSaveStatus(`Error: Network issue. Check your connection and try again.`);
      } else {
        setSaveStatus(`Error: ${error.message}`);
      }
      
      // Don't auto-clear error messages
    } finally {
      setIsSaving(false);
    }
  };

  /* ------------------------------------------------------------------ */
  /* Mobile – translate touch events to mouse events                     */
  /* ------------------------------------------------------------------ */

  const touchStart = (e) => {
    const touch = e.touches[0];
    startDrawing(new MouseEvent("mousedown", { clientX: touch.clientX, clientY: touch.clientY }));
  };

  const touchMove = (e) => {
    if (!isDrawing) return;
    const touch = e.touches[0];
    draw(new MouseEvent("mousemove", { clientX: touch.clientX, clientY: touch.clientY }));
  };

  const touchEnd = () => {
    stopDrawing();
  };

  /* ------------------------------------------------------------------ */
  /* Render                                                              */
  /* ------------------------------------------------------------------ */

  return (
    <div className="bg-gray-50 rounded-lg p-6 border border-gray-200 w-full max-w-3xl mx-auto">
      {/* Header */}
      <h2 className="font-semibold text-lg mb-3">Signature</h2>

      {/* Agreement checkbox */}
      <label className="flex items-center mb-4 text-sm select-none cursor-pointer">
        <input
          type="checkbox"
          checked={agreed}
          onChange={(e) => setAgreed(e.target.checked)}
          className="mr-2"
        />
        <span className="text-gray-500">
          I agree to use electronic records and signatures.
        </span>
      </label>

      {/* Canvas container */}
      <div className="relative bg-white border rounded mb-4" style={{ height: 200 }}>
        <canvas
          ref={customerCanvasRef}
          className={`w-full h-full block ${isDrawingMode ? "cursor-crosshair" : "cursor-default"}`}
          style={{ touchAction: "none" }}
          onMouseDown={startDrawing}
          onMouseMove={draw}
          onMouseUp={stopDrawing}
          onMouseOut={stopDrawing}
          onTouchStart={(e) => {
            e.preventDefault();
            touchStart(e);
          }}
          onTouchMove={(e) => {
            e.preventDefault();
            touchMove(e);
          }}
          onTouchEnd={(e) => {
            e.preventDefault();
            touchEnd();
          }}
        />

        {/* Watermark / label */}
        <div className="absolute left-3 bottom-2 flex items-center text-gray-400 text-sm pointer-events-none">
          <span className="mr-2">✖</span>
          <span>
            Customer Signature {customerSignature ? "(Signed)" : ""}
          </span>
        </div>
        {/* Bottom border (just aesthetic) */}
        <div className="absolute left-0 right-0 bottom-0 h-px bg-gray-400" />
      </div>

      {/* Status bubble */}
      <div className="flex items-center gap-2 text-sm mb-6">
        <span
          className={`w-3 h-3 rounded-full ${customerSignature ? "bg-green-500" : "bg-gray-300"}`}
        />
        <span>
          Customer Signature: {customerSignature ? "Completed" : isDrawingMode ? "Drawing" : "Not signed"}
        </span>
      </div>

      {/* Save status message */}
      {saveStatus && (
        <div className={`mb-4 p-2 text-sm rounded ${
          saveStatus.includes('Error') 
            ? 'bg-red-50 text-red-700 border border-red-200' 
            : saveStatus.includes('success') 
              ? 'bg-green-50 text-green-700 border border-green-200'
              : 'bg-blue-50 text-blue-700 border border-blue-200'
        }`}>
          {isSaving && (
            <div className="inline-block mr-2 w-4 h-4 border-2 border-t-transparent border-blue-600 rounded-full animate-spin"></div>
          )}
          {saveStatus}
        </div>
      )}

      {/* Action buttons */}
      <div className="flex flex-wrap gap-3">
        {/* Clear is *always* available */}
        <button
          type="button"
          onClick={clearSignature}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          disabled={isSaving}
        >
          Clear Signature
        </button>

        {isDrawingMode ? (
          <button
            type="button"
            onClick={finalizeSignature}
            className="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-green-700 rounded-md hover:bg-green-700"
            disabled={isSaving}
          >
            Done Signing
          </button>
        ) : (
          <button
            type="button"
            onClick={editSignature}
            className="px-4 py-2 text-sm font-medium text-blue-600 bg-white border border-blue-600 rounded-md hover:bg-blue-50"
            disabled={isSaving}
          >
            Edit Signature
          </button>
        )}

        <button
          type="button"
          onClick={saveAsPdf}
          className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-blue-700 rounded-md hover:bg-blue-700 disabled:opacity-40 flex items-center"
          disabled={!customerSignature || isSaving}
        >
          {isSaving && (
            <div className="mr-2 w-4 h-4 border-2 border-t-transparent border-white rounded-full animate-spin"></div>
          )}
          Save as PDF
        </button>
      </div>
    </div>
  );
};

export default Signature;
