import { useState } from 'react'
import But<PERSON> from './Button'
import useApi from '../hooks/useApi'

function OTPModal({ isOpen, onClose }) {
  if (!isOpen) return null

  const [step, setStep] = useState('request') // request, verify
  const [phoneNumber, setPhoneNumber] = useState('')
  const [otp, setOtp] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [tempToken, setTempToken] = useState(null)

  const phoneRegex = /^\+?[1-9]\d{1,14}$/ // Basic E.164 validation

  // API hooks
  const requestOtpApi = useApi('/v1/auth/request-otp/')
  const verifyOtpApi = useApi('/v1/auth/verify-otp/')

  const handleRequestOtp = async (e) => {
    e.preventDefault()
    setError(null)

    if (!phoneRegex.test(phoneNumber)) {
      setError('Please enter a valid phone number in E.164 format (e.g. +14255550123)')
      return
    }

    setLoading(true)
    const res = await requestOtpApi.createData({ phone_number: phoneNumber })
    setLoading(false)

    if (res && res.temp_token) {
      setTempToken(res.temp_token)
      setStep('verify')
    } else {
      setError(res?.detail || 'Failed to request OTP')
    }
  }

  const handleVerifyOtp = async (e) => {
    e.preventDefault()
    setError(null)

    if (otp.length !== 6) {
      setError('OTP must be 6 digits')
      return
    }

    setLoading(true)
    const res = await verifyOtpApi.createData({ phone_number: phoneNumber, otp })
    setLoading(false)

    if (res && res.access) {
      // Store tokens (simple localStorage for now)
      localStorage.setItem('auth_token', res.access)
      if (res.refresh) {
        localStorage.setItem('refresh_token', res.refresh)
      }
      onClose()
      window.location.href = '/' // Redirect to dashboard
    } else {
      setError(res?.detail || 'Failed to verify OTP')
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center px-4">
      <div className="bg-white w-full max-w-md rounded-lg shadow-lg p-6 relative">
        <button
          className="absolute top-3 right-3 text-gray-400 hover:text-gray-600"
          onClick={onClose}
        >
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5">
            <path fillRule="evenodd" d="M6.225 4.811a.75.75 0 0 1 1.06 0L12 9.525l4.715-4.714a.75.75 0 1 1 1.06 1.06L13.06 10.586l4.714 4.715a.75.75 0 0 1-1.06 1.06L12 11.646l-4.715 4.715a.75.75 0 0 1-1.06-1.06l4.714-4.715-4.714-4.714a.75.75 0 0 1 0-1.06Z" clipRule="evenodd" />
          </svg>
        </button>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Login with OTP</h3>

        {step === 'request' && (
          <form onSubmit={handleRequestOtp} className="space-y-4">
            <div>
              <label htmlFor="phone" className="block text-sm font-medium text-gray-700">Phone number</label>
              <input
                id="phone"
                type="tel"
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500"
                placeholder="+14255550123"
                value={phoneNumber}
                onChange={(e) => setPhoneNumber(e.target.value.trim())}
                required
              />
            </div>
            {error && <p className="text-sm text-red-600">{error}</p>}
            <Button type="submit" className="w-full" disabled={loading}>
              {loading ? 'Requesting...' : 'Send OTP'}
            </Button>
          </form>
        )}

        {step === 'verify' && (
          <form onSubmit={handleVerifyOtp} className="space-y-4">
            <p className="text-sm text-gray-700">Enter the 6-digit code sent to <strong>{phoneNumber}</strong></p>
            <div>
              <label htmlFor="otp" className="block text-sm font-medium text-gray-700">OTP Code</label>
              <input
                id="otp"
                type="text"
                inputMode="numeric"
                maxLength="6"
                pattern="[0-9]{6}"
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500 tracking-widest text-center"
                value={otp}
                onChange={(e) => setOtp(e.target.value.trim())}
                required
              />
            </div>
            {error && <p className="text-sm text-red-600">{error}</p>}
            <Button type="submit" className="w-full" disabled={loading}>
              {loading ? 'Verifying...' : 'Verify & Login'}
            </Button>
          </form>
        )}
      </div>
    </div>
  )
}

export default OTPModal 