# Django API Integration for Employee Calendar

This guide explains how the frontend calendar now connects to your Django backend APIs for employee and appointment management.

## 🚀 Quick Start

### 1. Environment Setup

Create a `.env` file in your frontend root:

```bash
# .env
VITE_API_BASE_URL=http://localhost:8000/api
```

### 2. Django Backend Requirements

Make sure your Django backend is running with these endpoints available:

- `GET /api/employees/` - List all employees
- `GET /api/employees/me/` - Current employee info
- `GET /api/employees/me/working-hours/` - Employee working hours
- `PUT /api/employees/me/working-hours/` - Update working hours
- `GET /api/employees/me/appointments/` - Employee appointments
- `GET /api/appointments/` - List appointments
- `POST /api/appointments/` - Create appointment
- `PUT /api/appointments/{id}/` - Update appointment
- `DELETE /api/appointments/{id}/` - Delete appointment

### 3. Frontend Usage

The calendar now automatically connects to your Django APIs! Here's how:

#### Basic Usage in Calendar

```jsx
// pages/Calendar.jsx
import Calendar from './pages/Calendar'

// The calendar page now automatically loads real data from Django
function App() {
  return <Calendar />
}
```

#### Advanced Usage with Data Connector

```jsx
import { EmployeeCalendarConnector } from '../features/calendar/components'
import { Calendar } from '../pages'

function EnhancedCalendar() {
  const handleDataLoaded = (data) => {
    console.log('API data loaded:', data)
    // { employees, currentEmployee, workingHours, appointments, apiStatus }
  }

  return (
    <EmployeeCalendarConnector onDataLoaded={handleDataLoaded}>
      <Calendar />
    </EmployeeCalendarConnector>
  )
}
```

#### Using Employee Hooks

```jsx
import { useEmployees } from '../features/employees'

function EmployeeList() {
  const { 
    employees, 
    currentEmployee, 
    loading, 
    error, 
    createEmployee,
    updateEmployee,
    deleteEmployee 
  } = useEmployees()

  if (loading) return <div>Loading employees...</div>
  if (error) return <div>Error: {error}</div>

  return (
    <div>
      <h2>Employees ({employees.length})</h2>
      {employees.map(emp => (
        <div key={emp.id}>{emp.name}</div>
      ))}
    </div>
  )
}
```

#### Direct API Service Usage

```jsx
import { employeeApiService, appointmentsApiService } from '../features/employees'

async function loadEmployeeData() {
  try {
    // Get all employees
    const employees = await employeeApiService.getAllEmployees()
    
    // Get current employee
    const currentEmployee = await employeeApiService.getCurrentEmployee()
    
    // Get employee working hours
    const workingHours = await employeeApiService.getEmployeeWorkingHours('me')
    
    // Get appointments
    const appointments = await appointmentsApiService.fetchAppointments({
      start_date: '2024-01-01',
      end_date: '2024-01-31'
    })
    
    console.log({ employees, currentEmployee, workingHours, appointments })
  } catch (error) {
    console.error('API Error:', error)
  }
}
```

## 🔧 API Service Details

### Employee API Service

```jsx
import { employeeApiService } from '../features/employees'

// Available methods:
- employeeApiService.getCurrentEmployee()
- employeeApiService.getAllEmployees()
- employeeApiService.getEmployeeWorkingHours(employeeId)
- employeeApiService.updateEmployeeWorkingHours(workingHours, employeeId)
- employeeApiService.getEmployeePermissions(employeeId)
- employeeApiService.getEmployeeCalendarConfigs(employeeId)
- employeeApiService.getEmployeeAppointments(employeeId, params)
- employeeApiService.createEmployee(employeeData)
- employeeApiService.updateEmployee(employeeId, employeeData)
- employeeApiService.deleteEmployee(employeeId)
```

### Appointments API Service

```jsx
import { appointmentsApiService } from '../features/calendar'

// Available methods:
- appointmentsApiService.fetchAppointments(params)
- appointmentsApiService.fetchEmployeeAppointments(employeeId, params)
- appointmentsApiService.createAppointment(appointmentData)
- appointmentsApiService.updateAppointment(appointmentId, updates)
- appointmentsApiService.deleteAppointment(appointmentId)
- appointmentsApiService.updateAppointmentStatus(appointmentId, status, reason)
- appointmentsApiService.getAvailableSlots(employeeId, date, serviceId)
- appointmentsApiService.checkAppointmentConflicts(employeeId, startTime, endTime)
```

## 🛡️ Error Handling & Fallbacks

The integration is designed to gracefully handle API failures:

1. **Automatic Fallbacks**: If Django APIs fail, the system falls back to mock data
2. **Error Logging**: All API errors are logged to console with context
3. **User Feedback**: Visual indicators show API connection status
4. **Retry Logic**: Failed requests include automatic token refresh

## 🔍 Debugging

### Check API Connection

1. Open browser DevTools
2. Look for these console messages:
   - `✅ Working hours loaded from Django API`
   - `✅ Appointments loaded from Django API`
   - `❌ Working hours API failed, using fallback`

### Verify API Calls

Check the Network tab in DevTools for:
- `GET /api/employees/`
- `GET /api/employees/me/working-hours/`
- `GET /api/appointments/`

### Authentication Issues

If you see 401 errors:
1. Check that `access_token` exists in localStorage
2. Verify the token hasn't expired
3. Check that your Django backend accepts Bearer tokens

## 📋 Data Format Examples

### Employee Data (from Django)

```json
{
  "id": 1,
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "role": "stylist",
  "is_active": true
}
```

### Working Hours Data

```json
{
  "monday": { "isWorking": true, "startTime": "09:00", "endTime": "17:00" },
  "tuesday": { "isWorking": true, "startTime": "09:00", "endTime": "17:00" },
  "wednesday": { "isWorking": false, "startTime": "09:00", "endTime": "17:00" }
}
```

### Appointment Data

```json
{
  "id": 123,
  "customer": { "name": "Jane Smith", "phone": "+1987654321" },
  "employee": { "id": 1, "name": "John Doe" },
  "service": { "id": 1, "name": "Haircut", "color": "#3b82f6" },
  "start_time": "2024-01-15T10:00:00Z",
  "end_time": "2024-01-15T11:00:00Z",
  "status": "confirmed",
  "notes": "Regular customer",
  "price": 50
}
```

## 🚦 Status Indicators

The calendar shows visual indicators for API status:

- 🟢 **Green**: Successfully connected to Django API
- 🟡 **Yellow**: API connection issues, using fallback data  
- 🔴 **Red**: Critical API failure

## 📚 Next Steps

1. **Test the Integration**: Start your Django backend and frontend
2. **Check Console**: Look for API success/error messages
3. **Verify Data**: Ensure calendar shows real data from Django
4. **Add Authentication**: Make sure users are properly authenticated
5. **Customize**: Modify the API services for your specific needs

## 🔧 Troubleshooting

### Common Issues

1. **CORS Errors**: Configure CORS in your Django settings
2. **Authentication Failed**: Check JWT token configuration
3. **404 Errors**: Verify Django URLs match the API calls
4. **Network Errors**: Ensure Django backend is running on the correct port

### Django Backend Checklist

- [ ] Django server running on `http://localhost:8000`
- [ ] CORS configured for frontend origin
- [ ] JWT authentication working
- [ ] Employee and appointment endpoints responding
- [ ] Database has employee and appointment data

The frontend will now use real Django data while gracefully falling back to mock data if the API is unavailable! 