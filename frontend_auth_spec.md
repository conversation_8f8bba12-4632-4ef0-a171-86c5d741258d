# Front-End Auth Specification (Lo<PERSON> & Sign-Up)

> This document distills the current Django templates – `templates/accounts/login.html` and `templates/accounts/signup.html` – into a framework-agnostic spec for front-end implementation (e.g. React, Vue, Svelte).  It details UI structure, validation rules, user flows, and the REST/JSON endpoints your front-end needs to call.

---

## 1. Pages & Routes

| Page | Suggested Front-End Route | Purpose |
|------|---------------------------|---------|
| **Login** | `/auth/login` | Authenticate existing users via password **or** OTP, plus social providers. |
| **Sign-Up** | `/auth/signup` | Create a customer user account (email + phone). |
| **Password Reset** | `/auth/forgot-password` | Send reset link (UI not in templates but endpoint exists). |
| **Business Registration** | `/business/register` | Opens separate flow (link shown on Login). |

---

## 2. Login Page UI

### 2.1 Top-Level Layout

```
<CenteredCard>
  <Heading level=2>Sign in to your account</Heading>
  <Alert variant="error" v-if="logoutMessage">You have been logged out.</Alert>
  <LoginForm />
  <Divider>Or continue with</Divider>
  <SocialButtons />
  <FooterLinks />
</CenteredCard>
```

### 2.2 `<LoginForm>` Fields

| Field | HTML `name` | Type | Validation |
|-------|-------------|------|------------|
| Email or phone | `username` | `text` | required, trimmed, accepts `<EMAIL>` **or** `+1xxxxxxxxxx`. |
| Password | `password` | `password` | required. |
| Remember me | `remember-me` | `checkbox` | optional. |

### 2.3 Secondary Actions

* **Login with OTP** – triggers modal → see §3.
* **Forgot your password?** – link to reset page (not shown in first templates but exists in default AllAuth).
* **Business Owner? Register your Business** – link to `/business/register` (Django URL `business:register`).

### 2.4 Social Login

One primary button (Google in current UI) but can be extended.

```
GET /accounts/google/login/
```
Redirect-flow handled by Django AllAuth.

### 2.5 Footer

> _"Don't have an account? Sign up"_ → `/auth/signup`

---

## 3. OTP Login Modal Flow

```
┌──────── Login Page ────────┐
│ click «Login with OTP»     │
└────────────┬───────────────┘
             │ open modal
┌────────────▼───────────────┐
│ 1. Enter phone number      │
│ 2. POST /api/v1/auth/request-otp/ │
│ 3. On 200 → swap to OTP in-put │
│ 4. POST /api/v1/auth/verify-otp/  │
│ 5. On 200 → redirect '/'        │
└────────────────────────────┘
```

Field IDs used by template JavaScript:
* `#phone-number` — E.164 string
* `#otp` — 6-digit code after step 3

Error handling: display `alert()` currently – should be replaced with toast/snackbar.

---

## 4. Sign-Up Page UI

### 4.1 `<SignupForm>` Fields

| Field | HTML `name` | Type | Validation |
|-------|-------------|------|------------|
| First Name | `first_name` | `text` | required |
| Last  Name | `last_name`  | `text` | required |
| Email      | `email`      | `email` | required, RFC-5322 |
| Phone      | `phone_number` | `tel` | required, **10-digit US** (pattern `[0-9]{10}`) |
| Password   | `password1` | `password` | required, min 8 chars (enforced server-side) |
| Confirm    | `password2` | `password` | must match `password1` |

On submit → `/auth/signup/`.

### 4.2 Social Sign-Up

Same Google button as login → `/accounts/google/login/`.

### 4.3 Footer

> _"Already have an account? Sign in"_ → `/auth/login`

---

## 5. Required API Endpoints

> All JSON endpoints are **served by the Django backend**. Front-end should include `Authorization: Bearer <token>` after authentication where required.

| Purpose | Method & Path | Request Body | Response (200) |
|---------|---------------|--------------|----------------|
| **Login (password)** | `POST /api/v1/auth/login/` | `{ "email": "<EMAIL>", "password": "••••" }` | `{ "access": "<jwt>", "refresh": "<jwt>", "mfa_required": false }` |
| **Request OTP** | `POST /api/v1/auth/request-otp/` | `{ "phone_number": "+***********" }` | `{ "temp_token": "uuid", "expires_in": 300 }` |
| **Verify OTP** | `POST /api/v1/auth/verify-otp/` | `{ "phone_number": "+***********", "otp": "123456" }` | `{ "access": "<jwt>", "refresh": "<jwt>" }` |
| **Refresh Token** | `POST /api/v1/auth/refresh/` | `{ "refresh": "<jwt>" }` | `{ "access": "<jwt>" }` |
| **User Sign-Up** | `POST /auth/signup/` (form-encoded) **or** SPA variant `POST /api/v1/auth/signup/` *(if implemented)* | See fields in §4.1 | Redirect 302 on success or JSON |
| **Password Reset (request link)** | `POST /api/v1/auth/password/reset/` | `{ "email": "<EMAIL>" }` | `{ "detail": "Reset e-mail sent" }` |
| **Password Reset (confirm)** | `POST /api/v1/auth/password/reset/confirm/` | `{ "uid": "...", "token": "...", "new_password": "••••" }` | `{ "detail": "Password has been reset" }` |
| **Google OAuth start** | `GET /accounts/google/login/` | – | 302 redirect to Google |

> ✨ **Business Registration** is out-of-scope here; see `/business/register` flow.

---

## 6. State Management & Security

1. **JWT Storage**: Prefer `httponly` cookies set by backend; otherwise, store in memory + refresh silently.
2. **CSRF**: When session auth is used (Django template routes), include CSRF token. For SPA JSON calls with JWT, CSRF is not needed.
3. **Rate-Limiting**: OTP endpoints should be throttled (backend responsibility).
4. **Input Masking**: Phone number field should enforce numeric pattern and auto-format.

---

## 7. Implementation Checklist

- [ ] Build Login page with form validation & error display
- [ ] Implement OTP modal as reusable component
- [ ] Integrate JWT login flow; store tokens; default axios/fetch headers
- [ ] Build Sign-Up page with client-side validation
- [ ] Hook Google social login button to `/accounts/google/login/`
- [ ] Display toast notifications for all error/success events
- [ ] Redirect to Dashboard `/` on successful auth

---

### Appendix A – Tailwind Class Mapping

Both templates rely on TailwindCSS utility classes.  If your front-end uses a different library (e.g. MUI, Chakra, Vuetify), map the semantics:

| Purpose | Tailwind Class | Equivalent Example |
|---------|----------------|--------------------|
| Primary button | `bg-indigo-600 hover:bg-indigo-700` | Chakra: `<Button colorScheme='indigo' variant='solid'/>` |
| Input | `border border-gray-300 rounded-md` | MUI: `<TextField variant='outlined' size='small'/>` |
| Card | `bg-white p-8 rounded-lg shadow` | Vuetify: `<v-card elevation="2" class="pa-8">` |

---

## 8. Business Registration (Self-Service)

### 8.1 Overview

A prospective salon owner can self-serve and create **both** a new business entity **and** their administrator user record in one wizard-style form.  The current Django implementation uses the server-rendered template `business/templates/business/registration.html` and the `BusinessRegistrationForm`.  For a SPA front-end you'll reproduce the same UX and submit JSON to a dedicated endpoint.

| Suggested Route | Purpose |
|-----------------|---------|
| `/business/register` | GET → Render form / POST → submit registration |
| `/business/register/success` | Shown after success with next steps |

### 8.2 Form Sections & Fields

#### 8.2.1 **Business Information**

| UI Label | Form `name` | Type | Validation |
|----------|-------------|------|-----------|
| Business Name | `business_name` | text | required, max 255 chars |
| Business Description | `business_description` | textarea | optional |
| Business Phone | `business_phone` | tel | optional, E.164 |
| Business Email | `business_email` | email | optional |
| Business Website | `business_website` | url | optional |

#### 8.2.2 **Your Information (Administrator)**

| UI Label | Form `name` | Type | Validation |
|----------|------------|------|-----------|
| First Name | `first_name` | text | required |
| Last Name | `last_name` | text | required |
| Email | `email` | email | required, unique |
| Phone Number | `phone_number` | tel | required, E.164 |
| Password | `password` | password | required, min 8, OWASP rules |
| Confirm Password | `password_confirm` | password | must match `password` |
| Accept Terms | `terms_accepted` | checkbox | required |

#### 8.2.3 **Errors & Messages**
* Inline field errors (from backend)
* Non-field errors section
* Global flash message container (success / danger)

### 8.3 Success Page (`/business/register/success`)
After successful submission the user is logged-in and redirected here.  The page lists **Next Steps** as cards:
1. Complete profile (`/business/settings`)
2. Add services (`/services/create`)
3. Invite employees (`/employees/invite`)

Include a "Go to Dashboard" CTA (`/dashboard`).

### 8.4 API Endpoints

> If you keep the server-rendered form you only need the **HTML routes** below.  For a pure SPA you'll expose equivalent JSON endpoints.

| Purpose | Method & Path | Request Body | Success (201 / 302) |
|---------|---------------|--------------|---------------------|
| **Render form** | `GET /business/register/` | – | HTML template |
| **Submit registration (template)** | `POST /business/register/` | `multipart/form-data` with fields in §8.2 | 302 → `/business/register/success` |
| **Submit registration (JSON)** *optional* | `POST /api/v1/business/register/` | ```json
{
  "business": {
    "name": "Salon Peach",
    "description": "Organic hair studio",
    "phone": "+***********",
    "email": "<EMAIL>",
    "website": "https://salonpeach.com"
  },
  "admin_user": {
    "first_name": "Alice",
    "last_name": "Peach",
    "email": "<EMAIL>",
    "phone_number": "+***********",
    "password": "Str0ngP@ssw0rd"
  }
}
``` | ```json
{ "business_id": 42, "user_id": 7, "access": "<jwt>", "refresh": "<jwt>" }
``` |
| **Registration success (JSON)** *optional* | `GET /api/v1/business/register/success/42/` | – | `{ "next_steps": [...] }` |

### 8.5 Front-End Flow

```
User opens /business/register           ─►  GET form
Fill business + admin info              ─►  POST form / JSON
Backend: Business.create_with_admin()   ─►  ✅ creates business + user
Set auth cookie / return JWT            ─►  Redirect /business/register/success
Show next-steps + Go to Dashboard
```

### 8.6 Validation Rules (Mirror Backend)
* Passwords must match.
* `terms_accepted` must be checked.
* Email & phone are unique at User level.
* Business slug generated server-side.

### 8.7 Checklist
- [ ] Build multi-section form with accordion OR single page scroll.
- [ ] Client-side validate passwords match & required fields.
- [ ] After success cache `business_id`, `jwt` into auth store.
- [ ] Show success page with dynamic next-steps links.
- [ ] Offer *Save & continue later* (optional enhancement).

---

**End of Spec** 