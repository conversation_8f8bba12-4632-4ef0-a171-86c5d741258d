# Checkout/POS System API Documentation

## 🎯 概述

本文档定义了美容院 POS 收银系统的完整 API 接口，支持服务预约、产品销售、支付处理、发票管理和退款操作。

---

## 📋 API 端点总览

### 1. 服务管理 (Services)

- `GET /api/services/` - 获取服务列表
- `GET /api/services/{id}/` - 获取服务详情
- `POST /api/services/` - 创建新服务
- `PUT /api/services/{id}/` - 更新服务信息

### 2. 产品管理 (Products)

- `GET /api/products/` - 获取产品列表
- `GET /api/products/{id}/` - 获取产品详情
- `POST /api/products/scan/{barcode}/` - 扫码获取产品

### 3. 订单处理 (Orders)

- `POST /api/orders/` - 创建新订单
- `GET /api/orders/{id}/` - 获取订单详情
- `PUT /api/orders/{id}/` - 更新订单
- `DELETE /api/orders/{id}/` - 取消订单

### 4. 支付处理 (Payments)

- `POST /api/payments/` - 处理支付
- `GET /api/payments/{id}/` - 获取支付详情
- `POST /api/payments/{id}/refund/` - 退款处理

### 5. 发票管理 (Invoices)

- `GET /api/invoices/` - 获取发票列表
- `GET /api/invoices/{id}/` - 获取发票详情
- `POST /api/invoices/` - 生成发票
- `GET /api/invoices/{id}/pdf/` - 下载发票 PDF

### 6. 员工管理 (Staff)

- `GET /api/staff/` - 获取员工列表
- `GET /api/staff/{id}/availability/` - 获取员工可用时间

### 7. 系统设置 (Settings)

- `GET /api/checkout/settings/` - 获取收银设置
- `PUT /api/checkout/settings/` - 更新收银设置

### 8. 报告管理 (Reports)

- `GET /api/reports/transactions/` - 获取交易报告
- `GET /api/reports/transactions/summary/` - 获取交易统计
- `GET /api/reports/transactions/export/` - 导出交易报告
- `GET /api/reports/drawer-balance/` - 获取收银机余额
- `GET /api/reports/sales-stats/` - 获取销售统计
- `GET /api/reports/payment-methods/` - 获取支付方式统计
- `GET /api/reports/staff-sales/` - 获取员工销售统计
- `POST /api/reports/daily/` - 生成日报
- `POST /api/reports/weekly/` - 生成周报
- `POST /api/reports/monthly/` - 生成月报

---

## 🔗 详细 API 规范

### 1. 服务管理 API

#### 获取服务列表

```http
GET /api/services/
Authorization: Bearer {token}

Query Parameters:
- category: string (optional) - 服务类别
- active: boolean (optional) - 是否激活
- search: string (optional) - 搜索关键词

Response (200):
{
  "results": [
    {
      "id": 1,
      "name": "Eyelash Extension Set",
      "description": "全套睫毛嫁接服务",
      "category": "eyelash",
      "duration": 120,
      "price": 150.00,
      "points_earned": 15,
      "active": true,
      "created_at": "2024-01-01T00:00:00Z"
    }
  ],
  "count": 10,
  "next": "...",
  "previous": null
}
```

#### 获取服务详情

```http
GET /api/services/{id}/
Authorization: Bearer {token}

Response (200):
{
  "id": 1,
  "name": "Eyelash Extension Set",
  "description": "全套睫毛嫁接服务",
  "category": "eyelash",
  "duration": 120,
  "price": 150.00,
  "points_earned": 15,
  "active": true,
  "staff_members": [
    {
      "id": 1,
      "name": "Dylan Zhang",
      "avatar": "DZ"
    }
  ],
  "created_at": "2024-01-01T00:00:00Z"
}
```

### 2. 产品管理 API

#### 获取产品列表

```http
GET /api/products/
Authorization: Bearer {token}

Query Parameters:
- category: string (optional) - 产品类别
- in_stock: boolean (optional) - 是否有库存
- search: string (optional) - 搜索关键词

Response (200):
{
  "results": [
    {
      "id": 1,
      "name": "睫毛护理液",
      "description": "专业睫毛护理产品",
      "category": "care",
      "price": 35.00,
      "cost": 20.00,
      "stock_quantity": 50,
      "barcode": "1234567890123",
      "active": true
    }
  ],
  "count": 25,
  "next": "...",
  "previous": null
}
```

#### 扫码获取产品

```http
POST /api/products/scan/{barcode}/
Authorization: Bearer {token}

Response (200):
{
  "id": 1,
  "name": "睫毛护理液",
  "description": "专业睫毛护理产品",
  "category": "care",
  "price": 35.00,
  "stock_quantity": 50,
  "barcode": "1234567890123"
}

Response (404):
{
  "error": "Product not found",
  "message": "未找到该条码对应的产品"
}
```

### 3. 订单处理 API

#### 创建订单

```http
POST /api/orders/
Authorization: Bearer {token}
Content-Type: application/json

Request Body:
{
  "customer_id": 123,
  "items": [
    {
      "type": "service",
      "service_id": 1,
      "staff_id": 1,
      "quantity": 1,
      "price": 150.00,
      "discount": 0.00,
      "start_time": "2024-01-15T14:00:00Z",
      "notes": "客户要求"
    },
    {
      "type": "product",
      "product_id": 1,
      "quantity": 2,
      "price": 35.00,
      "discount": 5.00
    }
  ],
  "payment_methods": [
    {
      "type": "cash",
      "amount": 200.00
    },
    {
      "type": "credit_card",
      "amount": 15.00
    }
  ],
  "discounts": [
    {
      "type": "percentage",
      "value": 10,
      "reason": "会员折扣"
    }
  ],
  "tax_rate": 0.08,
  "tip_amount": 20.00,
  "notes": "订单备注"
}

Response (201):
{
  "id": 1001,
  "order_number": "ORDER-2024-001001",
  "customer": {
    "id": 123,
    "name": "Yuechen Guo",
    "email": "<EMAIL>",
    "phone": "(*************"
  },
  "items": [...],
  "subtotal": 185.00,
  "discount_amount": 18.50,
  "tax_amount": 13.32,
  "tip_amount": 20.00,
  "total_amount": 199.82,
  "amount_paid": 215.00,
  "change_due": 15.18,
  "status": "completed",
  "created_at": "2024-01-15T14:00:00Z",
  "invoice_id": 2001
}
```

#### 获取订单详情

```http
GET /api/orders/{id}/
Authorization: Bearer {token}

Response (200):
{
  "id": 1001,
  "order_number": "ORDER-2024-001001",
  "customer": {
    "id": 123,
    "name": "Yuechen Guo",
    "email": "<EMAIL>",
    "phone": "(*************",
    "membership": "Gold",
    "points": 150
  },
  "items": [
    {
      "id": 1,
      "type": "service",
      "name": "Eyelash Extension Set",
      "staff_name": "Dylan Zhang",
      "quantity": 1,
      "unit_price": 150.00,
      "discount": 0.00,
      "subtotal": 150.00,
      "start_time": "2024-01-15T14:00:00Z",
      "status": "scheduled"
    }
  ],
  "payment_methods": [
    {
      "type": "cash",
      "amount": 200.00,
      "processed_at": "2024-01-15T14:00:00Z"
    }
  ],
  "subtotal": 185.00,
  "discount_amount": 18.50,
  "tax_amount": 13.32,
  "tip_amount": 20.00,
  "total_amount": 199.82,
  "amount_paid": 215.00,
  "change_due": 15.18,
  "status": "completed",
  "created_at": "2024-01-15T14:00:00Z",
  "updated_at": "2024-01-15T14:00:00Z"
}
```

### 4. 支付处理 API

#### 处理支付

```http
POST /api/payments/
Authorization: Bearer {token}
Content-Type: application/json

Request Body:
{
  "order_id": 1001,
  "payment_methods": [
    {
      "type": "credit_card",
      "amount": 199.82,
      "card_token": "tok_visa_1234",
      "card_last_four": "1234"
    }
  ],
  "customer_id": 123
}

Response (200):
{
  "id": 3001,
  "order_id": 1001,
  "total_amount": 199.82,
  "payments": [
    {
      "id": 3002,
      "type": "credit_card",
      "amount": 199.82,
      "status": "completed",
      "transaction_id": "txn_1234567890",
      "processed_at": "2024-01-15T14:00:00Z"
    }
  ],
  "status": "completed",
  "created_at": "2024-01-15T14:00:00Z"
}
```

#### 退款处理

```http
POST /api/payments/{id}/refund/
Authorization: Bearer {token}
Content-Type: application/json

Request Body:
{
  "amount": 50.00,
  "reason": "客户要求退款",
  "items": [
    {
      "order_item_id": 1,
      "quantity": 1
    }
  ]
}

Response (200):
{
  "id": 4001,
  "original_payment_id": 3001,
  "refund_amount": 50.00,
  "reason": "客户要求退款",
  "status": "completed",
  "transaction_id": "refund_1234567890",
  "processed_at": "2024-01-15T15:00:00Z"
}
```

### 5. 发票管理 API

#### 获取发票列表

```http
GET /api/invoices/
Authorization: Bearer {token}

Query Parameters:
- start_date: string (optional) - 开始日期 (YYYY-MM-DD)
- end_date: string (optional) - 结束日期 (YYYY-MM-DD)
- customer_id: integer (optional) - 客户ID
- status: string (optional) - 状态 (paid, pending, overdue)
- search: string (optional) - 搜索关键词

Response (200):
{
  "results": [
    {
      "id": 2001,
      "invoice_number": "INV-2024-002001",
      "customer": {
        "id": 123,
        "name": "Yuechen Guo",
        "email": "<EMAIL>"
      },
      "total_amount": 199.82,
      "paid_amount": 199.82,
      "status": "paid",
      "due_date": "2024-01-15",
      "created_at": "2024-01-15T14:00:00Z"
    }
  ],
  "count": 50,
  "next": "...",
  "previous": null
}
```

#### 生成发票

```http
POST /api/invoices/
Authorization: Bearer {token}
Content-Type: application/json

Request Body:
{
  "order_id": 1001,
  "due_date": "2024-02-15",
  "notes": "感谢您的惠顾"
}

Response (201):
{
  "id": 2001,
  "invoice_number": "INV-2024-002001",
  "order_id": 1001,
  "customer": {
    "id": 123,
    "name": "Yuechen Guo",
    "email": "<EMAIL>",
    "phone": "(*************"
  },
  "items": [...],
  "subtotal": 185.00,
  "tax_amount": 13.32,
  "total_amount": 199.82,
  "paid_amount": 199.82,
  "balance_due": 0.00,
  "status": "paid",
  "due_date": "2024-02-15",
  "created_at": "2024-01-15T14:00:00Z",
  "pdf_url": "/api/invoices/2001/pdf/"
}
```

### 6. 员工管理 API

#### 获取员工列表

```http
GET /api/staff/
Authorization: Bearer {token}

Query Parameters:
- active: boolean (optional) - 是否激活
- services: string (optional) - 服务类别筛选

Response (200):
{
  "results": [
    {
      "id": 1,
      "name": "Dylan Zhang",
      "avatar": "DZ",
      "position": "高级美睫师",
      "services": [
        {
          "id": 1,
          "name": "Eyelash Extension Set"
        }
      ],
      "active": true,
      "created_at": "2024-01-01T00:00:00Z"
    }
  ],
  "count": 4
}
```

#### 获取员工可用时间

```http
GET /api/staff/{id}/availability/
Authorization: Bearer {token}

Query Parameters:
- date: string (required) - 日期 (YYYY-MM-DD)
- service_id: integer (optional) - 服务ID

Response (200):
{
  "staff_id": 1,
  "date": "2024-01-15",
  "available_slots": [
    {
      "start_time": "09:00",
      "end_time": "11:00",
      "available": true
    },
    {
      "start_time": "11:00",
      "end_time": "13:00",
      "available": false,
      "reason": "已预约"
    }
  ]
}
```

### 7. 系统设置 API

#### 获取收银设置

```http
GET /api/checkout/settings/
Authorization: Bearer {token}

Response (200):
{
  "business_id": 1,
  "tax_rate": 0.08,
  "auto_calculate_tax": true,
  "default_payment_method": "cash",
  "accept_tips": true,
  "tip_suggestions": [15, 18, 20],
  "receipt_template": "default",
  "auto_print_receipt": true,
  "currency": "USD",
  "time_zone": "America/Los_Angeles",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

#### 更新收银设置

```http
PUT /api/checkout/settings/
Authorization: Bearer {token}
Content-Type: application/json

Request Body:
{
  "tax_rate": 0.08,
  "auto_calculate_tax": true,
  "default_payment_method": "cash",
  "accept_tips": true,
  "tip_suggestions": [15, 18, 20, 25],
  "auto_print_receipt": true
}

Response (200):
{
  "business_id": 1,
  "tax_rate": 0.08,
  "auto_calculate_tax": true,
  "default_payment_method": "cash",
  "accept_tips": true,
  "tip_suggestions": [15, 18, 20, 25],
  "receipt_template": "default",
  "auto_print_receipt": true,
  "currency": "USD",
  "time_zone": "America/Los_Angeles",
  "updated_at": "2024-01-15T14:00:00Z"
}
```

### 8. 报告管理 API

#### 获取交易报告

```http
GET /api/reports/transactions/
Authorization: Bearer {token}

Query Parameters:
- start_date: string (optional) - 开始日期 (YYYY-MM-DD)
- end_date: string (optional) - 结束日期 (YYYY-MM-DD)
- service_provider: string (optional) - 服务提供者ID
- customer: string (optional) - 客户ID
- page: integer (optional) - 页码
- limit: integer (optional) - 每页数量

Response (200):
{
  "results": [
    {
      "id": 1001,
      "transaction_id": "TXN-2024-001001",
      "checkout_date": "2024-01-15",
      "checkout_by": "Dylan Zhang",
      "app_date": "2024-01-15",
      "customer": "Yuechen Guo",
      "item_sold": "Eyelash Extension Set",
      "sold_by": "Dylan Zhang",
      "source": "POS",
      "qty": 1,
      "price": 150.00,
      "tax": 12.00,
      "tip": 20.00,
      "discount": 0.00,
      "amount_paid": 182.00,
      "cash": 182.00,
      "check": 0.00,
      "gift_card": 0.00,
      "package": 0.00,
      "membership": 0.00,
      "credit_card": 0.00,
      "bank_account": 0.00,
      "vagaro": 0.00,
      "pay_later": 0.00,
      "other": 0.00,
      "iou_invoice": 0.00,
      "points": "0(0)",
      "merchant_account": 0.00,
      "change_due": 0.00,
      "created_at": "2024-01-15T14:00:00Z"
    }
  ],
  "count": 1,
  "next": null,
  "previous": null
}
```

#### 获取交易统计

```http
GET /api/reports/transactions/summary/
Authorization: Bearer {token}

Query Parameters:
- start_date: string (optional) - 开始日期 (YYYY-MM-DD)
- end_date: string (optional) - 结束日期 (YYYY-MM-DD)
- service_provider: string (optional) - 服务提供者ID
- customer: string (optional) - 客户ID

Response (200):
{
  "total_transactions": 15,
  "total_earned": 2850.00,
  "total_tax": 228.00,
  "total_tips": 450.00,
  "total_discounts": 285.00,
  "payment_methods": {
    "cash": 1500.00,
    "credit_card": 1200.00,
    "check": 150.00,
    "gift_card": 0.00,
    "vagaro": 0.00,
    "pay_later": 0.00,
    "other": 0.00,
    "iou_invoice": 0.00,
    "points": 0,
    "merchant_account": 0.00,
    "change_due": 0.00
  },
  "average_transaction": 190.00,
  "date_range": {
    "start": "2024-01-01",
    "end": "2024-01-15"
  }
}
```

#### 导出交易报告

```http
GET /api/reports/transactions/export/
Authorization: Bearer {token}

Query Parameters:
- start_date: string (optional) - 开始日期 (YYYY-MM-DD)
- end_date: string (optional) - 结束日期 (YYYY-MM-DD)
- service_provider: string (optional) - 服务提供者ID
- customer: string (optional) - 客户ID
- format: string (optional) - 导出格式 (csv, xlsx, pdf)

Response (200):
[Binary file content]
Content-Type: application/octet-stream
Content-Disposition: attachment; filename="transactions_2024-01-01_2024-01-15.csv"
```

#### 获取收银机余额

```http
GET /api/reports/drawer-balance/
Authorization: Bearer {token}

Query Parameters:
- date: string (optional) - 日期 (YYYY-MM-DD)

Response (200):
{
  "date": "2024-01-15",
  "balance": 2500.00,
  "transactions": [
    {
      "type": "sale",
      "amount": 150.00,
      "time": "2024-01-15T14:00:00Z"
    },
    {
      "type": "refund",
      "amount": -50.00,
      "time": "2024-01-15T15:30:00Z"
    }
  ],
  "opening_balance": 2400.00,
  "closing_balance": 2500.00
}
```

#### 获取销售统计

```http
GET /api/reports/sales-stats/
Authorization: Bearer {token}

Query Parameters:
- start_date: string (optional) - 开始日期 (YYYY-MM-DD)
- end_date: string (optional) - 结束日期 (YYYY-MM-DD)
- group_by: string (optional) - 分组方式 (day, week, month)

Response (200):
{
  "total_sales": 15000.00,
  "total_orders": 75,
  "average_order_value": 200.00,
  "top_services": [
    {
      "service": "Eyelash Extension Set",
      "count": 25,
      "revenue": 3750.00
    }
  ],
  "sales_by_period": [
    {
      "period": "2024-01-01",
      "sales": 1200.00,
      "orders": 6
    }
  ]
}
```

#### 获取支付方式统计

```http
GET /api/reports/payment-methods/
Authorization: Bearer {token}

Query Parameters:
- start_date: string (optional) - 开始日期 (YYYY-MM-DD)
- end_date: string (optional) - 结束日期 (YYYY-MM-DD)

Response (200):
{
  "payment_methods": [
    {
      "method": "cash",
      "amount": 8500.00,
      "count": 45,
      "percentage": 56.7
    },
    {
      "method": "credit_card",
      "amount": 6000.00,
      "count": 25,
      "percentage": 40.0
    },
    {
      "method": "check",
      "amount": 500.00,
      "count": 5,
      "percentage": 3.3
    }
  ],
  "total_amount": 15000.00,
  "total_transactions": 75
}
```

#### 获取员工销售统计

```http
GET /api/reports/staff-sales/
Authorization: Bearer {token}

Query Parameters:
- start_date: string (optional) - 开始日期 (YYYY-MM-DD)
- end_date: string (optional) - 结束日期 (YYYY-MM-DD)
- staff_id: integer (optional) - 员工ID

Response (200):
{
  "staff_sales": [
    {
      "staff_id": 1,
      "staff_name": "Dylan Zhang",
      "total_sales": 4500.00,
      "total_orders": 30,
      "average_order": 150.00,
      "services_performed": [
        {
          "service": "Eyelash Extension Set",
          "count": 15,
          "revenue": 2250.00
        }
      ]
    }
  ],
  "total_staff_sales": 12000.00,
  "period": {
    "start": "2024-01-01",
    "end": "2024-01-15"
  }
}
```

---

## 🔧 前端集成指南

### 1. 创建 Checkout API 服务

```javascript
// src/features/checkout/services/checkoutApi.js
class CheckoutApiService {
  constructor() {
    this.baseURL = import.meta.env.VITE_API_URL || "http://localhost:8000/api";
  }

  // 获取服务列表
  async getServices(params = {}) {
    return await this.makeRequest("/services/", { params });
  }

  // 获取产品列表
  async getProducts(params = {}) {
    return await this.makeRequest("/products/", { params });
  }

  // 扫码获取产品
  async scanProduct(barcode) {
    return await this.makeRequest(`/products/scan/${barcode}/`, {
      method: "POST",
    });
  }

  // 创建订单
  async createOrder(orderData) {
    return await this.makeRequest("/orders/", {
      method: "POST",
      body: JSON.stringify(orderData),
    });
  }

  // 处理支付
  async processPayment(paymentData) {
    return await this.makeRequest("/payments/", {
      method: "POST",
      body: JSON.stringify(paymentData),
    });
  }

  // 获取员工列表
  async getStaff(params = {}) {
    return await this.makeRequest("/staff/", { params });
  }

  // 获取员工可用时间
  async getStaffAvailability(staffId, date, serviceId) {
    const params = { date };
    if (serviceId) params.service_id = serviceId;
    return await this.makeRequest(`/staff/${staffId}/availability/`, {
      params,
    });
  }
}

export const checkoutApi = new CheckoutApiService();
```

### 2. 环境变量配置

```env
# .env
VITE_API_URL=http://localhost:8000/api
VITE_STRIPE_PUBLIC_KEY=pk_test_...
```

### 3. 状态管理建议

```javascript
// src/features/checkout/hooks/useCheckout.js
export function useCheckout() {
  const [services, setServices] = useState([]);
  const [products, setProducts] = useState([]);
  const [staff, setStaff] = useState([]);
  const [cartItems, setCartItems] = useState([]);
  const [loading, setLoading] = useState(false);

  const loadServices = async () => {
    setLoading(true);
    try {
      const response = await checkoutApi.getServices();
      setServices(response.results);
    } catch (error) {
      console.error("Failed to load services:", error);
    } finally {
      setLoading(false);
    }
  };

  const createOrder = async (orderData) => {
    setLoading(true);
    try {
      const order = await checkoutApi.createOrder(orderData);
      return order;
    } catch (error) {
      console.error("Failed to create order:", error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  return {
    services,
    products,
    staff,
    cartItems,
    loading,
    loadServices,
    createOrder,
  };
}
```

---

## 📊 数据模型

### 订单状态流转

```
pending → confirmed → in_progress → completed → cancelled
```

### 支付状态

```
pending → processing → completed → failed → refunded
```

### 服务预约状态

```
scheduled → checked_in → in_progress → completed → no_show
```

---

## 🚀 实现优先级

### 第一阶段 (核心功能)

- [ ] 服务和产品管理 API
- [ ] 基础订单创建 API
- [ ] 现金支付处理
- [ ] 简单发票生成

### 第二阶段 (支付集成)

- [ ] 信用卡支付集成
- [ ] 礼品卡支付
- [ ] 退款处理 API

### 第三阶段 (高级功能)

- [ ] 员工排班管理
- [ ] 库存管理
- [ ] 报表统计
- [ ] 设置管理

---

## 🔒 安全考虑

1. **支付安全**: 所有支付信息使用 Token 化处理
2. **数据加密**: 敏感数据传输使用 HTTPS
3. **权限控制**: 基于角色的访问控制
4. **审计日志**: 记录所有财务操作
5. **数据备份**: 定期备份订单和支付数据

---

## 📝 测试建议

1. **单元测试**: 每个 API 端点的输入输出验证
2. **集成测试**: 完整的结账流程测试
3. **性能测试**: 高并发订单处理测试
4. **安全测试**: 支付流程安全测试

---

这个 API 文档为您的 checkout 系统提供了完整的后端支持。建议您先实现核心功能，然后逐步扩展到更复杂的特性。
